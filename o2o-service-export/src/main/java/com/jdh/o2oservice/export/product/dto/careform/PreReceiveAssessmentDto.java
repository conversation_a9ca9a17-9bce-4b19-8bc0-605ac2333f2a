package com.jdh.o2oservice.export.product.dto.careform;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/28
 * @description 接单前评估
 */
@Data
public class PreReceiveAssessmentDto {

    private Integer show;//1展示 0不展示

    private String evaluatePoint;//评估重点

    private Integer evaluatePointShow;//1展示 0不展示

    private Integer customEvaluateShow;//1展示 0不展示

    private List<Long> customEvaluate;//个性化评估信息

    private List<QuestionDto> customEvaluateQues;//查询详情时,把题列表渲染出来

}
