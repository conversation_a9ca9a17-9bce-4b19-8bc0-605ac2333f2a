package com.jdh.o2oservice.export.laboratory.cmd;

import lombok.*;

/**
 * 
 */
@Data
public class JdhStoreTransferStationAddParam {

    /** 接驳点类型 1-实验室自收样  2-无人机 */
    private Integer stationType;

    /** 京东接驳点id */
    private Long jdStationId;

    /** 京东接驳点名称 */
    private String jdStationName;

    /** 京东接驳点地址 */
    private String jdStationAddress;

    /** 京东接驳点维度 */
    private Double jdStationLatitude;

    /** 京东接驳点经度 */
    private Double jdStationLongitude;

    /** 下一个京东站点id */
    private Long jdStationTargetId;

    /** 外部系统接驳点id */
    private String thirdStationId;

    /** 下一个外部系统接驳点id */
    private String thirdStationTargetId;
}