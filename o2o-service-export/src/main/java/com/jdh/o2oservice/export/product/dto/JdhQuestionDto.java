package com.jdh.o2oservice.export.product.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/5/25
 * @description 题库
 */
@Data
public class JdhQuestionDto {

    private Long id;

    private String name;//题目名称

    private String quesDesc;//题目说明

    private Integer type;//1单选 2多选 3填空 4图片/视频上传 5题库

    private Integer required;//1必填 0非必填

    private Long quesId;//题目唯一单据

    private String quesCode;//题编码

    private Integer highRisk;//1高危 0否

    private Long version;//版本号

    private String unit;//单位

    private String extJson;//扩展字段 保存多选项
}
