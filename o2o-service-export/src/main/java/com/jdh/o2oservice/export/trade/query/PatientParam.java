package com.jdh.o2oservice.export.trade.query;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * ReceiveJdOrderAndAppointmentParam 收单入参
 *
 * <AUTHOR>
 * @version 2024/3/7 14:56
 **/
@Data
public class PatientParam implements Serializable {

    /**
     * 患者id
     */
    private Long patientId;

    /**
     * 预约人姓名
     */
    private String name;

    /**
     * 预约人手机号
     */
    private String phone;

    /**
     * 证件类型
     */
    private Integer credentialType;

    /**
     * 证件号码
     */
    private String credentialNo;

    /**
     * 生日 格式yyyy-MM-dd
     */
    private String birthday;

    /**
     * 性别【1男，2女】
     */
    private Integer gender;

}
