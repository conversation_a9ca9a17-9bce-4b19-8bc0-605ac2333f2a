package com.jdh.o2oservice.export.trade.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @ClassName StandardOrderDetailRequest
 * @Description
 * <AUTHOR>
 * @Date 2025/6/26 21:09
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StandardOrderDetailRequest implements Serializable {

    /**
     * 订单号
     */
    private Long orderId;

    /**
     * 业务身份
     */
    private String verticalCode;

    /**
     * 账号pin
     */
    private String userPin;
}