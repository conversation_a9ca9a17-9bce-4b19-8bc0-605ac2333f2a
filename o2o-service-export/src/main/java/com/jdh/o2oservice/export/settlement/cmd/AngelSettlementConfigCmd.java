package com.jdh.o2oservice.export.settlement.cmd;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 护士服务结算配置
 * <AUTHOR>
 * @Date 2025/4/22 14:57
 **/
@Data
public class AngelSettlementConfigCmd implements Serializable {
    /**
     * '费项配置id'
     */
    private Long feeConfigId;
    /**
     * 省编码
     */
    private String provinceCode;
    /**
     * 省名称
     */
    private String provinceName;
    /**
     * 市编码
     */
    private String cityCode;
    /**
     * 市名称
     */
    private String cityName;
    /**
     * 县地区code
     */
    private String countyCode;
    /**
     * 区名称
     */
    private String countyName;
    /**
     * 乡镇code
     */
    private String townCode;
    /**
     * 乡镇
     */
    private String townName;
    /**
     * 护士类型 0：兼职 1：全职
     * JobNatureEnum
     */
    private String angelType;
    /**
     * 上门费
     */
    private String onSiteFee;
    /**
     * 即时加价
     */
    private String immediatelyFee;
    /**
     * 节假日加价
     */
    private String holidayFee;
    /**
     * 夜间加价
     */
    private String nightDoorFee;
    /**
     * 动态调整费
     */
    private String dynamicAdjustFee;

    /**
     * 操作人
     */
    private String operator;
    /**
     * '费项配置id List'
     */
    private List<Long> feeConfigIdList;

    /**
     * 覆盖
     */
    private Boolean cover = false;
}
