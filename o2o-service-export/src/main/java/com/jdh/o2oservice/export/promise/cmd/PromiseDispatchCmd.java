package com.jdh.o2oservice.export.promise.cmd;


import com.jdh.o2oservice.common.result.request.AbstractBusinessIdentity;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 履约单延期命令
 *
 * @author: yang<PERSON><PERSON>
 * @date: 2022/8/30 10:11 下午
 * @version: 1.0
 */
@Data
@Builder
public class PromiseDispatchCmd extends AbstractBusinessIdentity implements Serializable {

    /**
     * 履约单ID
     */
    private Long promiseId;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 操作角色
     * @see com.jdh.o2oservice.common.enums.OperatorRoleTypeEnum
     */
    private Integer operatorRoleType;


    /**
     * 意向派单ID
     */
    private List<Long> intendedAngelIds;

    /**
     * 操作原因
     */
    private String reason;
}
