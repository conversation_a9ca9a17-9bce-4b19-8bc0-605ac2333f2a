package com.jdh.o2oservice.export.product.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/5/27
 * @description 题库
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class QueryQuesByGroupCode {

    private String groupCode;//节点code

    private Long serviceItemId;//服务项目id

    private String businessMode;// 业务模式

    private String serviceType;// 服务类型
}
