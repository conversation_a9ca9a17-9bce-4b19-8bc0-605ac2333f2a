package com.jdh.o2oservice.export.angelpromise.dto;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class AngelServiceInfoDto implements Serializable {

    /**
     * 被服务人姓名
     */
    private String name;
    /**
     * 被服务人性别
     */
    private String sex;
    /**
     * 被服务人年龄
     */
    private Integer age;
    /**
     * 预约时间
     */
    private Date planTime;
    /**
     * 地址
     */
    private String address;
    /**
     * 服务项目
     */
    private String serviceItem;
}
