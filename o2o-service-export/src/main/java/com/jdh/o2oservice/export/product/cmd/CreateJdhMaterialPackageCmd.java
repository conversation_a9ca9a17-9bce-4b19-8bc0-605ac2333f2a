package com.jdh.o2oservice.export.product.cmd;

import lombok.Data;

import javax.validation.constraints.*;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <pre>
 *  京东耗材包管理
 * </pre>
 *
 */
@Data
public class CreateJdhMaterialPackageCmd implements Serializable {

    /**
     * <pre>
     * 耗材包名称
     * </pre>
     */
    @NotBlank(message = "耗材名称不允许为空")
    @Size(max = 10, message = "耗材名称最大10个字符")
    private String materialPackageName;

    /**
     * <pre>
     * 耗材包详细清单
     * </pre>
     */
    @NotBlank(message = "耗材物品清单不允许为空")
    private String materialPackageDetail;

    /**
     * <pre>
     * 耗材包商品id
     * </pre>
     */
    @NotNull(message = "耗材商品sku不允许为空")
    private Long skuId;
    
    /**
     * <pre>
     * 备注
     * </pre>
     */
    private String remark;

    /**
     * <pre>
     * erp
     * </pre>
     */
    private String erp;

    /**
     * 自营护士结算价
     */
    @NotNull(message = "自营护士结算价不允许为空")
    @DecimalMin(value = "0.00", inclusive = true, message = "自营护士结算价必须大于等于0")
    @DecimalMax(value = "9999.99", inclusive = true, message = "自营护士结算价最大值为9999.99")
    //@Digits(integer = 4, fraction = 2, message = "自营护士结算价最多保留两位小数")
    private BigDecimal selfAngelSettlementPrice;

    /**
     * 兼职护士结算价
     */
    @NotNull(message = "兼职护士结算价不允许为空")
    @DecimalMin(value = "0.00", inclusive = true, message = "兼职护士结算价必须大于等于0")
    @DecimalMax(value = "9999.99", inclusive = true, message = "兼职护士结算价最大值为9999.99")
    //@Digits(integer = 4, fraction = 2, message = "兼职护士结算价最多保留两位小数")
    private BigDecimal partAngelSettlementPrice;
}