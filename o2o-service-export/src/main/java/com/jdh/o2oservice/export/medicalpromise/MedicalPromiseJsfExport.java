package com.jdh.o2oservice.export.medicalpromise;

import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.export.medicalpromise.cmd.MedicalPromiseCallbackCmd;
import com.jdh.o2oservice.export.medicalpromise.cmd.MedicalPromiseHandCmd;
import com.jdh.o2oservice.export.medicalpromise.cmd.MedicalPromiseReportCmd;
import com.jdh.o2oservice.export.medicalpromise.cmd.UpdateMedicalPromiseEtaCmd;
import com.jdh.o2oservice.export.medicalpromise.dto.MedicalPromiseCallbackResultDTO;
import com.jdh.o2oservice.export.medicalpromise.dto.MedicalPromiseDTO;
import com.jdh.o2oservice.export.medicalpromise.dto.MedicalPromiseFullDTO;
import com.jdh.o2oservice.export.medicalpromise.dto.MedicalPromisePushReportDTO;
import com.jdh.o2oservice.export.medicalpromise.query.LabQueryMedPromisePageRequest;
import com.jdh.o2oservice.export.medicalpromise.query.MedicalPromiseListRequest;
import com.jdh.o2oservice.export.medicalpromise.query.MedicalPromiseReportVerifyRequest;
import com.jdh.o2oservice.export.medicalpromise.query.MedicalPromiseRequest;
import com.jdh.o2oservice.export.provider.dto.ExportDataDTO;
import com.jdh.o2oservice.export.report.dto.QuickStructReportVerifyResultDTO;

import java.util.List;

/**
 * Description: 检测单JSF接口
 * Interface: MedicalPromiseJsfExport
 * Author: wangpengfei144
 * Date: 2024/4/16
 */
public interface MedicalPromiseJsfExport {
    /**
     * 根据条件分页查询检测单
     * @param medicalPromiseListRequest
     * @return
     */
    Response<List<MedicalPromiseDTO>> queryMedicalPromisePage(MedicalPromiseListRequest medicalPromiseListRequest);

    /**
     * 检测单状态回传
     * @param medicalPromiseCallbackCmd
     * @return
     */
    Response<MedicalPromiseCallbackResultDTO> medicalPromiseCallBack(MedicalPromiseCallbackCmd medicalPromiseCallbackCmd);

    /**
     * 推送检测单结构化报告
     * @param medicalPromiseReportCmd
     * @return
     */
    Response<MedicalPromisePushReportDTO> pushMedicalPromiseReport(MedicalPromiseReportCmd medicalPromiseReportCmd);

    /**
     * 创建检测单
     * @param medicalPromiseHandCmd
     * @return
     */
    Response<Boolean> handCreateMedicalPromise(MedicalPromiseHandCmd medicalPromiseHandCmd);

    /**
     * 根据条件查询检测单
     * @param medicalPromiseRequest
     * @return
     */
    Response<MedicalPromiseDTO> queryMedicalPromise(MedicalPromiseRequest medicalPromiseRequest);

    /**
     * 根据条件查询检测单
     * @param medicalPromiseListRequest
     * @return
     */
    Response<List<MedicalPromiseDTO>> queryMedicalPromiseList(MedicalPromiseListRequest medicalPromiseListRequest);

    /**
     * 分页查询检测单综合数据
     * @param labQueryMedPromisePageRequest
     * @return
     */
    Response<PageDto<MedicalPromiseFullDTO>> labQueryMedicalPromisePage(LabQueryMedPromisePageRequest labQueryMedPromisePageRequest);

    /**
     * 导出门店下检测单
     * @param labQueryMedPromisePageRequest
     * @return
     */
    Response<ExportDataDTO> exportMedicalPromiseList(LabQueryMedPromisePageRequest labQueryMedPromisePageRequest);

    /**
     * 校验结构化报告
     *
     * @param reportResult reportResult
     * @return dto dto
     */
    Response<QuickStructReportVerifyResultDTO> verifyStructQuickReport(List<MedicalPromiseReportVerifyRequest> reportResult);

    /**
     * 更新检测单eta信息
     *
     * @param medicalPromiseEtaCmd UpdateMedicalPromiseEtaCmd
     * @return {@link Response }<{@link Boolean }>
     */
    Response<Boolean> updateMedicalPromiseEta(UpdateMedicalPromiseEtaCmd medicalPromiseEtaCmd);;

    /**
     * 分页查询检测单综合数据
     * @param labQueryMedPromisePageRequest
     * @return
     */
    Response<MedicalPromiseFullDTO> labQueryMedicalPromiseDetail(LabQueryMedPromisePageRequest labQueryMedPromisePageRequest);


}
