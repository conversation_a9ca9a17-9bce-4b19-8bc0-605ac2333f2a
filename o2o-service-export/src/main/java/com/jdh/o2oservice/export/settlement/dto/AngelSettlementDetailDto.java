package com.jdh.o2oservice.export.settlement.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.Objects;

/**
 * <p>
 * 护士结算明细
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-13 05:53:18
 */
@Data
public class AngelSettlementDetailDto implements Serializable {


    private static final long serialVersionUID = -3878707159265370490L;
    /**
     * ID主键
     */
    private Long id;

    /**
     * 结算id
     */
    private Long settleId;

    /**
     * 费用项目名称
     */
    private String feeName;

    /**
     * 金额
     */
    private BigDecimal settleAmount;

    /**
     * 银行卡号
     */
    private String bankNo;

    /**
     * 银行名称
     */
    private String bankName;


    /**
     * 银行logo
     */
    private String bankLogo;


    /**
     * 背景图logo
     */
    private String bankLogoBackground;

    /**
     * 个税代缴
     */
    private BigDecimal tax;

    /**
     * 到账金额
     */
    private BigDecimal receivedAmount;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 是否有效 0-无效 1-有效
     */
    private Integer yn;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 更新人
     */
    private String updateUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 收入类型 1上门检测服务 2上门护理服务 3调整项 4 激励
     */
    private Integer itemType;

    /**
     * 收入类型 1上门检测服务 2上门护理服务 3调整项 4 激励
     */
    private String itemTypeDesc;

    /**
     * 费项说明
     */
    private String feeDescription;

    /**
     * 金额字符串
     * 保留两位小数
     * @return
     */
    public String getSettleAmountStr(){
        return Objects.isNull(settleAmount) ? "" : settleAmount.setScale(2, RoundingMode.HALF_UP).toPlainString();
    }

    /**
     * 个税代缴字符串
     * 保留两位小数
     * @return
     */
    public String getTaxStr(){
        return Objects.isNull(tax) ? "" : tax.setScale(2, RoundingMode.HALF_UP).toPlainString();
    }

    /**
     * 到账金额字符串
     * 保留两位小数
     * @return
     */
    public String getReceivedAmountStr(){
        return Objects.isNull(receivedAmount) ? "" : receivedAmount.setScale(2, RoundingMode.HALF_UP).toPlainString();
    }
}
