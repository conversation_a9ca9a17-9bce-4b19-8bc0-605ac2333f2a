package com.jdh.o2oservice.export.product.dto.careform;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.apache.tomcat.util.json.JSONParser;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/28
 * @description 服务前评估
 */
@Data
public class PreServiceAssessmentDto {

    private Integer show;//1展示 0不展示

    private String evaluatePoint;//评估重点

    private Integer evaluatePointShow;//1展示 0不展示

    private Integer customEvaluateShow;//1展示 0不展示

    private List<Long> customEvaluate;//个性化评估

    private Integer historyDisease;//历史疾病 1需要 0不需要

    private Integer vitalSigns;//生命体征 1需要 不需要

    private List<QuestionDto> customEvaluateQues;//查询详情时,把题列表渲染出来

}
