package com.jdh.o2oservice.export.support.command;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/30 21:51
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ParseVoiceCmd {

    private String domianCode;
    private String aggregateCode;
    private String aggregateId;
    private List<Long> fileIds;
    private Integer voiceType;

    /**
     * 通话开始时间
     */
    private Date startTime;

    /**
     * 通话结束时间
     */
    private Date finishTime;

    /**
     * 通话时长，单位秒
     */
    private Long callDuration;
    /**
     * 通话ID，唯一确定一次通话
     */
    private String callId;
}
