package com.jdh.o2oservice.export.product.query;

import com.jdh.o2oservice.common.result.request.AbstractPageQuery;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Set;

/**
 * @ClassName:ServiceItemQuery
 * @Description:
 * @Author: yaoqinghai
 * @Date: 2024/3/22 11:01
 * @Vserion: 1.0
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ServiceItemQuery extends AbstractPageQuery implements Serializable {

    private static final long serialVersionUID = 1981162572486360003L;
    /**
     * 项目编码
     */
    private Set<Long> itemIds;

    /**
     * 项目名称
     */
    private String itemName;

    /**
     * 商家类型 1-自营 2-POP
     */
    private Integer vendorType;

    /**
     * 商品一级类目
     */
    private Long firstSkuCategory;

    /**
     * 商品二级类目
     */
    private Long secondSkuCategory;

    /**
     * 商品三级类目
     */
    private Long thirdSkuCategory;

    /**
     * 一级分类编码
     */
    private Set<Long> firstIndicatorCategory;

    /**
     * 二级分类编码
     */
    private Set<Long> secondIndicatorCategory;

    /**
     * 三级分类编码
     */
    private Set<Long> thirdIndicatorCategory;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 项目类型1:供应商体检项目 2 检测类 3 护理类
     */
    private Integer itemType;

    /**
     * 项目类型1:供应商体检项目 2 检测类 3 护理类
     */
    private Set<Integer> itemTypes;

    /**
     * 修改人
     */
    private String updateUser;

    /**
     * 供应商（渠道）编码
     */
    private String channelNo;

    /**
     * 项目来源 1-消费医疗自定义,2-全国医疗服务技术规范
     */
    private Integer itemSource;

    /**
     * 项目名称-英文
     */
    private String itemNameEn;

    /**
     * 项目编码
     */
    private Long itemId;

    /**
     * 是否查询费项配置
     */
    private Boolean queryPriceFeeConfig;
}
