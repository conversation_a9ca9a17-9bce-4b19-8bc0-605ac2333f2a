package com.jdh.o2oservice.export.settlement.cmd;

import lombok.Data;

import java.io.Serializable;

/**
 * 护士服务结算配置
 * <AUTHOR>
 * @Date 2024/5/8 14:57
 **/
@Data
public class CityAngelSettlementConfigCmd implements Serializable {
    /**
     * '地区配置id'
     */
    private Long cityConfigId;
    /**
     * 省编码
     */
    private String provinceCode;
    /**
     * 省名称
     */
    private String provinceName;
    /**
     * 市编码
     */
    private String cityCode;
    /**
     * 市名称
     */
    private String cityName;
    /**
     * 地区等级
     */
    private String level;
    /**
     * 操作人
     */
    private String operator;
    /**
     * 自营护士服务系统
     */
    private String selfServiceCoefficient;
    /**
     * 兼职护士服务系统
     */
    private String sidelineServiceCoefficient;
    /**
     * 覆盖
     */
    private Boolean cover = false;
}
