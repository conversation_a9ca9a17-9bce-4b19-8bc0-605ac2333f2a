package com.jdh.o2oservice.export.trade.query;

import com.jdh.o2oservice.common.result.request.AbstractPageQuery;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> limeng363
 * @Date : 2024-05-09 15:19
 * @Desc :
 */
@Data
public class JdOrderFullPageParam  extends AbstractPageQuery implements Serializable {
    /**
     * 主键id
     */
    private String id;
    /**
     * 订单来源
     */
    private String partnerSource;

    /** 垂直业务身份编码 */
    private String verticalCode;

    /** 服务类型 */
    private String serviceType;
    /**
     * 订单id
     */
    private String orderId;
    /**
     * 服务单号（检测单id）
     */
    private String medicalPromiseId;
    /**
     * 样本编号
     */
    private String specimenCode;
    /**
     * 是否上传服务记录
     */
    private String taskBizExtStatus;
    /**
     * 用户pin
     */
    private String userPin;
    /**
     * 服务状态
     */
    private String commonStatus;
    /**
     * 护士姓名
     */
    private String angelName;
    /**
     * 检测人电话
     */
    private String userPhone;
    /**
     * 实验室名称
     */
    private String laboratoryStationName;
    /**
     * 订单创建时间-开始
     */
    private Long orderCreateTimeStart;
    /**
     * 订单创建时间-结束
     */
    private Long orderCreateTimeEnd;
    /**
     * 预约时间-开始
     */
    private Long appointmentStartTime;
    /**
     * 预约时间-结束
     */
    private Long appointmentEndTime;

    /**
     * 收样时间-开始
     */
    private Long checkStartTime;
    /**
     * 收样时间-结束
     */
    private Long checkEndTime;
    /**
     * 履约单号
     */
    private String promiseId;

    /**
     * 是否包含加项商品
     */
    private Integer includeAddSku;

    /**
     * 商品类型
     */
    private Integer wareType;

    /**
     * 订单类型
     */
    private String orderType;

    private String stationId;//实验室id

    private List<Integer> medicalPromiseStatus;//

    private String outShipId;//

    /**
     * 服务项目名称
     */
    private String serviceItemName;
}

