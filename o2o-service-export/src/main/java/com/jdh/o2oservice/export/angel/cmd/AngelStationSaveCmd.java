package com.jdh.o2oservice.export.angel.cmd;

import lombok.Data;

import java.util.List;

/**
 * @ClassName: AngelStationSaveCmd
 * @Description:
 * @Author: yaoqinghai
 * @Date: 2024/4/26 20:45
 * @Vserion: 1.0
 **/
@Data
public class AngelStationSaveCmd {

    /**
     * 服务站名称
     */
    private Long angelStationId;

    /**
     * 服务站名称
     */
    private String angelStationName;

    /**
     * 范围的衡量类型（1：公里数，2：分钟）
     */
    private Integer fenceRangeType;

    /**
     * 范围的衡量值（如xx公里或xx分钟）
     */
    private Integer fenceRangeRadius;

    /**
     * 中心点纬度
     */
    private String fenceRangeCenterLat;

    /**
     * 中心点经度
     */
    private String fenceRangeCenterLng;

    /**
     * 服务站地址
     */
    private String addressDetail;

    /**
     * 服务站地址
     */
    private String fullAddress;

    /**
     * 站长erp
     */
    private String stationMaster;

    /**
     * 围栏id
     */
    private String fenceId;

    /**
     * 地图id
     */
    private Long mapId;

    /**
     * 图层id
     */
    private Long layerId;

    /**
     * 开始营业时间（24小时）
     */
    private String openHour;

    /**
     * 停止营业时间（24小时）
     */
    private String closeHour;

    /**
     * 站点状态：1启用 2停用
     */
    private Integer stationStatus;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 围栏形状 空+1:圆形 2多边形
     */
    private Integer fenceShapeType;

    /**
     * 坐标集合 数据格式: [ [116.562913321,39.79562447840648],[116.562913321,39.79562447840648] ]
     */
    private List<List<Double>> fenceBoundaryList;

    /**
     * 服务资源类型 二进制编码 右向左 1位骑手 2位护士
     */
    private List<Integer> angelTypes;

    /**
     * 骑手供应商  二进制编码 右向左 1位达达 2位顺丰
     */
    private List<Integer> deliverySuppliers;

    /**
     * 实验室id
     */
    private String stationId;

    /**
     * 实验室名称
     */
    private String stationName;

    /**
     * 三方店铺id
     */
    private String outShopId;

    /**
     * 服务资源数量
     */
    private Integer angelNum;

    /**
     * 异常调整数量
     */
    private List<InventoryReadjustCmd> inventoryReadjustCmdList;

    /**
     * 护士资源数量
     */
    private Integer nurseNum;

    /**
     * 护士异常调整数量
     */
    private List<InventoryReadjustCmd> nurseInventoryReadjustCmdList;

    /**
     * 三方店铺id
     */
    private Long jdTransferStationId;
}
