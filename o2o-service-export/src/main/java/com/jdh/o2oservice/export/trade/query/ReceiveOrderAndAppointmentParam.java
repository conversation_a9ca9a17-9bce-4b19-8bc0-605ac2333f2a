package com.jdh.o2oservice.export.trade.query;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * ReceiveJdOrderAndAppointmentParam 收单入参
 *
 * <AUTHOR>
 * @version 2024/3/7 14:56
 **/
@Data
public class ReceiveOrderAndAppointmentParam implements Serializable {

    /**
     * 用户pin
     */
    private String userPin;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 备注信息
     */
    private RemarkParam remarkParam;

    /**
     * 合作方来源
     */
    private Integer partnerSource;

    /**
     * 合作方来源子渠道
     */
    private String saleChannelId;

    /**
     * 外部渠道的订单号
     */
    private String partnerSourceOrderId;

    /**
     * 预约人信息
     */
    private List<PatientParam> patientParamList;

    /**
     * 预约时间
     */
    private AppointmentTimeParam appointmentTimeParam;

    /**
     * 意向护士
     */
    private IntendedNurseParam intendedNurse;

}
