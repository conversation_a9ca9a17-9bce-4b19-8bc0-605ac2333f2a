package com.jdh.o2oservice.export.settlement.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 护士服务结算配置
 * <AUTHOR>
 * @Date 2024/5/8 14:57
 **/
@Data
public class AngelSettlementConfigDto implements Serializable {

    /**
     * '费项配置id'
     */
    private Long feeConfigId;
    /**
     * 省编码
     */
    private String provinceCode;
    /**
     * 市编码
     */
    private String cityCode;

    /**
     * 县地区code
     */
    private String countyCode;
    /**
     * 乡镇code
     */
    private String townCode;

    /**
     * 省名称
     */
    private String provinceName;

    /**
     * 市名称
     */
    private String cityName;
    /**
     * 区名称
     */
    private String countyName;
    /**
     * 乡镇
     */
    private String townName;
    /**
     * 目标地址code
     */
    private String destCode;
    /**
     * 操作人
     */
    private String operator;
    /**
     * 护士类型 0：兼职 1：全职
     * JobNatureEnum
     */
    private String angelType;
    /**
     * 护士类型 0：兼职 1：全职
     */
    private String angelTypeDesc;

    /**
     * 上门费
     */
    private String onSiteFee;
    /**
     * 即时加价
     */
    private String immediatelyFee;
    /**
     * 节假日加价
     */
    private String holidayFee;
    /**
     * 夜间加价
     */
    private String nightDoorFee;
    /**
     * 动态调整费
     */
    private String dynamicAdjustFee;
}
