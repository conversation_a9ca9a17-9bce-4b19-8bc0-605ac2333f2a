package com.jdh.o2oservice.export.angelpromise.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author:lichen55
 * @createTime: 2024-04-18 10:15
 * @Description: 服务者履约域订单信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AngelWorkOrderDto {

    /**
     * 订单Id
     */
    private Long orderId;

    /**
     * 订单备注
     */
    private String remark;

    /**
     * 预约人姓名
     */
    private String userName;

    /**
     * 预约人联系方式
     */
    private String userPhone;

    /**
     * 服务详细地址
     */
    private String userFullAddress;

    /**
     * 服务详细地址经度
     */
    private double userLng;

    /**
     * 服务详细地址纬度
     */
    private double userLat;

    /**
     * 服务开始时间
     */
    private String serviceStartTime;

    /**
     * 总距离（千米）
     */
    private Double kmDistance;

    /**
     * 方案估算时间（含路况）分钟
     */
    private Double duration;

    /**
     * 预约人加密电话
     */
    private String userPhoneEncrypt;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 服务者外呼
     */
    private AngelWorkCallDto angelWorkCall;
}
