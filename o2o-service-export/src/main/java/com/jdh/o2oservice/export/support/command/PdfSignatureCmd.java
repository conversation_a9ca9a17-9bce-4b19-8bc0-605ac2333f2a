package com.jdh.o2oservice.export.support.command;

import com.jdh.o2oservice.export.support.dto.PdfSignatureDto;
import lombok.Data;

import java.util.List;

/**
 * PDF签名
 * @author: yang<PERSON>yu
 * @date: 2024/5/9 6:27 下午
 * @version: 1.0
 */
@Data
public class PdfSignatureCmd {
    /**
     * 领域编码
     */
    private String domainCode;
    /**
     * 文件类型
     */
    private String fileBizType;
    /**
     * 签名图片的文件ID
     */
    private Long signatureImageFileId;
    /**
     * 签名PDF的文件IDs
     */
    private List<PdfSignatureDto> signaturePDFFiles;
    /**
     * 签名PDF的文件Url
     */
    private String signaturePDFFileUrl;
    /** 签名位置 */
    private String positionJson;
    /** 签名图片大小 */
    private Float[] scale;
    /**
     * 代签人关系
     * 0-本人、1-配偶、2-父母、3-子女、4-其它
     **/
    private Integer relation;

    /**
     * 代签人关系选择其它时的补充内容
     **/
    private String relationStr;

    private Integer signatureType;
}
