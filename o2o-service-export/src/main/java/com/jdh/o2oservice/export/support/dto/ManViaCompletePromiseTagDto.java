package com.jdh.o2oservice.export.support.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * ManViaCompletePromiseTagDto
 *
 * <AUTHOR>
 * @date 2024/05/24
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ManViaCompletePromiseTagDto implements Serializable {

    /**
     * 上传服务记录
     */
    @Builder.Default
    private Boolean uploadServiceRecord = Boolean.FALSE;

    /**
     * 上传服务记录 时间
     */
    private Date uploadServiceRecordDate;

    /**
     * 展示派单入口
     */
    @Builder.Default
    private Boolean showDispatch = Boolean.FALSE;

    /**
     * 1 - 重派  2- 定向派
     */
    private Integer dispatchBtnType;

    /**
     * 医生着装图片
     */
    private List<String> clothingFileList;


    /**
     * 医疗废物处理图片
     */
    private List<String> wasteDestroyFileList;

    /**
     * 服务记录图片
     */
    private List<String> serviceRecordFileList;

    /**
     * 服务类型
     */
    private String serviceTypeDesc;

    /**
     * 映射状态
     */
    private Integer viaStatus;

    /**
     * 状态描述
     */
    private String statusDesc;

    /**
     * partnerSource
     */
    private Integer partnerSource;

    /**
     * partnerSourceDesc
     */
    private String partnerSourceDesc;

    /**
     * 出报告状态
     */
    @Builder.Default
    private Boolean reportStatus = Boolean.FALSE;

    /**
     * 出报告时间
     */
    private Date reportDate;

    /**
     * 是否已经开始检测
     */
    @Builder.Default
    private Boolean checkStatus = Boolean.FALSE;

    /**
     * 实验室开始检测时间
     */
    private Date checkDate;

    /**
     * provider名称
     */
    private String providerName;

    /**
     * 配送方式
     */
    private String deliveryTypeDesc;

    /**
     * 是否赠品订单
     */
    private Boolean isGiftOrder;

    /**
     * 是否包含加项商品
     */
    private Boolean isIncludeAddSkuOrder;

    /**
     * 订单类型描述
     */
    private String orderTypeDesc;

    /**
     * 电子签名图片
     */
    private List<String> electSignatureFileList;

    /**
     * 知情同意书
     */
    private List<String> letterOfConsentFileList;

    /**
     * 是否已有采样盒
     */
    private Boolean preSampleFlag;

    /**
     * 任务id
     */
    private String taskId;

    /**
     * 就诊记录图片
     */
    private List<String> visitRecordFiles;
}
