package com.jdh.o2oservice.export.angelpromise.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @ClassName AngelWorkItemDto
 * @Description
 * <AUTHOR>
 * @Date 2025/5/27 19:16
 */
@Data
public class AngelWorkItemDto {

    /**
     * 垂直业务身份编码
     */
    private String verticalCode;

    /**
     * 服务类型
     */
    private String serviceType;

    /**
     * 服务工单Id
     */
    private Long workId;

    /**
     * 京东订单Id
     */
    private Long jdOrderId;

    /**
     * 履约单Id
     */
    private Long promiseId;

    /**
     * 服务工单类型：1=骑手,2=护士,3=护工,4=康复师
     */
    private Integer	workType;

    /**
     * 服务者Id
     */
    private String angelId;

    /**
     * 服务者Cpin
     */
    private String angelPin;

    /**
     * 服务者姓名
     */
    private String angelName;

    /**
     * 服务者联系方式
     */
    private String angelPhone;

    /**
     * 服务者工单状态：1待接单，2、已接单，3、待服务，4、服务中，5送检中，6服务完成，7退款中，8已退款，9已取消
     */
    private Integer	status;

    /**
     * 任务暂停状态：0正常，1退款暂停, 2取消暂停
     */
    private Integer stopStatus;

    /**
     * 服务站Id
     */
    private String angelStationId;

    /**
     * 服务开始时间
     */
    private Date workStartTime;

    /**
     * 服务结束时间
     */
    private Date workEndTime;

    /**
     * 计划派时间
     */
    private Date planCallTime;

    /**
     * 计划出门时间
     */
    private Date planOutTime;

    /**
     * 计划完成时间
     */
    private Date planFinishTime;

    /**
     * 项目列表
     */
    private List<WorkItemDto> itemDtoList;
}
