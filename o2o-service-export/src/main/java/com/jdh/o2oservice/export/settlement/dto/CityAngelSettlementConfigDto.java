package com.jdh.o2oservice.export.settlement.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 护士服务结算配置
 * <AUTHOR>
 * @Date 2024/5/8 14:57
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CityAngelSettlementConfigDto implements Serializable {
    /**
     * '地区配置id'
     */
    private Long cityConfigId;
    /**
     * 省编码
     */
    private String provinceCode;
    /**
     * 省名称
     */
    private String provinceName;
    /**
     * 市编码
     */
    private String cityCode;
    /**
     * 市名称
     */
    private String cityName;
    /**
     * 地区等级
     */
    private String level;
    /**
     * 操作人
     */
    private String operator;
    /**
     * 自营护士服务费系数
     */
    private String selfServiceCoefficient;
    /**
     * 兼职护士服务费系数
     */
    private String sidelineServiceCoefficient;

    /**
     * 平台使用费结算系数
     */
    protected String platformServiceCoefficient;

    /**
     * 平台补贴结算系数
     */
    protected String platformSubsidyCoefficient;
}
