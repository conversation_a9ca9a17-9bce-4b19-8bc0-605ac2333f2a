package com.jdh.o2oservice.export.medicalpromise.dto;

import lombok.Data;

import java.util.Date;

/**
 * @Description:
 * @Author: wangpengfei144
 * @Date: 2024/6/10
 */
@Data
public class MedicalPromiseFullDTO {
    /**
     * 检测单ID
     */
    private Long medicalPromiseId;

    /**
     * 垂直业务身份编码
     */
    private String verticalCode;

    /**
     * 服务类型
     */
    private String serviceType;

    /**
     * 服务ID，快检情况下是skuNo
     */
    private Long serviceId;

    /**
     * 用户pin
     */
    private String userPin;

    /**
     * 履约单ID
     */
    private Long promiseId;

    /**
     * 服务单ID
     */
    private Long voucherId;

    /**
     * 患者ID
     */
    private Long promisePatientId;

    /**
     * 检测单状态：详见 MedicalPromiseStatusEnum
     */
    private Integer medicalPromiseStatus;

    /**
     * 样本条码
     */
    private String specimenCode;

    /**
     * 检测项目信息
     */
    private String serviceItemId;

    /**
     * 检测项目名称
     */
    private String serviceItemName;

    /**
     * 供应商渠道编码
     */
    private Long providerId;

    /**
     * 实验室id
     */
    private String stationId;

    /**
     * 实验室名称
     */
    private String stationName;

    /**
     * 实验室联系方式
     */
    private String stationPhone;

    /**
     * 服务地点详细地址
     */
    private String stationAddress;

    /**
     * 是否有效 1有效 0无效
     */
    private Integer yn;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 数据来源分支
     */
    private String branch;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人
     */
    private String updateUser;

    /**
     * 修改时间
     */
    private Date updateTime;


    /**
     * 是否冻结，0.不冻结，1.冻结
     */
    private Integer freeze;

    /**
     * <pre>
     * 预计检测时长,单位分钟
     * </pre>
     */
    private Integer testDuration;

    /**
     * 报告状态，1.已出报告，0.未出报告
     */
    private Integer reportStatus;

    /**
     * 是否是测试单
     */
    private Boolean devMedicalPromise = Boolean.FALSE;

    /**
     * 患者id
     */
    private Long patientId;

    /**
     * 性别
     */
    private Integer gender;
    /**
     * 婚否
     */
    private Integer marriage;

    /**
     * 姓名
     */
    private String name;

    /**
     * phone
     */
    private String phone;

    /**
     * 亲属类型:1本人 21-父母 22-配偶 23-子女 34-其他
     */
    private Integer relativesType;

    /**
     * 年龄
     */
    private Integer age;

    /**
     * appointmentAddress
     */
    private String appointmentAddress;

    /**
     * 实物送达门店时间
     */
    private String deliveryStoreTime;

    /**
     * 报告是否超时
     */
    private Long reportTimeOutRemain;

    /**
     * 报告是否超时
     */
    private Integer reportCheckTimeOutStatus;

    /**
     * 报告是否超时名称
     */
    private String reportCheckTimeOutStatusName;

    /**
     * 报告是否超时
     */
    private Long reportCheckTimeOutRemain;


    /**
     * 报告是否超时
     */
    private Integer reportTimeOutStatus;

    /**
     * 报告是否超时名称
     */
    private String reportTimeOutStatusName;

    /**
     * 接收样本序列表
     */
    private String serialNum;


    /**
     * 综合状态名称
     */
    private String compositeStatusName;

    /**
     * 综合状态描述
     */
    private String compositeStatusDesc;

    /**
     * 综合状态
     */
    private Integer compositeStatus;

    /**
     * 运单状态码
     */
    private Integer shipStatus;

    /**
     * 运单状态
     */
    private String shipStatusName;

    /**
     * 运单描述
     */
    private String shipStatusDesc;

    /**
     * 报告获取时间
     */
    private String reportTime;

    /**
     * 到检时间
     */
    private String checkTime;


    /**
     * 预约开始时间
     */
    private String appointmentStartTime;

    /**
     * 预约结束时间
     */
    private String appointmentEndTime;

    /**
     * 性别
     */
    private String genderDesc;

    /**
     * 加密姓名
     */
    private String encryptionName;

}
