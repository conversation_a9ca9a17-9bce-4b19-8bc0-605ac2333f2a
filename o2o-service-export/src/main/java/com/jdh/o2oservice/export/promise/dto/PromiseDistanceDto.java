package com.jdh.o2oservice.export.promise.dto;

import lombok.Data;

/**
 * 获取服务者位置信息
 */
@Data
public class PromiseDistanceDto {

    /**
     * 服务者Id
     */
    private Long promiseId;

    /**
     * 配送距离,单位为米
     */
    private String distance;
    /**
     * 服务者点位展示的文案
     */
    private String angelDesc;

    /**
     * 骑手经度
     */
    private String angelLng;

    /**
     * 骑手纬度
     */
    private String angelLat;
    /**
     * 服务者点位展示的文案
     */
    private String stationDesc;

    /**
     * 实验室经度
     */
    private String stationLng;

    /**
     * 实验室纬度
     */
    private String stationLat;

    /**
     * 服务者点位展示的文案
     */
    private String promiseDesc;
    /**
     * 服务地址经度
     */
    private String promiseLng;

    /**
     * 服务地址纬度
     */
    private String promiseLat;

    /**
     * 是否刷新页面
     */
    private Boolean refresh = Boolean.TRUE;
    /**
     * 距离单位
     */
    private String unit;

    /**
     *
     */
    private Integer shipType;

    /**
     * 实验室接驳点经度
     */
    private String transformStationLng;

    /**
     * 实验室接驳点纬度
     */
    private String transformStationLat;
}
