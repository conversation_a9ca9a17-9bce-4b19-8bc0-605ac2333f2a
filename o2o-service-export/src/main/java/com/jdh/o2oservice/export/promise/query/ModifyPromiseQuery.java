package com.jdh.o2oservice.export.promise.query;

import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

;
;

/**
 * 提交预约单操作类
 *
 * @author: yang<PERSON>yu
 * @date: 2022/8/30 10:11 下午
 * @version: 1.0
 */
@Data
public class ModifyPromiseQuery implements Serializable {
    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = 1L;

    /**
     * 服务端
     */
    @NotNull(message = "检测单号不允许为空")
    private Long medicalPromiseId;

    /**
     * 服务端
     */
    @NotNull(message = "用户pin不允许为空")
    private String userPin;
}
