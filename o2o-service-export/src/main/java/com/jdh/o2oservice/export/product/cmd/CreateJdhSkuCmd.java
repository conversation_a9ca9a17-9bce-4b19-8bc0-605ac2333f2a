package com.jdh.o2oservice.export.product.cmd;

import com.jdh.o2oservice.export.product.cmd.validated.ProductServiceTypeAngelCareValidation;
import com.jdh.o2oservice.export.product.cmd.validated.ProductServiceTypeAngelTestValidation;
import com.jdh.o2oservice.export.product.cmd.validated.ProductServiceTypeSelfTestValidation;
import com.jdh.o2oservice.export.product.cmd.validated.provider.CreateJdhSkuCmdProvider;
import com.jdh.o2oservice.export.product.dto.ProductActivityFloorDto;
import lombok.Data;
import org.hibernate.validator.group.GroupSequenceProvider;

import javax.validation.constraints.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 创建JDH商品命令
 *
 * <AUTHOR>
 * @date 2024/04/17
 */
@Data
@GroupSequenceProvider(value = CreateJdhSkuCmdProvider.class)
public class CreateJdhSkuCmd implements Serializable {
    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = 1L;
    
    /**
     * <pre>
     * 主站商品id
     * </pre>
     */
    @NotNull(message = "skuId不允许为空")
    private Long skuId;
    
    /**
     * <pre>
     * 服务类型，注意AbstractBusinessIdentity已经有serviceType，这里成serviceType
     * </pre>
     */
    @NotNull(message = "业务身份不允许为空")
    @Min(value = 1, message = "业务身份必须是正整数")
    private Integer serviceType;
    
    /**
     * <pre>
     * 已选择检测项目id集合
     * </pre>
     */
    @NotEmpty(message = "服务项目不允许为空")
    @Size(min = 1, message = "至少选择一个项目")
    private List<Long> serviceItemIdList;
    
    /**
     * <pre>
     * 服务时长,单位分钟
     * </pre>
     */
    @NotNull(message = "服务时长不允许为空")
    @Min(value = 1, message = "服务时长必须是正整数")
    private Integer serviceDuration;
    
    /**
     * <pre>
     * 服务资源类型集合 1-骑手 2-护士 3-护工 4-康复师,多个值
     * </pre>
     */
    @NotEmpty(message = "服务资源类型不允许为空")
    @Size(min = 1, message = "至少选择一个服务资源类型")
    private List<Integer> serviceResourceType;
    
    /**
     * <pre>
     * 需提前预约时间,单位小时
     * </pre>
     */
    @Min(value = 1, message = "需提前预约时间必须是正整数")
    private Integer advanceAppointTime;
    
    /**
     * <pre>
     * 未来可约天数,单位天
     * </pre>
     */
    @NotNull(message = "未来可约天数不允许为空", groups = {ProductServiceTypeSelfTestValidation.class, ProductServiceTypeAngelTestValidation.class, ProductServiceTypeAngelCareValidation.class})
    @Min(value = 1, message = "未来可约天数必须是正整数")
    @Max(value = 30, message = "未来可约天数最大30天")
    private Integer maxScheduleDays;
    
    /**
     * <pre>
     * 每天可预约时间段,["08:00-12:00", "14:00-18:00"]
     * </pre>
     */
    @NotEmpty(message = "每天可预约时间段不允许为空", groups = {ProductServiceTypeSelfTestValidation.class, ProductServiceTypeAngelTestValidation.class, ProductServiceTypeAngelCareValidation.class})
    @Size(min = 1, message = "至少选择一个可约时段")
    private List<String> dayTimeFrame;
    
    /**
     * <pre>
     * 实验室分发规则 1-整单 2-时效 3-成本,多个值
     * </pre>
     */
    @NotEmpty(message = "实验室分发规则不允许为空")
    @Size(min = 1, message = "至少选择一个实验室分发规则")
    private List<Integer> stationAssignType;
    
    /**
     * <pre>
     * 是否需要投保 0-不需要 1-需要
     * </pre>
     */
    @NotNull(message = "是否需要投保不允许为空", groups = {ProductServiceTypeSelfTestValidation.class, ProductServiceTypeAngelTestValidation.class, ProductServiceTypeAngelCareValidation.class})
    @Min(value = 0, message = "是否需要投保不允许为负数")
    private Integer requiredInsure;

    /**
     * <pre>
     * 渠道id
     * </pre>
     */
    @NotNull(message = "售卖渠道不允许为空", groups = {ProductServiceTypeSelfTestValidation.class, ProductServiceTypeAngelTestValidation.class, ProductServiceTypeAngelCareValidation.class})
    @Min(value = 1, message = "售卖渠道必须为正整数")
    private Long channelId;
    
    /**
     * <pre>
     * 商品标签,多个
     * </pre>
     */
    @NotEmpty(message = "商品标签不允许为空", groups = {ProductServiceTypeSelfTestValidation.class, ProductServiceTypeAngelTestValidation.class, ProductServiceTypeAngelCareValidation.class})
    @Size(min = 1, message = "至少选择一个商品标签")
    private List<String> tags;
    
    /**
     * <pre>
     * 服务须知,JSON对象集合
     * </pre>
     */
    @NotEmpty(message = "服务须知不允许为空", groups = {ProductServiceTypeSelfTestValidation.class, ProductServiceTypeAngelTestValidation.class, ProductServiceTypeAngelCareValidation.class})
    @Size(min = 1, message = "至少选择一个服务须知", groups = {ProductServiceTypeSelfTestValidation.class, ProductServiceTypeAngelTestValidation.class, ProductServiceTypeAngelCareValidation.class})
    private List<CreateJdhSkuServiceNoticeCmd> serviceNotice;
    
    /**
     * <pre>
     * 服务流程图
     * </pre>
     */
    @NotNull(message = "服务流程图片id不允许为空", groups = {ProductServiceTypeSelfTestValidation.class, ProductServiceTypeAngelTestValidation.class, ProductServiceTypeAngelCareValidation.class})
    private String serviceProcessImgId;
    
    /**
     * <pre>
     * 是否实名 0-否 1-是
     * </pre>
     */
    @NotNull(message = "是否实名不允许为空")
    @Min(value = 0, message = "是否实名不允许为负数")
    private Integer requiredRealName;
    
    /**
     * <pre>
     * 使用教程地址
     * </pre>
     */
    @Pattern(regexp = "^(http|https)://(.*)", message = "使用教程地址不正确")
    private String tutorialUrl;
    
    /**
     * <pre>
     * 知情同意书地址
     * </pre>
     */
    @NotNull(message = "知情同意书地址不允许为空", groups = {ProductServiceTypeSelfTestValidation.class, ProductServiceTypeAngelTestValidation.class, ProductServiceTypeAngelCareValidation.class})
    @Pattern(regexp = "^(http|https)://(.*)", message = "知情同意书地址不正确")
    private String informedConsentUrl;
    
    /**
     * <pre>
     * 预约模板id
     * </pre>
     */
    @NotNull(message = "预约模板id不允许为空", groups = {ProductServiceTypeSelfTestValidation.class, ProductServiceTypeAngelTestValidation.class, ProductServiceTypeAngelCareValidation.class})
    @Min(value = 1, message = "预约模板id必须为正整数")
    private Integer appointTemplateId;
    
    /**
     * <pre>
     * 年龄范围
     * </pre>
     */
    private List<String> ageRange;

    /**
     * <pre>
     * 适用性别
     * </pre>
     */
    @NotEmpty(message = "适用性别不允许为空")
    @Size(min = 1, message = "至少选择一个适用性别")
    private List<String> genderLimit;
    
    /**
     * <pre>
     * 客户确认信息类型,1-医嘱证明
     * </pre>
     */
    private List<Integer> customerConfirmType;
    
    /**
     * <pre>
     * 服务记录类型,1-废料处理 2-服务记录 3-上传着装照片
     * </pre>
     */
    @NotEmpty(message = "服务记录类型不允许为空", groups = {ProductServiceTypeSelfTestValidation.class, ProductServiceTypeAngelTestValidation.class, ProductServiceTypeAngelCareValidation.class})
    @Size(min = 1, message = "至少选择一个服务记录类型")
    private List<Integer> serviceRecordType;
    
    /**
     * <pre>
     * 服务资源结算价
     * </pre>
     */
    @DecimalMin(value = "0", message = "护士结算价必须为正整数")
    private BigDecimal resourceSettlementPrice;

    /**
     * <pre>
     * 备注
     * </pre>
     */
    private String remark;

    /**
     * <pre>
     * 商品关联项目清单类型 1-项目（单项目） 2-服务（多项目套餐） 3-服务组（多服务套餐）
     * </pre>
     */
    private Integer skuItemType;

    /**
     * <pre>
     * erp
     * </pre>
     */
    private String erp;

    /**
     * <pre>
     * 草稿
     * </pre>
     */
    private Boolean draft;

    /**
     * <pre>
     * 商品类型；0为主品，1为加项品
     * </pre>
     */
    @NotNull(message = "商品类型不允许为空", groups = {ProductServiceTypeSelfTestValidation.class, ProductServiceTypeAngelTestValidation.class, ProductServiceTypeAngelCareValidation.class})
    @Min(value = 0, message = "商品类型不允许为负数")
    private Integer skuType;

    /**
     * <pre>
     * 技术难度 0-1000
     * </pre>
     */
    private Integer technicalLevel;

    /**
     * <pre>
     * 购买后服务有效时间,单位天
     * </pre>
     */
    private Integer buyValidPeriod;


    /**
     * <pre>
     * 采样教程轮播图fileId集合，格式：[fileId,fieldId]
     * </pre>
     */
    private List<Long> tutorialCarousel;

    /**
     * 采样教程视频fileId
     */
    private Long tutorialVideo;

    /**
     * 采样教程视频封面图fileId
     */
    private Long tutorialVideoThumbnail;

    /**
     * 采样方法说明 fileId
     */
    private Long tutorialMethod;

    /**
     * 采样方法说明跳转url
     */
    private String tutorialMethodJumpUrl;

    /**
     *
     */
    private List<ProductActivityFloorDto> activityFloors;

    /**
     * <pre>
     * 高优分配实验室
     * </pre>
     */
    private List<String> highQualityStoreId;

    /**
     * 护士基础结算价
     */
    private BigDecimal angelBasicSettlementPrice;
}
