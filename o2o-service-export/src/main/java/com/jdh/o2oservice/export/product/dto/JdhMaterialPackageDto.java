package com.jdh.o2oservice.export.product.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.Objects;

/**
 * <pre>
 *  京东耗材包管理
 * </pre>
 *
 */
@Data
public class JdhMaterialPackageDto {
    
    /**
     * <pre>
     * 耗材包id
     * </pre>
     */
    private Long materialPackageId;

    /**
     * <pre>
     * 耗材包名称
     * </pre>
     */
    private String materialPackageName;
    
    /**
     * <pre>
     * 耗材包详细清单
     * </pre>
     */
    private String materialPackageDetail;
    
    /**
     * <pre>
     * 耗材包商品id
     * </pre>
     */
    private Long skuId;

    /**
     * <pre>
     * 耗材包商品名称
     * </pre>
     */
    private String skuName;
    
    /**
     * <pre>
     * 备注
     * </pre>
     */
    private String remark;
    
    /**
     * <pre>
     * 创建人
     * </pre>
     */
    private String createUser;
    
    /**
     * <pre>
     * 更新人
     * </pre>
     */
    private String updateUser;
    
    /**
     * <pre>
     * 创建时间
     * </pre>
     */
    private Date createTime;
    
    /**
     * <pre>
     * 更新时间
     * </pre>
     */
    private Date updateTime;

    /**
     * 自营护士结算价
     */
    private BigDecimal selfAngelSettlementPrice;

    /**
     * 兼职护士结算价
     */
    private BigDecimal partAngelSettlementPrice;

    /**
     * 自营护士结算价字符串
     * 保留两位小数
     * @return
     */
    public String getSelfAngelSettlementPriceStr(){
        return Objects.isNull(selfAngelSettlementPrice) ? "" : selfAngelSettlementPrice.setScale(2, RoundingMode.DOWN).toPlainString();
    }

    /**
     * 兼职护士结算价字符串
     * 保留两位小数
     * @return
     */
    public String getPartAngelSettlementPriceStr(){
        return Objects.isNull(partAngelSettlementPrice) ? "" : partAngelSettlementPrice.setScale(2, RoundingMode.DOWN).toPlainString();
    }
}