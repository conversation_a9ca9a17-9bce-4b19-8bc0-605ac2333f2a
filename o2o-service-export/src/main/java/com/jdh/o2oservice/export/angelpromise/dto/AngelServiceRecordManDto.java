package com.jdh.o2oservice.export.angelpromise.dto;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class AngelServiceRecordManDto implements Serializable {

    /**
     * 记录类型
     * 1。新模式返回
     */
    private Integer recordType;
    /**
     * 任务ID
     */
    private Long taskId;

    /**
     * 服务记录状态
     * 初始状态：0、完成:1、取消：2、评估结果高风险：-1
     *
     */
    private Integer serviceRecordStatus;

    /**
     * 节点集合
     */
    private List<AngelServiceRecordQuestionGroupDto> questionGroupDtoList;

}
