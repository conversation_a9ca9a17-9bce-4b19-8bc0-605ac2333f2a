package com.jdh.o2oservice.export.dispatch.cmd;

import com.jdh.o2oservice.common.result.request.AbstractBusinessIdentity;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName SubmitDispatchCmd
 * @Description
 * <AUTHOR>
 * @Date 2024/4/17 18:41
 **/
@Data
public class SubmitDispatchCmd extends AbstractBusinessIdentity implements Serializable {

    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = 1L;

    /**
     * 服务单ID
     */
    private Long voucherId;

    /**
     * 履约单ID
     */
    private Long promiseId;

    /**
     * 用户pin
     */
    private String userPin;

    /**
     * 服务兑换来源id
     */
    private String sourceVoucherId;

    /**
     * 服务地点
     */
    private DispatchServiceLocation serviceLocation;

    /**
     * 预约时间
     */
    private DispatchAppointmentTime appointmentTime;

    /**
     * 顾客
     */
    private List<DispatchAppointmentPatient> patients;

    /**
     * 备注
     */
    private String remark;

    /**
     * 操作来源，1.运营端(运营端操作取消运单时 不校验操作权限)
     */
    private Integer operateSource;

    /**
     * 操作erp
     */
    private String operateErp;

    /**
     * 意向派单ID
     */
    private List<Long> intendedAngelIds;

    /**
     * 操作原因
     */
    private String reason;

    /**
     * 服务信息简略信息
     */
    private String serviceBriefInfo;

    /**
     * 操作角色
     * @see com.jdh.o2oservice.common.enums.OperatorRoleTypeEnum
     */
    private Integer operatorRoleType;

    /**
     * 操作pin
     */
    private String operatePin;
}