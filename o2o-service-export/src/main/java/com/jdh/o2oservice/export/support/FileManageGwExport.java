package com.jdh.o2oservice.export.support;

import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.export.support.dto.FilePreSignedUrlDto;
import com.jdh.o2oservice.export.support.dto.PdfSignatureResult;

import java.util.List;
import java.util.Map;

/**
 * 文件管理网关接口
 * @author: yang<PERSON>yu
 * @date: 2024/4/28 10:12 上午
 * @version: 1.0
 */
public interface FileManageGwExport {


    /**
     * 生成文件上传的预签名链接
     * @param params
     * @return
     */
    Response<FilePreSignedUrlDto> generatePutUrl(Map<String, String> params);

    /**
     * 获取文件的
     * @param params
     * @return
     */
    Response<List<FilePreSignedUrlDto> > generateGetUrl(Map<String, String> params);

    /**
     * 获取护理单签署文件链接
     * @param params
     * @return
     */
    Response<List<FilePreSignedUrlDto>> generateGetUrlsByTaskId(Map<String, String> params);

    /**
     * PDF签名接口
     * @param params
     * @return
     */
    Response<PdfSignatureResult> pdfSignature(Map<String, String> params);

    /**
     * 护理单PDF签名
     * @param params
     * @return
     */
    Response<List<PdfSignatureResult>> pdfSignatureAndSave(Map<String, String> params);
}
