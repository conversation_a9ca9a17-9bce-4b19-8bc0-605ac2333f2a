package com.jdh.o2oservice.export.support.command;

import com.jdh.o2oservice.common.result.request.AbstractCmd;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * @ClassName PricingServiceCmd
 * @Description
 * <AUTHOR>
 * @Date 2025/4/29 16:39
 **/
@Data
public class PricingServiceCalculateCmd extends AbstractCmd {

    /**
     * 场景名称
     */
    private String scene;

    /**
     * 必需, 订单号
     */
    private Long orderId;

    /**
     * promiseId
     */
    private Long promiseId;

    /**
     * 服务者id
     */
    private Long angelId;

    /**
     * 事实对象
     */
    private Map<String, Object> factObjectMap;

    /**
     * 护士全兼职标签列表
     */
    private List<Integer> jobNatureList;
}