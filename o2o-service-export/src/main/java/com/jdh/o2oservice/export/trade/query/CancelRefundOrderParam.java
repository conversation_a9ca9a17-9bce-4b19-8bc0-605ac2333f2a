package com.jdh.o2oservice.export.trade.query;

import com.jdh.o2oservice.common.result.request.AbstractQuery;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @ClassName CancelRefundOrderParam
 * @Description
 * <AUTHOR>
 * @Date 2025/6/26 20:56
 **/
@Data
public class CancelRefundOrderParam extends AbstractQuery {

    /**
     * 订单号
     */
    private Long orderId;
    /**
     * voucherId
     */
    private Long voucherId;
    /**
     * 履约单ID
     */
    private Long promiseId;

    /**
     * 操作人
     */
    private String operator;
    /**
     * 退款原因
     */
    private String refundReason;

    /**
     * 退款来源:1-C端用户 2-运营端平台 3-运单取消
     */
    private String refundSource;
}