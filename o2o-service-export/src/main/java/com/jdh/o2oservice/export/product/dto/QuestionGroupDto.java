package com.jdh.o2oservice.export.product.dto;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/27
 * @description 题节点
 */
@Data
public class QuestionGroupDto {

    private String name;//节点名称

    private String desc;//节点备注

    private String code;//节点唯一code码

    private Integer show;//是否展示 1展示 0否

    private Integer sort;//排序

    private String buttonName;//按钮名称

    private List<QuestionDTO> questionDTOS;//题
}
