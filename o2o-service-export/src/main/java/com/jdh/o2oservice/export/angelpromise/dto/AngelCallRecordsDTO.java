package com.jdh.o2oservice.export.angelpromise.dto;

import com.jdh.o2oservice.export.support.dto.CallRecordDetailDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025/3/10
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AngelCallRecordsDTO {
    /**
     * 是否存在已接通的通话记录
     */
    private Boolean existCallSuccessRecords;

    /**
     * 通话记录列表
     */
    private List<CallRecordDetailDto> callRecordDetailList;
}
