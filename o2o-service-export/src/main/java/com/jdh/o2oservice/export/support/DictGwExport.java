package com.jdh.o2oservice.export.support;

import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.export.support.dto.DictInfoDto;
import com.jdh.o2oservice.export.support.query.DictRequest;

import java.util.List;
import java.util.Map;

/**
 * 词典对外JSF接口
 *
 * <AUTHOR>
 * @date 2024/07/23
 */
public interface DictGwExport {

    /**
     * 查询多个词典
     *
     * @param param param
     * @return {@link Response}
     */
    Response<Map<String, List<DictInfoDto>>> queryMultiDictList(Map<String, String> param);

    /**
     * 查询子分类词典
     *
     * @param param param
     * @return {@link Response}
     */
    Response<List<DictInfoDto>> querySubList(Map<String, String> param);
}
