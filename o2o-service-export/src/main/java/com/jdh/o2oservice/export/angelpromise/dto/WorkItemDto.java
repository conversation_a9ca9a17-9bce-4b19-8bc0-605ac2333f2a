package com.jdh.o2oservice.export.angelpromise.dto;

import lombok.Data;

import java.util.Date;

/**
 * @ClassName WorkItemDto
 * @Description
 * <AUTHOR>
 * @Date 2025/5/27 19:23
 */
@Data
public class WorkItemDto {

    /**
     * 检测单ID
     */
    private Long medicalPromiseId;

    /**
     * 服务ID，快检情况下是skuNo
     */
    private Long serviceId;

    /**
     * 患者ID
     */
    private Long promisePatientId;

    /**
     * 检测单状态：详见 MedicalPromiseStatusEnum
     */
    private Integer status;

    /**
     * 样本条码
     */
    private String specimenCode;

    /**
     * 检测项目信息
     */
    private String serviceItemId;

    /**
     * 检测项目名称
     */
    private String serviceItemName;

    /**
     * 供应商渠道编码
     */
    private Long providerId;

    /**
     * 实验室id
     */
    private String stationId;

    /**
     * 实验室名称
     */
    private String stationName;

    /**
     * 检测时间
     */
    private Date checkTime;

    /**
     * 是否冻结，0.不冻结，1.冻结
     */
    private Integer freeze;

    /**
     * 报告状态，1.已出报告，0.未出报告
     */
    private Integer reportStatus;

    /**
     * 患者id
     */
    private Long patientId;

    /**
     * 性别
     */
    private Integer gender;

    /**
     * 婚否
     */
    private Integer marriage;

    /**
     * 姓名
     */
    private String name;

    /**
     * phone
     */
    private String phone;

    /**
     * 年龄
     */
    private Integer age;

    /**
     * appointmentAddress
     */
    private String appointmentAddress;

    /**
     *
     */
    private Date reportTime;

    /**
     * 服务站Id
     */
    private String angelStationId;

    /**
     * 检测单特殊标记（0.普通订单，1.加项订单）
     */
    private String flag;

    /** 合管检测单ID */
    private Long mergeMedicalId;

    /**
     * 待收样超时绝对时间
     */
    private Date waitingTestTimeOutDate;
}
