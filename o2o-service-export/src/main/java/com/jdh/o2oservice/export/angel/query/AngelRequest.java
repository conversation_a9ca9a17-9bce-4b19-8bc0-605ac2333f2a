package com.jdh.o2oservice.export.angel.query;

import com.jdh.o2oservice.common.result.request.AbstractQuery;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description: 服务者查询参数
 * <AUTHOR>
 * @Date 2024/4/21
 * @Version V1.0
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AngelRequest extends AbstractQuery {

    /**
     * 服务者id
     */
    private Long angelId;

    /**
     * 互医医生id
     */
    private Long nethpDocId;

    /**
     * 服务者pin
     */
    private String angelPin;

    /**
     * 客户端版本
     */
    private String clientVersion;
}
