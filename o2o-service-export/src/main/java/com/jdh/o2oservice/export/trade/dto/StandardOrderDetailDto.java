package com.jdh.o2oservice.export.trade.dto;

import com.jdh.o2oservice.export.medicalpromise.dto.MedicalPromiseDTO;
import com.jdh.o2oservice.export.promise.dto.PromiseDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName StandardOrderDetailDto
 * @Description
 * <AUTHOR>
 * @Date 2025/6/26 21:07
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StandardOrderDetailDto implements Serializable {

    /**
     * 订单信息
     */
    private JdOrderDTO jdOrder;

    /**
     * 检测单列表
     */
    private List<MedicalPromiseDTO> medicalPromiseList;

    /**
     * 是否可退款
     */
    private Boolean canRefundable;

    /**
     * 当前履约状态编码
     */
    private String stepCode;

    /**
     * 当前履约状态标题
     */
    private String stepTitle;
}