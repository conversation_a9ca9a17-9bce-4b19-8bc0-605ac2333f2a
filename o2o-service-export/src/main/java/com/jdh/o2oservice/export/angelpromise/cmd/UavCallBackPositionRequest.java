package com.jdh.o2oservice.export.angelpromise.cmd;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/6/3
 * @description 飞行需求状态变更
 */
@Data
public class UavCallBackPositionRequest {

    private String flight_work_id;//飞行需求ID

    private Long timestamp;//时间戳

    private Integer flight_work_state;//飞行需求状态

    private String msg;//变更原因

    private Integer uav_id;//飞机ID

    private Gps gps;//无人机实时位置信息


    @Data
    public static class Gps{

        private Integer remaining_time;//预计剩余时间 单位:分

        private Integer remaining_distance;//预计剩余航程 单位: 米

        private Double alt;//海波

        private Double lat;//飞机实时位置坐标系类型：WGS-84

        private Double lon;//飞机实时位置坐标系类型：WGS-84

        private Double vel;//速度 单位: m/s

    }

}
