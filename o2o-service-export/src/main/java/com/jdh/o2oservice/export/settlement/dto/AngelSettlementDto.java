package com.jdh.o2oservice.export.settlement.dto;

import com.jdh.o2oservice.export.angel.cmd.JdhActivityConfigAngelRecruitmentRule;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 护士结算
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-13 05:53:18
 */
@Data
public class AngelSettlementDto implements Serializable {


    private static final long serialVersionUID = -7620190034388079114L;
    /**
     * ID主键
     */
    private Long id;

    /**
     * 结算id
     */
    private Long settleId;

    /**
     * 服务者id
     */
    private Long angelId;
    /**
     * 履约单id
     */
    private Long promiseId;
    /**
     * 护士名称
     */
    private String angelName;
    /**
     * 结算单号 or 提现单号
     */
    private Long settlementNo;

    /**
     * 结算类型 1 收入2 支出
     */
    private Integer settlementType;

    /**
     * 收入类型 1上门检测服务 2上门护理服务 3调整项 4 激励
     */
    private Integer itemType;

    /**
     * 收入类型 1上门检测服务 2上门护理服务 3调整项 4 激励
     */
    private String itemTypeDesc;

    /**
     * 结算状态0 初始化 1 冻结中 2 已结算
     */
    private Integer settleStatus;

    /**
     * 提现状态 1提现中 2提现成功 3提现失败
     */
    private Integer cashStatus;

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 预计结算时间
     */
    private Date expectSettleTime;

    /**
     * 结算时间
     */
    private Date settleTime;

    /**
     * 提现时间
     */
    private Date cashTime;

    /**
     * 到账时间
     */
    private Date receivedTime;

    /**
     * 金额
     */
    private BigDecimal settleAmount;
    /**
     * 失败原因
     */
    private String failReason;
    /**
     * 失败按钮
     */
    private String cashFailButton;
    /**
     * 失败修改跳转链接
     */
    private String failSkipUrl;
    /**
     * 版本号
     */
    private Integer version;

    /**
     * 是否有效 0-无效 1-有效
     */
    private Integer yn;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 更新人
     */
    private String updateUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 明细
     */
    private List<AngelSettlementDetailDto> detailList;

    /**
     * 明细
     */
    private List<AngelSettleDetailCommonDto> detailCommonDtoList;

    /**
     * 下单时间
     */
    private Date paymentTime;

    /**
     * 预约开始时间
     */
    private String appointmentStartTime;

    /**
     * 结算状态0 初始化 1 冻结中 2 已结算
     */
    private String settleStatusDesc;
    /**
     * 下单时间
     */
    private String paymentTimeStr;
    /**
     * 服务完成时间
     */
    private String workFinishedTimeStr;
    /**
     * 可提现时间
     */
    private String cashDateStr;
    /**
     * 结算时间
     */
    private String settleDateStr;

    /**
     * 收入来源id
     */
    private Long itemSourceId;

    /**
     * 同步互医状态 0-未同步 1-已同步
     */
    private Integer syncStatus;

    /**
     * 活动规则
     */
    private ActivityConfigAngelRecruitmentRuleDto activityConfigRule;

    /**
     * 金额字符串
     * 保留两位小数
     * @return
     */
    public String getSettleAmountStr(){
        return Objects.isNull(settleAmount) ? "" : settleAmount.setScale(2, RoundingMode.HALF_UP).toPlainString();
    }
}
