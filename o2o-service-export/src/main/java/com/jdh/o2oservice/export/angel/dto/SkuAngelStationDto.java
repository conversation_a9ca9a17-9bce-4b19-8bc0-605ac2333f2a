package com.jdh.o2oservice.export.angel.dto;

import lombok.Data;

import java.util.List;

/**
 * @ClassName SkuAngelStationDto
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/6/13 9:54 AM
 * @Version 1.0
 **/
@Data
public class SkuAngelStationDto {

    /**
     * 服务站库存
     */
    private Long angelStationId;

    /**
     * 服务站库存
     */
    SkuInventoryDto skuInventoryDto;

    /**
     * 实验室列表
     */
    List<StationDto> stationDtoList;

    /**
     * 是否预占
     */
    private Boolean havePreemption=false;

    /**
     * 接驳点id
     */
    private Long jdTransferStationId;

}
