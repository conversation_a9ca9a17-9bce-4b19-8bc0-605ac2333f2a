package com.jdh.o2oservice.export.laboratory.dto;

import lombok.*;

import java.util.Date;

/**
 * 
 */
@Data
public class JdhStoreTransferStationDto {

    /** 京东实验室id */
    private String jdStoreId;

    /** 接驳点类型 1-实验室自收样  2-无人机 */
    private Integer stationType;

    /** 京东接驳点id */
    private Long jdStationId;

    /** 京东接驳点名称 */
    private String jdStationName;

    /** 京东接驳点地址 */
    private String jdStationAddress;

    /** 京东接驳点维度 */
    private Double jdStationLatitude;

    /** 京东接驳点经度 */
    private Double jdStationLongitude;

    /** 下一个京东站点id */
    private Long jdStationTargetId;

    /** 外部系统接驳点id */
    private String thirdStationId;

    /** 下一个外部系统接驳点id */
    private String thirdStationTargetId;

    /** 创建时间 */
    private Date createTime;

    /** 创建人 */
    private String createUser;

    /** 更新时间 */
    private Date updateTime;

    /** 修改人 */
    private String updateUser;

    /** 有效标志 0 无效 1有效 */
    private Integer yn;

    /** 数据来源分支 */
    private String branch;

    /** 版本号 */
    private Integer version;

    /** 是否可删除 */
    private Boolean canDelete;

    /** 是否可修改 */
    private Boolean canModify;
}