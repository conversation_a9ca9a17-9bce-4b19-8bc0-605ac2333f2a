package com.jdh.o2oservice.export.product.query;

import com.jdh.o2oservice.common.result.request.AbstractQuery;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Set;

/**
 * 京东商品查询对象
 *
 * <AUTHOR>
 * @date 2024/04/21
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class JdhSkuListRequest extends AbstractQuery {

    /**
     * skuId
     */
    private Set<Long> skuIdList;
    
    /**
     * 是否查询服务项目
     */
    private Boolean queryServiceItem;

    /**
     * 是否查询商品中台主数据
     */
    private Boolean querySkuCoreData;

    /**
     * 是否查询项目耗材信息
     */
    private Boolean queryItemMaterial;

    /**
     * 是否查询项目技能信息
     */
    private Boolean queryItemSkill;

    /**
     * 商品类型
     */
    private Integer skuType;

    /**
     * 是否查询费项配置
     */
    private Boolean queryPriceFeeConfig;
}
