package com.jdh.o2oservice.export.medicalpromise.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @date 2025/6/5
 */
@Data
public class MedPromiseDeliveryStepDTO {

    /** 接驳点类型 1-实验室自收样  2-无人机 */
    private Integer deliveryStepType;

    /**
     * 开始地址的唯一标识符。
     */
    private String startAddressId;

    /**
     * 开始地址的详细信息。
     */
    private String startAddress;

    /**
     * 结束地址的唯一标识符。
     */
    private String endAddressId;

    /**
     * 结束地址的详细信息。
     */
    private String endAddress;

    /** 外部系统接驳点id */
    private String thirdStationId;

    /** 下一个外部系统接驳点id */
    private String thirdStationTargetId;

    private Integer sort;


}
