package com.jdh.o2oservice.export.support.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @ClassName CallRecordGroupDto
 * @Description
 * <AUTHOR>
 * @Date 2025/6/17 00:59
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CallRecordGroupDto implements Serializable {

    /**
     * 履约单ID
     */
    private Long promiseId;

    /**
     *
     */
    private Map<String, List<CallRecordDetailDto>> groupMap;
}
