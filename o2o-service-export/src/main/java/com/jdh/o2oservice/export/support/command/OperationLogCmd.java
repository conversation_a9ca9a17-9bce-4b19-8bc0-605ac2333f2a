package com.jdh.o2oservice.export.support.command;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 操作日志类对象
 *
 * <AUTHOR>
 * @date 2025/01/16
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class OperationLogCmd implements Serializable {

    /** 业务场景key,默认:类全路径#方法 */
    private String bizSceneKey;

    /** 业务场景中文描述 */
    private String bizSceneDesc;

    /** 操作类型 1-新增 2-删除 3-修改 4-查询 */
    private Integer operateType;

    /** 业务对象唯一id,如skuId、angelId等 */
    private String bizUnionId;

    /** 请求端信息 */
    private String clientInfo;

    /** 入参信息 */
    private String param;

    /** 出参信息 */
    private String result;

    /** 结果类型 0-未知 1-成功 2-失败 */
    private Integer resultType;

    /** 操作人 */
    private String operator;

    /** 操作内容 */
    private Date operateTime;
}
