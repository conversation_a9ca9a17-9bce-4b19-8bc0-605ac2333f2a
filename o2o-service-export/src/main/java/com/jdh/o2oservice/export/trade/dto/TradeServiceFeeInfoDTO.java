package com.jdh.o2oservice.export.trade.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 交易服务费dto
 *
 * <AUTHOR>
 * @date 2024/04/24
 */
@Data
public class TradeServiceFeeInfoDTO implements Serializable {

    /**
     * 总费用
     */
    private BigDecimal serviceFee;


    /**
     * 聚合类型
     */
    private Integer aggregateType;

    /**
     * 聚合子类型
     */
    private Integer aggregateSubType;

    /**
     * 价格是否全部计算完成
     */
    private Boolean isFinish = true;

    /**
     * 收费项目表
     */
    private List<TradeServiceFeeDetailDTO> serviceFeeDetailList;

}
