package com.jdh.o2oservice.export.promise.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 生日dto
 *
 * <AUTHOR>
 * @date 2024/04/21
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ModifyRecordDto implements Serializable {

    /**
     * 原时间
     */
    private String beforeAppointTime;

    /**
     * 修改后时间
     */
    private String afterAppointTime;

    /**
     * 修改原因
     */
    private String reason;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 操作时间
     */
    private String operateTime;

    /**
     * 操作角色
     * @see com.jdh.o2oservice.common.enums.OperatorRoleTypeEnum
     */
    private Integer operatorRoleType;
}
