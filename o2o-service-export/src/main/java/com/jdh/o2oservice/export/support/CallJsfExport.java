package com.jdh.o2oservice.export.support;
import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.export.support.dto.CallRecordDto;
import com.jdh.o2oservice.export.support.query.QueryCallRecordRequest;
import java.util.List;

/**
 * @Description 外呼服务
 * @Date 2025/2/27 下午4:44
 * <AUTHOR>
 **/
public interface CallJsfExport {

    /**
     * 外呼记录列表
     * @param request
     */
    Response<List<CallRecordDto>> queryCallRecordList(QueryCallRecordRequest request);

    /**
     * 查询外呼url
     * @param request
     */
    Response<String> queryCallRecordUrl(QueryCallRecordRequest request);
}
