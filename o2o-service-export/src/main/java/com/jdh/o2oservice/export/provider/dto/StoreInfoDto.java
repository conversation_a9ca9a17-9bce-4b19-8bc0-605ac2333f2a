package com.jdh.o2oservice.export.provider.dto;

import com.jdh.o2oservice.export.laboratory.dto.JdhStoreTransferStationDto;
import lombok.Data;

import java.util.List;

/**
 * 服务类型
 * @author: yangxiyu
 * @date: 2024/3/11 10:54 上午
 * @version: 1.0
 */
@Data
public class StoreInfoDto {

    /**
     * 供应商ID
     */
    private Long providerId;

    /**
     * 门店id
     */
    private String storeId;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 京东健康-消费医疗门店id
     */
    private String stationId;

    /**
     * 京东健康-消费医疗门店名称
     */
    private String stationName;

    /**
     * 京东健康-消费医疗门店经度
     */
    private String lng;

    /**
     * 京东健康-消费医疗门店纬度
     */
    private String lat;

    /**
     * 门店地址
     */
    private String stationAddress;
    /** 门店电话,可能多个 */
    private String stationPhone;

    /** 京东门店编号 */
    private String jdStoreId;

    /**
     * 接驳点列表
     */
    List<JdhStoreTransferStationDto> transferStations;
}
