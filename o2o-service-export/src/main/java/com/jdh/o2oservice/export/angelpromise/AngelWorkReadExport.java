package com.jdh.o2oservice.export.angelpromise;

import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.export.angelpromise.dto.AngelWorkItemDto;
import com.jdh.o2oservice.export.angelpromise.query.AngelWorkItemQuery;

import java.util.List;

/**
 * @ClassName AngelWorkReadExport
 * @Description
 * <AUTHOR>
 * @Date 2025/5/27 19:15
 */
public interface AngelWorkReadExport {

    /**
     * 查询工单下项目id
     *
     * @param angelWorkItemQuery
     * @return
     */
    Response<List<AngelWorkItemDto>> queryAngelWorkItemList(AngelWorkItemQuery angelWorkItemQuery);
}
