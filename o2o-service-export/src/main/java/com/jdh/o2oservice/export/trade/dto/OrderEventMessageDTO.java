package com.jdh.o2oservice.export.trade.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 订单事件消息体
 *
 * <AUTHOR>
 * @version 2024/3/7 14:02
 **/
@Data
public class OrderEventMessageDTO implements Serializable {

    private static final long serialVersionUID = -1157739677206259507L;

    /**
     * 消息ID，用于幂等
     */
    private String messageId;

    /**
     * 订单号，无订单模式为空
     */
    private Long orderId;

    /**
     * 订单类型
     */
    private Integer orderType;

    /**
     * 售卖渠道ID
     */
    private Integer partnerSource;

    /**
     * 售卖渠道下的子渠道
     */
    private String saleChannelId;

    /**
     * 售卖渠道的外部订单ID；如互医的检验单ID
     */
    private String partnerSourceOrderId;

    /**
     * 业务身份
     */
    private String verticalCode;

    /**
     * 用户pin
     */
    private String userPin;

    /**
     * 履约单列表
     */
    private List<Long> promiseId;

    /**
     * 履约单生产的原单号
     */
    private String sourceVoucherId;

    /**
     * 标准状态
     */
    private Integer status;

    /**
     * 标准状态码
     */
    private String statusCode;

    /**
     * 标准状态描述
     */
    private String statusTitle;

    /**
     * 发生时间
     */
    private Date createTime;

}
