package com.jdh.o2oservice.export.support.dto;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;

/**
 * @Description 外呼记录
 * @Date 2024/12/21 下午8:18
 * <AUTHOR>
 **/
@Data
public class CallRecordDto implements Serializable {

    /**
     * 主键
     */
    private Long id;

    /**
     * 用户pin
     */
    private String userPin;

    /**
     * 服务者pin
     */
    private String angelPin;

    /**
     * 履约单ID
     */
    private Long promiseId;

    /**
     * 服务兑换的来源id，兑换id/orderId/outerOrderId
     */
    private String sourceVoucherId;

    /**
     * 通话类型：1-预约人打给服务者 2-被服务人打给服务者 3-服务者打给预约人 4-服务者打给被服务人
     */
    private Integer bizCallType;

    /**
     * 通话ID，唯一确定一次通话
     */
    private String callId;

    /**
     * 绑定关系ID
     */
    private String bindId;

    /**
     * 录音url
     */
    private String audioUrl;

    /**
     * 通话发生时间
     */
    private Date callTime;

    /**
     * 通话开始时间
     */
    private Date startTime;

    /**
     * 通话结束时间
     */
    private Date finishTime;

    /**
     * 通话时长，单位秒
     */
    private Long callDuration;

    /**
     * 接通状态：1-已接通 0-未接通
     */
    private Integer connectedStatus;

    /**
     * 结束发起方:0: 平台结束 1：主叫结束 2：被叫结束
     */
    private String finishType;

    /**
     * 结束状态（即挂断原因）1: 主叫挂机 2: 被叫挂机 3: 主叫放弃
     */
    private String finishState;

    /**
     * 用户附属信息
     */
    private String userData;

    /**
     * 扩展数据
     */
    private String extJson;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 录音类型描述
     */
    private String recordingTypeDesc = "护士与客户沟通录音";
}
