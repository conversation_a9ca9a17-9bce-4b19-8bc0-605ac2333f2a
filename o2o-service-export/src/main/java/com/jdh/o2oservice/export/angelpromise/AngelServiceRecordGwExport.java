package com.jdh.o2oservice.export.angelpromise;
import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.export.angelpromise.dto.SubmitAngelServiceRecordDto;
import com.jdh.o2oservice.export.angelpromise.dto.*;
import java.util.Map;

/**
 * 护理单接口
 */
public interface AngelServiceRecordGwExport {

    /**
     * 提交护理单
     * @param param
     * @return
     */
    Response<SubmitAngelServiceRecordDto> submitAngelServiceRecord(Map<String,String> param);


    /**
     * 查询服务者服务项详情
     * 1、每次查询返回最新创新的流程节点详情
     * 2、新的流程在上一个节点提交时完成创建
     * @param param
     * @return
     */
    Response<AngelServiceRecordDto> queryAngelServiceRecordFlow(Map<String,String> param);


    /**
     * jdh_o2o_query_angel_record_flow
     * @param param
     * @return
     */
    Response<AngelServiceRecordManDto> queryAngelServiceManRecordFlow(Map<String, String> param);
}
