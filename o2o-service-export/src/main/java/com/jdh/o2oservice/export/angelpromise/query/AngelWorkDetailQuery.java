package com.jdh.o2oservice.export.angelpromise.query;

import com.jdh.o2oservice.common.result.request.AbstractRequest;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author:lichen55
 * @Description: 工单详情查询入参
 * @date 2024-05-23 21:49
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AngelWorkDetailQuery extends AbstractRequest {

    /**
     * 工单Id
     */
    private Long workId;

    /**
     * 服务者Id
     */
    private Long angelId;

    /**
     * 履约单Id
     */
    private Long promiseId;

    /**
     * 是否需要返回虚拟状态组件
     */
    private Boolean needVirtualStatus = Boolean.FALSE;

    /**
     * 服务者当前纬度
     */
    private String angelAddressLat;

    /**
     * 服务者当前经度
     */
    private String angelAddressLng;

    /**
     * 服务者工单状态：1=待接单，2=待服务，3=已出门，4=服务中，5=送检中，6=服务完成，7=已退款，8=已取消
     */
    private Integer	workStatus;

    /**
     *
     */
    private List<Integer> notInWorkStatusList;
}
