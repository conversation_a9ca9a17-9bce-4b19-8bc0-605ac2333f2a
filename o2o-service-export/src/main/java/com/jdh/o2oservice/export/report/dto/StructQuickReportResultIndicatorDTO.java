package com.jdh.o2oservice.export.report.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR> limeng363
 * @Date : 2024-04-30 17:24
 * @Desc : 检测结果指标
 */
@Data
public class StructQuickReportResultIndicatorDTO implements Serializable {
    /**
     * 异常指标建议
     */
    private String advice;
    /**
     * 指标编码 京东标准指标编号，以SC开头，例：SC10000000041
     */
    private String indicatorNo;

    /**
     * 指标描述
     */
    private String indicatorDescription;

    /**
     * 指标名称
     */
    private String indicatorName;

    /**
     * 指标正常值范围,历史原因,该值作为结果的兜底（阴性、阳性）
     * 真正正常值范围使用 referenceRangeValue
     */
    private String normalRangeValue;

    /**
     * 指标检测单位
     */
    private String unit;

    /**
     * 指标检测结果
     */
    private String value;

    /**
     * ct结果值，明确定义，用于浓度计算
     */
    private String ctValue;

    /**
     * 指标检测结果描述
     * 定量检测（例：红细胞计数）描述：正常、偏低、偏高；
     * 定性检测（例：HPV11型基因分型）描述：阴性、阳性
     * 12联呼吸道病毒检测描述：阴性、阳性
     */
    private String valueDescription;

    /**
     * 异常类型	 0-正常 1-偏低 2-偏高 3-异常
     * 12联呼吸道病毒检测：0、3
     */
    private String abnormalType;

    /**
     * 特殊标签 1-条件致病菌,多个逗号隔开
     */
    private String tags;

    /**
     * 指标样本浓度
     */
    private String indicatorConcentration;

    /**
     * 指标样本浓度
     */
    private BigDecimal indicatorConcentrationBigDecimal;

    /**
     * 参考范围值
     */
    private String referenceRangeValue;

    /**
     * 结构化的正常值范围。
     */
    private List<RangeValueDTO> rangeValueDTOS;

    /**
     * 模板类型
     */
    private String templateType;

     /** 样本浓度计算公式-java计算公式引擎写法 */
    private String concentrationFormula;

    /**
     * 表示该范围值的百分比。
     */
    private String percentage;
}
