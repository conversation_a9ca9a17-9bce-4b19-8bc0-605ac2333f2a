package com.jdh.o2oservice.export.support.dto;

import com.jdh.o2oservice.export.product.dto.JdhMaterialPackageDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Map;

/**
 * @ClassName PricingServiceCalculateResultDto
 * @Description
 * <AUTHOR>
 * @Date 2025/4/29 16:41
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PricingServiceCalculateResultDto {

    /**
     * 总价
     */
    private BigDecimal totalPrice;

    /**
     * 费项金额明细
     * {"MATERIAL_FEE":50.00,"HOME_VISIT":5.00,"TIME_PERIOD_APPOINTMENT_TYPE_IMMEDIATELY":20.00}
     * @see com.jdh.o2oservice.core.domain.support.basic.enums.JdOrderFeeTypeEnum
     */
    private Map<String, BigDecimal> feeAmountMap;

    /**
     * sku--服务费明细
     * {"100144271632":50.00,"100144271600":5.00}
     */
    private Map<Long,BigDecimal> skuServiceAmountMap;

    /**
     * 耗材结算价格
     */
    private Map<Long, BigDecimal> materialFeeConfig;

    /**
     * 历史已结算金额
     */
    private BigDecimal historySettleAmount;
}