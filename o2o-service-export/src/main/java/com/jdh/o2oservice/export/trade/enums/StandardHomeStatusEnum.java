package com.jdh.o2oservice.export.trade.enums;


import com.google.common.collect.Lists;

import java.util.Arrays;
import java.util.List;

/**
 * @ClassName:StandardHomeStatusEnum
 * @Description:
 * @Author: yaoqinghai
 * @Date: 2023/12/26 10:46
 * @Vserion: 1.0
 **/
public enum StandardHomeStatusEnum {

    /**
     * StandardHomeStatusEnum
     */
    ORDER_WAIT_PAY(1, "ORDER_WAIT_PAY", "待付款"),
    PROMISE_WAIT_APPOINT(2, "PROMISE_WAIT_APPOINT","待预约"),
    DISPATCH_WAIT_RECEIVE(3, "DISPATCH_WAIT_RECEIVE","待接单"),
    WORK_STARTED(4, "WORK_STARTED","已接单"),
    WORK_GO_HOME(5, "WORK_GO_HOME","开始上门"),
    WORK_SERVICING(6, "WORK_SERVICING","服务中"),
    WORK_SAMPLED(7, "WORK_SAMPLED","采样完成"),
    WORK_FINISHED(8, "WORK_FINISHED","服务完成"),
    MEDICAL_PROMISE_GO_LAB(9, "MEDICAL_PROMISE_GO_LAB","送检中"),
    MEDICAL_PROMISE_DELIVERED(10, "MEDICAL_PROMISE_DELIVERED","已送达"),
    MEDICAL_PROMISE_TESTING(11, "MEDICAL_PROMISE_TESTING","检测中"),
    MEDICAL_PROMISE_REPORTED(12, "MEDICAL_PROMISE_REPORTED","已出报告"),
    ORDER_REFUNDING(13, "ORDER_REFUNDING","退款中"),
    ORDer_REFUNDED(14, "ORDer_REFUNDED", "已退款"),

    ;

    private Integer status;
    private String code;
    private String desc;

    StandardHomeStatusEnum(Integer status, String code, String desc) {
        this.status = status;
        this.code = code;
        this.desc = desc;
    }

    public static StandardHomeStatusEnum fromStatus(Integer status) {
        if (null == status) {
            return null;
        }
        for (StandardHomeStatusEnum orderStatusEnum : StandardHomeStatusEnum.values()) {
            if (orderStatusEnum.getStatus().intValue() == status.intValue()) {
                return orderStatusEnum;
            }
        }
        return null;
    }

    public Integer getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }

    public String getCode() {
        return code;
    }
}
