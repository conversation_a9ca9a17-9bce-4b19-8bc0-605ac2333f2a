package com.jdh.o2oservice.export.angelpromise.dto;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class AngelServiceRecordDto implements Serializable {

    /**
     * 任务ID
     */
    private Long taskId;

    /**
     * 服务记录状态
     * 初始状态：0、完成:1、取消：2、评估结果高风险：-1
     *
     */
    private Integer serviceRecordStatus;

    /**
     * 服务者工单状态：1=待接单，2=待服务，3=已出门，4=服务中，5=送检中，6=服务完成，7=已退款，8=已取消
     */
    private Integer workStatus;

    /**
     * 节点集合
     */
    private List<AngelServiceRecordQuestionGroupDto> questionGroupDtoList;

}
