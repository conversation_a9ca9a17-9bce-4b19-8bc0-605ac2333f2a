package com.jdh.o2oservice.export.angelpromise.cmd;

import lombok.Data;

/**
 * @ClassName QcAlarmDetailCmd
 * @Description
 * <AUTHOR>
 * @Date 2025/6/11 21:18
 **/
@Data
public class QcAlarmDetailCmd {
    /**
     * 行为id
     */
    private Long	behaviorId;

    /**
     * 动作名称
     */
    private String behaviorName;

    /**
     * 阶段
     */
    private String	behaviorStage;

    /**
     * 质控点id，业务主键
     */
    private Long	siteId;

    /**
     * 质控点名称
     */
    private String	siteName;

    /**
     * 质控点类型,1-应用质控 2-AI质控
     */
    private Integer	siteType;

    /**
     * 时效类型，1-实时质控 2-后置质控
     */
    private Integer	timelyType;
}