package com.jdh.o2oservice.application.support;

import com.jdh.o2oservice.export.support.command.PricingServiceCalculateCmd;
import com.jdh.o2oservice.export.support.dto.PricingServiceCalculateResultDto;

import java.math.BigDecimal;
import java.util.Map;

/**
 * @ClassName PricingServiceExtApplication
 * @Description
 * <AUTHOR>
 * @Date 2025/4/29 16:34
 **/
public interface PricingServiceExtApplication {
    /**
     * 计算价格，返回详细信息
     * @param cmd
     * @return
     */
    PricingServiceCalculateResultDto calculatePriceExtForDetail(PricingServiceCalculateCmd cmd);

}