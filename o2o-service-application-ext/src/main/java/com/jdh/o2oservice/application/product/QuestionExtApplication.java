package com.jdh.o2oservice.application.product;

import com.jdh.o2oservice.export.product.dto.QuestionGroupDto;
import com.jdh.o2oservice.export.product.query.QueryQuesByGroupCode;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/4
 * @description 题
 */
public interface QuestionExtApplication {

    /**
     * 查询题节点数据
     * @param queryQuesByGroupCode
     * @return
     */
    List<QuestionGroupDto> queryQuesByGroupCode(QueryQuesByGroupCode queryQuesByGroupCode);
}
