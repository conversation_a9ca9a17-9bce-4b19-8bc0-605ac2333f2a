<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.jdh.o2oservice</groupId>
    <artifactId>jdh-o2o-service</artifactId>
    <packaging>pom</packaging>
    <version>${revision}</version>

    <properties>
        <!-- 编译编码格式 -->
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>

        <!-- fastjson -->
        <fastjson.version>1.2.83-jdsec.rc1</fastjson.version>
        <!-- binlake -->
        <binlake.version>1.0.3_alpha</binlake.version>
        <!-- JSF -->
        <jsf.version>1.0.0-HOTFIX-T2</jsf.version>
        <!-- UMP监控 -->
        <com.jd.ump.profiler.version>20230930</com.jd.ump.profiler.version>
        <!-- jimdb -->
        <jimdb.version>2.1.12-HOTFIX-T4</jimdb.version>
        <!-- druid -->
        <druid.version>1.2.11</druid.version>
        <!-- matrix -->
        <matrix.version>1.5.3</matrix.version>
        <!-- lombok -->
        <lombok.version>1.18.22</lombok.version>
        <!-- mapstruct -->
        <org.mapstruct.version>1.4.2.Final</org.mapstruct.version>
        <lombok-mapstruct-binding.version>0.2.0</lombok-mapstruct-binding.version>
        <!-- hutool-->
        <hutool-all.version>5.7.20</hutool-all.version>
        <commons-lang3.version>3.2</commons-lang3.version>
        <commons-collections.version>3.2.2</commons-collections.version>
        <commons-collections4.version>4.1</commons-collections4.version>
        <commons-lang.version>2.6</commons-lang.version>
<!--        <pagehelper.boot.version>1.2.13</pagehelper.boot.version>-->
        <mybatis-plus.version>3.4.0</mybatis-plus.version>
        <elasticsearch.version>6.3.2</elasticsearch.version>
        <revision>1.0.57-SNAPSHOT</revision>
    </properties>

    <modules>
        <module>o2o-service-application</module>
        <module>o2o-service-infrastructure</module>
        <module>o2o-service-export</module>
        <module>o2o-service-web</module>
        <module>o2o-service-base</module>
        <module>o2o-service-common</module>
        <module>o2o-service-domain</module>
        <module>o2o-service-application-ext</module>
        <module>o2o-service-vertical-xfyl</module>
        <module>o2o-service-horizontal-xfyl</module>
    </modules>

    <dependencyManagement>
        <dependencies>

            <dependency>
                <groupId>com.jdh.o2oservice</groupId>
                <artifactId>o2o-service-common</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>org.elasticsearch</groupId>
                <artifactId>elasticsearch</artifactId>
                <version>${elasticsearch.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>log4j-api</artifactId>
                        <groupId>org.apache.logging.log4j</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.elasticsearch.client</groupId>
                <artifactId>transport</artifactId>
                <version>${elasticsearch.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>elasticsearch</artifactId>
                        <groupId>org.elasticsearch</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>org.elasticsearch.plugin</groupId>
                <artifactId>transport-netty4-client</artifactId>
                <version>${elasticsearch.version}</version>
            </dependency>

            <!-- SpringBoot的依赖配置-->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>2.5.14</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- lombok start -->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok-mapstruct-binding</artifactId>
                <version>${lombok-mapstruct-binding.version}</version>
            </dependency>
            <!-- lombok end -->

            <dependency>
                <groupId>javax.servlet</groupId>
                <artifactId>javax.servlet-api</artifactId>
                <version>4.0.1</version>
                <scope>provided</scope>
            </dependency>

            <!-- binlake start https://cf.jd.com/pages/viewpage.action?pageId=213588311 -->
            <dependency>
                <groupId>com.jd.binlake</groupId>
                <artifactId>binlake-wave.client</artifactId>
                <version>${binlake.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>commons-logging</artifactId>
                        <groupId>commons-logging</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- binlake end -->

            <!-- JSF -->
            <dependency>
                <groupId>com.jd</groupId>
                <artifactId>jsf-lite</artifactId>
                <version>${jsf.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>slf4j-log4j12</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.jd</groupId>
                        <artifactId>traceholder</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- 引用 PFinder SDK 库 -->
            <dependency>
                <groupId>com.jd.pfinder</groupId>
                <artifactId>pfinder-profiler-sdk</artifactId>
                <version>1.2.2-FINAL</version>
            </dependency>

            <!-- UMP监控 start -->
            <dependency>
                <groupId>com.jd.ump</groupId>
                <artifactId>profiler</artifactId>
                <version>${com.jd.ump.profiler.version}</version>
            </dependency>
            <!-- UMP监控 end -->

            <!-- SpringBoot集成mybatis-plus框架 -->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>

            <!-- pagehelper 分页插件 -->
<!--            <dependency>-->
<!--                <groupId>com.github.pagehelper</groupId>-->
<!--                <artifactId>pagehelper-spring-boot-starter</artifactId>-->
<!--                <version>${pagehelper.boot.version}</version>-->
<!--            </dependency>-->
<!--            &lt;!&ndash; 数据库 end &ndash;&gt;-->

            <!-- 上帝之手 -->
            <dependency>
                <groupId>com.jd.pioneer</groupId>
                <artifactId>godhand-core</artifactId>
                <version>1.0.1-SNAPSHOT</version>
            </dependency>

            <!-- jimdb start -->
            <dependency>
                <groupId>com.jd.jim.cli</groupId>
                <artifactId>jim-cli-spring</artifactId>
                <version>${jimdb.version}</version>
            </dependency>
            <!-- jimdb end -->


            <!-- 数据传换 -->
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${org.mapstruct.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>

            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>30.1.1-jre</version>
            </dependency>

            <!-- 工具包 -->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool-all.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>${commons-lang3.version}</version>
            </dependency>

            <!-- 阿里数据库连接池 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-starter</artifactId>
                <version>${druid.version}</version>
            </dependency>

            <!-- jmq start -->
            <dependency>
                <groupId>com.jd.jmq</groupId>
                <artifactId>jmq-client-springboot-starter</artifactId>
                <version>2.3.7-HOTFIX-T1</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.hibernate.validator</groupId>
                        <artifactId>hibernate-validator</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.hibernate</groupId>
                        <artifactId>hibernate-validator</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.jd.jmq</groupId>
                <artifactId>jmq-client-spring</artifactId>
                <version>2.3.7-HOTFIX-T1</version>
            </dependency>

            <dependency>
                <groupId>com.jd.jmq</groupId>
                <artifactId>jmq-client-ump</artifactId>
                <version>2.3.7-HOTFIX-T1</version>
            </dependency>

            <!-- jss -->
            <dependency>
                <groupId>com.jd.jss</groupId>
                <artifactId>jss-sdk-java</artifactId>
                <version>1.4.7.2</version>
            </dependency>

            <dependency>
                <groupId>com.jd.laf.config</groupId>
                <artifactId>laf-config-client-jd-springboot-starter</artifactId>
                <version>1.4.1</version>
                <type>pom</type> <!-- 注意：ducc sdk 使用 pom 类型依赖，type=pom 不能省略 -->
            </dependency>

            <dependency>
                <groupId>com.jd.laf.config</groupId>
                <artifactId>laf-config-client-jd-spring</artifactId>
                <version>1.4.1</version>
                <type>pom</type>
            </dependency>

            <dependency>
                <groupId>com.jd.laf.binding</groupId>
                <artifactId>laf-binding-java8</artifactId>
                <version>1.1.1</version>
            </dependency>

            <dependency>
                <groupId>com.jd.security.configsec</groupId>
                <artifactId>spring-configsec-sdk</artifactId>
                <version>1.0.3-SNAPSHOT</version>
            </dependency>

            <!--aces start-->
            <dependency>
                <groupId>com.jd.security</groupId>
                <artifactId>aces-mybatisclient</artifactId>
                <version>1.0.1-SNAPSHOT</version>
                <exclusions>
                    <exclusion>
                        <artifactId>tdeclient</artifactId>
                        <groupId>com.jd.security</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!--   ACES 开始     -->
            <!--  aces - spring bean插件      -->
            <dependency>
                <groupId>com.jd.security</groupId>
                <artifactId>aces-springclient</artifactId>
                <version>3.0.1-SNAPSHOT</version>
                <!-- Step 1. 移除以下两个依赖包("tdeclient"和"tdecommon") -->
                <exclusions>
                    <exclusion>
                        <groupId>com.jd.security</groupId>
                        <artifactId>tdeclient</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.jd.security</groupId>
                        <artifactId>tdecommon</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- Step 2. 重新导入其它版本的"tdeclient"依赖包 -->
            <dependency>
                <groupId>com.jd.security</groupId>
                <artifactId>tdeclient</artifactId>
                <version>3.0.1-SNAPSHOT</version>
            </dependency>
            <!--aces end-->

            <!--    elasticjob    -->
            <dependency>
                <groupId>org.apache.shardingsphere.elasticjob</groupId>
                <artifactId>elasticjob-lite-spring-boot-starter</artifactId>
                <version>3.0.0</version>
                <exclusions>
                    <exclusion>
                        <artifactId>log4j</artifactId>
                        <groupId>log4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>guava</artifactId>
                        <groupId>com.google.guava</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- 订单对象模型 -->
            <dependency>
                <groupId>com.jd.purchase.sdk.domain</groupId>
                <artifactId>purchase-sdk-domain</artifactId>
                <version>6.5.60-RELEASE</version>
            </dependency>

            <!--拆单mq解析包-->
            <dependency>
                <groupId>com.jd.ofc</groupId>
                <artifactId>splitMessageSerialization-pb</artifactId>
                <version>1.0-SNAPSHOT</version>
            </dependency>

            <!-- 消息对象 -->
            <dependency>
                <groupId>com.jd.purchase.common</groupId>
                <artifactId>purchase-msg-domain</artifactId>
                <version>1.0-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.jd.pop.seller</groupId>
                <artifactId>vender-center-open-api</artifactId>
                <version>1.6.18</version>
            </dependency>
            <dependency>
                <groupId>jakarta.validation</groupId>
                <artifactId>jakarta.validation-api</artifactId>
                <version>2.0.2</version>
            </dependency>
            <dependency>
                <groupId>org.codehaus.groovy</groupId>
                <artifactId>groovy-all</artifactId>
                <version>2.4.21</version>
            </dependency>

            <!--短链接-->
            <dependency>
                <groupId>com.jd.shorturl</groupId>
                <artifactId>shorturl-rpc-client</artifactId>
                <version>0.0.6-SNAPSHOT</version>
            </dependency>

            <!--虚拟号-->
            <dependency>
                <groupId>com.jd.jdcc</groupId>
                <artifactId>privacy-number-gateway-facade</artifactId>
                <version>0.0.5-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.googlecode.aviator</groupId>
                <artifactId>aviator</artifactId>
                <version>5.4.1</version>
            </dependency>

            <!--订单金额计算服务（ocs）查询接口-->
            <dependency>
                <groupId>com.jd.ofc</groupId>
                <artifactId>jd-ofc-queryocs-jsf</artifactId>
                <version>1.0.1-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.jd.purchase.common</groupId>
                <artifactId>purchase-serializer-utils</artifactId>
                <version>1.1-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.thoughtworks.xstream</groupId>
                <artifactId>xstream</artifactId>
                <version>1.4.18</version>
            </dependency>

            <dependency>
                <groupId>com.jd</groupId>
                <artifactId>matrix2-core</artifactId>
                <version>${matrix.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jd</groupId>
                <artifactId>matrix2-sdk</artifactId>
                <version>${matrix.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.jsqlparser</groupId>
                <artifactId>jsqlparser</artifactId>
                <version>3.2</version>
                <scope>compile</scope>
            </dependency>

            <!-- 地址编码服务 全地址转换经纬度 -->
            <dependency>
                <groupId>com.jd.lbs.geocode</groupId>
                <artifactId>geocode-api</artifactId>
                <version>1.1.6-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>4.0.0</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>4.0.0</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml-schemas</artifactId>
                <version>4.0.0</version>
            </dependency>
            <dependency>
                <groupId>cn.afterturn</groupId>
                <artifactId>easypoi-base</artifactId>
                <version>4.0.4.yao-SNAPSHOT</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-logging</artifactId>
                        <groupId>commons-logging</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.bval</groupId>
                        <artifactId>org.apache.bval.bundle</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>


            <!-- man端单点登录 begin-->
            <dependency>
                <groupId>com.jd.ssa</groupId>
                <artifactId>oidc-client</artifactId>
                <version>1.0.9-SNAPSHOT</version>
            </dependency>
            <!-- man端单点登录 end-->

            <!-- uim2  -->
            <dependency>
                <groupId>com.jd.uim2</groupId>
                <artifactId>uim2-facade</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <!-- 地图坐标生成 -->
            <dependency>
                <groupId>org.geotools</groupId>
                <artifactId>gt-main</artifactId>
                <version>26.4</version>
            </dependency>

            <!-- 京图路线接口 -->
            <dependency>
                <groupId>com.jd.lbs.jdlbsapi</groupId>
                <artifactId>jdlbsapi-api</artifactId>
                <version>1.3.17-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.google.maps</groupId>
                <artifactId>google-maps-services</artifactId>
                <version>0.15.0</version>
            </dependency>

            <dependency>
                <groupId>com.jdh.o2oservice</groupId>
                <artifactId>o2o-service-horizontal-xfyl</artifactId>
                <version>${revision}</version>
            </dependency>
            <!--视频中台-->
            <dependency>
                <groupId>vd-service-sdk</groupId>
                <artifactId>vd-service-sdk</artifactId>
                <version>1.4.5-SNAPSHOT</version>
            </dependency>

            <!-- 天网-敏感词接口 -->
            <dependency>
                <groupId>com.jd.risk.sensitive_word</groupId>
                <artifactId>sensitive_word_api</artifactId>
                <version>2.3.8</version>
            </dependency>


            <dependency>
                <groupId>com.jd.health.xfyl</groupId>
                <artifactId>jdhealth-xfyl-merchant-export</artifactId>
                <version>1.0.50</version>
            </dependency>

            <!-- O2O门店服务 -->
            <dependency>
                <groupId>com.jd.o2o</groupId>
                <artifactId>o2o-arrive-store-api</artifactId>
                <version>1.0-SNAPSHOT</version>
            </dependency>

            <!--基础用户服务查询接口-->
            <dependency>
                <groupId>com.jd.user.sdk</groupId>
                <artifactId>user-sdk-export</artifactId>
                <version>4.1.3-SNAPSHOT</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.jd</groupId>
                        <artifactId>traceholder</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.alicp.jetcache</groupId>
                <artifactId>jetcache-starter-redis</artifactId>
                <version>2.6.7</version>
            </dependency>

            <!--投保接口-->
            <dependency>
                <groupId>com.jdd.baoxian.b.insurance.core.trade</groupId>
                <artifactId>insurance-core-trade-export</artifactId>
                <version>1.0.8</version>
            </dependency>
                        <!--上传文件-->
        <dependency>
            <groupId>com.jdh.laputa.base</groupId>
            <artifactId>laputa-base-client</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

            <!--ElasticJob Java API 可以通过直接对注册中心进行操作的方式控制作业在分布式环境下的生命周期-->
<!--            <dependency>
                <groupId>org.apache.shardingsphere.elasticjob</groupId>
                <artifactId>elasticjob-lite-lifecycle</artifactId>
                <version>3.0.2</version>
            </dependency>-->
            <dependency>
                <groupId>com.jd.vtp</groupId>
                <artifactId>vtp-client</artifactId>
                <version>3.2.6-SNAPSHOT</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.alibaba</groupId>
                        <artifactId>transmittable-thread-local</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- freemarker模板 -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-freemarker</artifactId>
                <version>1.4.6.RELEASE</version>
            </dependency>

            <dependency>
                <groupId>org.java-websocket</groupId>
                <artifactId>Java-WebSocket</artifactId>
                <version>1.3.5</version>
            </dependency>

            <dependency>
                <groupId>JNative</groupId>
                <artifactId>JNative</artifactId>
                <version>1.4</version>
            </dependency>

            <dependency>
                <groupId>jacob</groupId>
                <artifactId>jacob-1.7</artifactId>
                <version>1.20-SNAPSHOT</version>
            </dependency>
            <!--解析sql语句-->
            <dependency>
                <groupId>com.github.jsqlparser</groupId>
                <artifactId>jsqlparser</artifactId>
                <version>5.0</version>
            </dependency>

            <dependency>
                <groupId>org.hibernate</groupId>
                <artifactId>hibernate-validator</artifactId>
                <version>6.2.5.Final</version>
            </dependency>

            <dependency>
                <groupId>com.jd.abtest</groupId>
                <artifactId>touchtonev2</artifactId>
                <version>1.4.6</version>
            </dependency>

            <dependency>
                <groupId>com.github.dpaukov</groupId>
                <artifactId>combinatoricslib3</artifactId>
                <version>3.4.0</version>
            </dependency>

            <dependency>
                <groupId>com.wangyin.schedule</groupId>
                <artifactId>schedule-client-starter</artifactId>
                <version>2.3.6</version>
            </dependency>


            <dependency>
                <groupId>com.itextpdf</groupId>
                <artifactId>itextpdf</artifactId>
                <version>5.5.13</version>
            </dependency>

            <dependency>
                <groupId>org.jgrapht</groupId>
                <artifactId>jgrapht-core</artifactId>
                <version>1.3.0</version>
            </dependency>

            <dependency>
                <groupId>com.jd.medicine.oplog.center</groupId>
                <artifactId>medicine-oplog-center-export</artifactId>
                <version>1.0.2</version>
            </dependency>

            <!-- 消费医疗商家相关数据 -->
            <dependency>
                <groupId>com.jd.health.xfyl</groupId>
                <artifactId>jdhealth-xfyl-open-export</artifactId>
                <version>1.0.32</version>
            </dependency>

            <!--查询病假条图片JSF接口-->
            <dependency>
                <groupId>com.jdh.afterdiag.medicalrecord</groupId>
                <artifactId>jdh-afterdiag-medicalrecord-export</artifactId>
                <version>1.1.59</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.mybatis</groupId>
                        <artifactId>mybatis</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-web</artifactId>
                    </exclusion>

                    <exclusion>
                        <groupId>com.alibaba</groupId>
                        <artifactId>easyexcel</artifactId>
                    </exclusion>

                    <exclusion>
                        <groupId>org.projectlombok</groupId>
                        <artifactId>lombok</artifactId>
                    </exclusion>

                    <exclusion>
                        <groupId>nethp-components-zootopia-joy</groupId>
                        <artifactId>nethp-components-zootopia-joy-export</artifactId>
                    </exclusion>

                </exclusions>
            </dependency>


            <!--智能体-->
            <dependency>
                <groupId>com.jd.autobots</groupId>
                <artifactId>autobots-client</artifactId>
                <version>1.2.1-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>org.apache.pdfbox</groupId>
                <artifactId>pdfbox</artifactId>
                <version>2.0.29</version> <!-- 使用最新稳定版本 -->
            </dependency>

            <!--文件处理-->
            <dependency>
                <groupId>com.jd.health.xfyl</groupId>
                <artifactId>jdhealth-file-transfer-export</artifactId>
                <version>0.0.1-SNAPSHOT</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <build>
        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>1.3.0</version>
                <inherited>true</inherited>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                        <configuration>
                            <!-- 避免IDE将 .flattened-pom.xml 自动识别为功能模块 -->
                            <updatePomFile>true</updatePomFile>
                            <flattenMode>resolveCiFriendliesOnly</flattenMode>
                            <pomElements>
                                <parent>expand</parent>
                                <distributionManagement>remove</distributionManagement>
                                <repositories>remove</repositories>
                            </pomElements>
                        </configuration>
                    </execution>
                    <execution>
                        <id>flatten.clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>1.4.2.Final</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>1.18.22</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok-mapstruct-binding</artifactId>
                            <version>0.2.0</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
        </plugins>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <excludes>
                    <exclude>dev/*</exclude>
                    <exclude>yfb/*</exclude>
                    <exclude>production/*</exclude>
                </excludes>
                <includes>
                    <include>**/*.properties</include>
                    <include>**/*.xml</include>
                    <include>**/*.yml</include>
                    <include>**/*.txt</include>
                </includes>
                <filtering>true</filtering>
            </resource>
            <resource>
                <directory>src/main/resources/${profiles.active}</directory>
                <includes>
                    <include>**/*.yml</include>
                    <include>**/*.xml</include>
                    <include>**/*.properties</include>
                    <include>**/*.txt</include>
                </includes>
                <filtering>false</filtering>
            </resource>
        </resources>
    </build>

    <repositories>
        <!--artifactory.jd.com私服域名 -->
        <repository>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
            <id>central-local</id>
            <name>libs-releases</name>
            <url>https://artifactory.jd.com/libs-releases-local</url>
        </repository>
        <repository>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
            <id>central</id>
            <name>libs-releases</name>
            <url>https://artifactory.jd.com/libs-releases</url>
        </repository>
        <repository>
            <snapshots/>
            <id>snapshots-local</id>
            <name>libs-snapshots-local</name>
            <url>https://artifactory.jd.com/libs-snapshots-local</url>
        </repository>
        <repository>
            <snapshots/>
            <id>snapshots</id>
            <name>libs-snapshots</name>
            <url>https://artifactory.jd.com/libs-snapshots</url>
        </repository>
    </repositories>
    <pluginRepositories>
        <pluginRepository>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
            <id>central</id>
            <name>plugins-releases</name>
            <url>https://artifactory.jd.com/plugins-releases</url>
        </pluginRepository>
        <pluginRepository>
            <snapshots/>
            <id>snapshots</id>
            <name>plugins-snapshots</name>
            <url>https://artifactory.jd.com/plugins-snapshots</url>
        </pluginRepository>
    </pluginRepositories>

    <distributionManagement>
        <snapshotRepository>
            <id>jd-snapshots</id>
            <name>JD maven2 repository-snapshots</name>
            <url>https://artifactory.jd.com/libs-snapshots-local</url>
        </snapshotRepository>
        <repository>
            <id>jd-central</id>
            <name>JD maven2 repository-releases</name>
            <url>https://artifactory.jd.com/libs-releases-local</url>
        </repository>
    </distributionManagement>

</project>
