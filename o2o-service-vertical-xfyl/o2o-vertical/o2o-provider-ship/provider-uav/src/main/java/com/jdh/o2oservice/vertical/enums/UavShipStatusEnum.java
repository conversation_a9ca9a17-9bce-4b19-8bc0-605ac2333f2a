package com.jdh.o2oservice.vertical.enums;

import com.jdh.o2oservice.ext.ship.enums.StanderAngelShipStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @ClassName SsShipStatusEnum
 * @Description
 * <AUTHOR>
 * @Date 2024/9/29 23:11
 */
@Getter
@AllArgsConstructor
public enum UavShipStatusEnum {

    /**
     * 对应异常情况: KY1/KY2//TQ3/TQ4/GZ5/YL8/YL9
     */
    ERROR_CODE_ONE(3,  "无人机起飞,运输中", StanderAngelShipStatusEnum.WAITING_RECEIVE_GOODS),

    DELIVERING_GOODS(4,  "无人机起飞,运输中", StanderAngelShipStatusEnum.DELIVERING_GOODS),

    /**
     * 对应异常情况: GZ6/GZ7
     */
    ERROR_CODE_TWO(5,  "无人机起飞,运输中", StanderAngelShipStatusEnum.DELIVERING_GOODS),

    ORDER_SHIP_FINISH(9,  "已完成末段物品交接，需求完成", StanderAngelShipStatusEnum.ORDER_SHIP_FINISH),

    ;

    /**
     * 订单状态
     */
    private Integer status;

    /**
     * 订单状态描述
     */
    private String statusDesc;


    /**
     * 标准订单状态
     */
    private StanderAngelShipStatusEnum shipStatusEnum;


    /**
     * 转换成标准运单状态
     * @param order_status
     * @return
     */
    public static Integer transferToStandShipStatus(Integer order_status) {
        for(UavShipStatusEnum shunFengShipStatusEnum : UavShipStatusEnum.values()){
            if(shunFengShipStatusEnum.status.equals(order_status)){
                if(shunFengShipStatusEnum.shipStatusEnum!=null){
                    return shunFengShipStatusEnum.shipStatusEnum.getShipStatus();
                }
            }
        }
        return null;
    }
}
