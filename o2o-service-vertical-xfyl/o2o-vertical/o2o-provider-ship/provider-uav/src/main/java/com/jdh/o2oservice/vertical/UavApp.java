package com.jdh.o2oservice.vertical;

import com.jd.matrix.sdk.annotation.App;
import com.jd.matrix.sdk.base.BaseApp;

/**
 * @ClassName:顺丰
 * @Description:
 * @Author: ya<PERSON>qing<PERSON>
 * @Date: 2024/5/14 22:01
 * @Vserion: 1.0
 **/
@App(code = UavApp.CODE,
        name = "无人机",
        priority = 1,
        parserClass = UavAppCodeParser.class,
        version = "1.0.0")
public class UavApp extends BaseApp {

    public static final String CODE="jdh-o2o-uav";
}
