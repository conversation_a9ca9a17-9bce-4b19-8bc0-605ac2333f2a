package com.jdh.o2oservice.vertical.ext;

/**
 * @ClassName ShunFengPromiseLinkExtImpl
 * @Description
 * <AUTHOR>
 * @Date 2024/12/24 11:03 AM
 * @Version 1.0
 **/

import com.google.common.collect.Lists;
import com.jd.jim.cli.Cluster;
import com.jd.matrix.sdk.annotation.Extension;
import com.jdh.o2oservice.common.ext.ExtResponse;
import com.jdh.o2oservice.ext.ship.CreateShipExt;
import com.jdh.o2oservice.ext.ship.reponse.DeliveryTrackResponse;
import com.jdh.o2oservice.ext.work.WorkPromiseLinkExt;
import com.jdh.o2oservice.ext.work.param.RealTrackParam;
import com.jdh.o2oservice.ext.work.param.TrackParam;
import com.jdh.o2oservice.ext.work.response.DeliveryRealTrackResponse;
import com.jdh.o2oservice.vertical.UavApp;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.List;


/**
 * @ClassName ShunFengPromiseLinkExtImpl
 * @Description
 * <AUTHOR>
 * @Date 2024/12/24 11:03 AM
 * @Version 1.0
 **/
@Slf4j
@Extension(code = UavApp.CODE)
public class UavPromiseLinkExtImpl implements WorkPromiseLinkExt {

    @Resource
    private CreateShipExt shunFengCreateShipExtImpl;


    @Resource
    private Cluster jimClient;

    private static final List<Integer> STATUS_LIST = Lists.newArrayList(2, 100, 3);

    /**
     * 查询H5页面骑手轨迹
     * @param trackParam 查询轨迹入参
     * @return
     */
    @Override
    public ExtResponse<DeliveryTrackResponse> getTransferTrack(TrackParam trackParam) {
        log.info("ShunFengPromiseLinkExtImpl getTransferTrack 方法未实现!!!");
        return null;
    }

    @Override
    public ExtResponse<DeliveryRealTrackResponse> getTransferRealTrack(RealTrackParam realTrackParam) {
        log.info("ShunFengPromiseLinkExtImpl getTransferRealTrack 方法未实现");
        return null;
    }
}
