package com.jdh.o2oservice.vertical.ext;


import IceInternal.Ex;
import com.alibaba.fastjson.JSON;
import com.google.common.base.Joiner;
import com.jd.jim.cli.Cluster;
import com.jd.matrix.sdk.annotation.Extension;
import com.jdh.o2oservice.application.medicalpromise.MedicalPromiseApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.cache.RedisUtil;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.enums.RedisKeyEnum;
import com.jdh.o2oservice.base.event.Event;
import com.jdh.o2oservice.base.event.EventCoordinator;
import com.jdh.o2oservice.base.event.factory.EventFactory;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode;
import com.jdh.o2oservice.base.util.RedisLockUtil;
import com.jdh.o2oservice.common.ext.ExtResponse;
import com.jdh.o2oservice.core.domain.support.ship.UavRpc;
import com.jdh.o2oservice.core.domain.support.ship.dto.AirportCreateDto;
import com.jdh.o2oservice.core.domain.support.ship.enums.UavEventTypeEnum;
import com.jdh.o2oservice.core.domain.support.ship.event.UavAngelShipSupportEventBody;
import com.jdh.o2oservice.core.domain.support.ship.model.UavAngelShipSupport;
import com.jdh.o2oservice.core.domain.support.ship.param.AirportChangeInfoParam;
import com.jdh.o2oservice.core.domain.support.ship.param.AirportCreateParam;
import com.jdh.o2oservice.core.domain.support.ship.param.AirportFlightScheduleParam;
import com.jdh.o2oservice.export.medicalpromise.dto.MedicalPromiseDTO;
import com.jdh.o2oservice.export.medicalpromise.query.MedicalPromiseListRequest;
import com.jdh.o2oservice.export.medicalpromise.query.MedicalPromiseRequest;
import com.jdh.o2oservice.ext.ship.CreateShipExt;
import com.jdh.o2oservice.ext.ship.bo.AngelShipPositionBo;
import com.jdh.o2oservice.ext.ship.param.CancelDadaShipParam;
import com.jdh.o2oservice.ext.ship.param.CreateDadaShipParam;
import com.jdh.o2oservice.ext.ship.param.DeliveryOrderDetailRequest;
import com.jdh.o2oservice.ext.ship.param.DeliveryTrackParam;
import com.jdh.o2oservice.ext.ship.reponse.*;
import com.jdh.o2oservice.vertical.UavApp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @ClassName ShunFengCreateShipExtImpl
 * @Description 运单相关能力扩展点
 * <AUTHOR>
 * @Date 2024/12/13 1:30 PM
 * @Version 1.0
 **/
@Extension(code = UavApp.CODE)
@Slf4j
public class UavCreateShipExtImpl implements CreateShipExt {

    @Resource
    private UavRpc uavRpc;

    @Resource
    private DuccConfig duccConfig;

    @Resource
    private EventCoordinator eventCoordinator;

    @Resource
    private Cluster jimClient;

    @Resource
    private RedisLockUtil redisLockUtil;

    String LOCKKEY = "uav_create_lock_key_%s";

    String FLIGHTKEY = "uav_flight_key_%s";

    @Resource
    private MedicalPromiseApplication medicalPromiseApplication;

    /**
     * 呼叫运力
     * @param createDadaShipParam
     * @param planOutTime
     * @param providerShopNo
     * @return
     */
    @Override
    @LogAndAlarm
    public ExtResponse<CreateShipExtResponse> callTransfer(CreateDadaShipParam createDadaShipParam, Date planOutTime, String providerShopNo) {
        AirportFlightScheduleParam airportFlightScheduleParam = new AirportFlightScheduleParam();
        airportFlightScheduleParam.setStartAirportId(createDadaShipParam.getStartAirportId());
        airportFlightScheduleParam.setEndAirportId(createDadaShipParam.getEndAirportId());
        List<Long> times =  uavRpc.airportFlightSchedule(airportFlightScheduleParam);
        if(CollectionUtils.isEmpty(times)){
            throw new BusinessException(BusinessErrorCode.UAV_SHIP_TIME_ERROR);
        }
        Collections.sort(times);
        Long curTime = System.currentTimeMillis()/1000;
        //记录航班时间
        Long flighTime=0L;
        for (int i = 0; i < times.size(); i++) {
            if(curTime<times.get(i)){
                flighTime = times.get(i);
                break;
            }
        }

        CreateShipExtResponse createShipExtResponse = new CreateShipExtResponse();

        //获取缓存航班飞行需求id
        String cacheKey = RedisKeyEnum.getRedisKey(RedisKeyEnum.UAV_FLIGHT,createDadaShipParam.getStartAirportId(),createDadaShipParam.getEndAirportId(),flighTime);
        String lockKey = RedisKeyEnum.getRedisKey(RedisKeyEnum.UAV_FLIGHT_LOCK_KEY,createDadaShipParam.getStartAirportId(),createDadaShipParam.getEndAirportId(),flighTime);
        String cacheFlightWorkId = jimClient.get(cacheKey);
        log.info("UavCreateShipExtImpl callTransfer cacheKey={},cacheFlightWorkId={}",cacheKey,cacheFlightWorkId);

        try{
            if(StringUtils.isEmpty(cacheFlightWorkId)){
                if(!redisLockUtil.tryLockWithRetry(lockKey,"1",3L,1, 3000L)){
                    log.info("callTransfer 未获取锁,逻辑终止!!!");
                    throw new BusinessException(BusinessErrorCode.UAV_SHIP_CREATE_LOCK_ERROR);
                }
            }
            //重新获取航班id
            cacheFlightWorkId = jimClient.get(cacheKey);
            if(StringUtils.isEmpty(cacheFlightWorkId)){
                //创建新航班id
                this.createNewFlight(createDadaShipParam,cacheKey,flighTime,createShipExtResponse);
                //记录航班已绑定的检测管数量
                String flightkey = String.format(FLIGHTKEY,createShipExtResponse.getOutOrderNo());
                int limitNum = Integer.parseInt(StringUtils.defaultIfBlank(jimClient.get(flightkey),"0"));
                Integer curNum = limitNum+createDadaShipParam.getMedicalPromiseIds().size();
                jimClient.setEx(flightkey,curNum+"",3L,TimeUnit.DAYS);
            }else{

                String flightkey = String.format(FLIGHTKEY,cacheFlightWorkId);
                int limitNum = Integer.parseInt(StringUtils.defaultIfBlank(jimClient.get(flightkey),"0"));
                Integer curNum = limitNum+createDadaShipParam.getMedicalPromiseIds().size();

                if(curNum>duccConfig.getUavConfig().getSpecimenCodeMaxSize()){
                    //创建新的航班
                    this.createNewFlight(createDadaShipParam,cacheKey,flighTime,createShipExtResponse);
                    jimClient.setEx(flightkey,curNum+"",3L,TimeUnit.DAYS);
                }else{
                    //追加航班
                    this.updateFlight(createDadaShipParam,cacheFlightWorkId,createShipExtResponse);
                    jimClient.setEx(flightkey,curNum+"",3L,TimeUnit.DAYS);
                }
            }
            createShipExtResponse.setExpectTakeoffTime(flighTime);

            CreateShipExtFeeResponse createShipExtFeeResponse = new CreateShipExtFeeResponse();
            createShipExtFeeResponse.setTotalAmountDetail("暂无");
            createShipExtFeeResponse.setAmount(new BigDecimal("0"));
            createShipExtFeeResponse.setTotalAmount(new BigDecimal("0"));
            createShipExtFeeResponse.setDiscountAmount(new BigDecimal("0"));
            createShipExtFeeResponse.setInterestList("暂无");
            createShipExtResponse.setExtFeeResponse(createShipExtFeeResponse);



            log.info("callTransfer 无人机飞行需求创建成功.发送模拟事件");
            //发送接单的延迟事件
            UavAngelShipSupport uavAngelShipSupport = UavAngelShipSupport.builder().shipId(Long.parseLong(createDadaShipParam.getOriginId())).build();
            UavAngelShipSupportEventBody uavAngelShipSupportEventBody = new UavAngelShipSupportEventBody();
            uavAngelShipSupportEventBody.setFlightWorkId(createShipExtResponse.getOutOrderNo());
            uavAngelShipSupportEventBody.setConnectName(createDadaShipParam.getSupplierName());
            uavAngelShipSupportEventBody.setConnectPhone(createDadaShipParam.getSupplierPhone());
            Event publishEvent = EventFactory.newDelayEvent(uavAngelShipSupport, UavEventTypeEnum.ANGEL_SHIP_RECEIVE, uavAngelShipSupportEventBody,3L);
            eventCoordinator.publishDelay(publishEvent);
            return ExtResponse.buildSuccess(createShipExtResponse);
        }catch (Exception ex){
            log.error("UavCreateShipExtImpl callTransfer 发生了异常",ex);
            throw ex;
        }finally {
            redisLockUtil.unLock(lockKey);
        }
    }

    /**
     * 创建新航班id
     * @param createDadaShipParam
     * @param cacheKey
     * @param flighTime
     * @param createShipExtResponse
     */
    private void createNewFlight(CreateDadaShipParam createDadaShipParam,String cacheKey,Long flighTime,CreateShipExtResponse createShipExtResponse){
        //创建新飞行需求ID
        AirportCreateParam airportCreateParam = new AirportCreateParam();
        AirportCreateParam.FlightWork flightWork = new AirportCreateParam.FlightWork();
        airportCreateParam.setFlightWork(flightWork);

        flightWork.setStartAirportId(createDadaShipParam.getStartAirportId());
        flightWork.setEndAirportId(createDadaShipParam.getEndAirportId());

        //格式化业务单单据,传给无人机
        List<String> orderNoList = new ArrayList<>();
        orderNoList.add(createDadaShipParam.getOriginId());
        orderNoList.add(createDadaShipParam.getSupplierPhone());

        MedicalPromiseRequest medicalPromiseRequest = new MedicalPromiseRequest();
        medicalPromiseRequest.setMedicalPromiseIds(createDadaShipParam.getMedicalPromiseIds());
        List<MedicalPromiseDTO> medicalPromiseDTOList = medicalPromiseApplication.queryMedicalPromiseList(medicalPromiseRequest);
        orderNoList.addAll(medicalPromiseDTOList.stream().map(MedicalPromiseDTO::getSpecimenCode).collect(Collectors.toList()));


        String orderData = Joiner.on("+").join(orderNoList);
        flightWork.setOrderNo(orderData);
        flightWork.setExpectTakeoffTime(flighTime);

        AirportCreateDto airportCreateDto = uavRpc.create(airportCreateParam);
        if(airportCreateDto==null){
            throw new BusinessException(BusinessErrorCode.UAV_SHIP_CREATE_ERROR);
        }
        jimClient.setEx(cacheKey,airportCreateDto.getFlightWorkId(),2,TimeUnit.DAYS);
        createShipExtResponse.setOutOrderNo(airportCreateDto.getFlightWorkId());
    }

    /**
     * 追加飞行ID的业务单据
     */
    private void updateFlight(CreateDadaShipParam createDadaShipParam,String cacheFlightWorkId,CreateShipExtResponse createShipExtResponse){
        //追加飞行需求ID
        AirportChangeInfoParam airportCreateParam = new AirportChangeInfoParam();

        //格式化业务单单据,传给无人机
        List<String> orderNoList = new ArrayList<>();
        orderNoList.add(createDadaShipParam.getOriginId());
        orderNoList.add(createDadaShipParam.getSupplierPhone());

        MedicalPromiseRequest medicalPromiseRequest = new MedicalPromiseRequest();
        medicalPromiseRequest.setMedicalPromiseIds(createDadaShipParam.getMedicalPromiseIds());
        List<MedicalPromiseDTO> medicalPromiseDTOList = medicalPromiseApplication.queryMedicalPromiseList(medicalPromiseRequest);
        orderNoList.addAll(medicalPromiseDTOList.stream().map(MedicalPromiseDTO::getSpecimenCode).collect(Collectors.toList()));


        String orderData = Joiner.on("+").join(orderNoList);
        airportCreateParam.setOrderNo(orderData);

        airportCreateParam.setFlightWorkId(cacheFlightWorkId);
        uavRpc.changeInfo(airportCreateParam);
        createShipExtResponse.setOutOrderNo(cacheFlightWorkId);
    }



    /**
     * 重新呼叫运力
     * @param createDadaShipParam
     * @return
     */
    @Override
    @LogAndAlarm
    public ExtResponse<Boolean> reCallTransfer(CreateDadaShipParam createDadaShipParam) {
        return null;
    }

    /**
     * 取消呼叫运力
     * @param cancelDadaShipParam
     * @return
     */
    @Override
    @LogAndAlarm
    public ExtResponse<Boolean> cancelTransfer(CancelDadaShipParam cancelDadaShipParam) {
        return null;
    }

    /**
     * 运力供应商状态回传转换
     * @param callbackParamMap
     * @return
     */
    @Override
    @LogAndAlarm
    public ExtResponse<ShipCallbackParamResponse> parseCallbackParam(Map<String, Object> callbackParamMap) {
        return null;
    }

    /**
     * 查询骑手轨迹
     * @param deliveryTrackParam
     * @return
     */
    @Override
    @LogAndAlarm
    public DeliveryTrackResponse getTransferTrack(DeliveryTrackParam deliveryTrackParam) {
        return null;
    }

    /**
     * 查询运单详情
     * @param deliveryOrderDetailRequest
     * @return
     */
    @Override
    public ExtResponse<DeliveryOrderDetailResponse> getShipOrderDetail(DeliveryOrderDetailRequest deliveryOrderDetailRequest) {
        String cacheKey = RedisKeyEnum.getRedisKey(RedisKeyEnum.DELIVERY_POSITION,deliveryOrderDetailRequest.getOutOrderId());
        String cacheData = jimClient.get(cacheKey);
        if(StringUtils.isEmpty(cacheData)){
            return ExtResponse.buildSuccess(null);
        }

        AngelShipPositionBo angelShipPositionBo = JSON.parseObject(cacheData,AngelShipPositionBo.class);
        DeliveryOrderDetailResponse deliveryOrderDetailResponse = new DeliveryOrderDetailResponse();
        deliveryOrderDetailResponse.setTransporterLng(angelShipPositionBo.getLon()+"");
        deliveryOrderDetailResponse.setTransporterLat(angelShipPositionBo.getLat()+"");
        deliveryOrderDetailResponse.setRemainingDistance(angelShipPositionBo.getRemainingDistance());
        deliveryOrderDetailResponse.setRemainingTime(angelShipPositionBo.getRemainingTime());
        deliveryOrderDetailResponse.setUavTimestamp(angelShipPositionBo.getTimestamp());
        deliveryOrderDetailResponse.setAlt(angelShipPositionBo.getAlt());
        return ExtResponse.buildSuccess(deliveryOrderDetailResponse);
    }
}
