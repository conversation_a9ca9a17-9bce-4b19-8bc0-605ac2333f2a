package com.jdh.o2oservice.application.settlement.listener;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.fastjson.JSON;
import com.jd.medicine.base.common.util.StringUtil;
import com.jdh.o2oservice.application.settlement.service.JdServiceCityAngelSettleApplication;
import com.jdh.o2oservice.application.support.service.FileManageApplication;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.enums.CityLevelEnum;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.model.DomainEnum;
import com.jdh.o2oservice.base.util.SpringUtil;
import com.jdh.o2oservice.core.domain.settlement.enums.SettleErrorCode;
import com.jdh.o2oservice.core.domain.settlement.enums.SettlementFileBizTypeEnum;
import com.jdh.o2oservice.core.domain.settlement.model.ImportAngelSettleCityConfig;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.BaseAddressBo;
import com.jdh.o2oservice.export.support.command.GeneratePutUrlCommand;
import com.jdh.o2oservice.export.support.dto.FilePreSignedUrlDto;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.time.LocalDateTime;
import java.util.*;

/**
 * 护士结算城市配置导入
 *
 * <AUTHOR>
 * @date 2025-04-21 11:46
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Slf4j
public class ImportAngelSettleCityConfigListener extends AnalysisEventListener<ImportAngelSettleCityConfig> {
    /**
     * 导入数据总量
     */
    private int total = 0;
    /**
     * 导入数据成功总量
     */
    private int successTotal = 0;
    /**
     * 导入数据百分比
     */
    private Integer percentage = 0;
    /**
     * 失败文件地址
     */
    private String failUrl;
    /**
     * 解析是否有失败
     */
    private Boolean isFail = false;
    /**
     * 失败列表
     */
    private List<ImportAngelSettleCityConfig> failList = new ArrayList<>();
    /**
     * 成功列表
     */
    private Map<String,ImportAngelSettleCityConfig> succMap = new HashMap<>();

    /**
     * userPin
     */
    private String userPin;
    /**
     * filePreSignedUrlDto
     */
    private FilePreSignedUrlDto filePreSignedUrlDto;
    /**
     * cityAreaMap
     */
    private Map<String,BaseAddressBo> cityAreaMap = new HashMap<>();

    /**
     * 构造函数
     */
    public ImportAngelSettleCityConfigListener(String userPin) {
        this.userPin = userPin;
    }

    /**
     *
     * @param importAngelSettleCityConfig importAngelSettleCityConfig
     * @param analysisContext     analysisContext
     */
    @Override
    public void invoke(ImportAngelSettleCityConfig importAngelSettleCityConfig, AnalysisContext analysisContext) {
        String failReason = "";
        try {
            Integer rowNumber = analysisContext.readSheetHolder().getApproximateTotalRowNumber();
            log.info("ImportAngelSettleCityConfigListener#invoke importAngelSettleCityConfig={} rowNumber={}", JSON.toJSONString(importAngelSettleCityConfig), rowNumber);
            if(rowNumber > 99){
                throw new BusinessException(SettleErrorCode.ANGEL_ASJUST_DATA_OUT);
            }
            StringBuilder stringBuilder = new StringBuilder();
            String provinceName = importAngelSettleCityConfig.getProvinceName();
            Integer provinceCode = null;
            if (StringUtil.isEmpty(provinceName)) {
                stringBuilder.append("省名称为空,");
            }else {
                BaseAddressBo baseAddress = getBaseAddressBo(provinceName);
                if (Objects.isNull(baseAddress)) {
                    stringBuilder.append("省名称查不到，请确认,");
                } else {
                    provinceCode = baseAddress.getProvinceCode();
                    importAngelSettleCityConfig.setProvinceCode(String.valueOf(provinceCode));
                    String costName = provinceName;
                    String cityName = importAngelSettleCityConfig.getCityName();
                    if (StringUtil.isEmpty(cityName)) {
                        if (Objects.nonNull(provinceCode) && provinceCode > CommonConstant.FOUR) {
                            stringBuilder.append("市名称为空,");
                        }
                    } else {
                        if (Objects.nonNull(provinceCode) && provinceCode <= CommonConstant.FOUR) {
                            stringBuilder.append("市不需要配置,");
                        }else{
                            costName += cityName;
                            if (Objects.nonNull(provinceCode) && provinceCode > CommonConstant.FOUR) {
                                baseAddress = getBaseAddressBo(cityName);
                            } else {
                                baseAddress = getBaseAddressBo(costName);
                            }
                            if (Objects.isNull(baseAddress)) {
                                stringBuilder.append("市名称查不到，请确认,");
                            } else {
                                Integer cityCode = baseAddress.getCityCode();
                                if (!provinceCode.equals(baseAddress.getProvinceCode())) {
                                    stringBuilder.append("省市不匹配，请确认,");
                                } else {
                                    if (!cityName.contains(baseAddress.getCityName()) && !baseAddress.getCityName().contains(cityName)) {
                                        stringBuilder.append("省市不匹配，请确认,");
                                    } else {
                                        importAngelSettleCityConfig.setCityCode(String.valueOf(cityCode));
                                    }
                                }
                            }
                        }
                    }
                }
            }

            if(StringUtil.isEmpty(importAngelSettleCityConfig.getLevelDesc())){
                stringBuilder.append("城市等级为空,");
            }else{
                String levelDesc = importAngelSettleCityConfig.getLevelDesc();
                if(!CityLevelEnum.validateCityLevel(levelDesc)){
                    stringBuilder.append("城市等级不正确,");
                }
                importAngelSettleCityConfig.setLevel(CityLevelEnum.getLevelByDesc(levelDesc));
            }

            if(StringUtil.isNotBlank(stringBuilder.toString())){
                importAngelSettleCityConfig.setFailReason(stringBuilder.toString());
                isFail = true;
            }
            failList.add(importAngelSettleCityConfig);
        } catch (Exception e) {
            log.error("ImportAngelSettleCityConfigListener exception", e);
            failReason = e.getMessage();
            importAngelSettleCityConfig.setFailReason(failReason);
            isFail = true;
            failList.add(importAngelSettleCityConfig);
        }
        String costCode = importAngelSettleCityConfig.getProvinceCode() + importAngelSettleCityConfig.getCityCode();
        succMap.put(costCode,importAngelSettleCityConfig);
    }

    /**
     *
     * @param cityName
     * @return
     */
    private BaseAddressBo getBaseAddressBo(String cityName){
        BaseAddressBo baseAddressBo = cityAreaMap.get(cityName);
        if(Objects.nonNull(baseAddressBo)){
            return baseAddressBo;
        }else{
            JdServiceCityAngelSettleApplication angelApplication = SpringUtil.getBean(JdServiceCityAngelSettleApplication.class);
            baseAddressBo = angelApplication.getProvinceName(cityName);
            if(Objects.nonNull(baseAddressBo)){
                cityAreaMap.put(cityName,baseAddressBo);
            }
        }
        return baseAddressBo;
    }

    /**
     *
     * @param analysisContext analysisContext
     */
    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        if (isFail) {
            log.info("ImportAngelSettleCityConfigListener doAfterAllAnalysed failList={}", JSON.toJSONString(failList));
            FileManageApplication fileManageApplication = SpringUtil.getBean(FileManageApplication.class);
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            EasyExcelFactory.write(outputStream, ImportAngelSettleCityConfig.class).sheet("失败记录").doWrite(failList);
            String fileName = "错误文件.xlsx";
            GeneratePutUrlCommand data = new GeneratePutUrlCommand();
            data.setUserPin(userPin);
            data.setDomainCode(DomainEnum.SETTLE_MENT.getCode());
            data.setFileBizType(SettlementFileBizTypeEnum.ANGEL_SETTLE_ADJUST.getBizType());
            filePreSignedUrlDto = fileManageApplication.upload(fileName, new ByteArrayInputStream(outputStream.toByteArray()), LocalDateTime.now().plusHours(24), data);
        }
    }
}
