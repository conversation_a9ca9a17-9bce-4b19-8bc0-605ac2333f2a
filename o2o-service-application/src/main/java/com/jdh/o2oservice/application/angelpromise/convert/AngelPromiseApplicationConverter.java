package com.jdh.o2oservice.application.angelpromise.convert;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.jdh.o2oservice.application.support.service.FileManageApplication;
import com.jdh.o2oservice.base.ducc.model.AngelWorkDetailShowTemplateConfig;
import com.jdh.o2oservice.base.ducc.model.AngelWorkEnumConfig;
import com.jdh.o2oservice.base.model.DomainEnum;
import com.jdh.o2oservice.base.util.SpringUtil;
import com.jdh.o2oservice.base.util.TimeUtils;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.core.domain.angelpromise.bo.AngelWorkCreateResultBo;
import com.jdh.o2oservice.core.domain.angelpromise.context.*;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelWorkStatusEnum;
import com.jdh.o2oservice.core.domain.angelpromise.event.eventbody.AngelShipCreateFailBody;
import com.jdh.o2oservice.core.domain.angelpromise.model.*;
import com.jdh.o2oservice.core.domain.angelpromise.repository.query.AngelWorkDBQuery;
import com.jdh.o2oservice.core.domain.angelpromise.vo.AngelCharge;
import com.jdh.o2oservice.core.domain.angelpromise.vo.JdhAngelWorkExtVo;
import com.jdh.o2oservice.core.domain.support.basic.model.PhoneNumber;
import com.jdh.o2oservice.core.domain.support.basic.model.UserName;
import com.jdh.o2oservice.core.domain.support.ship.param.CreateDadaShipBo;
import com.jdh.o2oservice.export.angelpromise.cmd.*;
import com.jdh.o2oservice.export.angelpromise.dto.*;
import com.jdh.o2oservice.export.angelpromise.query.AngelRealLocationQuery;
import com.jdh.o2oservice.export.angelpromise.query.AngelWorkDetailQuery;
import com.jdh.o2oservice.export.angelpromise.query.AngelWorkListQuery;
import com.jdh.o2oservice.export.support.command.GenerateGetUrlCommand;
import com.jdh.o2oservice.export.support.dto.FilePreSignedUrlDto;
import com.jdh.o2oservice.export.trade.query.RefundOrderParam;
import org.apache.commons.collections4.CollectionUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 服务者履约域applicationConvert
 */
@Mapper
public interface AngelPromiseApplicationConverter {

    Integer EXPIRE_TIME = 24;

    AngelPromiseApplicationConverter instance = Mappers.getMapper(AngelPromiseApplicationConverter.class);

    @Mapping(target = "workIds", source = "workId", qualifiedByName = "singleLongToList")
    @Mapping(target = "angelIds", source = "angelId", qualifiedByName = "singleLongToStringList")
    AngelWorkDBQuery detailQuery2DBQuery(AngelWorkDetailQuery detailQuery);

    AngelWorkHistoryDto convertToAngelWorkHistory(AngelWorkHistory angelWorkHistory);

    List<AngelWorkHistoryDto> convertToAngelWorkHistoryList(List<AngelWorkHistory> angelWorkHistoryList);

    /**
     *
     * @param bo
     * @return
     */
    AngelWorkCreateResultDto convertToAngelWorkCreateResultDto(AngelWorkCreateResultBo bo);

    @Mapping(source = "work.jdhAngelWorkExtVo.angelClothingImg", target = "clothingPicUrls")
    @Mapping(source = "work.jdhAngelWorkExtVo.medicalWasteImg", target = "medicalWastePicUrls")
    @Mapping(source = "work.status", target = "statusDesc", qualifiedByName = "statusDescQualified")
    default AngelWorkDto entity2WorkDto(AngelWork work){
        if ( work == null ) {
            return null;
        }

        AngelWorkDto angelWorkDto = new AngelWorkDto();
        JdhAngelWorkExtVo jdhAngelWorkExtVo = work.getJdhAngelWorkExtVo();
        if (Objects.nonNull(jdhAngelWorkExtVo)){
            List<Long> clothingFileIds = jdhAngelWorkExtVo.getClothingFileIds();
            List<Long> wasteDestroyFileIds = jdhAngelWorkExtVo.getWasteDestroyFileIds();
            List<Long> serviceRecordFileIds = jdhAngelWorkExtVo.getServiceRecordFileIds();
            FileManageApplication application = SpringUtil.getBean(FileManageApplication.class);

            LocalDateTime time = LocalDateTime.now().plus(EXPIRE_TIME, ChronoUnit.HOURS);
            Date expireTime = TimeUtils.localDateTimeToDate(time);

            if (CollectionUtils.isNotEmpty(clothingFileIds)){
                GenerateGetUrlCommand command = new GenerateGetUrlCommand();
                command.setFileIds(Sets.newHashSet(clothingFileIds));
                command.setDomainCode(DomainEnum.ANGEL_PROMISE.getCode());
                command.setIsPublic(Boolean.TRUE);
                command.setExpireTime(expireTime);
                List<FilePreSignedUrlDto> urlDtos = application.generateGetUrl(command);
                List<String> angelClothingImg = urlDtos.stream().map(FilePreSignedUrlDto::getUrl).collect(Collectors.toList());
                angelWorkDto.setClothingPicUrls(angelClothingImg);
            }

            if (CollectionUtils.isNotEmpty(wasteDestroyFileIds)){
                GenerateGetUrlCommand command = new GenerateGetUrlCommand();
                command.setFileIds(Sets.newHashSet(wasteDestroyFileIds));
                command.setDomainCode(DomainEnum.ANGEL_PROMISE.getCode());
                command.setIsPublic(Boolean.TRUE);
                command.setExpireTime(expireTime);
                List<FilePreSignedUrlDto> urlDtos = application.generateGetUrl(command);
                List<String> medicalWasteImg = urlDtos.stream().map(FilePreSignedUrlDto::getUrl).collect(Collectors.toList());
                angelWorkDto.setMedicalWastePicUrls(medicalWasteImg);
            }

            if (CollectionUtils.isNotEmpty(serviceRecordFileIds)){
                GenerateGetUrlCommand command = new GenerateGetUrlCommand();
                command.setFileIds(Sets.newHashSet(serviceRecordFileIds));
                command.setDomainCode(DomainEnum.ANGEL_PROMISE.getCode());
                command.setIsPublic(Boolean.TRUE);
                command.setExpireTime(expireTime);
                List<FilePreSignedUrlDto> urlDtos = application.generateGetUrl(command);
                List<String> serviceRecordImg = urlDtos.stream().map(FilePreSignedUrlDto::getUrl).collect(Collectors.toList());
                angelWorkDto.setServiceRecordPicUrls(serviceRecordImg);
            }
        }

        if ( work.getWorkStatus() != null ) {
            angelWorkDto.setStatusDesc( statusDescQualified(work.getWorkStatus()) );
        }
        angelWorkDto.setId( work.getId() );
        angelWorkDto.setWorkId( work.getWorkId() );
        angelWorkDto.setJdOrderId( work.getJdOrderId() );
        angelWorkDto.setPromiseId( work.getPromiseId() );
        angelWorkDto.setWorkType( work.getWorkType() );
        angelWorkDto.setWorkTypeDesc( work.getWorkTypeDesc() );
        angelWorkDto.setAngelId( work.getAngelId() );
        angelWorkDto.setAngelPin( work.getAngelPin() );
        angelWorkDto.setAngelName( new UserName(work.getAngelName()).mask());
        angelWorkDto.setAngelPhone( new PhoneNumber(work.getAngelPhone()).mask() );
        angelWorkDto.setAngelCharge( work.getAngelCharge() );
        angelWorkDto.setStatus( work.getWorkStatus() );
        angelWorkDto.setInsureId( work.getInsureId() );
        angelWorkDto.setInsureStatus( work.getInsureStatus() );
        angelWorkDto.setAngelTasks( entity2DtoList( work.getAngelTasks() ) );
        angelWorkDto.setCreateTime(work.getCreateTime());
        angelWorkDto.setVerticalCode(work.getVerticalCode());
        angelWorkDto.setServiceType(work.getServiceType());
        angelWorkDto.setWorkStartTime(work.getWorkStartTime());
        angelWorkDto.setWorkEndTime(work.getWorkEndTime());
        if(CollUtil.isNotEmpty(work.getWorkHistories())){
            List<AngelWorkHistory> workHistories = work.getWorkHistories();
            List<AngelWorkHistoryDto> workHistoryDtoList = new ArrayList<>();
            for (AngelWorkHistory workHistory : workHistories) {
                AngelWorkHistoryDto historyDto = AngelWorkHistoryDto.builder()
                        .angelWorkId(work.getWorkId().toString())
                        .beforeStatus(workHistory.getBeforeStatus())
                        .afterStatus(workHistory.getAfterStatus())
                        .createTime(workHistory.getCreateTime())
                        .operateTime(workHistory.getOperateTime())
                        .build();
                workHistoryDtoList.add(historyDto);
            }
            angelWorkDto.setWorkHistoryList(workHistoryDtoList);
        }
        return angelWorkDto;
    }

    List<AngelWorkDto> entity2WorkDtoList(List<AngelWork> works);


    default PageDto<AngelWorkDto> convertToAngelWorkPage(Page<AngelWork> workPages){
        PageDto pageDto = new PageDto();
        if (Objects.isNull(workPages)){
            return pageDto;
        }

        pageDto.setPageNum(workPages.getCurrent());
        pageDto.setPageSize(workPages.getSize());
        pageDto.setTotalPage(workPages.getPages());
        pageDto.setTotalCount(workPages.getTotal());
        List<AngelWorkDto> list = Lists.newArrayList();
        for (AngelWork record : workPages.getRecords()) {
            AngelWorkDto dto = entity2WorkDto(record);
            list.add(dto);
        }
        pageDto.setList(list);
        return pageDto;
    }



    List<AngelTaskDto> entity2DtoList(List<AngelTask> tasks);

    CreateDadaShipBo convertToCreateDadaShipBo(AngelWorkShipCreateContext shipCreateContext);

    AngelShipCallBackContext convertToAngelShipDadaCallBackContext(ShipInfoForCallBackRequest callBackRequest);

    AngelWorkListQueryContext convertToAngelWorkListQueryContext(AngelWorkListQuery angelWorkListQuery);


    AngelWorkShowTemplateDto AngelWorkDetailShowTemplateConfigToDto(AngelWorkDetailShowTemplateConfig config);

    List<AngelWorkShowTemplateDto> AngelWorkDetailShowTemplateConfigToDtoList(List<AngelWorkDetailShowTemplateConfig> configs);

    DeliverContext convertToDeliverContext(DeliverCmd deliverCmd);

    AngelWorkEnumDto AngelWorkEnumConfigToDto(AngelWorkEnumConfig config);

    List<AngelWorkEnumDto> AngelWorkEnumConfigToDtoList(List<AngelWorkEnumConfig> configs);

    AngelTaskContext convertToAngelTaskContext(AngelTaskCmd angelTaskCmd);

    List<AngelTaskContext> convertToAngelTaskContextList(List<AngelTaskCmd> angelTaskCmdList);

    AngelWorkShipCancelContext convertToAngelWorkShipCancelContext(AngelWorkCancelShipCmd angelWorkCancelShipCmd);

    AngelCharge convertToAngelCharge(AngelChargeInfo chargeInfo);

    AngelWorkDetailForManDto convertToAngelWorkDetailForManDto(AngelWork angelWork);

    @Mapping(target = "angelWorkId", source = "workId")
    @Mapping(target = "angelShipExtDTO",source = "jdhAngelShipExtVo.angelWorkCreateShipExt")
    @Mapping(target = "uavResultDto",source = "jdhAngelShipExtVo.uavResult")
    AngelShipDto convertToAngelShipDto(AngelShip angelShip);

    List<AngelShipDto> convertToAngelShipDtoList(List<AngelShip> angelShipList);

    AngelShipHistoryDto convertToAngelShipHistoryDto(AngelShipHistory angelShipHistory);

    List<AngelShipHistoryDto> convertToAngelShipHistoryDtoList(List<AngelShipHistory> angelShipHistorys);

    @Mapping(target = "status", source = "workStatus")
    AngelWorkDetailDto convertToAngelWorkDetailDto(AngelWork angelWork);

    List<AngelWorkDetailDto> convertToAngelWorkDetailDtoList(List<AngelWork> angelWorkList);

    RefundOrderParam convertToRefundOrderParam(AngelShipCreateFailBody angelShipCreateFailBody);

    AngelShipTrackContext convertToAngelShipTrackContext(AngelRealLocationQuery angelRealLocationQuery);

    @Named("statusDescQualified")
    default String statusDescQualified(int status) {
        AngelWorkStatusEnum enumByCode = AngelWorkStatusEnum.getEnumByCode(status);
        if(Objects.isNull(enumByCode)){
            return "其他";
        }
        return enumByCode.getShowDesc();
    }

    @Named("angelWorkMeterialPackagesQualified")
    default Integer angelWorkMeterialPackagesQualified(List<AngelWorkMaterialPackageDto> angelWorkMeterialPackages) {
        if(CollectionUtils.isEmpty(angelWorkMeterialPackages)){
            return 0;
        }
        return angelWorkMeterialPackages.stream().mapToInt(meterialPackage -> Integer.parseInt(meterialPackage.getPlanConsumeTime())).sum();
    }

    @Named("serviceItemsQualified")
    default List<AngelWorkMedPromiseDto> serviceItemsQualified(List<AngelTaskDto> angelTasks) {
        return null;
    }

    @Named("singleLongToList")
    default List<Long> singleLongToList(Long source){
        if(Objects.isNull(source)){
            return null;
        }
        return Lists.newArrayList(source);
    }

    @Named("singleLongToStringList")
    default List<String> singleLongToStringList(Long source){
        if(Objects.isNull(source)){
            return null;
        }
        return Lists.newArrayList(String.valueOf(source));
    }

    @Named("singleStringToList")
    default List<String> singleStringToList(String source){
        if(Objects.isNull(source)){
            return null;
        }
        return Lists.newArrayList(source);
    }

    List<AngelWorkDto> convertToAngelWorkDtoList(List<AngelWork> angelWorkList);
}
