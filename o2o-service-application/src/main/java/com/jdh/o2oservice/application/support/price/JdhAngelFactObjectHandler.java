package com.jdh.o2oservice.application.support.price;

import com.jdh.o2oservice.application.angel.service.AngelApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.core.domain.support.basic.enums.PricingServiceFactObjectEnum;
import com.jdh.o2oservice.core.domain.support.price.context.PricingServiceCalculateContext;
import com.jdh.o2oservice.core.domain.support.price.enums.PricingServiceErrorCode;
import com.jdh.o2oservice.core.domain.support.price.handler.PricingServiceFactObjectHandler;
import com.jdh.o2oservice.export.angel.dto.JdhAngelDto;
import com.jdh.o2oservice.export.angel.query.AngelRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @ClassName JdhAngelFactObjectHandler
 * @Description
 * <AUTHOR>
 * @Date 2025/4/30 16:41
 **/
@Component
@Slf4j
public class JdhAngelFactObjectHandler extends AbstractFactObjectHandler {

    /**
     * 护士服务
     */
    @Resource
    private AngelApplication angelApplication;

    /**
     * 获取事实对象
     * @param context
     * @return
     */
    @Override
    public Object getData(PricingServiceCalculateContext context) {
        boolean containsKey = context.getFactObjectMap().containsKey(getMapKey());
        if (containsKey) {
            return context.getFactObjectMap().get(getMapKey());
        }
        JdhAngelDto jdhAngelDto = angelApplication.queryByAngelInfo(AngelRequest.builder().angelId(context.getAngelId()).build());
        if (jdhAngelDto == null) {
            throw new BusinessException(PricingServiceErrorCode.ANGEL_NOT_EXIST);
        }
        context.getFactObjectMap().put(getMapKey(), jdhAngelDto);
        return jdhAngelDto;
    }

    /**
     * 获取map key
     * @return
     */
    @Override
    public String getMapKey() {
        return PricingServiceFactObjectEnum.ANGEL_INFO.getCode();
    }
}