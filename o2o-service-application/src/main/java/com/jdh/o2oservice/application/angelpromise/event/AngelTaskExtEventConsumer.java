package com.jdh.o2oservice.application.angelpromise.event;

import com.alibaba.fastjson.JSON;
import com.jdh.o2oservice.application.angelpromise.service.AngelServiceRecordApplication;
import com.jdh.o2oservice.application.product.service.ProductApplication;
import com.jdh.o2oservice.base.enums.RedisKeyEnum;
import com.jdh.o2oservice.base.event.Event;
import com.jdh.o2oservice.base.event.EventConsumerRegister;
import com.jdh.o2oservice.base.event.WrapperEventConsumer;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.model.DomainEnum;
import com.jdh.o2oservice.base.util.RedisLockUtil;
import com.jdh.o2oservice.core.domain.angelpromise.bo.AngelTaskExtStateBo;
import com.jdh.o2oservice.core.domain.angelpromise.bo.AngelTaskStateBo;
import com.jdh.o2oservice.core.domain.angelpromise.context.AngelTaskExtStatusContext;
import com.jdh.o2oservice.core.domain.angelpromise.context.AngelTaskStatusContext;
import com.jdh.o2oservice.core.domain.angelpromise.enums.*;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelTask;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelTaskIdentifier;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelWork;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelWorkIdentifier;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelTaskRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelWorkRepository;
import com.jdh.o2oservice.core.domain.angelpromise.service.AngelTaskDomainService;
import com.jdh.o2oservice.export.angelpromise.query.AngelServiceRecordQuery;
import com.jdh.o2oservice.export.product.dto.JdhSkuDto;
import com.jdh.o2oservice.export.product.query.JdhSkuListRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * @ClassName:AngelTaskExtEventConsumer
 * @Description:
 * @Author: yaoqinghai
 * @Date: 2024/4/19 20:40
 * @Vserion: 1.0
 **/
@Component
@Slf4j
public class AngelTaskExtEventConsumer {

    /**
     * 事件消费注册
     */
    @Resource
    private EventConsumerRegister eventConsumerRegister;

    @Resource
    private AngelTaskDomainService angelTaskDomainService;

    @Resource
    private AngelTaskRepository angelTaskRepository;

    @Resource
    private ProductApplication productApplication;

    @Resource
    private AngelWorkRepository angelWorkRepository;

    @Resource
    private RedisLockUtil redisLockUtil;

    @Resource
    private AngelServiceRecordApplication angelServiceRecordApplication;

    @PostConstruct
    public void registerEventConsumer(){

        eventConsumerRegister.register(AngelTaskBizExtEventTypeEnum.ANGEL_TASK_EXT_EVENT_OUT,
                WrapperEventConsumer.newInstance(DomainEnum.ANGEL_PROMISE, "taskGetOut", this::handleTaskGetOut, Boolean.TRUE, Boolean.FALSE));

        eventConsumerRegister.register(AngelTaskBizExtEventTypeEnum.ANGEL_TASK_EXT_EVENT_CHECKED,
                WrapperEventConsumer.newInstance(DomainEnum.ANGEL_PROMISE, "taskInServed", this::handleTaskInServed, Boolean.FALSE, Boolean.FALSE));

        eventConsumerRegister.register(AngelTaskBizExtEventTypeEnum.ANGEL_TASK_EXT_EVENT_BIND,
                WrapperEventConsumer.newInstance(DomainEnum.ANGEL_PROMISE, "homeEnd", this::handleTaskHomeEnd, Boolean.FALSE, Boolean.FALSE));

        eventConsumerRegister.register(AngelTaskBizExtEventTypeEnum.ANGEL_TASK_EXT_EVENT_DELIVERY,
                WrapperEventConsumer.newInstance(DomainEnum.ANGEL_PROMISE, "taskDelivery", this::handleTaskDelivery, Boolean.FALSE, Boolean.FALSE));

        eventConsumerRegister.register(AngelTaskBizExtEventTypeEnum.SERVICE_FINISH,
                WrapperEventConsumer.newInstance(DomainEnum.ANGEL_PROMISE, "taskFinish", this::handleTaskFinish, Boolean.FALSE, Boolean.FALSE));
    }

    /**
     * 已出门
     * @param event
     */
    private void handleTaskGetOut(Event event) {
        handle(event, AngelTaskStatusEnum.WAIT_SERVICE, AngelTaskEventTypeEnum.ANGEL_TASK_EVENT_WAITING_SERVED);
    }

    /**
     * 处理上门结束
     *
     * @param event
     */
    private void handleTaskHomeEnd(Event event) {
        handle(event, AngelTaskStatusEnum.SERVICED, AngelTaskEventTypeEnum.ANGEL_TASK_EVENT_DONE_SERVED);
    }

    /**
     * 处理任务完成
     *
     * @param event
     */
    private void handleTaskFinish(Event event) {
        handle(event, AngelTaskStatusEnum.COMPLETED, AngelTaskEventTypeEnum.ANGEL_TASK_EVENT_FINISH_SERVED);
    }

    /**
     * 处理任务配送中
     *
     * @param event
     */
    private void handleTaskDelivery(Event event) {
        handle(event, AngelTaskStatusEnum.CONFIRMED, AngelTaskEventTypeEnum.ANGEL_TASK_EVENT_DELIVERY);
    }

    /**
     * 处理任务服务中
     *
     * @param event
     */
    private void handleTaskInServed(Event event) {
        //推任务状态到服务中
        handle(event, AngelTaskStatusEnum.SERVICING, AngelTaskEventTypeEnum.ANGEL_TASK_EVENT_IN_SERVED);

        //检查任务下的sku是否是不需要核验信息的，如果都不需要核验信息把任务扩展状态推到核验信息完成状态
        AngelTask angelTask = getAngelTask(event);
        AngelWork angelWork = angelWorkRepository.find(new AngelWorkIdentifier(angelTask.getWorkId()));

        if(Objects.isNull(angelWork)){
            log.error("[AngelTaskExtEventConsumer.handleTaskInServed],工单信息不存在!event={}", JSON.toJSONString(event));
            throw new BusinessException(AngelPromiseBizErrorCode.ANGEL_WORK_NOT_EXIST);
        }
        if(Objects.isNull(angelWork.getJdhAngelWorkExtVo()) || Objects.isNull(angelWork.getJdhAngelWorkExtVo().getAngelOrder())
                || CollectionUtils.isEmpty(angelWork.getJdhAngelWorkExtVo().getAngelOrder().getSkuIds())) {
            log.info("[AngelTaskExtEventConsumer.handleTaskInServed],扩展信息不存在!angelWork={}", JSON.toJSONString(angelWork));
            return;
        }

        Set<Long> skuIds = angelWork.getJdhAngelWorkExtVo().getAngelOrder().getSkuIds();
        Map<Long, JdhSkuDto> longJdhSkuDtoMap = querySkuBySkuIds(skuIds);
        if (MapUtils.isEmpty(longJdhSkuDtoMap)) {
            log.info("[AngelTaskExtEventConsumer.handleTaskInServed],扩展信息不存在!angelWork={}", JSON.toJSONString(angelWork));
            return;
        }

        boolean skipConfirm1 = longJdhSkuDtoMap.values().stream().allMatch(item -> CollectionUtils.isEmpty(item.getCustomerConfirmType()));
        boolean skipConfirm2 = angelServiceRecordApplication.checkAngelServiceRecordConfig(AngelServiceRecordQuery.builder().workId(angelTask.getWorkId()).build());
        if(skipConfirm1 || skipConfirm2){
            //推业务扩展状态到核验信息完成
            log.info("[AngelTaskExtEventConsumer.handleTaskInServed],推核验信息完成状态!event={}", JSON.toJSONString(event));
            AngelTaskExtStateBo taskExtStateBo = AngelTaskExtStateBo.builder()
                    .taskExtStatus(Objects.equals(angelWork.getWorkType(), AngelWorkTypeEnum.NURSE.getType()) ? AngelBizExtStatusEnum.CONFIRM_IDENTITY.getType() : AngelBizExtStatusEnum.CARE_CONFIRM_IDENTITY.getType())
                    .taskId(angelTask.getTaskId())
                    .build();
            AngelTaskExtStatusContext angelTaskExtStatusContext = AngelTaskExtStatusContext.builder()
                    .workId(angelWork.getWorkId())
                    .angelTaskExtStateBoList(Lists.newArrayList(taskExtStateBo))
                    .operator(angelWork.getAngelPin())
                    .build();
            angelTaskDomainService.executeTaskExt(angelTaskExtStatusContext);
        }
    }

    /**
     * 执行
     *
     * @param event
     * @param angelTaskStatusEnum
     * @param angelTaskEventTypeEnum
     */
    private boolean handle(Event event, AngelTaskStatusEnum angelTaskStatusEnum, AngelTaskEventTypeEnum angelTaskEventTypeEnum){
        log.info("[AngelTaskExtEventConsumer.handleTaskInServed],任务扩展状态执行事件开始!event={}", JSON.toJSONString(event));
        AngelTask angelTask = getAngelTask(event);

        String lockKey = RedisKeyEnum.getRedisKey(RedisKeyEnum.ANGEL_TASK_STATUS_CHANGE_LOCK_KEY, angelTask.getTaskId(), angelTaskStatusEnum.getType());
        boolean isLock = redisLockUtil.tryLock(lockKey, "1", RedisKeyEnum.ANGEL_TASK_STATUS_CHANGE_LOCK_KEY.getExpireTime(), RedisKeyEnum.ANGEL_TASK_STATUS_CHANGE_LOCK_KEY.getExpireTimeUnit());
        if(!isLock){
            log.error("[[AngelTaskExtEventConsumer.handle],任务单状态变更获取分布式锁失败!], event={}", JSON.toJSONString(event));
            return false;
        }
        try{
            AngelTaskStatusContext angelTaskStatusContext = AngelTaskStatusContext.builder().build();
            angelTaskStatusContext.setWorkId(angelTask.getWorkId());

            List<AngelTaskStateBo> angelTaskStateBoList = Lists.newArrayList();
            AngelTaskStateBo angelTaskStateBo = AngelTaskStateBo.builder()
                    .taskId(angelTask.getTaskId())
                    .taskStatus(angelTaskStatusEnum.getType())
                    .build();
            angelTaskStateBoList.add(angelTaskStateBo);

            angelTaskStatusContext.setAngelTaskStateBoList(angelTaskStateBoList);
            angelTaskStatusContext.setAngelTaskEventTypeEnum(angelTaskEventTypeEnum);
            angelTaskDomainService.executeTask(angelTaskStatusContext);
        }finally {
            redisLockUtil.unLock(lockKey);
        }
        return true;
    }

    /**
     * 查询任务单
     *
     * @param event
     * @return
     */
    private AngelTask getAngelTask(Event event) {
        String aggregateId = event.getAggregateId();
        if(StringUtils.isBlank(aggregateId)){
            log.error("[AngelTaskExtEventConsumer.handleTaskInServed],任务单扩展状态聚合根id为空!event={}", JSON.toJSONString(event));
            throw new BusinessException(AngelPromiseBizErrorCode.EVENT_INFO_ERROR);
        }
        AngelTask angelTask = angelTaskRepository.find(new AngelTaskIdentifier(Long.valueOf(aggregateId)));
        if(Objects.isNull(angelTask)){
            log.error("[AngelTaskExtEventConsumer.getAngelTask],任务单信息不存在!event={}", JSON.toJSONString(event));
            throw new BusinessException(AngelPromiseBizErrorCode.ANGEL_TASK_NOT_EXIST);
        }
        return angelTask;
    }


    /**
     * 根据skuId查商品详情
     * @param skuIds
     * @return
     */
    private Map<Long, JdhSkuDto> querySkuBySkuIds(Set<Long> skuIds){
        JdhSkuListRequest skuRequest = new JdhSkuListRequest();
        skuRequest.setSkuIdList(skuIds);
        skuRequest.setQuerySkuCoreData(Boolean.TRUE);
        skuRequest.setQueryServiceItem(Boolean.FALSE);
        return productApplication.queryJdhSkuInfoList(skuRequest);
    }

}
