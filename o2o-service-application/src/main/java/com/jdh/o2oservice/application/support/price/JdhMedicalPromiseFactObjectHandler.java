package com.jdh.o2oservice.application.support.price;

import com.jdh.o2oservice.application.medicalpromise.MedicalPromiseApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.core.domain.support.basic.enums.PricingServiceFactObjectEnum;
import com.jdh.o2oservice.core.domain.support.price.context.PricingServiceCalculateContext;
import com.jdh.o2oservice.core.domain.support.price.enums.PricingServiceErrorCode;
import com.jdh.o2oservice.core.domain.support.price.handler.PricingServiceFactObjectHandler;
import com.jdh.o2oservice.export.medicalpromise.dto.MedicalPromiseDTO;
import com.jdh.o2oservice.export.medicalpromise.query.MedicalPromiseRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * @ClassName JdhMedicalPromiseFactObjectHandler
 * @Description
 * <AUTHOR>
 * @Date 2025/5/5 13:55
 **/
@Component
@Slf4j
public class JdhMedicalPromiseFactObjectHandler extends AbstractFactObjectHandler {

    /**
     * 检测单数据
     */
    @Resource
    private MedicalPromiseApplication medicalPromiseApplication;

    /**
     * 获取事实对象
     * @param context
     * @return
     */
    @Override
    public Object getData(PricingServiceCalculateContext context) {
        boolean containsKey = context.getFactObjectMap().containsKey(getMapKey());
        if (containsKey) {
            return context.getFactObjectMap().get(getMapKey());
        }
        //查询检测单数据，过滤掉作废和已冻结的
        List<MedicalPromiseDTO> medicalPromiseDTOList = medicalPromiseApplication.queryMedicalPromiseList(MedicalPromiseRequest.builder().promiseId(context.getPromiseId()).invalid(false).freezeQuery(false).build());
        if (CollectionUtils.isEmpty(medicalPromiseDTOList)) {
            throw new BusinessException(PricingServiceErrorCode.MEDICAL_PROMISE_NOT_EXIST);
        }
        context.getFactObjectMap().put(getMapKey(), medicalPromiseDTOList);
        return medicalPromiseDTOList;
    }

    /**
     * 获取map key
     * @return
     */
    @Override
    public String getMapKey() {
        return PricingServiceFactObjectEnum.MEDICAL_PROMISE_LIST.getCode();
    }
}