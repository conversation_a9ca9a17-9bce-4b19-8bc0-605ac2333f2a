package com.jdh.o2oservice.application.support.price;

import cn.hutool.core.convert.Convert;
import com.jdh.o2oservice.application.settlement.service.JdServiceCityAngelSettleApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.ducc.model.fee.CityLevelSettlementCoefficientConfig;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.core.domain.settlement.repository.query.CityAngelSettlementPageQuery;
import com.jdh.o2oservice.core.domain.support.basic.enums.PricingServiceFactObjectEnum;
import com.jdh.o2oservice.core.domain.support.price.context.PricingServiceCalculateContext;
import com.jdh.o2oservice.core.domain.support.price.handler.PricingServiceFactObjectHandler;
import com.jdh.o2oservice.export.settlement.dto.CityAngelSettlementConfigDto;
import com.jdh.o2oservice.export.trade.dto.AddressInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;

/**
 * @ClassName JdhCityLevelFactObjectHandler
 * @Description
 * <AUTHOR>
 * @Date 2025/5/6 08:19
 **/
@Component
@Slf4j
public class JdhCityLevelFactObjectHandler extends AbstractFactObjectHandler {

    /**
     * cityAngelSettleApplication
     */
    @Resource
    private JdServiceCityAngelSettleApplication cityAngelSettleApplication;

    /**
     * 地址服务
     */
    @Resource
    private JdhAddressFactObjectHandler addressFactObjectHandler;

    /**
     *
     */
    @Resource
    private DuccConfig duccConfig;

    /**
     * 获取事实对象
     * @param context
     * @return
     */
    @Override
    public Object getData(PricingServiceCalculateContext context) {
        boolean containsKey = context.getFactObjectMap().containsKey(getMapKey());
        if (containsKey) {
            return context.getFactObjectMap().get(getMapKey());
        }
        //前置依赖地址数据，先从上下文中获取，没有则调用接口获取，并放入上下文
        Object factObject = addressFactObjectHandler.getFactObject(context);
        if (Objects.isNull(factObject)) {
            context.getFactObjectMap().put(getMapKey(), null);
            return null;
        }
        AddressInfoDTO addressInfoDTO = Convert.convert(AddressInfoDTO.class, factObject);

        //先查对应省 + 市有没有配置
        CityAngelSettlementPageQuery cityPageQuery = new CityAngelSettlementPageQuery();
        cityPageQuery.setDestCode(addressInfoDTO.getCityId().toString());
        cityPageQuery.setPageNum(1);
        cityPageQuery.setPageSize(1);
        PageDto<CityAngelSettlementConfigDto> cityPageDto = cityAngelSettleApplication.queryCityAngelSettlementPage(cityPageQuery);
        //有配置数据直接返回
        if (Objects.nonNull(cityPageDto) && CollectionUtils.isNotEmpty(cityPageDto.getList())) {
            CityAngelSettlementConfigDto settlementConfigDto = cityPageDto.getList().get(0);
            context.getFactObjectMap().put(getMapKey(), settlementConfigDto);
            return settlementConfigDto;
        }

        //再查对应省有没有配置
        CityAngelSettlementPageQuery provincePageQuery = new CityAngelSettlementPageQuery();
        provincePageQuery.setDestCode(addressInfoDTO.getProvinceId().toString());
        provincePageQuery.setPageNum(1);
        provincePageQuery.setPageSize(1);
        PageDto<CityAngelSettlementConfigDto> provincePageDto = cityAngelSettleApplication.queryCityAngelSettlementPage(provincePageQuery);
        //有配置数据直接返回
        if (Objects.nonNull(provincePageDto) && CollectionUtils.isNotEmpty(provincePageDto.getList())) {
            CityAngelSettlementConfigDto settlementConfigDto = provincePageDto.getList().get(0);
            context.getFactObjectMap().put(getMapKey(), settlementConfigDto);
            return settlementConfigDto;
        }
        //未找到配置数据，返回兜底
        Map<String, CityLevelSettlementCoefficientConfig> cityLevelSettlementCoefficientConfigMap = duccConfig.getCityLevelSettlementCoefficientConfigMap();
        if (Objects.nonNull(cityLevelSettlementCoefficientConfigMap) && Objects.nonNull(cityLevelSettlementCoefficientConfigMap.get("default"))) {
            CityLevelSettlementCoefficientConfig result = cityLevelSettlementCoefficientConfigMap.get("default");
            CityAngelSettlementConfigDto settlementConfigDto = CityAngelSettlementConfigDto.builder()
                    .provinceName("兜底配置")
                    .selfServiceCoefficient(result.getSelfServiceCoefficient())
                    .sidelineServiceCoefficient(result.getSidelineServiceCoefficient())
                    .platformServiceCoefficient(result.getPlatformServiceCoefficient())
                    .platformSubsidyCoefficient(result.getPlatformSubsidyCoefficient())
                    .build();
            context.getFactObjectMap().put(getMapKey(), settlementConfigDto);
            return settlementConfigDto;
        }
        context.getFactObjectMap().put(getMapKey(), null);
        return null;
    }

    /**
     * 获取map key
     * @return
     */
    @Override
    public String getMapKey() {
        return PricingServiceFactObjectEnum.CITY_LEVEL_CONFIG.getCode();
    }
}