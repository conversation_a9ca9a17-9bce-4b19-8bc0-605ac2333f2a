package com.jdh.o2oservice.application.angelpromise.service;

import com.jdh.o2oservice.export.angelpromise.dto.AngelWorkItemDto;
import com.jdh.o2oservice.export.angelpromise.query.AngelWorkItemQuery;

import java.util.List;

/**
 * @ClassName AngelWorkReadApplication
 * @Description
 * <AUTHOR>
 * @Date 2025/5/27 21:44
 */
public interface AngelWorkReadApplication {

    /**
     * 查询工单及工单项目信息
     *
     * @param angelWorkItemQuery
     * @return
     */
    List<AngelWorkItemDto> queryAngelWorkItemList(AngelWorkItemQuery angelWorkItemQuery);

}
