package com.jdh.o2oservice.application.support.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.amazonaws.HttpMethod;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.jdh.o2oservice.application.support.FileManageExtApplication;
import com.jdh.o2oservice.application.support.convert.FileApplicationConverter;
import com.jdh.o2oservice.application.support.service.FileManageApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.annotation.MapAutowired;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.enums.ThreadPoolConfigEnum;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.SystemException;
import com.jdh.o2oservice.base.exception.errorcode.SystemErrorCode;
import com.jdh.o2oservice.base.factory.ExecutorPoolFactory;
import com.jdh.o2oservice.base.util.AssertUtils;
import com.jdh.o2oservice.base.util.TimeUtils;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.core.domain.support.basic.enums.SupportErrorCode;
import com.jdh.o2oservice.core.domain.support.basic.model.BaseDomainErrorCode;
import com.jdh.o2oservice.core.domain.support.file.context.*;
import com.jdh.o2oservice.core.domain.support.file.enums.FileExportTypeEnum;
import com.jdh.o2oservice.core.domain.support.file.enums.FileImportTypeEnum;
import com.jdh.o2oservice.core.domain.support.file.enums.FileSignaturePositionEnum;
import com.jdh.o2oservice.core.domain.support.file.enums.FileSignatureRelationEnum;
import com.jdh.o2oservice.core.domain.support.file.factory.JdhFileTaskFactory;
import com.jdh.o2oservice.core.domain.support.file.model.*;
import com.jdh.o2oservice.core.domain.support.file.repository.db.ExportConfigRepository;
import com.jdh.o2oservice.core.domain.support.file.repository.db.JdhFileRepository;
import com.jdh.o2oservice.core.domain.support.file.repository.db.JdhFileTaskRepository;
import com.jdh.o2oservice.core.domain.support.file.repository.query.PageFileTaskQuery;
import com.jdh.o2oservice.core.domain.support.file.service.AbstractFileExportHandler;
import com.jdh.o2oservice.core.domain.support.file.service.AbstractFileImportHandler;
import com.jdh.o2oservice.core.domain.support.file.service.FileManageService;
import com.jdh.o2oservice.core.domain.support.file.service.impl.FileManageServiceImpl;
import com.jdh.o2oservice.export.support.command.GenerateGetUrlCommand;
import com.jdh.o2oservice.export.support.command.GeneratePutUrlCommand;
import com.jdh.o2oservice.export.support.command.PdfSignatureCmd;
import com.jdh.o2oservice.export.support.command.PutHttpFileCommand;
import com.jdh.o2oservice.export.support.dto.*;
import com.jdh.o2oservice.export.support.query.GetFileUrlRequest;
import com.jdh.o2oservice.export.support.query.PageFileTaskRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.net.URL;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executors;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * 文件管理 application
 * @author: yangxiyu
 * @date: 2024/3/20 4:53 下午
 * @version: 1.0
 */
@Component
@Slf4j
public class FileManageApplicationImpl implements FileManageApplication,FileManageExtApplication {
    /** */
    @Resource
    private JdhFileTaskRepository jdhFileTaskRepository;
    /** */
    @Resource
    private ExportConfigRepository exportConfigRepository;
    /** */
    @Resource
    private FileManageService fileManageService;
    /** */
    @Resource
    private JdhFileRepository jdhFileRepository;

    /**
     * serviceMap
     */
    @MapAutowired
    private Map<String, AbstractFileExportHandler> serviceMap;

    /**
     * importServiceMap
     */
    @MapAutowired
    private Map<String, AbstractFileImportHandler> importServiceMap;

    /**
     * executorPoolFactory
     */
    @Resource
    private ExecutorPoolFactory executorPoolFactory;

    @Resource
    private DuccConfig duccConfig;

    /**
     * （1）先根据scene获取导出的表头配置，如果有则使用这个配置，否则获取export的默认配置。
     * （2）根据scene获取service的实现，不存在的话使用exportType获取service
     * @param param
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.support.service.impl.FileManageApplicationImpl.export")
    public Boolean export(Map<String, Object> param) {
        importExportTask(param);
        return Boolean.TRUE;
    }

    private JdhImportExportTask importExportTask(Map<String, Object> param){
        String operationType = Objects.toString(param.get("operationType"), null);
        String userPin = Objects.toString(param.get("userPin"), null);
        String scene = Objects.toString(param.get("scene"), null);
        AssertUtils.hasText(operationType, SystemErrorCode.UNKNOWN_ERROR);
        AssertUtils.hasText(scene, SystemErrorCode.UNKNOWN_ERROR);

        // 获取场景的文件导出服务，如果没有则获取默认的导出类型的导出服务
        AbstractFileExportHandler service = serviceMap.get(scene);
        if (Objects.isNull(service)){
            service = serviceMap.get(operationType);
        }
        if (Objects.isNull(service)){
            log.warn("FileManageApplicationImpl->export service is null");
            throw new SystemException(SystemErrorCode.CONFIG_ERROR.formatDescription("配置缺失"));
        }
        FileExportContext ctx = FileExportContext.builder()
                .scene(scene)
                .userPin(userPin)
                .operatorType(FileExportTypeEnum.findType(operationType))
                .queryParam(param)
                .queue(new LinkedBlockingQueue<>())
                .build();
        // 前置处理
        service.prepare(ctx);

        // 查询配置
        FileExportConfig config = exportConfigRepository.find(new FileExportConfigIdentifier(scene));
        if (Objects.isNull(config)){
            log.info("FileManageApplicationImpl->export scene config is null, queryByType");
            config = exportConfigRepository.find(new FileExportConfigIdentifier(operationType));
        }
        if (Objects.isNull(config)){
            throw new SystemException(SystemErrorCode.CONFIG_ERROR.formatDescription("配置缺失"));
        }

        // 创建任务
        ctx.setConfig(config);
        JdhImportExportTask task = JdhFileTaskFactory.create(ctx);
        ctx.setTask(task);
        jdhFileTaskRepository.add(task);

        // 执行导出
        service.syncExport(ctx);
        log.info("FileManageApplicationImpl importExportTask export task={}", JSON.toJSONString(ctx.getTask()));
        return ctx.getTask();
    }

    @Override
    public PutFileResultDto exportAndDownloadFile(Map<String, Object> param) {
        log.info("FileManageApplicationImpl exportAndDownloadFile param={}", JSON.toJSONString(param));
        JdhImportExportTask task = importExportTask(param);
        log.info("FileManageApplicationImpl exportAndDownloadFile task={}", JSON.toJSONString(task));
        String publicUrl = fileManageService.getPublicUrl(task.getFileUrl(), true, DateUtil.offsetDay(new Date(), 5));
        // 返回结果
        PutFileResultDto result = new PutFileResultDto();
        result.setBucket(task.getBucket());
        result.setExpireTime(task.getExpireTime());
        result.setFilePath(task.getFileUrl());
        result.setFileUrl(publicUrl);
        return result;
    }

    /**
     * 文件导出
     *
     * @param param
     */
    @Override
    public JdhImportExportTask exportTask(Map<String, Object> param) {
        String operationType = Objects.toString(param.get("operationType"), null);
        String userPin = Objects.toString(param.get("userPin"), null);
        String scene = Objects.toString(param.get("scene"), null);
        AssertUtils.hasText(operationType, SystemErrorCode.UNKNOWN_ERROR);
        AssertUtils.hasText(scene, SystemErrorCode.UNKNOWN_ERROR);

        // 获取场景的文件导出服务，如果没有则获取默认的导出类型的导出服务
        AbstractFileExportHandler service = serviceMap.get(scene);
        if (Objects.isNull(service)){
            service = serviceMap.get(operationType);
        }
        if (Objects.isNull(service)){
            log.warn("FileManageApplicationImpl->export service is null");
            throw new SystemException(SystemErrorCode.CONFIG_ERROR.formatDescription("配置缺失"));
        }
        FileExportContext ctx = FileExportContext.builder()
                .scene(scene)
                .userPin(userPin)
                .operatorType(FileExportTypeEnum.findType(operationType))
                .queryParam(param)
                .queue(new LinkedBlockingQueue<>())
                .build();

        // 前置处理
        service.prepare(ctx);

        // 查询配置
        FileExportConfig config = exportConfigRepository.find(new FileExportConfigIdentifier(scene));
        if (Objects.isNull(config)){
            log.info("FileManageApplicationImpl->export scene config is null, queryByType");
            config = exportConfigRepository.find(new FileExportConfigIdentifier(operationType));
        }
        if (Objects.isNull(config)){
            throw new SystemException(SystemErrorCode.CONFIG_ERROR.formatDescription("配置缺失"));
        }

        // 创建任务
        ctx.setConfig(config);
        JdhImportExportTask task = JdhFileTaskFactory.create(ctx);
        ctx.setTask(task);
        jdhFileTaskRepository.add(task);
        // 执行导出
        service.syncExport(ctx);
        log.info("FileManageApplicationImpl exportTask task={}", JSON.toJSONString(ctx.getTask()));
        return ctx.getTask();
    }

    /**
     * 文件导入
     *
     * @param param
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.support.service.impl.FileManageApplicationImpl.importFile")
    public Boolean importFile(Map<String, Object> param) {
        String operationType = Objects.toString(param.get("operationType"), null);
        String userPin = Objects.toString(param.get("userPin"), null);
        String scene = Objects.toString(param.get("scene"), null);
        Long fileId = Convert.convert(Long.class,param.get("fileId"));
        AssertUtils.nonNull(fileId, SupportErrorCode.SUPPORT_FILE_IMPORT_PARAM_MISS_ERROR);
        AssertUtils.hasText(operationType, SupportErrorCode.SUPPORT_FILE_IMPORT_PARAM_MISS_ERROR);
        AssertUtils.hasText(scene, SupportErrorCode.SUPPORT_FILE_IMPORT_PARAM_MISS_ERROR);

        //1、根据scene获取具体处理类
        AbstractFileImportHandler fileImportHandler = importServiceMap.get(scene);
        if (Objects.isNull(fileImportHandler)){
            log.warn("FileManageApplicationImpl-> import service is null");
            throw new SystemException(SystemErrorCode.CONFIG_ERROR.formatDescription("配置缺失"));
        }

        //2、构建上下文
        FileImportContext ctx = FileImportContext.builder()
                .userPin(userPin)
                .operationType(FileImportTypeEnum.findType(operationType))
                .fileId(fileId)
                .scene(scene)
                .build();
        //3、构建task入库
        JdhImportExportTask task = JdhFileTaskFactory.create(ctx);
        ctx.setTaskId(task.getTaskId());
        log.info("FileManageApplicationImpl-> import task:{}",JSON.toJSONString(task));
        jdhFileTaskRepository.add(task);

        log.info("FileManageApplicationImpl-> import ctx:{}",JSON.toJSONString(ctx));
        //4、异步调用具体处理类进行异步解析逻辑
        CompletableFuture.runAsync(() -> fileImportHandler.doImport(ctx),
                executorPoolFactory.get(ThreadPoolConfigEnum.DEFAULT));

        return Boolean.TRUE;
    }

    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.support.service.impl.FileManageApplicationImpl.pageFileTask")
    public PageDto<FileTaskDto> pageFileTask(PageFileTaskRequest request) {
        PageFileTaskQuery query = new PageFileTaskQuery();
        query.setScene(request.getScene());
        query.setOperationType(request.getOperationType());
        query.setUserPin(request.getUserPin());
        query.setOperationTypeList(request.getOperationTypeList());
        query.setExpire(Objects.isNull(request.getExpire()) ? Boolean.FALSE : request.getExpire());
        query.setPageNum(request.getPageNum());
        query.setPageSize(request.getPageSize());
        Page<JdhImportExportTask> page = jdhFileTaskRepository.page(query);
        if (page == null){
            return PageDto.getEmptyPage();
        }
        log.info("FileManageApplicationImpl pageFileTask page:{}",JSON.toJSONString(page));
        List<FileTaskDto> list = FileApplicationConverter.INSTANCE.convert2TaskDto(page.getRecords());
        PageDto<FileTaskDto> pageDto = new PageDto<>();
        pageDto.setList(list);
        pageDto.setTotalPage(page.getPages());
        pageDto.setPageNum(page.getCurrent());
        pageDto.setPageSize(page.getSize());
        pageDto.setTotalCount(page.getTotal());
        return pageDto;
    }

    /**
     * 获取文件访问链接
     * @param request
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.support.service.impl.FileManageApplicationImpl.getFileUrl")
    public String getFileUrl(GetFileUrlRequest request) {
        boolean isPublic = Boolean.FALSE.equals(request.getIsPublic()) ? Boolean.FALSE : Boolean.TRUE;
        if(Objects.nonNull(request.getFileId())){
            JdhFile jdhFile = jdhFileRepository.find(JdhFileIdentifier.builder().fileId(request.getFileId()).build());
            URL url = fileManageService.generateGetUrl(jdhFile, isPublic, request.getExpiration());
            log.info("FileManageApplicationImpl -> getFileUrl url:{}",JSON.toJSONString(url));
            return url.toString();
        }else{
            AssertUtils.hasText(request.getTaskId(), SystemErrorCode.SYSTEM_ERROR);
            AssertUtils.hasText(request.getUserPin(), SystemErrorCode.SYSTEM_ERROR);
            JdhImportExportTask task = jdhFileTaskRepository.find(new JdhImportExportTaskIdentifier(Long.valueOf(request.getTaskId())), request.getUserPin());
            return fileManageService.getPublicUrl(task.getFileUrl(), isPublic, request.getExpiration());
        }
    }

    /**
     * 生成文件上传的预签名链接
     * @param command
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.support.service.impl.FileManageApplicationImpl.generatePutUrl")
    public FilePreSignedUrlDto generatePutUrl(GeneratePutUrlCommand command) {
        GeneratePutUrlBo bo = FileApplicationConverter.INSTANCE.convert2GeneratePutUrl(command);

        bo.init();

        JdhFile file = JdhFileTaskFactory.createPutFile(bo);

        URL url = fileManageService.generatePutUrl(file, bo.getIsPublic(), bo.getExpireTime(), command.getContentType());

        jdhFileRepository.save(file);
        return FileApplicationConverter.INSTANCE.convert2PreSignedUrl(file, url, bo);
    }

    /**
     * 使用httpUrl获取文件数据，并上传到OSS
     * @param command
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public JdhFile putHttpFile(PutHttpFileCommand command) {
        PutHttpFileUrlBo bo = FileApplicationConverter.INSTANCE.convert2GeneratePutUrl(command);

        bo.init();
        JdhFile file = JdhFileTaskFactory.createPutFile(bo);
        FileManageServiceImpl.FolderPathEnum filePath = FileManageServiceImpl.FolderPathEnum.parse(command.getFolder());
        jdhFileRepository.save(file);

        byte[] bytes = null;
        try {
            // 创建HttpClient实例
            HttpClient httpClient = HttpClients.createDefault();
            // 创建HttpGet实例，设置请求的URL
            HttpGet httpGet = new HttpGet(command.getHttpFileUrl());
            // 执行GET请求
            org.apache.http.HttpResponse httpResponse = httpClient.execute(httpGet);
            bytes = EntityUtils.toByteArray(httpResponse.getEntity());
            log.info("FileManageServiceImpl->putHttpFile response end");
        }catch (Exception e){
            //文件下载失败
            log.info("putHttpFile,error",e);
            throw new BusinessException(BaseDomainErrorCode.FILE_DOWNLOAD_ERROR);
        }

        ByteArrayInputStream inputStream = new ByteArrayInputStream(bytes);
        fileManageService.put(file.getFileName(), inputStream, filePath, command.getContentType(), Boolean.FALSE);
        return file;
    }

    /**
     * 根据文件ID批量获取访问链接
     * @param command
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.support.service.impl.FileManageApplicationImpl.generateGetUrl")
    public List<FilePreSignedUrlDto> generateGetUrl(GenerateGetUrlCommand command) {
        GenerateGetUrlBo generateGetUrlBo = FileApplicationConverter.INSTANCE.convert2GenerateGetUrl(command);
        generateGetUrlBo.init();
        List<JdhFile> files = jdhFileRepository.findList(generateGetUrlBo.getIdentifierList(), command.getUserPin());
        Map<Long, String> map = fileManageService.generateGetUrl(files, command.getIsPublic(), generateGetUrlBo.getExpireTime());

        List<FilePreSignedUrlDto> res = Lists.newArrayList();
        for (JdhFile file : files) {
            FilePreSignedUrlDto dto = new FilePreSignedUrlDto();
            dto.setFileId(file.getFileId());
            dto.setUrl(map.get(file.getFileId()));
            dto.setExpireTime(generateGetUrlBo.getExpireTime().getTime());
            dto.setHttpMethod(HttpMethod.GET.name());
            res.add(dto);
        }
        return res;
    }

    /**
     * PDF文件追加签名
     * @param command
     * @return
     */
    @Override
    @LogAndAlarm
    public PdfSignatureResult pdfSignature(PdfSignatureCmd command) {

        JdhFile file = jdhFileRepository.find(new JdhFileIdentifier(command.getSignatureImageFileId()));
        JdhFile jdhFile = fileManageService.pdfAddSignature(file, command.getDomainCode(), command.getFileBizType());

        URL url = fileManageService.generateGetUrl(jdhFile, Boolean.TRUE, null);

        PdfSignatureResult result = new PdfSignatureResult();
        result.setUrl(url.toString());
        result.setFileId(jdhFile.getFileId());

        jdhFileRepository.save(jdhFile);
        return result;
    }


    /**
     * PDF文件签名
     * @param command
     * @return
     */
    @Override
    @LogAndAlarm
    public List<PdfSignatureResult> pdfSignatureAndSave(PdfSignatureCmd command) {
        List<PdfSignatureResult> pdfSignatureResults = new Vector<>();
        JSONObject position = new JSONObject();
        JdhFile file = jdhFileRepository.find(new JdhFileIdentifier(command.getSignatureImageFileId()));
        JSONObject obj = JSON.parseObject(duccConfig.getAngelServiceRecordConfig());
        Long addWhiteFileId = obj.getLong("addWhiteFileId");
        JdhFile wFile = jdhFileRepository.find(new JdhFileIdentifier(addWhiteFileId));
        JSONObject positionJson = JSON.parseObject(command.getPositionJson());
        if(command.getSignaturePDFFiles().size() > 2) {
            command.getSignaturePDFFiles().parallelStream().forEach(pdfSignatureDto -> {
                PdfAddSignatureBO pdfAddSignatureBO = JSON.parseObject(JSON.toJSONString(command), PdfAddSignatureBO.class);
                PdfSignatureResult pdfSignatureResult = this.pdfSignature(pdfAddSignatureBO,positionJson,pdfSignatureDto,file,wFile);
                pdfSignatureResults.add(pdfSignatureResult);
            });
        }else{
            command.getSignaturePDFFiles().forEach(pdfSignatureDto -> {
                PdfAddSignatureBO pdfAddSignatureBO = JSON.parseObject(JSON.toJSONString(command), PdfAddSignatureBO.class);
                PdfSignatureResult pdfSignatureResult = this.pdfSignature(pdfAddSignatureBO,positionJson,pdfSignatureDto,file,wFile);
                pdfSignatureResults.add(pdfSignatureResult);
            });
        }
        pdfSignatureResults.forEach(pdfSignatureResult -> {
            position.put(String.valueOf(pdfSignatureResult.getFileId()),JSON.parseObject(pdfSignatureResult.getPositionJson()));
        });
        pdfSignatureResults.forEach(pdfSignatureResult -> pdfSignatureResult.setPositionJson(position.toJSONString()));
        return pdfSignatureResults;
    }

    private PdfSignatureResult pdfSignature(PdfAddSignatureBO pdfAddSignatureBO, JSONObject positionJson, PdfSignatureDto pdfSignatureDto, JdhFile file,JdhFile wFile){
        AtomicReference<Boolean> isSignature = new AtomicReference<>(false);
        PdfSignatureResult result = new PdfSignatureResult();
        result.setPositionJson("{}");
        int fontSize = 12;
        positionJson.forEach((fileId, v) -> {
            if (pdfSignatureDto.getFileId().equals(Long.parseLong(fileId))) {
                JSONObject json = JSON.parseObject(v.toString());
                result.setPositionJson(v.toString());
                if (pdfAddSignatureBO.getSignatureType() != null && pdfAddSignatureBO.getSignatureType().equals(1)) {
                    if (StringUtils.isNotEmpty(json.getString(FileSignaturePositionEnum.NURSE_SIGNATURE.getCode().toString()))) {
                        String[] nursePositionPage = json.getString(FileSignaturePositionEnum.NURSE_SIGNATURE.getCode().toString()).split(":");
                        String[] nursePositionStr = nursePositionPage[1].split(",");
                        Float[] nursePosition = {Float.parseFloat(nursePositionPage[0]), Float.parseFloat(nursePositionStr[0])+fontSize, Float.parseFloat(nursePositionStr[1])+fontSize};
                        pdfAddSignatureBO.setNursePosition(nursePosition);
                        isSignature.set(true);
                    }
                } else {
                    if (pdfAddSignatureBO.getRelation().equals(FileSignatureRelationEnum.SELF.getRelation())) {
                        if (StringUtils.isNotEmpty(json.getString(FileSignaturePositionEnum.SELF_SIGNATURE.getCode().toString()))) {
                            String[] positionPage = json.getString(FileSignaturePositionEnum.SELF_SIGNATURE.getCode().toString()).split(":");
                            String[] positionStr = positionPage[1].split(",");
                            Float[] position = {Float.parseFloat(positionPage[0]), Float.parseFloat(positionStr[0])+fontSize, Float.parseFloat(positionStr[1])+fontSize};
                            pdfAddSignatureBO.setPosition(position);
                            isSignature.set(true);
                        }
                        if (StringUtils.isNotEmpty(json.getString(FileSignaturePositionEnum.AGENT_SIGNATURE.getCode().toString()))) {
                            String[] agentPositionPage = json.getString(FileSignaturePositionEnum.AGENT_SIGNATURE.getCode().toString()).split(":");
                            String[] agentPositionStr = agentPositionPage[1].split(",");
                            Float[] agentPosition = {Float.parseFloat(agentPositionPage[0]), Float.parseFloat(agentPositionStr[0]) + fontSize, Float.parseFloat(agentPositionStr[1]) + fontSize};
                            pdfAddSignatureBO.setAgentPosition(agentPosition);
                        }
                    }else {
                        if (StringUtils.isNotEmpty(json.getString(FileSignaturePositionEnum.AGENT_SIGNATURE.getCode().toString()))) {
                            String[] agentPositionPage = json.getString(FileSignaturePositionEnum.AGENT_SIGNATURE.getCode().toString()).split(":");
                            String[] agentPositionStr = agentPositionPage[1].split(",");
                            Float[] agentPosition = {Float.parseFloat(agentPositionPage[0]), Float.parseFloat(agentPositionStr[0]) + fontSize, Float.parseFloat(agentPositionStr[1]) + fontSize};
                            pdfAddSignatureBO.setAgentPosition(agentPosition);
                            isSignature.set(true);
                        }
                        if (StringUtils.isNotEmpty(json.getString(FileSignaturePositionEnum.SELF_SIGNATURE.getCode().toString()))) {
                            String[] positionPage = json.getString(FileSignaturePositionEnum.SELF_SIGNATURE.getCode().toString()).split(":");
                            String[] positionStr = positionPage[1].split(",");
                            Float[] position = {Float.parseFloat(positionPage[0]), Float.parseFloat(positionStr[0])+fontSize, Float.parseFloat(positionStr[1])+fontSize};
                            pdfAddSignatureBO.setPosition(position);
                        }
                    }
                    if (StringUtils.isNotEmpty(json.getString(FileSignaturePositionEnum.SIGNATURE_TIME.getCode().toString()))) {
                        String[] timePositionPage = json.getString(FileSignaturePositionEnum.SIGNATURE_TIME.getCode().toString()).split(":");
                        String[] timePositionStr = timePositionPage[1].split(",");
                        Float[] timePosition = {Float.parseFloat(timePositionPage[0]), Float.parseFloat(timePositionStr[0])+fontSize, Float.parseFloat(timePositionStr[1])};
                        pdfAddSignatureBO.setTimePosition(timePosition);
                    }
                    if (StringUtils.isNotEmpty(json.getString(FileSignaturePositionEnum.RELATION.getCode().toString()))) {
                        String[] relationPositionPage = json.getString(FileSignaturePositionEnum.RELATION.getCode().toString()).split(":");
                        String[] relationPositionStr = relationPositionPage[1].split(",");
                        Float[] relationPosition = {Float.parseFloat(relationPositionPage[0]), Float.parseFloat(relationPositionStr[0])+fontSize, Float.parseFloat(relationPositionStr[1])};
                        pdfAddSignatureBO.setRelationPosition(relationPosition);
                    }
                }
            }
        });
        JdhFile pdfFile = jdhFileRepository.find(new JdhFileIdentifier(pdfSignatureDto.getFileId()));
        if(isSignature.get()){
            JdhFile jdhFile = fileManageService.pdfSignature(file,wFile, pdfFile, pdfAddSignatureBO);
            URL url = fileManageService.generateGetUrl(jdhFile, Boolean.TRUE, null);
            result.setUrl(url.toString());
            result.setFileId(jdhFile.getFileId());
            result.setFileName(pdfSignatureDto.getFileName());
            jdhFileRepository.save(jdhFile);
        }else{
            URL url = fileManageService.generateGetUrl(pdfFile, Boolean.TRUE, null);
            result.setUrl(url.toString());
            result.setFileId(pdfFile.getFileId());
            result.setFileName(pdfSignatureDto.getFileName());
        }
        return result;
    }

    /**
     * 上传文件,工具类接口,实际上传使用generatePutUrl生成预签名上传链接后使用生成的上传链接进行上传
     *
     * @param originFileName fileName
     * @param fileInputStream fileInputStream
     * @return dto
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.support.service.impl.FileManageApplicationImpl.upload")
    public FilePreSignedUrlDto upload(String originFileName, InputStream fileInputStream, LocalDateTime fileSaveExpireTime, GeneratePutUrlCommand data) {
        log.info("FileManageApplicationImpl#upload start originFileName={}", originFileName);
        if (fileInputStream == null || StringUtils.isBlank(originFileName)) {
            throw new SystemException(SystemErrorCode.PARAM_NULL_ERROR.formatDescription("上传文件为空"));
        }
        if (data == null || StringUtils.isBlank(data.getDomainCode()) || StringUtils.isBlank(data.getFileBizType())) {
            throw new SystemException(SystemErrorCode.PARAM_NULL_ERROR.formatDescription("领域编码、文件业务标识不允许为空"));
        }
        int fileNameLastIndex = originFileName.lastIndexOf(".");
        if (fileNameLastIndex < 1) {
            throw new SystemException(SystemErrorCode.PARAM_NULL_ERROR.formatDescription("原始文件须包含文件名称与文件后缀"));
        }
        String fileSuffix = originFileName.substring(fileNameLastIndex);
        data.setSuffix(fileSuffix);
        GeneratePutUrlBo bo = FileApplicationConverter.INSTANCE.convert2GeneratePutUrl(data);
        JdhFile jdhFile = JdhFileTaskFactory.createPutFile(bo);
        jdhFile.setExpireTime(fileSaveExpireTime);
        log.info("FileManageApplicationImpl#upload jdhFile={}", JSON.toJSONString(jdhFile));
        fileManageService.put(jdhFile.getFileName(), fileInputStream, FileManageServiceImpl.FolderPathEnum.FILE_MANAGE, null,Boolean.FALSE);
        int count = jdhFileRepository.save(jdhFile);
        if (count < 1) {
            log.error("FileManageApplicationImpl#upload 数据保存失败");
            throw new SystemException(SystemErrorCode.SYSTEM_ERROR);
        }
        FilePreSignedUrlDto filePreSignedUrlDto = new FilePreSignedUrlDto();
        filePreSignedUrlDto.setFileId(jdhFile.getFileId());

        LocalDateTime expireTime = LocalDateTime.now().plusHours(1);
        Date expire = TimeUtils.localDateTimeToDate(expireTime);
        URL url = fileManageService.generateGetUrl(jdhFile, true, expire);
        filePreSignedUrlDto.setUrl(url.toString());
        return filePreSignedUrlDto;
    }

    /**
     * 获取文件访问链接
     *
     * @param request
     * @return
     */
    @Override
    public List<FileUrlDto> getMultiFileUrl(GetFileUrlRequest request) {
        if (request == null || CollUtil.isEmpty(request.getFileIdList())) {
            return Collections.emptyList();
        }
        List<JdhFileIdentifier> list = new ArrayList<>();
        request.getFileIdList().forEach(s -> {
            list.add(JdhFileIdentifier.builder().fileId(s).build());
        });
        List<JdhFile> jdhFileList = jdhFileRepository.findList(list, null);
        if (CollUtil.isEmpty(jdhFileList)) {
            return Collections.emptyList();
        }
        List<CompletableFuture<FileUrlDto>> futures = jdhFileList.stream()
                .map(jdhFile -> CompletableFuture.supplyAsync(
                        () -> queryFileUrl(jdhFile),
                        Executors.newFixedThreadPool(20)
                )).collect(Collectors.toList());
        return futures.stream()
                .map(CompletableFuture::join)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     *
     * @param jdhFile
     * @return
     */
    private FileUrlDto queryFileUrl(JdhFile jdhFile) {
        FileUrlDto fuDto = new FileUrlDto();
        try {
            fuDto.setFileId(jdhFile.getFileId());
            fuDto.setFilePath(jdhFile.getFilePath());
            URL url = fileManageService.generateGetUrl(jdhFile, Boolean.TRUE, null);
            fuDto.setUrl(url.toString());
        } catch (Exception e) {
            log.error("FileManageApplicationImpl#queryMulti 文件地址获取失败", e);
        }
        return fuDto;
    }
}
