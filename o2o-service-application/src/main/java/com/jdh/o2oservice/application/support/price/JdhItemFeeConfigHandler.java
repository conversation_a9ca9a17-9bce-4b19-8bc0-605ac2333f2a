package com.jdh.o2oservice.application.support.price;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.TypeReference;
import com.google.common.collect.Lists;
import com.jdh.o2oservice.application.product.service.ProductServiceItemApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.core.domain.support.basic.enums.PricingServiceFactObjectEnum;
import com.jdh.o2oservice.core.domain.support.price.context.PricingServiceCalculateContext;
import com.jdh.o2oservice.core.domain.support.price.handler.PricingServiceFactObjectHandler;
import com.jdh.o2oservice.export.medicalpromise.dto.MedicalPromiseDTO;
import com.jdh.o2oservice.export.product.dto.JdhSkuDto;
import com.jdh.o2oservice.export.product.dto.ServiceItemDto;
import com.jdh.o2oservice.export.product.query.ServiceItemQuery;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName JdhItemFeeConfigHandler
 * @Description
 * <AUTHOR>
 * @Date 2025/5/3 18:59
 **/
@Component
@Slf4j
public class JdhItemFeeConfigHandler extends AbstractFactObjectHandler {

    /**
     * sku数据
     */
    //@Resource
    //private JdhSkuFeeConfigHandler jdhSkuFeeConfigHandler;

    /**
     * 项目数据
     */
    @Resource
    private ProductServiceItemApplication productServiceItemApplication;

    /**
     * 检测单数据
     */
    @Resource
    private JdhMedicalPromiseFactObjectHandler medicalPromiseFactObjectHandler;

    /**
     * 获取事实对象
     * @param context
     * @return
     */
    @Override
    public Object getData(PricingServiceCalculateContext context) {
        boolean containsKey = context.getFactObjectMap().containsKey(getMapKey());
        if (containsKey) {
            return context.getFactObjectMap().get(getMapKey());
        }
        /*//前置依赖履约单数据，先从上下文中获取，没有则调用接口获取，并放入上下文
        Object factObject = jdhSkuFeeConfigHandler.getFactObject(context);
        if (Objects.isNull(factObject)) {
            context.getFactObjectMap().put(getMapKey(), null);
            return null;
        }
        List<JdhSkuDto> jdhSkuDtos = Convert.convert(new TypeReference<List<JdhSkuDto>>(){}, factObject);

        //获取检测项目列表
        List<ServiceItemDto> serviceItemDtoList = jdhSkuDtos.stream().filter(jdhSkuDto -> CollectionUtils.isNotEmpty(jdhSkuDto.getServiceItemList())).flatMap(jdhSkuDto -> jdhSkuDto.getServiceItemList().stream()).collect(Collectors.toList());
        Set<Long> serviceItemIdSet = serviceItemDtoList.stream().map(ServiceItemDto::getItemId).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(serviceItemIdSet)) {
            context.getFactObjectMap().put(getMapKey(), Lists.newArrayList());
            return null;
        }
        List<ServiceItemDto> serviceItemDtos = productServiceItemApplication.queryServiceItemList(ServiceItemQuery.builder().itemIds(serviceItemIdSet).queryPriceFeeConfig(true).build());
        context.getFactObjectMap().put(getMapKey(), serviceItemDtos);*/

        //前置依赖履约单数据，先从上下文中获取，没有则调用接口获取，并放入上下文
        Object factObject = medicalPromiseFactObjectHandler.getFactObject(context);
        if (Objects.isNull(factObject)) {
            return null;
        }
        List<MedicalPromiseDTO> medicalPromiseDTOList = Convert.convert(new TypeReference<List<MedicalPromiseDTO>>() {}, factObject);
        //获取item列表
        Set<Long> itemIdSet = medicalPromiseDTOList.stream().map(MedicalPromiseDTO::getServiceItemId).map(Long::valueOf).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(itemIdSet)) {
            context.getFactObjectMap().put(getMapKey(), Lists.newArrayList());
            return null;
        }
        List<ServiceItemDto> serviceItemDtos = productServiceItemApplication.queryServiceItemList(ServiceItemQuery.builder().itemIds(itemIdSet).queryPriceFeeConfig(true).build());
        if (CollectionUtils.isEmpty(serviceItemDtos)) {
            context.getFactObjectMap().put(getMapKey(), null);
            return null;
        }
        Map<Long, ServiceItemDto> id2ItemMap = serviceItemDtos.stream().collect(Collectors.toMap(ServiceItemDto::getItemId, item -> item, (a, b) -> a));
        context.getFactObjectMap().put(getMapKey(), id2ItemMap);
        return id2ItemMap;
    }

    /**
     * 获取map key
     * @return
     */
    @Override
    public String getMapKey() {
        return PricingServiceFactObjectEnum.ITEM_FEE_CONFIG.getCode();
    }
}