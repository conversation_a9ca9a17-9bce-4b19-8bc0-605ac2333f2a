package com.jdh.o2oservice.application.angelpromise.ability;
import com.jdh.o2oservice.core.domain.angelpromise.context.AngelTaskExtStatusContext;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelTask;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelWork;

import java.util.List;

public interface AngelWorkFinishServiceAbility {

    AngelTaskExtStatusContext execute(AngelWork angelWork, List<AngelTask> angelTaskList);

}
