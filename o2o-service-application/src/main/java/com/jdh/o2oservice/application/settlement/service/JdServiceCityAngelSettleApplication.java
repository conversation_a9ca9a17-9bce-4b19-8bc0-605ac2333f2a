package com.jdh.o2oservice.application.settlement.service;

import com.jdh.o2oservice.base.ducc.model.fee.CityLevelSettlementCoefficientConfig;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.core.domain.settlement.repository.query.CityAngelSettlementPageQuery;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.BaseAddressBo;
import com.jdh.o2oservice.export.settlement.cmd.AngelSettleCityConfigFileCmd;
import com.jdh.o2oservice.export.settlement.cmd.CityAngelSettlementConfigCmd;
import com.jdh.o2oservice.export.settlement.dto.CityAngelSettlementConfigDto;

/**
 * JdServiceCityAngelSettleApplication
 * 城市级别护士结算配置
 * <AUTHOR>
 * @version 2025/4/21 12:15
 **/
public interface JdServiceCityAngelSettleApplication {

    /**
     * 分页护士结算城市配置
     * @param cityAngelSettlementPageQuery
     * @return
     */
    PageDto<CityAngelSettlementConfigDto> queryCityAngelSettlementPage(CityAngelSettlementPageQuery cityAngelSettlementPageQuery);

    /**
     * 保存城市级别护士结算配置
     * @param cityAngelSettlementConfigCmd
     * @return
     */
    boolean saveCityAngelSettlement(CityAngelSettlementConfigCmd cityAngelSettlementConfigCmd);

    /**
     * 更新城市级别护士结算配置
     * @param cityAngelSettlementConfigCmd
     * @return
     */
    boolean updateCityAngelSettlement(CityAngelSettlementConfigCmd cityAngelSettlementConfigCmd);

    /**
     *
     * @param angelSettleCityConfigFileCmd
     * @return
     */
    boolean batchSubmitCityConfig(AngelSettleCityConfigFileCmd angelSettleCityConfigFileCmd);
    /**
     *
     * @param cityName
     * @return
     */
    BaseAddressBo getProvinceName(String cityName);

    /**
     *
     * @param cityAngelSettlementPageQuery
     * @return
     */
    Boolean exportCityLevelConfig(CityAngelSettlementPageQuery cityAngelSettlementPageQuery);

    /**
     *
     * @param cityLevel
     * @return
     */
    CityLevelSettlementCoefficientConfig getCityLevelSettlementCoefficientConfig(String cityLevel);
}
