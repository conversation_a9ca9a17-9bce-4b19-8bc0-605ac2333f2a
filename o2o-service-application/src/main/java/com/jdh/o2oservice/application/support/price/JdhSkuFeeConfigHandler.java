package com.jdh.o2oservice.application.support.price;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.TypeReference;
import com.google.common.collect.Lists;
import com.jdh.o2oservice.application.product.service.ProductApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.core.domain.support.basic.enums.PricingServiceFactObjectEnum;
import com.jdh.o2oservice.core.domain.support.price.context.PricingServiceCalculateContext;
import com.jdh.o2oservice.core.domain.support.price.handler.PricingServiceFactObjectHandler;
import com.jdh.o2oservice.export.medicalpromise.dto.MedicalPromiseDTO;
import com.jdh.o2oservice.export.product.dto.JdhSkuDto;
import com.jdh.o2oservice.export.product.query.JdhSkuListRequest;
import com.jdh.o2oservice.export.promise.dto.PromiseDto;
import com.jdh.o2oservice.export.promise.dto.PromiseServiceDetailDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName JdhSkuFeeConfigHandler
 * @Description
 * <AUTHOR>
 * @Date 2025/5/3 18:59
 **/
@Component
@Slf4j
public class JdhSkuFeeConfigHandler extends AbstractFactObjectHandler {

    /**
     * 检测单数据
     */
    @Resource
    private JdhMedicalPromiseFactObjectHandler medicalPromiseFactObjectHandler;

    /**
     *
     */
    @Resource
    private ProductApplication productApplication;

    /**
     * 获取事实对象
     * @param context
     * @return
     */
    @Override
    public Object getData(PricingServiceCalculateContext context) {
        boolean containsKey = context.getFactObjectMap().containsKey(getMapKey());
        if (containsKey) {
            return context.getFactObjectMap().get(getMapKey());
        }
        /*//前置依赖履约单数据，先从上下文中获取，没有则调用接口获取，并放入上下文
        Object factObject = promiseFactObjectHandler.getFactObject(context);
        if (Objects.isNull(factObject)) {
            return null;
        }
        PromiseDto promiseDto = Convert.convert(PromiseDto.class, factObject);
        //获取购买的sku列表
        List<PromiseServiceDetailDto> services = promiseDto.getServices();
        if (CollectionUtils.isEmpty(services)) {
            context.getFactObjectMap().put(getMapKey(), Lists.newArrayList());
            return null;
        }
        //获取sku列表
        Set<Long> skuIdSet = services.stream().map(PromiseServiceDetailDto::getServiceId).collect(Collectors.toSet());*/

        //前置依赖履约单数据，先从上下文中获取，没有则调用接口获取，并放入上下文
        Object factObject = medicalPromiseFactObjectHandler.getFactObject(context);
        if (Objects.isNull(factObject)) {
            return null;
        }
        List<MedicalPromiseDTO> medicalPromiseDTOList = Convert.convert(new TypeReference<List<MedicalPromiseDTO>>() {}, factObject);

        //获取sku列表
        Set<Long> skuIdSet = medicalPromiseDTOList.stream().map(MedicalPromiseDTO::getServiceId).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(skuIdSet)) {
            context.getFactObjectMap().put(getMapKey(), Lists.newArrayList());
            return null;
        }
        //Map<Long, JdhSkuDto> jdhSkuDtoMap = productApplication.queryJdhSkuInfoList(JdhSkuListRequest.builder().skuIdList(skuIdSet).queryServiceItem(true).queryPriceFeeConfig(true).build());
        Map<Long, JdhSkuDto> jdhSkuDtoMap = productApplication.queryJdhSkuInfoList(JdhSkuListRequest.builder().skuIdList(skuIdSet).queryPriceFeeConfig(true).build());
        if (MapUtils.isEmpty(jdhSkuDtoMap)) {
            context.getFactObjectMap().put(getMapKey(), null);
            return null;
        }
        //List<JdhSkuDto> jdhSkuDtos = Lists.newArrayList(jdhSkuDtoMap.values());
        context.getFactObjectMap().put(getMapKey(), jdhSkuDtoMap);
        return jdhSkuDtoMap;
    }

    /**
     * 获取map key
     * @return
     */
    @Override
    public String getMapKey() {
        return PricingServiceFactObjectEnum.SKU_FEE_CONFIG.getCode();
    }
}