package com.jdh.o2oservice.application.support.price;

import cn.hutool.core.convert.Convert;
import com.jdh.o2oservice.application.settlement.service.JdhAngelSettleConfigApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.core.domain.angel.enums.JobNatureEnum;
import com.jdh.o2oservice.core.domain.settlement.repository.query.JdhAngelSettleAreaFeeQuery;
import com.jdh.o2oservice.core.domain.support.basic.enums.PricingServiceFactObjectEnum;
import com.jdh.o2oservice.core.domain.support.price.context.PricingServiceCalculateContext;
import com.jdh.o2oservice.core.domain.support.price.enums.PricingServiceErrorCode;
import com.jdh.o2oservice.core.domain.support.price.handler.PricingServiceFactObjectHandler;
import com.jdh.o2oservice.export.angel.dto.JdhAngelDto;
import com.jdh.o2oservice.export.settlement.dto.AngelSettlementConfigDto;
import com.jdh.o2oservice.export.trade.dto.AddressInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @ClassName JdhAreaFeeConfigHandler
 * @Description
 * <AUTHOR>
 * @Date 2025/5/2 10:44
 **/
@Component
@Slf4j
public class JdhAreaFeeConfigHandler extends AbstractFactObjectHandler {

    /**
     * 护士结算配置
     */
    @Resource
    private JdhAngelSettleConfigApplication settleConfigApplication;

    /**
     * 地址数据
     */
    @Resource
    private JdhAddressFactObjectHandler addressFactObjectHandler;

    /**
     * 护士数据
     */
    @Resource
    private JdhAngelFactObjectHandler jdhAngelFactObjectHandler;

    /**
     * 获取事实对象
     * @param context
     * @return
     */
    @Override
    public Object getData(PricingServiceCalculateContext context) {
        boolean containsKey = context.getFactObjectMap().containsKey(getMapKey());
        if (containsKey) {
            return context.getFactObjectMap().get(getMapKey());
        }
        //前置依赖履约单数据，先从上下文中获取，没有则调用接口获取，并放入上下文
        Object factObject = addressFactObjectHandler.getFactObject(context);
        if (Objects.isNull(factObject)) {
            return null;
        }
        AddressInfoDTO addressInfoDTO = Convert.convert(AddressInfoDTO.class, factObject);

        //前置依赖护士数据
        Object angel = jdhAngelFactObjectHandler.getFactObject(context);
        if (Objects.isNull(angel)) {
            return null;
        }
        JdhAngelDto jdhAngelDto = Convert.convert(JdhAngelDto.class, angel);
        if (Objects.isNull(jdhAngelDto.getJobNature())) {
            throw new BusinessException(PricingServiceErrorCode.ANGEL_NATURE_NOT_EXIST);
        }

        //按区域查询时段费
        //使用流和Optional处理ID，构建非空的destCodeList
        List<String> destCodeList = Stream.of(
                        Optional.ofNullable(addressInfoDTO.getProvinceId()).map(Object::toString),
                        Optional.ofNullable(addressInfoDTO.getCityId()).map(Object::toString),
                        Optional.ofNullable(addressInfoDTO.getCountyId()).map(Object::toString),
                        Optional.ofNullable(addressInfoDTO.getTownId()).map(Object::toString)
                ).filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());
        PageDto<AngelSettlementConfigDto> configDtoPageDto = settleConfigApplication.queryAngelSettleConfigPage(
                JdhAngelSettleAreaFeeQuery.builder().destCodeList(destCodeList).angelType(jdhAngelDto.getJobNature().toString()).build());

        //定义配置对象
        AngelSettlementConfigDto angelSettleAreaConfig = null;

        if (Objects.isNull(configDtoPageDto) || CollectionUtils.isEmpty(configDtoPageDto.getList())) {
            context.getFactObjectMap().put(getMapKey(), null);
            return null;
        }
        Map<String, AngelSettlementConfigDto> dtoMap = configDtoPageDto.getList().stream().collect(Collectors.toMap(AngelSettlementConfigDto::getDestCode, dto -> dto, (t, t2) -> t2));


        // 依次查找街道、区、市、省的配置
        List<Optional<String>> addressIds = Stream.of(
                Optional.ofNullable(addressInfoDTO.getTownId()).map(Object::toString),
                Optional.ofNullable(addressInfoDTO.getCountyId()).map(Object::toString),
                Optional.ofNullable(addressInfoDTO.getCityId()).map(Object::toString),
                Optional.ofNullable(addressInfoDTO.getProvinceId()).map(Object::toString)
        ).collect(Collectors.toList());
        for (Optional<String> idOptional : addressIds) {
            if (idOptional.isPresent()) {
                angelSettleAreaConfig = dtoMap.get(idOptional.get());
                if (Objects.nonNull(angelSettleAreaConfig)) {
                    context.getFactObjectMap().put(getMapKey(), angelSettleAreaConfig);
                    return angelSettleAreaConfig;
                }
            }
        }
        //都没有，直接返回空
        context.getFactObjectMap().put(getMapKey(), angelSettleAreaConfig);
        return angelSettleAreaConfig;

        /*angelSettleAreaConfig = new AngelSettlementConfigDto();
        angelSettleAreaConfig.setFeeConfigId(1001L);
        angelSettleAreaConfig.setProvinceCode("1");
        angelSettleAreaConfig.setCityCode("72");
        angelSettleAreaConfig.setDestCode("72");
        angelSettleAreaConfig.setAngelType(String.valueOf(JobNatureEnum.FULL_TIME.getValue()));
        angelSettleAreaConfig.setOnSiteFee("80");
        angelSettleAreaConfig.setImmediatelyFee("50");
        angelSettleAreaConfig.setHolidayFee("50");
        angelSettleAreaConfig.setNightDoorFee("100");

        context.getFactObjectMap().put(getMapKey(), angelSettleAreaConfig);
        return angelSettleAreaConfig;*/
    }

    /**
     * 获取map key
     * @return
     */
    @Override
    public String getMapKey() {
        return PricingServiceFactObjectEnum.AREA_FEE_CONFIG.getCode();
    }
}