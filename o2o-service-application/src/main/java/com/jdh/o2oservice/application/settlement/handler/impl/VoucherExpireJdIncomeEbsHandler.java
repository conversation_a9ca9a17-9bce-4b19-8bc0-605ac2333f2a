package com.jdh.o2oservice.application.settlement.handler.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.jdh.o2oservice.application.settlement.context.SettlementEbsContext;
import com.jdh.o2oservice.application.settlement.handler.SettlementEbsHandler;
import com.jdh.o2oservice.application.settlement.handler.SettlementEbsHandlerManager;
import com.jdh.o2oservice.application.settlement.handler.SettlementEbsUtil;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.errorcode.SystemErrorCode;
import com.jdh.o2oservice.base.util.TimeUtils;
import com.jdh.o2oservice.common.enums.BusinessModeEnum;
import com.jdh.o2oservice.common.enums.EbsSettleMainBodyTypeEnum;
import com.jdh.o2oservice.common.enums.EbsSettleSplitTypeEnum;
import com.jdh.o2oservice.common.enums.EbsSettleTypeEnum;
import com.jdh.o2oservice.core.domain.settlement.bo.JdOrderDetailBo;
import com.jdh.o2oservice.core.domain.settlement.model.AngelSettlementAndEbsDetail;
import com.jdh.o2oservice.core.domain.settlement.model.JdhSettlementEbs;
import com.jdh.o2oservice.export.promise.cmd.VoucherExpireSettleCmd;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @ClassName VoucherExpireJdIncomeEbsHandler
 * @Description
 * <AUTHOR>
 * @Date 2024/9/5 22:07
 */
@Slf4j
@Component
public class VoucherExpireJdIncomeEbsHandler implements SettlementEbsHandler {
    /**
     * 结算前置计算构建对象
     *
     * @param settlementEbsContext context
     * @return ebs
     */
    @Override
    public JdhSettlementEbs prepareEbs(SettlementEbsContext settlementEbsContext) {
        log.info("VoucherExpireJdIncomeEbsHandler -> prepareEbs settlementEbsContext={}", JSON.toJSONString(settlementEbsContext));
        if (settlementEbsContext == null || settlementEbsContext.getExtBusinessModel() == null) {
            throw new BusinessException(SystemErrorCode.PARAM_NULL_ERROR.formatDescription("extBusinessModel为空"));
        }
        VoucherExpireSettleCmd settleCmd = (VoucherExpireSettleCmd) settlementEbsContext.getExtBusinessModel();

        String preId = SettlementEbsUtil.appendSplitMultiKey(CommonConstant.CHARACTER_UNDERLINE, settleCmd.getVoucherId(), TimeUtils.getCurrentDateTime());
        JdhSettlementEbs jdhSettlementEbs = new JdhSettlementEbs();
        jdhSettlementEbs.setPreId(preId);
        jdhSettlementEbs.setPreDocNum(settleCmd.getSourceVoucherId());
        jdhSettlementEbs.setDocCreateTime(settleCmd.getPayTime());
        jdhSettlementEbs.setAppliedDate(settleCmd.getExpireTime());
        jdhSettlementEbs.setTotalAmount(settleCmd.getSettlePrice());
        jdhSettlementEbs.setSkuId(settleCmd.getSkuId());
        jdhSettlementEbs.setOrderId(Long.valueOf(settleCmd.getSourceVoucherId()));
        return jdhSettlementEbs;
    }

    /**
     * 结算前置计算构建对象
     *
     * @param settlementEbsContext context
     */
    @Override
    public void afterEbsSend(SettlementEbsContext settlementEbsContext) {
        log.info("VoucherExpireJdIncomeEbsHandler -> afterEbsSend settlementEbsContext={}", JSON.toJSONString(settlementEbsContext));
    }

    /**
     * 结算前置计算构建对象
     *
     * @return ebs
     */
    @Override
    public List<String> settlementSceneCode() {
        String angelTest = SettlementEbsUtil.getSettlementSceneCode(BusinessModeEnum.ANGEL_TEST, EbsSettleTypeEnum.INCOME, EbsSettleSplitTypeEnum.SKU_FEE, EbsSettleMainBodyTypeEnum.VOUCHER);
        String angelCare = SettlementEbsUtil.getSettlementSceneCode(BusinessModeEnum.ANGEL_CARE, EbsSettleTypeEnum.INCOME, EbsSettleSplitTypeEnum.SKU_FEE, EbsSettleMainBodyTypeEnum.VOUCHER);
        String angelTestNoLaboratory = SettlementEbsUtil.getSettlementSceneCode(BusinessModeEnum.ANGEL_TEST_NO_LABORATORY, EbsSettleTypeEnum.INCOME, EbsSettleSplitTypeEnum.SKU_FEE, EbsSettleMainBodyTypeEnum.VOUCHER);
        String selfTest = SettlementEbsUtil.getSettlementSceneCode(BusinessModeEnum.SELF_TEST, EbsSettleTypeEnum.INCOME, EbsSettleSplitTypeEnum.SKU_FEE, EbsSettleMainBodyTypeEnum.VOUCHER);
        return Stream.of(angelTest, angelCare, selfTest, angelTestNoLaboratory).collect(Collectors.toList());
    }

    /**
     */
    @Override
    public void afterPropertiesSet() {
        if (CollUtil.isEmpty(this.settlementSceneCode())) {
            return;
        }
        for (String sceneCode : this.settlementSceneCode()) {
            SettlementEbsHandlerManager.register(sceneCode, this);
        }
    }
}
