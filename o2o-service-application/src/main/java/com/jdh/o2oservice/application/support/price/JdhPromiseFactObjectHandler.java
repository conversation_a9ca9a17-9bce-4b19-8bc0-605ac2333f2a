package com.jdh.o2oservice.application.support.price;

import com.jdh.o2oservice.application.promise.service.PromiseApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.core.domain.support.basic.enums.PricingServiceFactObjectEnum;
import com.jdh.o2oservice.core.domain.support.price.context.PricingServiceCalculateContext;
import com.jdh.o2oservice.core.domain.support.price.enums.PricingServiceErrorCode;
import com.jdh.o2oservice.core.domain.support.price.handler.PricingServiceFactObjectHandler;
import com.jdh.o2oservice.export.promise.dto.PromiseDto;
import com.jdh.o2oservice.export.promise.query.PromiseIdRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @ClassName JdhPromiseFactObjectHandler
 * @Description
 * <AUTHOR>
 * @Date 2025/5/5 13:50
 **/
@Component
@Slf4j
public class JdhPromiseFactObjectHandler extends AbstractFactObjectHandler {

    /**
     * 护士服务
     */
    @Resource
    private PromiseApplication promiseApplication;

    /**
     * 获取事实对象
     * @param context
     * @return
     */
    @Override
    public Object getData(PricingServiceCalculateContext context) {
        boolean containsKey = context.getFactObjectMap().containsKey(getMapKey());
        if (containsKey) {
            return context.getFactObjectMap().get(getMapKey());
        }
        PromiseDto promiseDto = promiseApplication.findPromiseByPromiseId(PromiseIdRequest.builder().promiseId(context.getPromiseId()).build());
        if (Objects.isNull(promiseDto)) {
            throw new BusinessException(PricingServiceErrorCode.USER_PROMISE_NOT_EXIST);
        }
        if (Objects.isNull(promiseDto.getAppointmentTime())) {
            throw new BusinessException(PricingServiceErrorCode.USER_APPOINTMENT_TIME_NOT_EXIST);
        }
        context.getFactObjectMap().put(getMapKey(), promiseDto);
        return promiseDto;
    }

    /**
     * 获取map key
     * @return
     */
    @Override
    public String getMapKey() {
        return PricingServiceFactObjectEnum.PROMISE_INFO.getCode();
    }
}