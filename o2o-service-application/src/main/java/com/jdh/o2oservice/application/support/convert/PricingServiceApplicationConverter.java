package com.jdh.o2oservice.application.support.convert;

import com.jdh.o2oservice.core.domain.support.price.context.PricingServiceCalculateContext;
import com.jdh.o2oservice.export.support.command.PricingServiceCalculateCmd;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * @ClassName PricingServiceApplicationConverter
 * @Description
 * <AUTHOR>
 * @Date 2025/4/29 16:52
 **/
@Mapper
public interface PricingServiceApplicationConverter {

    PricingServiceApplicationConverter INSTANCE = Mappers.getMapper(PricingServiceApplicationConverter.class);

    /**
     *
     * @param cmd
     * @return
     */
    @Mapping(target = "feeAmountMap", expression = "java(new java.util.HashMap<>())")
    PricingServiceCalculateContext cmd2CalculateContext(PricingServiceCalculateCmd cmd);
}