package com.jdh.o2oservice.application.support.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.jd.common.web.LoginContext;
import com.jdh.o2oservice.application.angelpromise.service.AngelWorkApplication;
import com.jdh.o2oservice.application.dispatch.service.DispatchApplication;
import com.jdh.o2oservice.application.medicalpromise.MedicalPromiseApplication;
import com.jdh.o2oservice.application.medicalpromise.service.MedPromiseHistoryApplication;
import com.jdh.o2oservice.application.product.service.ProductApplication;
import com.jdh.o2oservice.application.support.service.OpsApplication;
import com.jdh.o2oservice.application.support.service.ReachApplication;
import com.jdh.o2oservice.application.support.service.SupportDevApplication;
import com.jdh.o2oservice.base.constatnt.NumConstant;
import com.jdh.o2oservice.base.enums.ThreadPoolConfigEnum;
import com.jdh.o2oservice.base.enums.YnStatusEnum;
import com.jdh.o2oservice.base.event.Event;
import com.jdh.o2oservice.base.event.EventConsumerTask;
import com.jdh.o2oservice.base.event.extension.ConsumerTaskRepository;
import com.jdh.o2oservice.base.event.extension.EventRepository;
import com.jdh.o2oservice.base.event.query.EventPageQuery;
import com.jdh.o2oservice.base.factory.ExecutorPoolFactory;
import com.jdh.o2oservice.base.model.DomainEnum;
import com.jdh.o2oservice.base.util.SpringUtil;
import com.jdh.o2oservice.base.util.TimeFormat;
import com.jdh.o2oservice.base.util.TimeUtils;
import com.jdh.o2oservice.common.enums.BusinessModeEnum;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.core.domain.angel.enums.AngelAggregateEnum;
import com.jdh.o2oservice.core.domain.angelpromise.enums.*;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelShip;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelShipHistory;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelTask;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelShipHistoryRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelShipRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelTaskRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.query.AngelShipDBQuery;
import com.jdh.o2oservice.core.domain.angelpromise.repository.query.AngelShipHistoryDBQuery;
import com.jdh.o2oservice.core.domain.angelpromise.repository.query.AngelTaskDBQuery;
import com.jdh.o2oservice.core.domain.dispatch.enums.DispatchAggregateEnum;
import com.jdh.o2oservice.core.domain.dispatch.enums.DispatchEventTypeEnum;
import com.jdh.o2oservice.core.domain.dispatch.model.JdhDispatch;
import com.jdh.o2oservice.core.domain.dispatch.repository.db.DispatchRepository;
import com.jdh.o2oservice.core.domain.dispatch.repository.query.DispatchRepQuery;
import com.jdh.o2oservice.core.domain.medpromise.enums.MedPromiseAggregateEnum;
import com.jdh.o2oservice.core.domain.medpromise.enums.MedPromiseEventTypeEnum;
import com.jdh.o2oservice.core.domain.product.enums.ProductAggregateCodeEnum;
import com.jdh.o2oservice.core.domain.promise.enums.PromiseAggregateEnum;
import com.jdh.o2oservice.core.domain.promise.enums.PromiseEventTypeEnum;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromise;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromiseHistory;
import com.jdh.o2oservice.core.domain.promise.model.JdhVoucher;
import com.jdh.o2oservice.core.domain.promise.model.JdhVoucherIdentifier;
import com.jdh.o2oservice.core.domain.promise.repository.db.PromiseHistoryRepository;
import com.jdh.o2oservice.core.domain.promise.repository.db.PromiseRepository;
import com.jdh.o2oservice.core.domain.promise.repository.db.VoucherRepository;
import com.jdh.o2oservice.core.domain.promise.repository.query.PromiseHistoryRepQuery;
import com.jdh.o2oservice.core.domain.promise.repository.query.PromiseRepQuery;
import com.jdh.o2oservice.core.domain.promise.repository.query.VoucherRepQuery;
import com.jdh.o2oservice.core.domain.provider.enums.ProviderAggregateEnum;
import com.jdh.o2oservice.core.domain.settlement.enums.SettleAggregateEnum;
import com.jdh.o2oservice.core.domain.settlement.enums.SettleEventTypeEnum;
import com.jdh.o2oservice.core.domain.support.basic.enums.SupportAggregateEnum;
import com.jdh.o2oservice.core.domain.support.basic.rpc.SkuInfoRpc;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.RpcSkuBO;
import com.jdh.o2oservice.core.domain.support.file.service.FileManageService;
import com.jdh.o2oservice.core.domain.support.reach.ext.ReachServiceSelectDataExt;
import com.jdh.o2oservice.core.domain.support.reach.ext.ReachServiceSelectUserExt;
import com.jdh.o2oservice.core.domain.support.reach.model.JdhReachChannelAccount;
import com.jdh.o2oservice.core.domain.support.reach.repository.db.JdhReachConfigRepository;
import com.jdh.o2oservice.core.domain.support.reach.repository.db.JdhReachMessageTypeRepository;
import com.jdh.o2oservice.core.domain.support.reach.valueobject.ReachMessageBizType;
import com.jdh.o2oservice.core.domain.trade.enums.TradeAggregateEnum;
import com.jdh.o2oservice.core.domain.trade.enums.TradeEventTypeEnum;
import com.jdh.o2oservice.core.domain.trade.model.*;
import com.jdh.o2oservice.core.domain.trade.repository.db.*;
import com.jdh.o2oservice.core.domain.trade.rpc.OrderInfoRpc;
import com.jdh.o2oservice.export.angelpromise.dto.AngelWorkDetailForManDto;
import com.jdh.o2oservice.export.angelpromise.query.AngelWorkDetailForManRequest;
import com.jdh.o2oservice.export.dispatch.dto.JdhDispatchHistoryDto;
import com.jdh.o2oservice.export.dispatch.query.DispatchHistoryListRequest;
import com.jdh.o2oservice.export.medicalpromise.cmd.MedicalPromiseBindSpecimenCodeCmd;
import com.jdh.o2oservice.export.medicalpromise.dto.MedPromiseHistoryDTO;
import com.jdh.o2oservice.export.medicalpromise.dto.MedicalPromiseDTO;
import com.jdh.o2oservice.export.medicalpromise.query.MedPromiseHistoryRequest;
import com.jdh.o2oservice.export.medicalpromise.query.MedicalPromiseListRequest;
import com.jdh.o2oservice.export.medicalpromise.query.MedicalPromiseRequest;
import com.jdh.o2oservice.export.product.dto.JdhSkuDto;
import com.jdh.o2oservice.export.product.query.JdhSkuRequest;
import com.jdh.o2oservice.export.support.command.*;
import com.jdh.o2oservice.export.support.query.*;
import com.jdh.o2oservice.infrastructure.repository.db.dao.*;
import com.jdh.o2oservice.infrastructure.repository.db.po.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

/**
 * OpsApplicationImpl
 *
 * <AUTHOR>
 * @date 2024/03/01
 */
@Slf4j
@Service
public class OpsApplicationImpl implements OpsApplication {

    /**
     * jdOrderRepository
     */
    @Autowired
    private JdOrderRepository jdOrderRepository;

    /**
     * jdOrderItemRepository
     */
    @Autowired
    private JdOrderItemRepository jdOrderItemRepository;

    /**
     * voucherRepository
     */
    @Autowired
    private VoucherRepository voucherRepository;

    /**
     * promiseRepository
     */
    @Autowired
    private PromiseRepository promiseRepository;

    /**
     * promiseHistoryRepository
     */
    @Autowired
    private PromiseHistoryRepository promiseHistoryRepository;

    /**
     * eventRepository
     */
    @Autowired
    private EventRepository eventRepository;

    /**
     * consumerTaskRepository
     */
    @Autowired
    private ConsumerTaskRepository consumerTaskRepository;

    /**
     * skuInfoRpc
     */
    @Autowired
    private SkuInfoRpc skuInfoRpc;

    /**
     * medicalPromiseApplication
     */
    @Autowired
    private MedicalPromiseApplication medicalPromiseApplication;

    /**
     * productApplication
     */
    @Autowired
    private ProductApplication productApplication;

    /**
     * supportDevApplication
     */
    @Autowired
    private SupportDevApplication supportDevApplication;

    /**
     * MedPromiseHistoryApplication
     */
    @Autowired
    private MedPromiseHistoryApplication medPromiseHistoryApplication;

    /**
     * jdhVerticalBusinessPoMapper
     */
    @Autowired
    private JdhVerticalBusinessPoMapper jdhVerticalBusinessPoMapper;

    /**
     * dispatchApplication
     */
    @Autowired
    private DispatchApplication dispatchApplication;

    /**
     * dispatchRepository
     */
    @Autowired
    private DispatchRepository dispatchRepository;

    /**
     * jdOrderExtRepository
     */
    @Autowired
    private JdOrderExtRepository jdOrderExtRepository;

    /**
     * jdOrderMoneyRepository
     */
    @Autowired
    private JdOrderMoneyRepository jdOrderMoneyRepository;

    /**
     * jdOrderRefundTaskRepository
     */
    @Autowired
    private JdOrderRefundTaskRepository jdOrderRefundTaskRepository;

    /**
     * jdhBusinessModePoMapper
     */
    @Autowired
    private JdhBusinessModePoMapper jdhBusinessModePoMapper;

    /**
     * jdhServiceTypePoMapper
     */
    @Autowired
    private JdhServiceTypePoMapper jdhServiceTypePoMapper;

    /**
     * jdhServiceTypeCategoryRelationPoMapper
     */
    @Autowired
    private JdhServiceTypeCategoryRelationPoMapper jdhServiceTypeCategoryRelationPoMapper;

    /**
     * jdhAngelWorkPoMapper
     */
    @Autowired
    private JdhAngelWorkPoMapper jdhAngelWorkPoMapper;

    /**
     * jdhAngelTaskPoMapper
     */
    @Autowired
    private JdhAngelTaskPoMapper jdhAngelTaskPoMapper;

    /**
     * jdhAngelShipPoMapper
     */
    @Autowired
    private JdhAngelShipPoMapper jdhAngelShipPoMapper;

    /**
     * angelSettlementMapper
     */
    @Autowired
    private AngelSettlementMapper angelSettlementMapper;

    /**
     * orderInfoRpc
     */
    @Autowired
    private OrderInfoRpc orderInfoRpc;

    /**
     * reachTemplatePoMapper
     */
    @Autowired
    private JdhReachTemplatePoMapper reachTemplatePoMapper;

    /**
     * reachTaskPoMapper
     */
    @Autowired
    private JdhReachTaskPoMapper reachTaskPoMapper;

    /**
     * reachTriggerPoMapper
     */
    @Autowired
    private JdhReachTriggerPoMapper reachTriggerPoMapper;

    /**
     * reachMessagePoMapper
     */
    @Autowired
    private JdhReachMessagePoMapper reachMessagePoMapper;

    /**
     * reachApplication
     */
    @Autowired
    private ReachApplication reachApplication;

    /**
     * jdhReachMessageTypeRepository
     */
    @Autowired
    private JdhReachMessageTypeRepository jdhReachMessageTypeRepository;

    /**
     * jdhReachChannelAccountRepository
     */
    @Autowired
    private JdhReachConfigRepository jdhReachConfigRepository;

    /**
     * jdhVerticalBusinessServiceTypeRelationPoMapper
     */
    @Autowired
    private JdhVerticalBusinessServiceTypeRelationPoMapper jdhVerticalBusinessServiceTypeRelationPoMapper;

    /**
     * medicalReportMapper
     */
    @Autowired
    private MedicalReportMapper medicalReportMapper;

    /**
     * 文件管理领域服务
     */
    @Autowired
    private FileManageService fileManageService;

    /**
     * workApplication
     */
    @Autowired
    private AngelWorkApplication workApplication;

    /**
     * executorPoolFactory
     */
    @Autowired
    private ExecutorPoolFactory executorPoolFactory;

    /**
     * angelShipRepository
     */
    @Autowired
    private AngelShipRepository angelShipRepository;

    /**
     * angelShipHistoryRepository
     */
    @Autowired
    private AngelShipHistoryRepository angelShipHistoryRepository;

    /** */
    @Resource
    private AngelTaskRepository angelTaskRepository;

    /**
     * jdhAngelTaskHistoryPoMapper
     */
    @Autowired
    private JdhAngelTaskHistoryPoMapper jdhAngelTaskHistoryPoMapper;

    /**
     * 按订单ID查询
     *
     * @param orderId 订单id
     * @return {@link Object}
     */
    @Override
    public Map<String,Object> queryByOrderId(Long orderId) {
        Map<String,Object> result = new HashMap<>();
        JdOrder jdOrder = jdOrderRepository.find(JdOrderIdentifier.builder().orderId(orderId).build());
        if(Objects.nonNull(jdOrder)){
            result.put("jdOrder",jdOrder);
            List<JdOrderItem> jdOrderItems = jdOrderItemRepository.listByOrderId(orderId);
            result.put("orderItemList",jdOrderItems);
            List<JdOrderExt> orderExtList = jdOrderExtRepository.findJdOrderExtList(jdOrder.getOrderId());
            result.put("orderExtList",orderExtList);
            List<JdOrderMoney> jdOrderMoneyList = jdOrderMoneyRepository.findJdOrderMoneyList(jdOrder.getOrderId());
            result.put("orderMoneyList",jdOrderMoneyList);
            List<JdOrderRefundTask> orderRefundTaskList = jdOrderRefundTaskRepository.findJdOrderRefundTaskList(JdOrderRefundTask.builder().orderId(jdOrder.getOrderId()).build());
            result.put("orderRefundTaskList",orderRefundTaskList);
        }
        return result;
    }

    /**
     * 按聚合ID查询事件
     *
     * @param aggregateId 聚合ID
     * @return {@link JSONObject}
     */
    @Override
    public List<Map<String,Object>> queryEventByAggregateId(String aggregateId) {
        List<Event> eventList = eventRepository.getEventListByAggregateId(aggregateId);
        List<Map<String,Object>> result = new ArrayList<>();
        if(CollUtil.isNotEmpty(eventList)){
            for (Event event : eventList) {
                Map<String, Object> eventMap = BeanUtil.beanToMap(event);
                eventMap.put("publishTime",TimeUtils.localDateTimeToStr(event.getPublishTime(),TimeFormat.LONG_PATTERN_LINE));
                result.add(eventMap);
            }
        }
        return result;
    }

    /**
     * queryPromiseBySourceVoucherId
     *
     * @param sourceVoucherId sourceVoucherId
     * @return {@link JSONObject}
     */
    @Override
    public List<Map<String,Object>> queryVoucherListBySourceVoucherId(String sourceVoucherId) {
        List<Map<String,Object>> result = new ArrayList<>();
        List<JdhVoucher> jdhVouchers = voucherRepository.listByQuery(VoucherRepQuery.builder().sourceVoucherId(sourceVoucherId).build());
        if(CollUtil.isNotEmpty(jdhVouchers)){
            for (JdhVoucher jdhVoucher : jdhVouchers) {
                Map<String, Object> bean = BeanUtil.beanToMap(jdhVoucher);
                result.add(bean);
            }
        }
        return result;
    }

    /**
     * queryPromiseListByVoucherId
     *
     * @param voucherId 凭证ID
     * @return {@link List}<{@link Map}<{@link String}, {@link Object}>>
     */
    @Override
    public List<Map<String, Object>> queryPromiseListByVoucherId(Long voucherId) {
        List<Map<String,Object>> result = new ArrayList<>();
        List<JdhPromise> jdhPromises = promiseRepository.findList(PromiseRepQuery.builder().voucherIds(Collections.singletonList(voucherId)).build());
        if(CollUtil.isNotEmpty(jdhPromises)){
            for (JdhPromise jdhPromise : jdhPromises) {
                Map<String, Object> beanToMap = BeanUtil.beanToMap(jdhPromise);
                result.add(beanToMap);
            }
        }
        return result;
    }

    /**
     * queryPromiseHistoryListByPromiseId
     *
     * @param promiseId Promise ID
     * @return {@link List}<{@link Map}<{@link String}, {@link Object}>>
     */
    @Override
    public List<Map<String, Object>> queryPromiseHistoryListByPromiseId(Long promiseId) {
        List<Map<String,Object>> result = new ArrayList<>();
        List<JdhPromiseHistory> promiseHistories = promiseHistoryRepository.findList(PromiseHistoryRepQuery.builder().promiseId(promiseId).build());
        if(CollUtil.isNotEmpty(promiseHistories)){
            for (JdhPromiseHistory history : promiseHistories) {
                Map<String, Object> beanToMap = BeanUtil.beanToMap(history);
                result.add(beanToMap);
            }
        }
        return result;
    }

    /**
     * queryEventConsumerByEventId
     *
     * @param eventId eventId
     * @return {@link List}<{@link Map}<{@link String}, {@link Object}>>
     */
    @Override
    public List<Map<String, Object>> queryEventConsumerByEventId(Long eventId) {
        Event event = eventRepository.getEvent(eventId);
        List<Map<String, Object>> eventConsumerList = new ArrayList<>();
        String consumerCodes = event.getConsumerCodes();
        String trimStart = StrUtil.removePrefix(consumerCodes,"[");
        String consumerCodesStr =  StrUtil.removeSuffix(trimStart,"]");
        if(!consumerCodesStr.isEmpty()){
            String[] consumerArray = consumerCodesStr.split(",");
            for (String consumerCode : consumerArray) {
                try {
                    EventConsumerTask task = consumerTaskRepository.getTask(event.getEventId(), consumerCode);
                    if(Objects.nonNull(task)){
                        eventConsumerList.add(BeanUtil.beanToMap(task));
                    }
                }catch (Exception e){
                    log.error("consumerTaskRepository.getTask error",e);
                }
            }
        }
        return eventConsumerList;
    }

    /**
     * 按SKU ID查询SKU信息
     *
     * @param skuId SKU ID
     * @return {@link Map}<{@link String}, {@link Object}>
     */
    @Override
    public Map<String, Object> querySkuInfoBySkuId(String skuId) {
        try {
            Map<String, Object> result = new HashMap<>();
            try {
                RpcSkuBO skuBO = skuInfoRpc.getCrsSkuBoBySkuId(skuId);
                result.put("jdSkuInfo",BeanUtil.beanToMap(skuBO));
            }catch (Exception e){
                log.error("OpsApplicationImpl -> querySkuInfoBySkuId error",e);
            }

            try {
                JdhSkuRequest skuRequest = new JdhSkuRequest();
                skuRequest.setSkuId(Long.parseLong(skuId));
                skuRequest.setQueryServiceItem(Boolean.TRUE);
                skuRequest.setQuerySkuCoreData(Boolean.FALSE);
                JdhSkuDto jdhSkuDto = productApplication.queryJdhSkuInfo(skuRequest);

                result.put("jdhSkuInfo",BeanUtil.beanToMap(jdhSkuDto));
            }catch (Exception e){
                log.error("OpsApplicationImpl -> queryJdhSkuInfo error",e);
            }
            return result;
        }catch (Exception e){
            log.error("OpsApplicationImpl -> querySkuInfoBySkuId error",e);
            return MapUtil.empty();
        }
    }


    /**
     * queryPromiseInfoV2
     *
     * @param promiseId       promiseId
     * @param medPromiseId    medPromiseId
     * @param orderId         orderId
     * @return {@link List}<{@link Map}<{@link String}, {@link Object}>>
     */
    @Override
    public List<Map<String, Object>> queryPromiseInfoV2(String sourceVoucherId,String promiseId,String medPromiseId, String orderId,String specimenCode) {
        String findSourceVoucherId = findSourceVoucherId(sourceVoucherId,promiseId,medPromiseId,orderId,specimenCode);
        if(StrUtil.isBlank(findSourceVoucherId)){
            return new ArrayList<>();
        }
        List<JdhVoucher> vouchers = voucherRepository.listByQuery(VoucherRepQuery.builder().sourceVoucherId(findSourceVoucherId).build());
        List<Map<String,Object>> result = new ArrayList<>();
        if(CollUtil.isNotEmpty(vouchers)){
            for (JdhVoucher voucher : vouchers) {
                try {
                    Map<String, Object> voucherInfoMap = BeanUtil.beanToMap(voucher);
                    voucherInfoMap.put("jdhVoucher",BeanUtil.beanToMap(voucher));
                    List<JdhPromise> jdhPromiseList = promiseRepository.findList(PromiseRepQuery.builder().voucherIds(Arrays.asList(voucher.getVoucherId())).build());
                    if(CollUtil.isNotEmpty(jdhPromiseList)){
                        List<Map<String,Object>> jdhPromiseMapList = new ArrayList<>();
                        for (JdhPromise jdhPromise : jdhPromiseList) {
                            Map<String,Object> promiseInfoMap = new HashMap<>();
                            fillPromiseInfo(promiseInfoMap,voucher,jdhPromise);
                            jdhPromiseMapList.add(promiseInfoMap);
                        }
                        voucherInfoMap.put("jdhPromiseList",jdhPromiseMapList);
                    }
                    result.add(voucherInfoMap);
                }catch (Exception e){
                    // ignore
                    log.error("queryPromiseInfoV2 exception",e);
                }
            }
        }
        return result;
    }

    /**
     * findSourceVoucherId
     *
     * @param promiseId    promiseId
     * @param medPromiseId medPromiseId
     * @param orderId      订单id
     * @return {@link String}
     */
    private String findSourceVoucherId(String sourceVoucherId,String promiseId,String medPromiseId, String orderId,String specimenCode){
        String findSourceVoucherId = "";
        if(StrUtil.isNotBlank(sourceVoucherId)){
            findSourceVoucherId = sourceVoucherId;
        }

        if(StrUtil.isNotBlank(orderId)){
            JdOrder jdOrder = jdOrderRepository.find(JdOrderIdentifier.builder().orderId(Long.parseLong(orderId)).build());
            if(Objects.nonNull(jdOrder)){
                if(StrUtil.isNotBlank(jdOrder.getVerticalCode())){
                    LambdaQueryWrapper<JdhVerticalBusinessPo> queryWrapper = new LambdaQueryWrapper<>();
                    queryWrapper.eq(JdhVerticalBusinessPo::getVerticalCode,jdOrder.getVerticalCode());
                    JdhVerticalBusinessPo businessPo = jdhVerticalBusinessPoMapper.selectOne(queryWrapper);
                    if(Objects.nonNull(businessPo)){

                        if(BusinessModeEnum.POP_LOC.getCode().equals(businessPo.getBusinessModeCode())){
                            List<JdOrderItem> jdOrderItems = jdOrderItemRepository.listByOrderId(Long.parseLong(orderId));
                            findSourceVoucherId = jdOrderItems.get(0).getOrderItemId().toString();
                        }else{
                            //如果有父单号
                            Long parentId = jdOrder.getParentId();
                            if(Objects.nonNull(parentId) && parentId > 0){
                                findSourceVoucherId = parentId.toString();
                            }else{
                                findSourceVoucherId = orderId;
                            }
                        }
                    }
                }
            }
        }

        if(StrUtil.isNotBlank(promiseId)){
            Long queryPromiseId = StrUtil.isBlank(promiseId) ? null : Long.parseLong(promiseId);
            JdhPromise promise = promiseRepository.findPromise(PromiseRepQuery.builder().promiseId(queryPromiseId).build());
            if(Objects.nonNull(promise)){
                findSourceVoucherId = promise.getSourceVoucherId();
            }
        }

        if(StrUtil.isNotBlank(medPromiseId)){
            MedicalPromiseDTO medicalPromiseDTO = medicalPromiseApplication.queryMedicalPromise(MedicalPromiseRequest.builder().medicalPromiseId(Long.parseLong(medPromiseId)).build());
            if(Objects.nonNull(medicalPromiseDTO)){
                JdhPromise promise = promiseRepository.findPromise(PromiseRepQuery.builder().promiseId(medicalPromiseDTO.getPromiseId()).build());
                if(Objects.nonNull(promise)){
                    findSourceVoucherId = promise.getSourceVoucherId();
                }
            }
        }

        if(StrUtil.isNotBlank(specimenCode)){
            MedicalPromiseDTO medicalPromiseDTO = medicalPromiseApplication.queryMedicalPromise(MedicalPromiseRequest.builder().specimenCode(specimenCode).build());
            if(Objects.nonNull(medicalPromiseDTO)){
                JdhPromise promise = promiseRepository.findPromise(PromiseRepQuery.builder().promiseId(medicalPromiseDTO.getPromiseId()).build());
                if(Objects.nonNull(promise)){
                    findSourceVoucherId = promise.getSourceVoucherId();
                }
            }
        }

        return findSourceVoucherId;
    }

    /**
     * queryPromiseInfo
     *
     * @param promiseId       promiseId
     * @param sourceVoucherId sourceVoucherId
     * @return {@link Map}<{@link String}, {@link Object}>
     */
    @Override
    public Map<String, Object> queryPromiseInfo(String promiseId, String sourceVoucherId,String medPromiseId) {
        JdhPromise promise = null;
        JdhVoucher jdhVoucher = null;
        if(StrUtil.isNotBlank(promiseId)){
            Long queryPromiseId = StrUtil.isBlank(promiseId) ? null : Long.parseLong(promiseId);
            promise = promiseRepository.findPromise(PromiseRepQuery.builder().promiseId(queryPromiseId).build());
            if(Objects.nonNull(promise)){
                jdhVoucher = voucherRepository.find(JdhVoucherIdentifier.builder().voucherId(promise.getVoucherId()).build());
            }
        }

        if(StrUtil.isNotBlank(medPromiseId)){
            MedicalPromiseDTO medicalPromiseDTO = medicalPromiseApplication.queryMedicalPromise(MedicalPromiseRequest.builder().medicalPromiseId(Long.parseLong(medPromiseId)).build());
            if(Objects.nonNull(medicalPromiseDTO)){
                promise = promiseRepository.findPromise(PromiseRepQuery.builder().promiseId(medicalPromiseDTO.getPromiseId()).build());
                if(Objects.nonNull(promise)){
                    jdhVoucher = voucherRepository.find(JdhVoucherIdentifier.builder().voucherId(promise.getVoucherId()).build());
                }
            }
        }

        if(StrUtil.isNotBlank(sourceVoucherId)){
            List<JdhVoucher> vouchers = voucherRepository.listByQuery(VoucherRepQuery.builder().sourceVoucherId(sourceVoucherId).build());
            if(CollUtil.isNotEmpty(vouchers)){
                jdhVoucher = vouchers.get(0);
                promise = promiseRepository.findPromise(PromiseRepQuery.builder().sourceVoucherId(sourceVoucherId).build());
            }
        }

        Map<String,Object> result = new HashMap<>();
        fillPromiseInfo(result,jdhVoucher,promise);
        return result;
    }

    /**
     * 填充数据
     *
     * @param result     结果
     * @param jdhVoucher jdhVoucher
     * @param promise    promise
     */
    private void fillPromiseInfo(Map<String,Object> result,JdhVoucher jdhVoucher,JdhPromise promise){

        if(Objects.nonNull(jdhVoucher)){
            Map<String, Object> voucherMap = BeanUtil.beanToMap(jdhVoucher);
            result.put("jdhVoucher",voucherMap);
        }

        if(Objects.nonNull(promise)){
            ExecutorService executorService = executorPoolFactory.get(ThreadPoolConfigEnum.OPS_POOL);
            List<CompletableFuture<Void>> futures = new ArrayList<>();
            futures.add(CompletableFuture.runAsync(() -> {
                Map<String, Object> promiseMap = BeanUtil.beanToMap(promise);
                if (Objects.nonNull(promise.getAppointmentTime())) {
                    Map<String, Object> appointTimeMap = Convert.convert(new TypeReference<Map<String, Object>>() {
                    }, promiseMap.get("appointmentTime"));
                    if (Objects.nonNull(promise.getAppointmentTime().getAppointmentStartTime())) {
                        appointTimeMap.put("appointmentStartTime", TimeUtils.localDateTimeToStr(promise.getAppointmentTime().getAppointmentStartTime(), TimeFormat.LONG_PATTERN_LINE_NO_S));
                    }
                    if (Objects.nonNull(promise.getAppointmentTime().getAppointmentEndTime())) {
                        appointTimeMap.put("appointmentEndTime", TimeUtils.localDateTimeToStr(promise.getAppointmentTime().getAppointmentEndTime(), TimeFormat.LONG_PATTERN_LINE_NO_S));
                    }
                    promiseMap.put("appointmentTime", appointTimeMap);
                }
                List<JdhPromiseHistory> promiseHistories = promiseHistoryRepository.findList(PromiseHistoryRepQuery.builder().promiseId(promise.getPromiseId()).build());
                if (CollUtil.isNotEmpty(promiseHistories)) {
                    List<JdhPromiseHistory> sortedList = promiseHistories.stream().sorted((o1, o2) -> o2.getCreateTime().compareTo(o1.getCreateTime())).collect(Collectors.toList());
                    List<Map<String, Object>> promiseHistoryList = new ArrayList<>();
                    for (JdhPromiseHistory promiseHistory : sortedList) {
                        Map<String, Object> bean = BeanUtil.beanToMap(promiseHistory);
                        bean.put("label", TimeUtils.dateTimeToStr(promiseHistory.getCreateTime(), TimeFormat.LONG_PATTERN_LINE));
                        PromiseEventTypeEnum promiseEventTypeEnum = PromiseEventTypeEnum.getByCode(promiseHistory.getEventCode());
                        bean.put("children", Objects.isNull(promiseEventTypeEnum) ? promiseHistory.getEventCode() : promiseEventTypeEnum.getDesc() + "(" + promiseHistory.getEventCode() + ")");
                        promiseHistoryList.add(bean);
                    }
                    promiseMap.put("promiseHistory", promiseHistoryList);
                }
                result.put("jdhPromise", promiseMap);
            },executorService));

            futures.add(CompletableFuture.runAsync(() -> {
                try {
                    List<JdhDispatch> dispatchList = dispatchRepository.findDispatchList(DispatchRepQuery.builder().promiseId(promise.getPromiseId()).build());
                    if (CollUtil.isEmpty(dispatchList)) {
                        return;
                    }
                    //派单任务排序，取最后创建的派单任务
                    JdhDispatch dispatch = dispatchList.stream().sorted((o1, o2) -> o2.getCreateTime().compareTo(o1.getCreateTime())).collect(Collectors.toList()).get(0);
                    Map<String, Object> dispatchMap = BeanUtil.beanToMap(dispatch);
                    List<JdhDispatchHistoryDto> dispatchHistoryDtoList = dispatchApplication.queryDispatchHistoryListByPromiseId(DispatchHistoryListRequest.builder().promiseId(promise.getPromiseId()).build());
                    if(CollUtil.isNotEmpty(dispatchHistoryDtoList)){
                        List<JdhDispatchHistoryDto> sortedList = dispatchHistoryDtoList.stream().sorted((o1, o2) -> o2.getCreateTime().compareTo(o1.getCreateTime())).collect(Collectors.toList());
                        List<Map<String,Object>> historyList = new ArrayList<>();
                        for (JdhDispatchHistoryDto jdhDispatchHistoryDto : sortedList) {
                            Map<String, Object> bean = BeanUtil.beanToMap(jdhDispatchHistoryDto);
                            bean.put("label",TimeUtils.dateTimeToStr(jdhDispatchHistoryDto.getCreateTime(),TimeFormat.LONG_PATTERN_LINE));
                            bean.put("children", jdhDispatchHistoryDto.getEventDesc() + "(" + jdhDispatchHistoryDto.getEventCode() + ")");
                            historyList.add(bean);
                        }
                        dispatchMap.put("dispatchHistory",historyList);
                    }


                    result.put("dispatch",dispatchMap);
                }catch (Exception e){
                    log.error("queryPromiseInfo findDispatch exception",e);
                }
            },executorService));


            futures.add(CompletableFuture.runAsync(() -> {
                try {
                    AngelWorkDetailForManRequest request = new AngelWorkDetailForManRequest();
                    request.setPromiseId(promise.getPromiseId());
                    AngelWorkDetailForManDto angelWorkDetailForManDto = workApplication.queryAngelWorkForMan(request);
                    Map<String, Object> workMap = BeanUtil.beanToMap(angelWorkDetailForManDto);
                    result.put("angelWork", workMap);

                    //查运单
                    AngelShipDBQuery dbQuery = new AngelShipDBQuery();
                    dbQuery.setWorkId(Long.parseLong(angelWorkDetailForManDto.getWorkId()));
                    dbQuery.setStatus(new HashSet<>(AngelShipStatusEnum.getValidStatus()));
                    List<AngelShip> shipList = angelShipRepository.findList(dbQuery);

                    if(CollUtil.isNotEmpty(shipList)){
                        //查运单历史
                        for (AngelShip angelShip : shipList) {
                            AngelShipHistoryDBQuery historyDBQuery = AngelShipHistoryDBQuery.builder()
                                    .workId(Long.parseLong(angelWorkDetailForManDto.getWorkId()))
                                    .shipIds(Sets.newHashSet(angelShip.getShipId()))
                                    .build();
                            List<AngelShipHistory> historyList = angelShipHistoryRepository.findList(historyDBQuery);
                            angelShip.setShipHistoryList(historyList);
                        }
                        workMap.put("shipList",shipList);
                    }

                    AngelTaskDBQuery angelTaskDBQuery = new AngelTaskDBQuery();
                    angelTaskDBQuery.setWorkId(Long.parseLong(angelWorkDetailForManDto.getWorkId()));
                    List<AngelTask> angelTaskList = angelTaskRepository.findList(angelTaskDBQuery);
                    if(CollUtil.isNotEmpty(angelTaskList)){
                        List<Map<String,Object>> angelTaskBeanList = new ArrayList<>();
                        for (AngelTask angelTask : angelTaskList) {
                            Map<String, Object> taskBeanMap = BeanUtil.beanToMap(angelTask);
                            LambdaQueryWrapper<JdhAngelTaskHistoryPo> queryWrapper = new LambdaQueryWrapper<JdhAngelTaskHistoryPo>();
                            queryWrapper.eq(JdhAngelTaskHistoryPo::getTaskId, angelTask.getTaskId());
                            List<JdhAngelTaskHistoryPo> taskHistoryPos = jdhAngelTaskHistoryPoMapper.selectList(queryWrapper);
                            taskBeanMap.put("taskHistories",taskHistoryPos);
                            angelTaskBeanList.add(taskBeanMap);
                        }
                        workMap.put("taskList",angelTaskBeanList);
                    }


                } catch (Exception e) {
                    log.error("queryPromiseInfo queryAngelWorkForMan exception", e);
                }
            },executorService));

            futures.add(CompletableFuture.runAsync(() -> {
                try {
                    List<MedicalPromiseDTO> medicalPromiseDTOS = medicalPromiseApplication.queryMedicalPromiseList(MedicalPromiseListRequest.builder().promiseId(promise.getPromiseId()).build());
                    if(CollUtil.isNotEmpty(medicalPromiseDTOS)){
                        List<Map<String,Object>> medicalPromiseList = new ArrayList<>();
                        for (MedicalPromiseDTO medicalPromiseDTO : medicalPromiseDTOS) {
                            Map<String,Object> ele = BeanUtil.beanToMap(medicalPromiseDTO);
                            List<MedPromiseHistoryDTO> history = medPromiseHistoryApplication.queryMedPromiseHistoryList(MedPromiseHistoryRequest.builder().medicalPromiseId(medicalPromiseDTO.getMedicalPromiseId()).build());
                            if(CollUtil.isNotEmpty(history)){
                                List<MedPromiseHistoryDTO> sortedList = history.stream().sorted((o1, o2) -> o2.getCreateTime().compareTo(o1.getCreateTime())).collect(Collectors.toList());
                                List<Map<String,Object>> historyList = new ArrayList<>();
                                for (MedPromiseHistoryDTO medPromiseHistoryDTO : sortedList) {
                                    Map<String, Object> bean = BeanUtil.beanToMap(medPromiseHistoryDTO);
                                    bean.put("label",TimeUtils.dateTimeToStr(medPromiseHistoryDTO.getCreateTime(),TimeFormat.LONG_PATTERN_LINE));
                                    bean.put("children", medPromiseHistoryDTO.getEventDesc() + "(" + medPromiseHistoryDTO.getEventCode() + ")");
                                    historyList.add(bean);
                                }
                                ele.put("medPromiseHistory",historyList);
                            }
                            medicalPromiseList.add(ele);
                        }
                        result.put("medicalPromiseList",medicalPromiseList);
                    }
                }catch (Exception e){
                    log.error("queryPromiseInfo queryMedicalPromiseList exception",e);
                }
            },executorService));

            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        }
    }

    /**
     * 分页查询事件
     *
     * @param request 请求
     * @return {@link PageDto}<{@link Map}<{@link String}, {@link Object}>>
     */
    @Override
    public PageDto<Map<String, Object>> queryEventPage(EventPageRequest request) {

        EventPageQuery query = new EventPageQuery();
        BeanUtil.copyProperties(request,query);
        Page<Event> eventPage = eventRepository.pageQueryEvent(query);

        PageDto<Map<String, Object>> pageDto = new PageDto<>();
        pageDto.setTotalPage(eventPage.getPages());
        pageDto.setPageNum(eventPage.getCurrent());
        pageDto.setPageSize(eventPage.getSize());
        pageDto.setTotalCount(eventPage.getTotal());
        List<Event> records = eventPage.getRecords();

        List<Map<String, Object>> recordList =  new ArrayList<>();
        if(CollUtil.isNotEmpty(records)){
            records.forEach(ele -> {
                Map<String, Object> eventMap = BeanUtil.beanToMap(ele);
                eventMap.put("publishTime",TimeUtils.localDateTimeToStr(ele.getPublishTime(), TimeFormat.LONG_PATTERN_LINE));
                recordList.add(eventMap);
            });
        }
        pageDto.setList(recordList);

        return pageDto;
    }

    /**
     * 模仿发布事件
     *
     * @param cmd cmd
     * @return {@link Boolean}
     */
    @Override
    public Boolean imitatePublishEvent(ImitatePublishEventCmd cmd) {
        return supportDevApplication.imitatePublishEvent(cmd);
    }

    /**
     * queryVerticalCodePage
     *
     * @param request 请求
     * @return {@link PageDto}<{@link Map}<{@link String}, {@link Object}>>
     */
    @Override
    public PageDto<Map<String, Object>> queryVerticalCodePage(VerticalCodePageRequest request) {
        Page<JdhVerticalBusinessPo> page = new Page<>(request.getPageNum(),request.getPageSize());
        LambdaQueryWrapper<JdhVerticalBusinessPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.like(StrUtil.isNotEmpty(request.getVerticalCode()),JdhVerticalBusinessPo::getVerticalCode,request.getVerticalCode())
                .eq(JdhVerticalBusinessPo::getYn,YnStatusEnum.YES.getCode())
                .orderByDesc(JdhVerticalBusinessPo::getCreateTime);
        Page<JdhVerticalBusinessPo> verticalBusinessPoPage = jdhVerticalBusinessPoMapper.selectPage(page, queryWrapper);

        PageDto<Map<String, Object>> pageDto = new PageDto<>();
        pageDto.setTotalPage(verticalBusinessPoPage.getPages());
        pageDto.setPageNum(verticalBusinessPoPage.getCurrent());
        pageDto.setPageSize(verticalBusinessPoPage.getSize());
        pageDto.setTotalCount(verticalBusinessPoPage.getTotal());
        List<Map<String, Object>> recordsList = new ArrayList<>();
        List<JdhVerticalBusinessPo> records = verticalBusinessPoPage.getRecords();
        if(CollUtil.isNotEmpty(records)){
            records.forEach(ele -> recordsList.add(BeanUtil.beanToMap(ele)));
        }
        pageDto.setList(recordsList);
        return pageDto;
    }

    /**
     * 按垂直代码删除垂直代码
     *
     * @param verticalCode 垂直代码
     * @return {@link Boolean}
     */
    @Override
    public Boolean deleteVerticalCode(String verticalCode) {
        LambdaUpdateWrapper<JdhVerticalBusinessPo> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(JdhVerticalBusinessPo::getYn, YnStatusEnum.NO.getCode())
                .set(JdhVerticalBusinessPo::getUpdateUser,LoginContext.getLoginContext().getPin())
                .eq(JdhVerticalBusinessPo::getVerticalCode,verticalCode);
        jdhVerticalBusinessPoMapper.update(null,updateWrapper);

        LambdaUpdateWrapper<JdhVerticalBusinessServiceTypeRelationPo> relationUpdateWrapper = Wrappers.lambdaUpdate();
        relationUpdateWrapper.set(JdhVerticalBusinessServiceTypeRelationPo::getYn, YnStatusEnum.NO.getCode())
                .set(JdhVerticalBusinessServiceTypeRelationPo::getUpdateUser,LoginContext.getLoginContext().getPin())
                .eq(JdhVerticalBusinessServiceTypeRelationPo::getVerticalCode,verticalCode);

        jdhVerticalBusinessServiceTypeRelationPoMapper.update(null,relationUpdateWrapper);

        return Boolean.TRUE;
    }

    /**
     * 启用或禁用垂直代码
     *
     * @param verticalCode 垂直代码
     * @param opType       操作类型
     * @return {@link Boolean}
     */
    @Override
    public Boolean enableOrDisableVerticalCode(String verticalCode, Integer opType) {
        LambdaUpdateWrapper<JdhVerticalBusinessPo> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(JdhVerticalBusinessPo::getStatus, opType)
                .set(JdhVerticalBusinessPo::getUpdateUser,LoginContext.getLoginContext().getPin())
                .eq(JdhVerticalBusinessPo::getVerticalCode,verticalCode);
        return jdhVerticalBusinessPoMapper.update(null,updateWrapper) > 0;
    }

    /**
     * 创建垂直代码
     *
     * @param cmd cmd
     * @return {@link Boolean}
     */
    @Override
    public Boolean createVerticalCode(CreateVerticalCodeCmd cmd) {
        String pin = LoginContext.getLoginContext().getPin();
        Date now = new Date();
        JdhVerticalBusinessPo po = new JdhVerticalBusinessPo();
        BeanUtil.copyProperties(cmd,po);
        po.setStatus(YnStatusEnum.YES.getCode());
        po.setCreateUser(pin);
        po.setUpdateUser(pin);
        po.setCreateTime(now);
        po.setUpdateTime(now);
        po.setVersion(NumConstant.NUM_1);
        po.setYn(YnStatusEnum.YES.getCode());

        JdhVerticalBusinessServiceTypeRelationPo serviceTypeRelationPo = new JdhVerticalBusinessServiceTypeRelationPo();
        serviceTypeRelationPo.setVerticalId(po.getVerticalId());
        serviceTypeRelationPo.setVerticalCode(po.getVerticalCode());
        serviceTypeRelationPo.setServiceType(cmd.getServiceType());

        serviceTypeRelationPo.setCreateTime(now);
        serviceTypeRelationPo.setCreateUser(pin);
        serviceTypeRelationPo.setUpdateUser(pin);
        serviceTypeRelationPo.setUpdateTime(now);
        serviceTypeRelationPo.setVersion(NumConstant.NUM_1);
        serviceTypeRelationPo.setYn(YnStatusEnum.YES.getCode());

        jdhVerticalBusinessServiceTypeRelationPoMapper.insert(serviceTypeRelationPo);
        jdhVerticalBusinessPoMapper.insert(po);
        return Boolean.TRUE;
    }

    /**
     * 创建服务类型
     *
     * @param cmd cmd
     * @return {@link Boolean}
     */
    @Override
    public Boolean createServiceType(CreateServiceTypeCmd cmd) {
        String pin = LoginContext.getLoginContext().getPin();
        Date now = new Date();
        JdhServiceTypePo po = new JdhServiceTypePo();
        BeanUtil.copyProperties(cmd,po);
        po.setCreator(pin);
        po.setUpdater(pin);
        po.setCreateTime(now);
        po.setUpdateTime(now);
        po.setVersion(NumConstant.NUM_1);
        po.setYn(YnStatusEnum.YES.getCode());
        return jdhServiceTypePoMapper.insert(po) > 0;
    }

    /**
     * 删除服务类型
     *
     * @param serviceType 代码
     * @return {@link Boolean}
     */
    @Override
    public Boolean deleteServiceType(String serviceType) {
        LambdaUpdateWrapper<JdhServiceTypePo> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(JdhServiceTypePo::getYn, YnStatusEnum.NO.getCode())
                .set(JdhServiceTypePo::getUpdater,LoginContext.getLoginContext().getPin())
                .eq(JdhServiceTypePo::getServiceType,serviceType);
        return jdhServiceTypePoMapper.update(null,updateWrapper) > 0;
    }

    /**
     * createBusinessMode
     *
     * @param cmd cmd
     * @return {@link Boolean}
     */
    @Override
    public Boolean createBusinessMode(CreateBusinessModeCmd cmd) {
        String pin = LoginContext.getLoginContext().getPin();
        Date now = new Date();
        JdhBusinessModePo po = new JdhBusinessModePo();
        BeanUtil.copyProperties(cmd,po);
        po.setCreator(pin);
        po.setUpdater(pin);
        po.setCreateTime(now);
        po.setUpdateTime(now);
        po.setVersion(NumConstant.NUM_1);
        po.setYn(YnStatusEnum.YES.getCode());
        return jdhBusinessModePoMapper.insert(po) > 0;
    }

    /**
     * 删除业务模式
     *
     * @param code 代码
     * @return {@link Boolean}
     */
    @Override
    public Boolean deleteBusinessMode(String code) {
        LambdaUpdateWrapper<JdhBusinessModePo> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(JdhBusinessModePo::getYn, YnStatusEnum.NO.getCode())
                .set(JdhBusinessModePo::getUpdater,LoginContext.getLoginContext().getPin())
                .eq(JdhBusinessModePo::getCode,code);
        return jdhBusinessModePoMapper.update(null,updateWrapper) > 0;
    }

    /**
     * 创建服务类型类别关系
     *
     * @param cmd cmd
     * @return {@link Boolean}
     */
    @Override
    public Boolean createServiceTypeCategoryRelation(CreateServiceTypeCategoryRelationCmd cmd) {
        String pin = LoginContext.getLoginContext().getPin();
        Date now = new Date();
        JdhServiceTypeCategoryRelationPo po = new JdhServiceTypeCategoryRelationPo();
        BeanUtil.copyProperties(cmd,po);
        po.setUpdater(pin);
        po.setCreator(pin);
        po.setCreateTime(now);
        po.setUpdateTime(now);
        po.setVersion(NumConstant.NUM_1);
        po.setYn(YnStatusEnum.YES.getCode());
        return jdhServiceTypeCategoryRelationPoMapper.insert(po) > 0;
    }

    /**
     * 删除服务类型类别关系
     *
     * @param serviceType 服务类型
     * @param categoryId  类别id
     * @return {@link Boolean}
     */
    @Override
    public Boolean deleteServiceTypeCategoryRelation(String serviceType, Integer categoryId) {
        LambdaUpdateWrapper<JdhServiceTypeCategoryRelationPo> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(JdhServiceTypeCategoryRelationPo::getYn, YnStatusEnum.NO.getCode())
                .set(JdhServiceTypeCategoryRelationPo::getUpdater,LoginContext.getLoginContext().getPin())
                .eq(JdhServiceTypeCategoryRelationPo::getServiceType,serviceType)
                .eq(JdhServiceTypeCategoryRelationPo::getCategoryId,categoryId);
        return jdhServiceTypeCategoryRelationPoMapper.update(null,updateWrapper) > 0;
    }

    /**
     * 修改订单完成状态
     *
     * @param orderId 订单id
     * @param idCompanyBranch idCompanyBranch
     * @return {@link Boolean}
     */
    @Override
    public Boolean reviseOrderFinishState(Long orderId,Integer idCompanyBranch) {
        return orderInfoRpc.reviseOrderInstitutionNumber(orderId,idCompanyBranch);
    }

    /**
     * 查询事件链
     *
     * @param domainCode  域代码
     * @param aggregateId 聚合id
     * @return {@link List}<{@link Map}<{@link String}, {@link String}>>
     */
    @Override
    public Map<String, List<Map<String, Object>>> queryEventChain(String domainCode,String aggregateCode,String aggregateId) {
        Long promiseId = detectPromiseId(domainCode, aggregateCode, aggregateId);
        if(Objects.isNull(promiseId)){
            return new HashMap<>();
        }
        Map<String, List<Map<String, Object>>> eventList = new HashMap<>();

        //查交易事件
        findTradeEventList(promiseId,eventList);
        //查voucher & promise
        findVoucherPromiseEventList(promiseId,eventList);
        //查dispatch
        findDispatchEventList(promiseId,eventList);
        //查medPromise
        findMedPromiseEventList(promiseId,eventList);
        //查angelPromise - work、task、ship
        findAngelPromiseEventList(promiseId,eventList);
        //查结算
        findSettlementEventList(promiseId,eventList);

        return eventList;
    }

    /**
     * 查询事件链无agg
     *
     * @param domainCode    域代码
     * @param aggregateCode 聚合码
     * @param aggregateId   聚合id
     * @return {@link List}<{@link Map}<{@link String}, {@link Object}>>
     */
    @Override
    public List<Map<String, Object>> queryEventChainNoAgg(String domainCode, String aggregateCode, String aggregateId) {
        Map<String, List<Map<String, Object>>> eventChain = this.queryEventChain(domainCode, aggregateCode, aggregateId);
        if(MapUtil.isNotEmpty(eventChain)){
            List<Map<String, Object>> result = new ArrayList<>();
            for (Map.Entry<String, List<Map<String, Object>>> entry : eventChain.entrySet()) {
                result.addAll(entry.getValue());
            }
            CollUtil.sort(result, Comparator.comparing(o -> Convert.convert(LocalDateTime.class, o.get("publishTime"))));
            return result;
        }
        return new ArrayList<>();
    }

    /**
     * 查询交易事件列表
     *
     * @param promiseId promiseId
     * @param eventList 事件列表
     */
    private void findTradeEventList(Long promiseId, Map<String, List<Map<String, Object>>> eventList) {
        JdhPromise promise = promiseRepository.findPromise(PromiseRepQuery.builder().promiseId(promiseId).build());
        if(Objects.nonNull(promise)){
            LambdaQueryWrapper<JdhVerticalBusinessPo> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(JdhVerticalBusinessPo::getVerticalCode,promise.getVerticalCode());
            JdhVerticalBusinessPo businessPo = jdhVerticalBusinessPoMapper.selectOne(queryWrapper);
            if(Objects.nonNull(businessPo)){
                List<Map<String, Object>> tempEventList = new ArrayList<>();
                if(BusinessModeEnum.SELF_TEST.getCode().equals(businessPo.getBusinessModeCode())
                        || BusinessModeEnum.ANGEL_CARE.getCode().equals(businessPo.getBusinessModeCode())
                        || BusinessModeEnum.ANGEL_TEST.getCode().equals(businessPo.getBusinessModeCode())
                        || BusinessModeEnum.ANGEL_TEST_NO_LABORATORY.getCode().equals(businessPo.getBusinessModeCode())) {
                    Long orderId = Long.parseLong(promise.getSourceVoucherId());
                    List<JdOrder> listByOrderId = jdOrderRepository.findOrderListByOrderId(JdOrderIdentifier.builder().orderId(orderId).build());
                    if(CollUtil.isNotEmpty(listByOrderId)){
                        listByOrderId.forEach(ele -> tempEventList.addAll(queryEventListByAggregateId(ele.getOrderId().toString())));
                    }
                }else{
                    Long orderItemId = Long.parseLong(promise.getSourceVoucherId());
                    JdOrderItem jdOrderItem = jdOrderItemRepository.queryJdOrderItemByOrderItemId(orderItemId);
                    tempEventList.addAll(queryEventListByAggregateId(jdOrderItem.getOrderId().toString()));
                }

                if(CollUtil.isNotEmpty(tempEventList)){
                    for (Map<String, Object> eventMap : tempEventList) {
                        TradeEventTypeEnum eventType = TradeEventTypeEnum.getEventTypeEnumByCode(Convert.convert(String.class, eventMap.get("eventCode")));
                        if(Objects.nonNull(eventType)){
                            eventMap.put("eventCodeDesc","【trade】" + eventType.getDesc() + "(" + eventType.getCode() + ")");
                        }
                    }
                    CollUtil.sort(tempEventList, (o1, o2) -> Convert.convert(LocalDateTime.class,o2.get("publishTime")).compareTo(Convert.convert(LocalDateTime.class,o1.get("publishTime"))));
                    eventList.put("trade",tempEventList);
                }
            }
        }
    }

    /**
     * 查找结算事件列表
     *
     * @param promiseId promiseId
     * @param eventList 事件列表
     */
    private void findSettlementEventList(Long promiseId, Map<String, List<Map<String, Object>>>  eventList) {
        LambdaQueryWrapper<AngelSettlementPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(AngelSettlementPo::getPromiseId, promiseId);
        List<AngelSettlementPo> poList = angelSettlementMapper.selectList(queryWrapper);
        if(CollUtil.isNotEmpty(poList)){
            List<Map<String, Object>> tempEventList = new ArrayList<>();
            poList.forEach(ele -> tempEventList.addAll(queryEventListByAggregateId(ele.getSettleId().toString())));
            if(CollUtil.isNotEmpty(tempEventList)){
                for (Map<String, Object> eventMap : tempEventList) {
                    SettleEventTypeEnum eventType = SettleEventTypeEnum.getEnumByCode(Convert.convert(String.class, eventMap.get("eventCode")));
                    if(Objects.nonNull(eventType)){
                        eventMap.put("eventCodeDesc","【settlement】" + eventType.getDesc() + "(" + eventType.getCode() + ")");
                    }
                }
                CollUtil.sort(tempEventList, (o1, o2) -> Convert.convert(LocalDateTime.class,o2.get("publishTime")).compareTo(Convert.convert(LocalDateTime.class,o1.get("publishTime"))));
                eventList.put("settlement",tempEventList);
            }
        }
    }

    /**
     * 按聚合ID查询事件列表
     *
     * @param aggregateId 聚合id
     * @return {@link List}<{@link Map}<{@link String},{@link Object}>>
     */
    private List<Map<String, Object>> queryEventListByAggregateId(String aggregateId){
        Event event = new Event();
        event.setAggregateId(aggregateId);
        List<Event> eventList = eventRepository.getEventListByCondition(event);
        List<Map<String, Object>> result = new ArrayList<>();
        if(CollUtil.isNotEmpty(eventList)){
            eventList.forEach(ele -> {
                Map<String, Object> eventMap = BeanUtil.beanToMap(ele);
                eventMap.put("pubTime",TimeUtils.localDateTimeToStr(ele.getPublishTime(), TimeFormat.LONG_PATTERN_LINE));
                result.add(eventMap);
            });
        }
        return result;
    }

    /**
     * findVoucherPromiseEventList
     *
     * @param promiseId promiseId
     * @param result    结果
     */
    private void findVoucherPromiseEventList(Long promiseId,Map<String, List<Map<String, Object>>> result){
        JdhPromise promise = promiseRepository.findPromise(PromiseRepQuery.builder().promiseId(promiseId).build());
        if(Objects.nonNull(promise)){
            List<Map<String,Object>> tempEventList = new ArrayList<>();
            tempEventList.addAll(queryEventListByAggregateId(promiseId.toString()));
            tempEventList.addAll(queryEventListByAggregateId(promise.getVoucherId().toString()));

            if(CollUtil.isNotEmpty(tempEventList)){
                for (Map<String, Object> eventMap : tempEventList) {
                    PromiseEventTypeEnum eventType = PromiseEventTypeEnum.getByCode(Convert.convert(String.class, eventMap.get("eventCode")));
                    if(Objects.nonNull(eventType)){
                        eventMap.put("eventCodeDesc","【promise】" + eventType.getDesc() + "(" + eventType.getCode() + ")");
                    }
                }
                CollUtil.sort(tempEventList, (o1, o2) -> Convert.convert(LocalDateTime.class,o2.get("publishTime")).compareTo(Convert.convert(LocalDateTime.class,o1.get("publishTime"))));
                result.put("promise",tempEventList);
            }
        }

    }


    /**
     * findDispatchEventList
     *
     * @param promiseId promiseId
     * @param result    结果
     */
    private void findDispatchEventList(Long promiseId,Map<String, List<Map<String, Object>>> result){
        JdhDispatch dispatch = dispatchRepository.findValidDispatch(DispatchRepQuery.builder().promiseId(promiseId).build());
        if(Objects.nonNull(dispatch)){
            List<Map<String, Object>> eventList = queryEventListByAggregateId(dispatch.getDispatchId().toString());
            if(CollUtil.isNotEmpty(eventList)){
                for (Map<String, Object> eventMap : eventList) {
                    DispatchEventTypeEnum eventType = DispatchEventTypeEnum.getByCode(Convert.convert(String.class, eventMap.get("eventCode")));
                    if(Objects.nonNull(eventType)){
                        eventMap.put("eventCodeDesc","【dispatch】" + eventType.getDesc() + "(" + eventType.getCode() + ")");
                    }
                }
                CollUtil.sort(eventList, (o1, o2) -> Convert.convert(LocalDateTime.class,o2.get("publishTime")).compareTo(Convert.convert(LocalDateTime.class,o1.get("publishTime"))));
                result.put("dispatch",eventList);
            }

        }
    }

    /**
     * findMedPromiseEventList
     *
     * @param promiseId promiseId
     * @param result    结果
     */
    private void findMedPromiseEventList(Long promiseId,Map<String, List<Map<String, Object>>> result){
        List<MedicalPromiseDTO> dtoList = medicalPromiseApplication.queryMedicalPromiseList(MedicalPromiseListRequest.builder().promiseIdList(CollUtil.newArrayList(promiseId)).build());
        if(CollUtil.isNotEmpty(dtoList)){
            List<Map<String,Object>> tempEventList = new ArrayList<>();
            dtoList.forEach(ele -> {
                tempEventList.addAll(queryEventListByAggregateId(ele.getMedicalPromiseId().toString()));
            });

            if(CollUtil.isNotEmpty(tempEventList)){
                for (Map<String, Object> eventMap : tempEventList) {
                    MedPromiseEventTypeEnum eventType = MedPromiseEventTypeEnum.getByCode(Convert.convert(String.class, eventMap.get("eventCode")));
                    if(Objects.nonNull(eventType)){
                        eventMap.put("eventCodeDesc","【medPromise】" + eventType.getDesc() + "(" + eventType.getCode() + ")");
                    }
                }
                CollUtil.sort(tempEventList, (o1, o2) -> Convert.convert(LocalDateTime.class,o2.get("publishTime")).compareTo(Convert.convert(LocalDateTime.class,o1.get("publishTime"))));
                result.put("medPromise",tempEventList);
            }
        }
    }

    /**
     * findAngelPromiseEventList
     *
     * @param promiseId promiseId
     * @param result    结果
     */
    private void findAngelPromiseEventList(Long promiseId,Map<String, List<Map<String, Object>>> result){
        LambdaQueryWrapper<JdhAngelWorkPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(JdhAngelWorkPo::getPromiseId,promiseId);
        List<JdhAngelWorkPo> jdhAngelWorkPos = jdhAngelWorkPoMapper.selectList(queryWrapper);
        if(CollUtil.isNotEmpty(jdhAngelWorkPos)){
            List<Map<String,Object>> workTempEventList = new ArrayList<>();
            List<Map<String,Object>> taskTempEventList = new ArrayList<>();
            List<Map<String,Object>> shipTempEventList = new ArrayList<>();
            jdhAngelWorkPos.forEach(ele -> {
                //work
                Long workId = ele.getWorkId();
                workTempEventList.addAll(queryEventListByAggregateId(workId.toString()));

                //task
                LambdaQueryWrapper<JdhAngelTaskPo> taskQueryWrapper = new LambdaQueryWrapper<>();
                taskQueryWrapper.eq(JdhAngelTaskPo::getWorkId,workId);
                List<JdhAngelTaskPo> angelTaskPos = jdhAngelTaskPoMapper.selectList(taskQueryWrapper);
                if(CollUtil.isNotEmpty(angelTaskPos)){
                    angelTaskPos.forEach(task -> taskTempEventList.addAll(queryEventListByAggregateId(task.getTaskId().toString())));
                }

                //ship
                LambdaQueryWrapper<JdhAngelShipPo> shipQueryWrapper = new LambdaQueryWrapper<>();
                shipQueryWrapper.eq(JdhAngelShipPo::getWorkId,workId);
                List<JdhAngelShipPo> angelShipPos = jdhAngelShipPoMapper.selectList(shipQueryWrapper);
                if(CollUtil.isNotEmpty(angelShipPos)){
                    angelShipPos.forEach(ship -> shipTempEventList.addAll(queryEventListByAggregateId(ship.getShipId().toString())));
                }
            });

            if(CollUtil.isNotEmpty(workTempEventList)){
                for (Map<String, Object> eventMap : workTempEventList) {
                    if(AngelWorkAggregateEnum.WORK.getCode().equals(Convert.convert(String.class,eventMap.get("aggregateCode")))){
                        AngelWorkEventTypeEnum eventType = AngelWorkEventTypeEnum.getEnumByEvent(Convert.convert(String.class, eventMap.get("eventCode")));
                        if(Objects.nonNull(eventType)){
                            eventMap.put("eventCodeDesc","【angelWork】" + eventType.getDesc() + "(" + eventType.getCode() + ")");
                        }
                    }
                    if(AngelWorkAggregateEnum.TASK.getCode().equals(Convert.convert(String.class,eventMap.get("aggregateCode")))){
                        AngelTaskEventTypeEnum eventType = AngelTaskEventTypeEnum.getEnumByCode(Convert.convert(String.class, eventMap.get("eventCode")));
                        if(Objects.nonNull(eventType)){
                            eventMap.put("eventCodeDesc","【angelWork】" + eventType.getDesc() + "(" + eventType.getCode() + ")");
                        }
                    }

                }
                CollUtil.sort(workTempEventList, (o1, o2) -> Convert.convert(LocalDateTime.class,o2.get("publishTime")).compareTo(Convert.convert(LocalDateTime.class,o1.get("publishTime"))));
                result.put("angelWork",workTempEventList);
            }

            if(CollUtil.isNotEmpty(taskTempEventList)){
                for (Map<String, Object> eventMap : taskTempEventList) {
                    AngelTaskEventTypeEnum eventType = AngelTaskEventTypeEnum.getEnumByCode(Convert.convert(String.class, eventMap.get("eventCode")));
                    if(Objects.isNull(eventType)){
                        AngelTaskBizExtEventTypeEnum eventEnum = AngelTaskBizExtEventTypeEnum.getEnumByEvent(Convert.convert(String.class, eventMap.get("eventCode")));
                        if(Objects.nonNull(eventEnum)){
                            eventMap.put("eventCodeDesc","【angelTask】" + eventEnum.getDesc() + "(" + eventEnum.getCode() + ")");
                        }
                    }else{
                        eventMap.put("eventCodeDesc","【angelTask】" + eventType.getDesc() + "(" + eventType.getCode() + ")");
                    }
                }
                CollUtil.sort(taskTempEventList, (o1, o2) -> Convert.convert(LocalDateTime.class,o2.get("publishTime")).compareTo(Convert.convert(LocalDateTime.class,o1.get("publishTime"))));
                result.put("angelTask",taskTempEventList);
            }

            if(CollUtil.isNotEmpty(shipTempEventList)){
                for (Map<String, Object> eventMap : shipTempEventList) {
                    AngelShipEventTypeEnum eventType = AngelShipEventTypeEnum.getEnumByEvent(Convert.convert(String.class, eventMap.get("eventCode")));
                    if(Objects.nonNull(eventType)){
                        eventMap.put("eventCodeDesc","【angelShip】" + eventType.getDesc() + "(" + eventType.getCode() + ")");
                    }
                }
                CollUtil.sort(shipTempEventList, (o1, o2) -> Convert.convert(LocalDateTime.class,o2.get("publishTime")).compareTo(Convert.convert(LocalDateTime.class,o1.get("publishTime"))));
                result.put("angelShip",shipTempEventList);
            }
        }
    }

    /**
     * 获取promiseId
     *
     * @param domainCode    域代码
     * @param aggregateCode 聚合码
     * @param aggregateId   聚合id
     * @return {@link Long}
     */
    private Long detectPromiseId(String domainCode,String aggregateCode,String aggregateId){
        Long promiseId = null;
        //trade
        if (DomainEnum.TRADE.getCode().equals(domainCode)) {
            if(TradeAggregateEnum.ORDER.getCode().equals(aggregateCode)){
                List<JdOrder> orderList = jdOrderRepository.findOrderListByOrderId(JdOrderIdentifier.builder().orderId(Long.parseLong(aggregateId)).build());
                if(CollUtil.isNotEmpty(orderList)){
                    //确定业务身份
                    String verticalCode = "";
                    for (JdOrder jdOrder : orderList) {
                        if(StrUtil.isNotBlank(jdOrder.getVerticalCode())){
                            verticalCode = jdOrder.getVerticalCode();
                            break;
                        }
                    }
                    //获取业务模式
                    if(StrUtil.isNotBlank(verticalCode)){
                        LambdaQueryWrapper<JdhVerticalBusinessPo> queryWrapper = new LambdaQueryWrapper<>();
                        queryWrapper.eq(JdhVerticalBusinessPo::getVerticalCode,verticalCode);
                        JdhVerticalBusinessPo businessPo = jdhVerticalBusinessPoMapper.selectOne(queryWrapper);
                        if(Objects.nonNull(businessPo)){
                            String sourceVoucherId = "";
                            if(BusinessModeEnum.SELF_TEST.getCode().equals(businessPo.getBusinessModeCode())
                                    || BusinessModeEnum.ANGEL_CARE.getCode().equals(businessPo.getBusinessModeCode())
                                    || BusinessModeEnum.ANGEL_TEST.getCode().equals(businessPo.getBusinessModeCode())
                                    || BusinessModeEnum.ANGEL_TEST_NO_LABORATORY.getCode().equals(businessPo.getBusinessModeCode())) {
                                for (JdOrder jdOrder : orderList) {
                                    //如果有父订单，并且父订单有效
                                    if (Objects.nonNull(jdOrder.getParentId()) && jdOrder.getParentId() > 0) {
                                        sourceVoucherId = jdOrder.getParentId().toString();
                                        break;
                                    }else{
                                        sourceVoucherId = jdOrder.getOrderId().toString();
                                    }
                                }
                            }else{
                                List<JdOrderItem> jdOrderItems = jdOrderItemRepository.listByOrderId(orderList.get(0).getOrderId());
                                if(CollUtil.isNotEmpty(jdOrderItems)){
                                    sourceVoucherId = jdOrderItems.get(0).getOrderItemId().toString();
                                }
                            }
                            JdhPromise promise = promiseRepository.findPromise(PromiseRepQuery.builder().sourceVoucherId(sourceVoucherId).build());
                            if(Objects.nonNull(promise)){
                                promiseId = promise.getPromiseId();
                            }
                        }
                    }
                }
            }
        }
        //promise
        if (DomainEnum.PROMISE.getCode().equals(domainCode)) {
            if(PromiseAggregateEnum.VOUCHER.getCode().equals(aggregateCode)){
                JdhPromise promise = promiseRepository.findPromise(PromiseRepQuery.builder().voucherIds(CollUtil.newArrayList(Long.parseLong(aggregateId))).build());
                if(Objects.nonNull(promise)){
                    promiseId = promise.getPromiseId();
                }
            }
            if(PromiseAggregateEnum.PROMISE.getCode().equals(aggregateCode)){
                promiseId = Long.parseLong(aggregateId);
            }
        }
        //angelPromise
        if (DomainEnum.ANGEL_PROMISE.getCode().equals(domainCode)) {
            if(AngelWorkAggregateEnum.WORK.getCode().equals(aggregateCode)){
                LambdaQueryWrapper<JdhAngelWorkPo> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(JdhAngelWorkPo::getWorkId,aggregateId);
                List<JdhAngelWorkPo> jdhAngelWorkPos = jdhAngelWorkPoMapper.selectList(queryWrapper);
                if(CollUtil.isNotEmpty(jdhAngelWorkPos)){
                    promiseId = jdhAngelWorkPos.get(0).getPromiseId();
                }
            }
            if(AngelWorkAggregateEnum.TASK.getCode().equals(aggregateCode)){
                LambdaQueryWrapper<JdhAngelTaskPo> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(JdhAngelTaskPo::getTaskId,aggregateId);
                List<JdhAngelTaskPo> angelTaskPos = jdhAngelTaskPoMapper.selectList(queryWrapper);
                if(CollUtil.isNotEmpty(angelTaskPos)){
                    Long workId = angelTaskPos.get(0).getWorkId();
                    LambdaQueryWrapper<JdhAngelWorkPo> workQueryWrapper = new LambdaQueryWrapper<>();
                    workQueryWrapper.eq(JdhAngelWorkPo::getWorkId,workId);
                    List<JdhAngelWorkPo> jdhAngelWorkPos = jdhAngelWorkPoMapper.selectList(workQueryWrapper);
                    if(CollUtil.isNotEmpty(jdhAngelWorkPos)){
                        promiseId = jdhAngelWorkPos.get(0).getPromiseId();
                    }
                }
            }
            if(AngelWorkAggregateEnum.SHIP.getCode().equals(aggregateCode)){
                LambdaQueryWrapper<JdhAngelShipPo> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(JdhAngelShipPo::getShipId,aggregateId);
                List<JdhAngelShipPo> angelShipPos = jdhAngelShipPoMapper.selectList(queryWrapper);
                if(CollUtil.isNotEmpty(angelShipPos)){
                    Long workId = angelShipPos.get(0).getWorkId();
                    LambdaQueryWrapper<JdhAngelWorkPo> workQueryWrapper = new LambdaQueryWrapper<>();
                    workQueryWrapper.eq(JdhAngelWorkPo::getWorkId,workId);
                    List<JdhAngelWorkPo> jdhAngelWorkPos = jdhAngelWorkPoMapper.selectList(workQueryWrapper);
                    if(CollUtil.isNotEmpty(jdhAngelWorkPos)){
                        promiseId = jdhAngelWorkPos.get(0).getPromiseId();
                    }
                }
            }
        }
        //dispatch
        if (DomainEnum.DISPATCH.getCode().equals(domainCode)) {
            JdhDispatch dispatch = dispatchRepository.findValidDispatch(DispatchRepQuery.builder().dispatchId(Long.parseLong(aggregateId)).build());
            if(Objects.nonNull(dispatch)){
                promiseId = dispatch.getPromiseId();
            }
        }
        //medPromise
        if (DomainEnum.MED_PROMISE.getCode().equals(domainCode)) {
            MedicalPromiseDTO medicalPromiseDTO = medicalPromiseApplication.queryMedicalPromise(MedicalPromiseRequest.builder().medicalPromiseId(Long.parseLong(aggregateId)).build());
            if(Objects.nonNull(medicalPromiseDTO)){
                promiseId = medicalPromiseDTO.getPromiseId();
            }
        }
        //settlement
        if (DomainEnum.SETTLE_MENT.getCode().equals(domainCode)) {
            if(SettleAggregateEnum.SETTLE_ANGEL.getCode().equals(aggregateCode)){
                LambdaQueryWrapper<AngelSettlementPo> queryWrapper = Wrappers.lambdaQuery();
                queryWrapper.eq(AngelSettlementPo::getSettleId, Long.parseLong(aggregateId));
                List<AngelSettlementPo> poList = angelSettlementMapper.selectList(queryWrapper);
                if(CollUtil.isNotEmpty(poList)){
                    promiseId = poList.get(0).getPromiseId();
                }
            }
        }

        return promiseId;
    }

    /**
     * 查询域列表
     *
     * @return {@link List}<{@link Map}<{@link String}, {@link String}>>
     */
    @Override
    public List<Map<String, String>> queryDomainList() {
        List<Map<String, String>> result = new ArrayList<>();
        for (DomainEnum domainEnum : DomainEnum.values()) {
            Map<String, String> ele = new HashMap<>();
            ele.put("label",domainEnum.getName());
            ele.put("value",domainEnum.getCode());
            result.add(ele);
        }
        return result;
    }

    /**
     * 按域查询聚合列表
     *
     * @param domainCode 域代码
     * @return {@link List}<{@link Map}<{@link String}, {@link String}>>
     */
    @Override
    public List<Map<String, String>> queryAggregateListByDomainCode(String domainCode) {
        DomainEnum domainEnum = DomainEnum.getByCode(domainCode);
        if(Objects.isNull(domainEnum)){
            return null;
        }
        List<Map<String, String>> result = new ArrayList<>();
        switch (domainEnum){
            case BASE:
                Arrays.stream(SupportAggregateEnum.values()).forEach(ele -> {
                    Map<String, String> eleMap = new HashMap<>();
                    eleMap.put("label",ele.getCode());
                    eleMap.put("value",ele.getCode());
                    result.add(eleMap);
                });
                break;
            case PROMISE:
                Arrays.stream(PromiseAggregateEnum.values()).forEach(ele -> {
                    Map<String, String> eleMap = new HashMap<>();
                    eleMap.put("label",ele.getCode());
                    eleMap.put("value",ele.getCode());
                    result.add(eleMap);
                });
                break;
            case PROVIDER:
                Arrays.stream(ProviderAggregateEnum.values()).forEach(ele -> {
                    Map<String, String> eleMap = new HashMap<>();
                    eleMap.put("label",ele.getCode());
                    eleMap.put("value",ele.getCode());
                    result.add(eleMap);
                });
                break;
            case TRADE:
                Arrays.stream(TradeAggregateEnum.values()).forEach(ele -> {
                    Map<String, String> eleMap = new HashMap<>();
                    eleMap.put("label",ele.getCode());
                    eleMap.put("value",ele.getCode());
                    result.add(eleMap);
                });
                break;
            case PRODUCT:
                Arrays.stream(ProductAggregateCodeEnum.values()).forEach(ele -> {
                    Map<String, String> eleMap = new HashMap<>();
                    eleMap.put("label",ele.getCode());
                    eleMap.put("value",ele.getCode());
                    result.add(eleMap);
                });
                break;
//            case SUPPORT:
//                Arrays.stream(SupportAggregateEnum.values()).forEach(ele -> {
//                    Map<String, String> eleMap = new HashMap<>();
//                    eleMap.put("label",ele.getCode());
//                    eleMap.put("value",ele.getCode());
//                    result.add(eleMap);
//                });
//                break;
            case MED_PROMISE:
                Arrays.stream(MedPromiseAggregateEnum.values()).forEach(ele -> {
                    Map<String, String> eleMap = new HashMap<>();
                    eleMap.put("label",ele.getCode());
                    eleMap.put("value",ele.getCode());
                    result.add(eleMap);
                });
                break;
            case DISPATCH:
                Arrays.stream(DispatchAggregateEnum.values()).forEach(ele -> {
                    Map<String, String> eleMap = new HashMap<>();
                    eleMap.put("label",ele.getCode());
                    eleMap.put("value",ele.getCode());
                    result.add(eleMap);
                });
                break;
            case ANGEL:
                Arrays.stream(AngelAggregateEnum.values()).forEach(ele -> {
                    Map<String, String> eleMap = new HashMap<>();
                    eleMap.put("label",ele.getCode());
                    eleMap.put("value",ele.getCode());
                    result.add(eleMap);
                });
                break;
            case ANGEL_PROMISE:
                Arrays.stream(AngelWorkAggregateEnum.values()).forEach(ele -> {
                    Map<String, String> eleMap = new HashMap<>();
                    eleMap.put("label",ele.getCode());
                    eleMap.put("value",ele.getCode());
                    result.add(eleMap);
                });
                break;
            case SETTLE_MENT:
                Arrays.stream(SettleAggregateEnum.values()).forEach(ele -> {
                    Map<String, String> eleMap = new HashMap<>();
                    eleMap.put("label",ele.getCode());
                    eleMap.put("value",ele.getCode());
                    result.add(eleMap);
                });
                break;
        }

        return result;
    }

    /**
     * 分页查询业务模式
     *
     * @param request 请求
     * @return {@link PageDto}<{@link Map}<{@link String}, {@link Object}>>
     */
    @Override
    public PageDto<Map<String, Object>> queryBusinessModePage(BusinessModePageRequest request) {
        Page<JdhBusinessModePo> page = new Page<>(request.getPageNum(),request.getPageSize());
        LambdaQueryWrapper<JdhBusinessModePo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.like(StrUtil.isNotEmpty(request.getCode()),JdhBusinessModePo::getCode,request.getCode())
                .eq(JdhBusinessModePo::getYn,YnStatusEnum.YES.getCode())
                .orderByDesc(JdhBusinessModePo::getCreateTime);
        Page<JdhBusinessModePo> verticalBusinessPoPage = jdhBusinessModePoMapper.selectPage(page, queryWrapper);

        PageDto<Map<String, Object>> pageDto = new PageDto<>();
        pageDto.setTotalPage(verticalBusinessPoPage.getPages());
        pageDto.setPageNum(verticalBusinessPoPage.getCurrent());
        pageDto.setPageSize(verticalBusinessPoPage.getSize());
        pageDto.setTotalCount(verticalBusinessPoPage.getTotal());
        List<Map<String, Object>> recordsList = new ArrayList<>();
        List<JdhBusinessModePo> records = verticalBusinessPoPage.getRecords();
        if(CollUtil.isNotEmpty(records)){
            records.forEach(ele -> recordsList.add(BeanUtil.beanToMap(ele)));
        }
        pageDto.setList(recordsList);
        return pageDto;
    }

    /**
     * 查询服务类型
     *
     * @param request 请求
     * @return {@link PageDto}<{@link Map}<{@link String}, {@link Object}>>
     */
    @Override
    public PageDto<Map<String, Object>> queryServiceTypePage(ServiceTypePageRequest request) {
        Page<JdhServiceTypePo> page = new Page<>(request.getPageNum(),request.getPageSize());
        LambdaQueryWrapper<JdhServiceTypePo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.like(StrUtil.isNotEmpty(request.getServiceType()),JdhServiceTypePo::getServiceType,request.getServiceType())
                .eq(JdhServiceTypePo::getYn,YnStatusEnum.YES.getCode())
                .orderByDesc(JdhServiceTypePo::getCreateTime);
        Page<JdhServiceTypePo> verticalBusinessPoPage = jdhServiceTypePoMapper.selectPage(page, queryWrapper);

        PageDto<Map<String, Object>> pageDto = new PageDto<>();
        pageDto.setTotalPage(verticalBusinessPoPage.getPages());
        pageDto.setPageNum(verticalBusinessPoPage.getCurrent());
        pageDto.setPageSize(verticalBusinessPoPage.getSize());
        pageDto.setTotalCount(verticalBusinessPoPage.getTotal());
        List<Map<String, Object>> recordsList = new ArrayList<>();
        List<JdhServiceTypePo> records = verticalBusinessPoPage.getRecords();
        if(CollUtil.isNotEmpty(records)){
            records.forEach(ele -> recordsList.add(BeanUtil.beanToMap(ele)));
        }
        pageDto.setList(recordsList);
        return pageDto;
    }

    /**
     * 查询服务类型 - 类目
     *
     * @param request 请求
     * @return {@link PageDto}<{@link Map}<{@link String}, {@link Object}>>
     */
    @Override
    public List<Map<String, Object>> queryServiceTypeCategoryRelation(ServiceTypePageRequest request) {
        LambdaQueryWrapper<JdhServiceTypeCategoryRelationPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.like(StrUtil.isNotEmpty(request.getServiceType()),JdhServiceTypeCategoryRelationPo::getServiceType,request.getServiceType())
                .eq(JdhServiceTypeCategoryRelationPo::getYn,YnStatusEnum.YES.getCode())
                .orderByDesc(JdhServiceTypeCategoryRelationPo::getCreateTime);
        List<JdhServiceTypeCategoryRelationPo> categoryRelationPos = jdhServiceTypeCategoryRelationPoMapper.selectList(queryWrapper);
        List<Map<String, Object>> result = new ArrayList<>();
        if(CollUtil.isNotEmpty(categoryRelationPos)){
            categoryRelationPos.forEach(ele -> result.add(BeanUtil.beanToMap(ele)));
        }
        return result;
    }

    /**
     * queryReachTemplatePage
     *
     * @param request 请求
     * @return {@link PageDto}<{@link Map}<{@link String}, {@link Object}>>
     */
    @Override
    public PageDto<Map<String, Object>> queryReachTemplatePage(ReachTemplatePageRequest request) {
        Page<JdhReachTemplatePo> page = new Page<>(request.getPageNum(),request.getPageSize());
        LambdaQueryWrapper<JdhReachTemplatePo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StrUtil.isNotBlank(request.getTemplateName()),JdhReachTemplatePo::getTemplateName,request.getTemplateName())
                        .like(StrUtil.isNotBlank(request.getContent()),JdhReachTemplatePo::getContent,request.getContent())
                        .eq(StrUtil.isNotBlank(request.getChannelTemplateId()),JdhReachTemplatePo::getChannelTemplateId,request.getChannelTemplateId())
                        .eq(StrUtil.isNotBlank(request.getAccountId()),JdhReachTemplatePo::getAccountId,request.getAccountId())
                        .eq(StrUtil.isNotBlank(request.getMessageBizType()),JdhReachTemplatePo::getMessageBizType,request.getMessageBizType())
                        .eq(Objects.nonNull(request.getTemplateId()),JdhReachTemplatePo::getTemplateId,request.getTemplateId())
                        .eq(JdhReachTemplatePo::getYn,YnStatusEnum.YES.getCode())
                        .orderByDesc(JdhReachTemplatePo::getCreateTime);
        Page<JdhReachTemplatePo> poPage = reachTemplatePoMapper.selectPage(page, queryWrapper);

        PageDto<Map<String, Object>> pageDto = new PageDto<>();
        pageDto.setPageNum(poPage.getCurrent());
        pageDto.setTotalCount(poPage.getTotal());
        pageDto.setPageSize(poPage.getSize());
        pageDto.setTotalPage(poPage.getPages());
        if(CollUtil.isNotEmpty(poPage.getRecords())){
            pageDto.setList(poPage.getRecords().stream().map(BeanUtil::beanToMap).collect(Collectors.toList()));
        }else{
            pageDto.setList(Lists.newArrayList());
        }
        return pageDto;
    }

    /**
     * 查询触达消息类型列表
     *
     * @return {@link List}<{@link Map}<{@link String},{@link Object}>>
     */
    @Override
    public List<Map<String, Object>> queryReachMessageTypeList() {
        List<ReachMessageBizType> all = jdhReachMessageTypeRepository.findAll();
        List<Map<String, Object>> result = new ArrayList<>();
        all.forEach(ele -> {
            Map<String, Object> bean = BeanUtil.beanToMap(ele);
            bean.put("label",ele.getMessageTitle());
            bean.put("value",ele.getMessageBizType());
            result.add(bean);
        });
        return result;
    }

    /**
     * 查询触达账号列表
     *
     * @return {@link List}<{@link Map}<{@link String},{@link Object}>>
     */
    @Override
    public List<Map<String, Object>> queryReachAccountList() {
        List<JdhReachChannelAccount> all = jdhReachConfigRepository.findAll();
        List<Map<String, Object>> result = new ArrayList<>();
        all.forEach(ele -> {
            Map<String, Object> bean = BeanUtil.beanToMap(ele);
            bean.put("label",ele.getDesc());
            bean.put("value",ele.getAccountId());
            result.add(bean);
        });
        return result;
    }

    /**
     * queryReachTaskPage
     *
     * @param request 请求
     * @return {@link PageDto}<{@link Map}<{@link String}, {@link Object}>>
     */
    @Override
    public PageDto<Map<String, Object>> queryReachTaskPage(ReachTaskPageRequest request) {
        Page<JdhReachTaskPo> page = new Page<>(request.getPageNum(),request.getPageSize());
        LambdaQueryWrapper<JdhReachTaskPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(Objects.nonNull(request.getTaskId()),JdhReachTaskPo::getTaskId,request.getTaskId())
                .like(StrUtil.isNotBlank(request.getDomainCode()),JdhReachTaskPo::getDomainCode,request.getDomainCode())
                .eq(StrUtil.isNotBlank(request.getAggregateCode()),JdhReachTaskPo::getAggregateCode,request.getAggregateCode())
                .eq(StrUtil.isNotBlank(request.getEventCode()),JdhReachTaskPo::getEventCode,request.getEventCode())
                .eq(StrUtil.isNotBlank(request.getAggregateId()),JdhReachTaskPo::getAggregateId,request.getAggregateId())
                .eq(Objects.nonNull(request.getStatus()),JdhReachTaskPo::getStatus,request.getStatus())
                .eq(Objects.nonNull(request.getType()),JdhReachTaskPo::getType,request.getType())
                .eq(Objects.nonNull(request.getTemplateId()),JdhReachTaskPo::getTemplateId,request.getTemplateId())
                .eq(Objects.nonNull(request.getEventId()),JdhReachTaskPo::getEventId,request.getEventId())
                .like(StrUtil.isNotBlank(request.getSelectUserFunctionId()),JdhReachTaskPo::getSelectUserFunctionId,request.getSelectUserFunctionId())
                .eq(JdhReachTaskPo::getYn,YnStatusEnum.YES.getCode())
                .orderByDesc(JdhReachTaskPo::getCreateTime);
        Page<JdhReachTaskPo> poPage = reachTaskPoMapper.selectPage(page, queryWrapper);

        PageDto<Map<String, Object>> pageDto = new PageDto<>();
        pageDto.setPageNum(poPage.getCurrent());
        pageDto.setTotalCount(poPage.getTotal());
        pageDto.setPageSize(poPage.getSize());
        pageDto.setTotalPage(poPage.getPages());
        if(CollUtil.isNotEmpty(poPage.getRecords())){
            pageDto.setList(poPage.getRecords().stream().map(BeanUtil::beanToMap).collect(Collectors.toList()));
        }else{
            pageDto.setList(Lists.newArrayList());
        }
        return pageDto;
    }

    /**
     * queryReachTriggerPage
     *
     * @param request 请求
     * @return {@link PageDto}<{@link Map}<{@link String}, {@link Object}>>
     */
    @Override
    public PageDto<Map<String, Object>> queryReachTriggerPage(ReachTriggerPageRequest request) {
        Page<JdhReachTriggerPo> page = new Page<>(request.getPageNum(),request.getPageSize());
        LambdaQueryWrapper<JdhReachTriggerPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Objects.nonNull(request.getTriggerId()),JdhReachTriggerPo::getTriggerId,request.getTriggerId())
                .eq(StrUtil.isNotBlank(request.getDomainCode()),JdhReachTriggerPo::getDomainCode,request.getDomainCode())
                .like(StrUtil.isNotBlank(request.getTriggerName()),JdhReachTriggerPo::getTriggerName,request.getTriggerName())
                .eq(StrUtil.isNotBlank(request.getAggregateCode()),JdhReachTriggerPo::getAggregateCode,request.getAggregateCode())
                .eq(StrUtil.isNotBlank(request.getEventCode()),JdhReachTriggerPo::getEventCode,request.getEventCode())
                .eq(Objects.nonNull(request.getTemplateId()),JdhReachTriggerPo::getTemplateId,request.getTemplateId())
                .like(StrUtil.isNotBlank(request.getSelectUserFunctionId()),JdhReachTriggerPo::getSelectUserFunctionId,request.getSelectUserFunctionId())
                .eq(JdhReachTriggerPo::getYn,YnStatusEnum.YES.getCode())
                .orderByDesc(JdhReachTriggerPo::getCreateTime);
        Page<JdhReachTriggerPo> poPage = reachTriggerPoMapper.selectPage(page, queryWrapper);

        PageDto<Map<String, Object>> pageDto = new PageDto<>();
        pageDto.setPageNum(poPage.getCurrent());
        pageDto.setTotalCount(poPage.getTotal());
        pageDto.setPageSize(poPage.getSize());
        pageDto.setTotalPage(poPage.getPages());
        if(CollUtil.isNotEmpty(poPage.getRecords())){
            pageDto.setList(poPage.getRecords().stream().map(BeanUtil::beanToMap).collect(Collectors.toList()));
        }else{
            pageDto.setList(Lists.newArrayList());
        }
        return pageDto;
    }

    /**
     * queryReachMessagePage
     *
     * @param request 请求
     * @return {@link PageDto}<{@link Map}<{@link String}, {@link Object}>>
     */
    @Override
    public PageDto<Map<String, Object>> queryReachMessagePage(ReachMessagePageRequest request) {
        Page<JdhReachMessagePo> page = new Page<>(request.getPageNum(),request.getPageSize());
        LambdaQueryWrapper<JdhReachMessagePo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Objects.nonNull(request.getMessageId()),JdhReachMessagePo::getMessageId,request.getMessageId())
                .eq(StrUtil.isNotBlank(request.getDomainCode()),JdhReachMessagePo::getDomainCode,request.getDomainCode())
                .eq(StrUtil.isNotBlank(request.getAggregateCode()),JdhReachMessagePo::getAggregateCode,request.getAggregateCode())
                .eq(StrUtil.isNotBlank(request.getEventCode()),JdhReachMessagePo::getEventCode,request.getEventCode())
                .eq(StrUtil.isNotBlank(request.getUserPin()),JdhReachMessagePo::getUserPin,request.getUserPin())
                .eq(StrUtil.isNotBlank(request.getAngelId()),JdhReachMessagePo::getAngelId,request.getAngelId())
                .eq(StrUtil.isNotBlank(request.getAppId()),JdhReachMessagePo::getAppId,request.getAppId())
                .eq(StrUtil.isNotBlank(request.getMessageBizType()),JdhReachMessagePo::getMessageBizType,request.getMessageBizType())
                .like(StrUtil.isNotBlank(request.getMessageTitle()),JdhReachMessagePo::getMessageTitle,request.getMessageTitle())
                .like(StrUtil.isNotBlank(request.getMessagePayload()),JdhReachMessagePo::getMessagePayload,request.getMessagePayload())
                .like(StrUtil.isNotBlank(request.getExtras()),JdhReachMessagePo::getExtras,request.getExtras())
                .like(StrUtil.isNotBlank(request.getUrl()),JdhReachMessagePo::getUrl,request.getUrl())
                .eq(Objects.nonNull(request.getReachType()),JdhReachMessagePo::getReachType,request.getReachType())
                .eq(Objects.nonNull(request.getTaskId()),JdhReachMessagePo::getTaskId,request.getTaskId())
                .eq(Objects.nonNull(request.getEventId()),JdhReachMessagePo::getEventId,request.getEventId())
                .eq(JdhReachMessagePo::getYn,YnStatusEnum.YES.getCode())
                .orderByDesc(JdhReachMessagePo::getCreateTime);

        Page<JdhReachMessagePo> poPage = reachMessagePoMapper.selectPage(page, queryWrapper);

        PageDto<Map<String, Object>> pageDto = new PageDto<>();
        pageDto.setPageNum(poPage.getCurrent());
        pageDto.setTotalCount(poPage.getTotal());
        pageDto.setPageSize(poPage.getSize());
        pageDto.setTotalPage(poPage.getPages());
        if(CollUtil.isNotEmpty(poPage.getRecords())){
            pageDto.setList(poPage.getRecords().stream().map(BeanUtil::beanToMap).collect(Collectors.toList()));
        }else{
            pageDto.setList(Lists.newArrayList());
        }
        return pageDto;
    }

    /**
     * 创建触达模板
     *
     * @param cmd cmd
     * @return {@link Boolean}
     */
    @Override
    public Boolean createReachTemplate(CreateReachTemplateCmd cmd) {
        return reachApplication.createReachTemplate(cmd);
    }

    /**
     * updateReachTemplate
     *
     * @param cmd cmd
     * @return {@link Boolean}
     */
    @Override
    public Boolean updateReachTemplate(UpdateReachTemplateCmd cmd) {
        return reachApplication.updateReachTemplate(cmd);
    }

    /**
     * deleteReachTrigger
     *
     * @param cmd cmd
     * @return {@link Boolean}
     */
    @Override
    public Boolean deleteReachTrigger(DeleteReachTriggerCmd cmd) {
        return reachApplication.deleteReachTrigger(cmd);
    }

    /**
     * updateReachTrigger
     *
     * @param cmd cmd
     * @return {@link Boolean}
     */
    @Override
    public Boolean updateReachTrigger(UpdateReachTriggerCmd cmd) {
        return reachApplication.updateReachTrigger(cmd);
    }

    /**
     * createReachTrigger
     *
     * @param cmd cmd
     * @return {@link Boolean}
     */
    @Override
    public Boolean createReachTrigger(CreateReachTriggerCmd cmd) {
        return reachApplication.createReachTrigger(cmd);
    }

    /**
     * deleteReachTemplate
     *
     * @param cmd cmd
     * @return {@link Boolean}
     */
    @Override
    public Boolean deleteReachTemplate(DeleteReachTemplateCmd cmd) {
        return reachApplication.deleteReachTemplate(cmd);
    }

    /**
     * queryTriggerSelectUserFunIdList
     *
     * @return {@link List}<{@link String}>
     */
    @Override
    public List<String> queryTriggerSelectUserFunIdList() {
        Map<String, ReachServiceSelectUserExt> beans = SpringUtil.getBeansOfType(ReachServiceSelectUserExt.class);
        if(MapUtil.isNotEmpty(beans)){
            return beans.values().stream().map(ReachServiceSelectUserExt::functionId).collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    /**
     * queryTriggerSelectDataFunIdList
     *
     * @return {@link List}<{@link String}>
     */
    @Override
    public List<String> queryTriggerSelectDataFunIdList() {
        Map<String, ReachServiceSelectDataExt> beans = SpringUtil.getBeansOfType(ReachServiceSelectDataExt.class);
        if(MapUtil.isNotEmpty(beans)){
            return beans.values().stream().map(ReachServiceSelectDataExt::functionId).collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    /**
     * 批量绑定样本
     *
     * @param cmd cmd
     * @return {@link Boolean}
     */
    @Override
    public Boolean batchBindSpecimenCode(MedicalPromiseBindSpecimenCodeCmd cmd) {
        return medicalPromiseApplication.batchBindSpecimenCode(cmd);
    }

    /**
     * 查询报告网址
     *
     * @param medPromiseId medPromiseId
     * @return {@link Map }<{@link String }, {@link String }>
     */
    @Override
    public Map<String, String> queryReportUrl(Long medPromiseId) {
        if(medPromiseId == null){
            return new HashMap<>();
        }
        LambdaQueryWrapper<MedicalReportPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(MedicalReportPo::getMedicalPromiseId, medPromiseId)
                .eq(MedicalReportPo::getYn,YnStatusEnum.YES.getCode());
        MedicalReportPo medicalReport =  medicalReportMapper.selectOne(queryWrapper);
        if(medicalReport == null){
            return new HashMap<>();
        }

        Map<String, String> result = new HashMap();
        String reportOss = medicalReport.getReportOss();
        if(StrUtil.isNotBlank(reportOss)){
            String publicUrl = fileManageService.getPublicUrl(reportOss, true, DateUtil.offsetDay(new Date(), 1));
            result.put("reportOss", publicUrl);
        }

        String sourceOss = medicalReport.getSourceOss();
        if(StrUtil.isNotBlank(sourceOss)){
            String publicUrl = fileManageService.getPublicUrl(sourceOss, true, DateUtil.offsetDay(new Date(), 1));
            result.put("sourceOss", publicUrl);
        }

        String structReportOss = medicalReport.getStructReportOss();
        if(StrUtil.isNotBlank(structReportOss)){
            String publicUrl = fileManageService.getPublicUrl(structReportOss, true, DateUtil.offsetDay(new Date(), 1));
            result.put("structReportOss", publicUrl);
        }
        return result;
    }

}
