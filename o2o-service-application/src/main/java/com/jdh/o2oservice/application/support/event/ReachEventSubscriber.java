package com.jdh.o2oservice.application.support.event;

import cn.hutool.core.date.DateField;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Sets;
import com.jd.medicine.base.common.util.CollectionUtil;
import com.jd.medicine.base.common.util.StringUtil;
import com.jdh.o2oservice.application.angelpromise.AngelShipQueryApplication;
import com.jdh.o2oservice.application.angelpromise.service.AngelWorkApplication;
import com.jdh.o2oservice.application.support.service.ReachApplication;
import com.jdh.o2oservice.base.event.*;
import com.jdh.o2oservice.base.event.factory.EventFactory;
import com.jdh.o2oservice.base.model.DomainEnum;
import com.jdh.o2oservice.common.enums.DeliveryTypeEnum;
import com.jdh.o2oservice.core.domain.angel.enums.AngelEventTypeEnum;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelShipEventTypeEnum;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelShipStatusEnum;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelWorkEventTypeEnum;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelShip;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelShipHistory;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelShipHistoryRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelShipRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.query.AngelShipDBQuery;
import com.jdh.o2oservice.core.domain.dispatch.enums.DispatchEventTypeEnum;
import com.jdh.o2oservice.core.domain.medpromise.enums.MedPromiseEventTypeEnum;
import com.jdh.o2oservice.core.domain.medpromise.event.MedicalPromiseEventBody;
import com.jdh.o2oservice.core.domain.medpromise.model.MedicalPromise;
import com.jdh.o2oservice.core.domain.medpromise.query.MedicalPromiseListQuery;
import com.jdh.o2oservice.core.domain.medpromise.repository.db.MedicalPromiseRepository;
import com.jdh.o2oservice.core.domain.medpromise.repository.query.MedicalPromiseRepQuery;
import com.jdh.o2oservice.core.domain.promise.enums.PromiseEventTypeEnum;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromise;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromiseIdentifier;
import com.jdh.o2oservice.core.domain.promise.repository.db.PromiseRepository;
import com.jdh.o2oservice.core.domain.provider.enums.ProviderEventTypeEnum;
import com.jdh.o2oservice.core.domain.settlement.enums.SettleEventTypeEnum;
import com.jdh.o2oservice.core.domain.trade.enums.TradeEventTypeEnum;
import com.jdh.o2oservice.export.angelpromise.dto.AngelWorkDto;
import com.jdh.o2oservice.export.support.command.ReachCommand;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import cn.hutool.core.date.DateUtil;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 触达领域事件订阅
 * <AUTHOR>
 * @date 2023-12-25-7:34 下午
 */
@Slf4j
@Service
public class ReachEventSubscriber {

    /**
     * 事件消费注册
     */
    @Resource
    private EventConsumerRegister eventConsumerRegister;

    /**
     * reachApplication
     */
    @Resource
    private ReachApplication reachApplication;

    /**
     * promiseRepository
     */
    @Resource
    private PromiseRepository promiseRepository;
    /**
     * medicalPromiseRepository
     */
    @Resource
    private MedicalPromiseRepository medicalPromiseRepository;

    /**
     * angelWorkApplication
     */
    @Resource
    private AngelWorkApplication angelWorkApplication;

    /**
     * angelShipRepository
     */
    @Resource
    private AngelShipRepository angelShipRepository;

    /**
     * angelShipHistoryRepository
     */
    @Autowired
    private AngelShipHistoryRepository angelShipHistoryRepository;

    @Resource
    private EventCoordinator eventCoordinator;

    /**
     * 注册事件使用者
     */
    @PostConstruct
    public void registerEventConsumer() {

        /** 下单成功 */
        eventConsumerRegister.register(PromiseEventTypeEnum.VOUCHER_BATCH_CREATE,
                WrapperEventConsumer.newInstance(DomainEnum.BASE, "reachSubmitTask", reachApplication::submitTask, Boolean.TRUE));

        //预约成功
        eventConsumerRegister.register(PromiseEventTypeEnum.PROMISE_APPOINTMENT_SUCCESS, WrapperEventConsumer.newInstance(DomainEnum.BASE,
                "promiseReachConsumer", this::promiseReach, Boolean.TRUE));

        //预约失败
        eventConsumerRegister.register(PromiseEventTypeEnum.PROMISE_APPOINTMENT_FAIL, WrapperEventConsumer.newInstance(DomainEnum.BASE,
                "promiseReachConsumer", this::promiseReach, Boolean.TRUE));

        //取消预约成功
        eventConsumerRegister.register(PromiseEventTypeEnum.PROMISE_CANCEL_SUCCESS, WrapperEventConsumer.newInstance(DomainEnum.BASE,
                "promiseReachConsumer", this::promiseReach, Boolean.TRUE));

        //取消预约成功
        eventConsumerRegister.register(AngelWorkEventTypeEnum.ANGEL_WORK_EVENT_C_USER_CANCEL_SERVED, WrapperEventConsumer.newInstance(DomainEnum.BASE,
                "reachSubmitTask", reachApplication::submitTask, Boolean.TRUE));

        //取消预约失败
        eventConsumerRegister.register(PromiseEventTypeEnum.PROMISE_CANCEL_FAIL, WrapperEventConsumer.newInstance(DomainEnum.BASE,
                "promiseReachConsumer", this::promiseReach, Boolean.TRUE));

        //商家修改预约成功
        eventConsumerRegister.register(ProviderEventTypeEnum.PROMISE_PROVIDER_MODIFY, WrapperEventConsumer.newInstance(DomainEnum.BASE,
                "promiseReachConsumer", this::promiseReach, Boolean.TRUE));


        // 预约中事件处理
        eventConsumerRegister.register(PromiseEventTypeEnum.PROMISE_SUBMIT, WrapperEventConsumer.newInstance(DomainEnum.BASE,
                "promiseReachConsumer", this::promiseReach, Boolean.TRUE));
        // 自动预约中事件处理
        eventConsumerRegister.register(PromiseEventTypeEnum.PROMISE_AUTO_SUBMIT, WrapperEventConsumer.newInstance(DomainEnum.BASE,
                "promiseReachConsumer", this::promiseReach, Boolean.TRUE));

        //修改预约失败
        eventConsumerRegister.register(PromiseEventTypeEnum.PROMISE_MODIFY_FAIL, WrapperEventConsumer.newInstance(DomainEnum.BASE,
                "promiseReachConsumer", reachApplication::submitTask, Boolean.TRUE));
        // 核销
        eventConsumerRegister.register(PromiseEventTypeEnum.PROMISE_WRITE_OFF, WrapperEventConsumer.newInstance(DomainEnum.BASE,
                "promiseReachConsumer", reachApplication::submitTask, Boolean.TRUE));


        /** 履约单创建成功 */
        eventConsumerRegister.register(PromiseEventTypeEnum.PROMISE_CREATED, WrapperEventConsumer.newInstance(DomainEnum.BASE,
                "reachSubmitTask", reachApplication::submitTask, Boolean.TRUE));
        /** 预约成功 */
        eventConsumerRegister.register(PromiseEventTypeEnum.PROMISE_APPOINTMENT_SUCCESS, WrapperEventConsumer.newInstance(DomainEnum.BASE,
                "reachSubmitTask", reachApplication::submitTask, Boolean.TRUE));
        /** 提交预约 */
        eventConsumerRegister.register(PromiseEventTypeEnum.PROMISE_SUBMIT, WrapperEventConsumer.newInstance(DomainEnum.BASE,
                "reachSubmitTask", reachApplication::submitTask, Boolean.TRUE));
        /** 自动提交预约 */
        eventConsumerRegister.register(PromiseEventTypeEnum.PROMISE_AUTO_SUBMIT, WrapperEventConsumer.newInstance(DomainEnum.BASE,
                "reachSubmitTask", reachApplication::submitTask, Boolean.TRUE));
        /** 修改预约 */
        eventConsumerRegister.register(PromiseEventTypeEnum.PROMISE_USER_SUBMIT_MODIFY, WrapperEventConsumer.newInstance(DomainEnum.BASE,
                "reachSubmitTask", reachApplication::submitTask, Boolean.TRUE));
        /** 取消预约 */
        eventConsumerRegister.register(PromiseEventTypeEnum.PROMISE_USER_SUBMIT_CANCEL, WrapperEventConsumer.newInstance(DomainEnum.BASE,
                "reachSubmitTask", reachApplication::submitTask, Boolean.TRUE));

        /** 服务中 */
        eventConsumerRegister.register(PromiseEventTypeEnum.PROMISE_SERVICE_READY, WrapperEventConsumer.newInstance(DomainEnum.BASE,
                "reachSubmitTask", reachApplication::submitTask, Boolean.TRUE));
        /** 首次出报告 */
        eventConsumerRegister.register(MedPromiseEventTypeEnum.MED_PROMISE_FIRST_GENERATE_REPORT, WrapperEventConsumer.newInstance(DomainEnum.BASE,
                "reachSubmitTask", reachApplication::submitTask, Boolean.TRUE));
        /** 派单成功 */
        eventConsumerRegister.register(DispatchEventTypeEnum.DISPATCH_SUCCESS, WrapperEventConsumer.newInstance(DomainEnum.BASE,
                "reachSubmitTask", reachApplication::submitTask, Boolean.TRUE));
        /** 派单失败 */
        eventConsumerRegister.register(DispatchEventTypeEnum.DISPATCH_FAIL, WrapperEventConsumer.newInstance(DomainEnum.BASE,
                "reachSubmitTask", reachApplication::submitTask, Boolean.TRUE));
        /** 指定派单 */
        eventConsumerRegister.register(DispatchEventTypeEnum.DISPATCH_TARGET_DISPATCH, WrapperEventConsumer.newInstance(DomainEnum.BASE,
                "reachSubmitTask", reachApplication::submitTask, Boolean.TRUE));
        /** 派单成功 */
        eventConsumerRegister.register(DispatchEventTypeEnum.DISPATCH_SUCCESS_AFTER_MODIFY_DATE_SUCCESS, WrapperEventConsumer.newInstance(DomainEnum.BASE,
                "reachSubmitTask", reachApplication::submitTask, Boolean.TRUE));
        /** 修改预约时间派单成功 */
        eventConsumerRegister.register(PromiseEventTypeEnum.PROMISE_MODIFY_SUCCESS, WrapperEventConsumer.newInstance(DomainEnum.BASE,
                "reachSubmitTask", reachApplication::submitTask, Boolean.TRUE));

        /** 护士接单任务冲突 */
        eventConsumerRegister.register(DispatchEventTypeEnum.DISPATCH_TIME_CONFLICT, WrapperEventConsumer.newInstance(DomainEnum.BASE,
                "reachSubmitTask", reachApplication::submitTask, Boolean.TRUE));

        /** 无人接单且指定派单失败 */
        eventConsumerRegister.register(DispatchEventTypeEnum.DISPATCH_FAIL_TARGET_DISPATCH, WrapperEventConsumer.newInstance(DomainEnum.BASE,
                "reachSubmitTask", reachApplication::submitTask, Boolean.TRUE));

        /** 指定派单 */
        eventConsumerRegister.register(DispatchEventTypeEnum.DISPATCH_ASSIGN_SUCCESS_RECEIVED, WrapperEventConsumer.newInstance(DomainEnum.BASE,
                "reachSubmitTask", reachApplication::submitTask, Boolean.TRUE));

        /** 服务者入驻审核通过 */
        eventConsumerRegister.register(AngelEventTypeEnum.ANGEL_SETTLE_AUDIT_PASS, WrapperEventConsumer.newInstance(DomainEnum.BASE,
                "reachSubmitTask", reachApplication::submitTask, Boolean.TRUE));
        /** 服务者入驻审核拒绝 */
        eventConsumerRegister.register(AngelEventTypeEnum.ANGEL_SETTLE_AUDIT_REJECT, WrapperEventConsumer.newInstance(DomainEnum.BASE,
                "reachSubmitTask", reachApplication::submitTask, Boolean.TRUE));
        /** 信息修改审核通过 */
        eventConsumerRegister.register(AngelEventTypeEnum.ANGEL_UPDATE_AUDIT_PASS, WrapperEventConsumer.newInstance(DomainEnum.BASE,
                "reachSubmitTask", reachApplication::submitTask, Boolean.TRUE));
        /** 信息修改审核拒绝 */
        eventConsumerRegister.register(AngelEventTypeEnum.ANGEL_UPDATE_AUDIT_REJECT, WrapperEventConsumer.newInstance(DomainEnum.BASE,
                "reachSubmitTask", reachApplication::submitTask, Boolean.TRUE));
        /** 实名认证失败 */
        eventConsumerRegister.register(AngelEventTypeEnum.ANGEL_REAL_NAME_AUDIT_REJECT, WrapperEventConsumer.newInstance(DomainEnum.BASE,
                "reachSubmitTask", reachApplication::submitTask, Boolean.TRUE));
        /** 运单取消 */
        eventConsumerRegister.register(AngelShipEventTypeEnum.ANGEL_WORK_SHIP_EVENT_CANCEL,
                WrapperEventConsumer.newInstance(DomainEnum.BASE, "reachSubmitTask", reachApplication::submitTask, Boolean.TRUE));
        /** 护士结算 */
        eventConsumerRegister.register(SettleEventTypeEnum.ANGEL_SETTLE_AMOUNT_FOR_CASH,
                WrapperEventConsumer.newInstance(DomainEnum.BASE, "reachSubmitTask", reachApplication::submitTask, Boolean.TRUE));


        /**
         * 交易域事件
         */
        eventConsumerRegister.register(TradeEventTypeEnum.LOC_CODE_REFUND_SUCCESS,
                WrapperEventConsumer.newInstance(DomainEnum.BASE, "reachSubmitTask", reachApplication::submitTask, Boolean.TRUE));

        /** 运单取消 */
        eventConsumerRegister.register(AngelShipEventTypeEnum.ANGEL_WORK_SHIP_EVENT_CREATE_FAIL,
                WrapperEventConsumer.newInstance(DomainEnum.BASE, "reachSubmitTask", reachApplication::submitTask, Boolean.TRUE));

        /** 履约完成 */
        eventConsumerRegister.register(PromiseEventTypeEnum.PROMISE_COMPLETE,
                WrapperEventConsumer.newInstance(DomainEnum.BASE, "reachSubmitTask", reachApplication::submitTask, Boolean.TRUE));
        /** 履约作废 */
        eventConsumerRegister.register(PromiseEventTypeEnum.PROMISE_INVALID,
                WrapperEventConsumer.newInstance(DomainEnum.BASE, "reachSubmitTask", reachApplication::submitTask, Boolean.TRUE));
        /** 订单收单完成 */
        eventConsumerRegister.register(TradeEventTypeEnum.RECEIVE_ORDER,
                WrapperEventConsumer.newInstance(DomainEnum.BASE, "reachSubmitTask", reachApplication::submitTask, Boolean.TRUE));

        /** 运单超时取消 */
        eventConsumerRegister.register(AngelShipEventTypeEnum.ANGEL_WORK_SHIP_EVENT_TIMEOUT_CANCEL,
                WrapperEventConsumer.newInstance(DomainEnum.BASE, "reachSubmitTask", reachApplication::submitTask, Boolean.TRUE));

        /** 体检出报告 */
        eventConsumerRegister.register(PromiseEventTypeEnum.PROMISE_REPORT_GENERATE,
                WrapperEventConsumer.newInstance(DomainEnum.BASE, "reachSubmitTask", reachApplication::submitTask, Boolean.TRUE));

        /** 体检出报告 */
        eventConsumerRegister.register(PromiseEventTypeEnum.PROMISE_REPORT_GENERATE_TO_BUY,
                WrapperEventConsumer.newInstance(DomainEnum.BASE, "reachSubmitTask", reachApplication::submitTask, Boolean.TRUE));

        //样本收样，判断运单状态
        eventConsumerRegister.register(MedPromiseEventTypeEnum.MED_PROMISE_STATION_RECEIVE,
                WrapperEventConsumer.newInstance(DomainEnum.MED_PROMISE, "stationReceiveForShip", this::stationReceiveForShip, Boolean.TRUE, Boolean.TRUE, EventConsumerRetryTemplate.defaultInstance()));

        /** 服务者工单退款 */
        eventConsumerRegister.register(AngelWorkEventTypeEnum.ANGEL_WORK_EVENT_REFUND_SERVED,
                WrapperEventConsumer.newInstance(DomainEnum.BASE, "reachSubmitTask", reachApplication::submitTask, Boolean.TRUE));


        /** 检测单出报告 */
        eventConsumerRegister.register(MedPromiseEventTypeEnum.MED_PROMISE_GENERATE_REPORT,
                WrapperEventConsumer.newInstance(DomainEnum.BASE, "reachSubmitTask", reachApplication::submitTask, Boolean.TRUE));


        /** 全部报告已出 */
        eventConsumerRegister.register(MedPromiseEventTypeEnum.MED_PROMISE_SMS,
                WrapperEventConsumer.newInstance(DomainEnum.BASE, "reachSubmitTask",this::medPromiseReportGenerateSms ,Boolean.TRUE));

        eventConsumerRegister.register(MedPromiseEventTypeEnum.MED_PROMISE_SMS_PART,
                WrapperEventConsumer.newInstance(DomainEnum.BASE, "reachSubmitTask", reachApplication::submitTask, Boolean.TRUE));

        eventConsumerRegister.register(MedPromiseEventTypeEnum.MED_PROMISE_SMS_ALL,
                WrapperEventConsumer.newInstance(DomainEnum.BASE, "reachSubmitTask", reachApplication::submitTask, Boolean.TRUE));
   }

    /**
     * 预约触达
     * @param event
     */
    public void promiseReach(Event event){
        log.info("ReachEventSubscriber -> promiseReach event:{}",JSON.toJSONString(event));
        if (StringUtils.isEmpty(event.getAggregateId())) {
            return;
        }
        //预约相关触达，查询预约单信息
        JdhPromise jdhPromise = promiseRepository.find(new JdhPromiseIdentifier(Long.valueOf(event.getAggregateId())));
        if (Objects.isNull(jdhPromise)) {
            return;
        }

        ReachCommand reachCommand = ReachCommand.builder()
                .eventCode(event.getEventCode())
                .eventId(event.getEventId())
                .promiseId(Long.valueOf(event.getAggregateId()))
                .serviceType(jdhPromise.getServiceType())
                .verticalCode(jdhPromise.getVerticalCode())
                .build();

        reachApplication.reach(reachCommand);
        
    }

    /**
     * 实验室收样,判断是否三方运单
     * @param event
     */
    public void stationReceiveForShip(Event event){
        log.info("ReachEventSubscriber -> stationReceiveForShip event:{}",JSON.toJSONString(event));
        if (StringUtils.isEmpty(event.getAggregateId())) {
            return;
        }
        MedicalPromise medicalPromise = medicalPromiseRepository.findMedicalPromise(MedicalPromiseRepQuery.builder().medicalPromiseId(Long.valueOf(event.getAggregateId())).build());
        if (Objects.isNull(medicalPromise)) {
            log.info("ReachEventSubscriber->stationReceiveForShip,medicalPromise is null,medicalPromiseId:{}",event.getAggregateId());
            return;
        }

        //1.查工单
        AngelWorkDto angelWorkDto = angelWorkApplication.getAngelWorkByPromiseId(medicalPromise.getPromiseId());
        if (angelWorkDto == null) {
            log.info("ReachEventSubscriber->stationReceiveForShip,angelWorkDto is null,medicalPromiseId:{}",event.getAggregateId());
            return;
        }

        //2.查运单
        AngelShipDBQuery aq = new AngelShipDBQuery();
        aq.setWorkId(angelWorkDto.getWorkId());
        List<AngelShip> angelShips = angelShipRepository.findList(aq);

        if (CollectionUtil.isEmpty(angelShips)){
            log.info("ReachEventSubscriber->stationReceiveForShip,angelShips is null,medicalPromiseId:{}",event.getAggregateId());
            return;
        }

        AngelShip angelShip = angelShips.stream()
                .filter(
                        p -> StringUtil.equals(p.getReceiverFullAddress(), medicalPromise.getStationAddress()) &&
                                Objects.equals(DeliveryTypeEnum.THIRD_DELIVERY.getType(),p.getType())
                ).findFirst().orElse(null);

        if (Objects.isNull(angelShip)) {
            log.info("ReachEventSubscriber->stationReceiveForShip,angelShip is null,medicalPromiseId:{}",event.getAggregateId());
            return;
        }

        Set<Integer> needUpdateStatus = Sets.newHashSet(
                AngelShipStatusEnum.KNIGHT_REACH_STORE.getShipStatus(),
                AngelShipStatusEnum.SHIP_ORDER_INIT.getShipStatus(),
                AngelShipStatusEnum.WAITING_RECEIVE_ORDER.getShipStatus(),
                AngelShipStatusEnum.WAITING_RECEIVE_GOODS.getShipStatus(),
                AngelShipStatusEnum.DELIVERING_GOODS.getShipStatus()
        );

        if (needUpdateStatus.contains(angelShip.getShipStatus())){

            AngelShipHistory shipHistory = AngelShipHistory.builder()
                    .shipId(angelShip.getShipId())
                    .beforeStatus(angelShip.getShipStatus())
                    .afterStatus(AngelShipStatusEnum.ORDER_SHIP_FINISH.getShipStatus())
                    .beforeTransfer(angelWorkDto.getAngelName())
                    .afterTransfer(angelWorkDto.getAngelName())
                    .workId(angelWorkDto.getWorkId())
                    .operateTime(new Date())
                    .createTime(new Date())
                    .build();

            angelShipHistoryRepository.save(shipHistory);
            angelShip.setShipStatus(AngelShipStatusEnum.ORDER_SHIP_FINISH.getShipStatus());
            angelShipRepository.updateByShipId(angelShip);
            //history
            eventCoordinator.publish(EventFactory.newDefaultEvent(angelShip, AngelShipEventTypeEnum.ANGEL_WORK_SHIP_EVENT_STEP_FINISH,null));
        }

    }

    public void medPromiseReportGenerateSms(Event event){
        log.info("ReachEventSubscriber->medPromiseReportGenerateSms event:{}",JSON.toJSONString(event));

        Date now = new Date();
        Date delayEnd = DateUtil.offset(now, DateField.SECOND,-30);
        MedicalPromiseEventBody eventBody = JSON.parseObject(event.getBody(), MedicalPromiseEventBody.class);
        List<MedicalPromise> medicalPromises = medicalPromiseRepository.queryMedicalPromiseList(MedicalPromiseListQuery.builder().promiseId(eventBody.getPromiseId()).invalid(Boolean.FALSE).freezeQuery(Boolean.FALSE).build());
        log.info("ReachEventSubscriber->medPromiseReportGenerateSms medicalPromises:{}",JSON.toJSONString(medicalPromises));

        if (CollectionUtil.isEmpty(medicalPromises)){
            return;
        }

        List<MedicalPromise> sort = medicalPromises.stream().filter(p -> Objects.nonNull(p.getReportTime())).sorted(Comparator.comparing(MedicalPromise::getReportTime).reversed()).collect(Collectors.toList());
        log.info("ReachEventSubscriber->medPromiseReportGenerateSms sort:{}",JSON.toJSONString(sort));

        //如果最后一份出报告的时间 大于 事件发送的时间 则忽略
        if (DateUtil.compare(sort.get(0).getReportTime() , delayEnd) > 0){
            return;
        }

        //否则 发送
        //全部已出
        if (Objects.equals(medicalPromises.size(),sort.size())){

            eventCoordinator.publish(EventFactory.newDefaultEvent(sort.get(0), MedPromiseEventTypeEnum.MED_PROMISE_SMS_ALL,
                    MedicalPromiseEventBody.builder()
                            .medicalPromiseId(sort.get(0).getMedicalPromiseId())
                            .promiseId(sort.get(0).getPromiseId())
                            .build()));
        }else {
            eventCoordinator.publish(EventFactory.newDefaultEvent(sort.get(0), MedPromiseEventTypeEnum.MED_PROMISE_SMS_PART,
                    MedicalPromiseEventBody.builder()
                            .medicalPromiseId(sort.get(0).getMedicalPromiseId())
                            .promiseId(sort.get(0).getPromiseId())
                            .build()));
        }


    }



}
