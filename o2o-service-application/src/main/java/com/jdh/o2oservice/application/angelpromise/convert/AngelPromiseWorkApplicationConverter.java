package com.jdh.o2oservice.application.angelpromise.convert;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.IdcardUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.enums.GenderEnum;
import com.jdh.o2oservice.base.util.SpringUtil;
import com.jdh.o2oservice.base.util.TimeUtils;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelWorkStatusEnum;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelTask;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelWork;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelWorkGroupCount;
import com.jdh.o2oservice.core.domain.angelpromise.vo.JdhAngelTaskExtVo;
import com.jdh.o2oservice.core.domain.medpromise.model.MedicalPromise;
import com.jdh.o2oservice.core.domain.support.basic.model.Birthday;
import com.jdh.o2oservice.core.domain.support.basic.model.UserName;
import com.jdh.o2oservice.export.angelpromise.dto.*;
import com.jdh.o2oservice.export.medicalpromise.dto.MedicalPromiseDTO;
import com.jdh.o2oservice.export.product.dto.ServiceItemDto;
import com.jdh.o2oservice.export.promise.dto.PromisePatientDto;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.mapstruct.Mapper;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 服务单 work应用层转换
 * @author: yangxiyu
 * @date: 2024/5/08 4:21 下午
 * @version: 1.0
 */
@Mapper
public interface AngelPromiseWorkApplicationConverter {

    /** */
    AngelPromiseWorkApplicationConverter INS = Mappers.getMapper(AngelPromiseWorkApplicationConverter.class);

    Logger logger = LoggerFactory.getLogger(AngelPromiseWorkApplicationConverter.class);

    /**
     *
     * @param list
     * @return
     */
    List<AngelWorkGroupCountDto> convertAngelWorkGroupCountDto(List<AngelWorkGroupCount> list);

    /**
     * 护士端列表查询，DTO转换
     * @param page
     * @param serviceItems
     * @param medicalPromises
     * @return
     */
    default PageDto<AngelWorkListNodeDto> convert2PageWorkDto(Page<AngelWork> page, List<ServiceItemDto> serviceItems, List<MedicalPromiseDTO> medicalPromises, String orderDetailLink){
        if (Objects.isNull(page) || CollectionUtils.isEmpty(page.getRecords())){
            return null;
        }
        List<AngelWorkListNodeDto> res = Lists.newArrayList();
        Map<Long, List<MedicalPromiseDTO>> medicalPromiseMap = Maps.newHashMap();
        if(CollectionUtils.isNotEmpty(medicalPromises)){
            medicalPromiseMap =  medicalPromises.stream().collect(Collectors.groupingBy(MedicalPromiseDTO::getPromiseId));
        }
        Map<Long, ServiceItemDto> serviceItemDtoMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(serviceItems)){
            serviceItemDtoMap = serviceItems.stream().collect(Collectors.toMap(ServiceItemDto::getItemId, Function.identity(), (o,n)->o));
        }
        logger.info("convert2PageWorkDto serviceItemDtoMap={}",JSON.toJSONString(serviceItemDtoMap));
        for (AngelWork work : page.getRecords()) {
            logger.info("convert2PageWorkDto work={}",JSON.toJSONString(work));
            List<MedicalPromiseDTO> workMedicalPromises = medicalPromiseMap.get(work.getPromiseId());
            List<ServiceItemDto> workServiceItem = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(workMedicalPromises)){
                for (MedicalPromiseDTO workMedicalPromise : workMedicalPromises) {
                    logger.info("convert2PageWorkDto workMedicalPromise={}",JSON.toJSONString(workMedicalPromise));
                    logger.info("convert2PageWorkDto 匹配结果={}",serviceItemDtoMap.get(Long.valueOf(workMedicalPromise.getServiceItemId())));
                    workServiceItem.add(serviceItemDtoMap.get(Long.valueOf(workMedicalPromise.getServiceItemId())));
                }
            }
            AngelWorkListNodeDto dto = convert2PageWorkDto(work, workServiceItem, workMedicalPromises, orderDetailLink);
            res.add(dto);
        }

        PageDto<AngelWorkListNodeDto> pageDto = new PageDto<AngelWorkListNodeDto>();
        pageDto.setList(res);
        pageDto.setPageSize(page.getSize());
        pageDto.setTotalPage(page.getPages());
        pageDto.setPageNum(page.getCurrent());
        pageDto.setTotalCount(page.getTotal());
        return pageDto;
    }

    /**
     * 转换单个列表查询的workDto
     * @param work
     * @param serviceItems
     * @return
     */
    default AngelWorkListNodeDto convert2PageWorkDto(AngelWork work, List<ServiceItemDto> serviceItems, List<MedicalPromiseDTO> medicalPromises, String orderDetailLink){
        if (Objects.isNull(work)){
            return null;
        }
        AngelWorkListNodeDto nodeDto = new AngelWorkListNodeDto();
        nodeDto.setWorkId(work.getWorkId());
        nodeDto.setPromiseId(work.getPromiseId());
        nodeDto.setWorkType(work.getWorkType());
        nodeDto.setWorkTypeDesc(work.getWorkTypeDesc());
        nodeDto.setAngelCharge(work.getAngelCharge());
        nodeDto.setStatus(work.getWorkStatus());

        AngelWorkStatusEnum workStatusEnum = AngelWorkStatusEnum.getEnumByCode(work.getWorkStatus());
        nodeDto.setStatusDesc(workStatusEnum.getShowDesc());

        DuccConfig duccConfig = SpringUtil.getBean(DuccConfig.class);
        String workTypeDesc = duccConfig.getWorkTypeDescByType(work.getWorkType());
        nodeDto.setWorkTypeDesc(workTypeDesc);
        nodeDto.setUserFullAddress(work.takeUserFullAddress());
        nodeDto.setLongitude(work.takeUserLng());
        nodeDto.setLatitude(work.takeUserLat());
        nodeDto.setServiceStartTime(TimeUtils.dateTimeToStr(work.getWorkStartTime()));
        nodeDto.setServicePlanConsumeTime(sumConsumeTime(serviceItems, medicalPromises));
        nodeDto.setAngelTasks(convert2PageTaskDto(work.getAngelTasks()));
        nodeDto.setServiceItems(serviceItems);
        nodeDto.setOrderDetailLink(String.format(orderDetailLink, work.getWorkId()));

        List<AngelTaskDto> angelTasks = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(work.getAngelTasks())){
            for (AngelTask angelTask : work.getAngelTasks()) {
                AngelTaskDto taskDto = new AngelTaskDto();
                if (Objects.nonNull(angelTask.getJdhAngelTaskExtVo())){
                    taskDto.setPatientName(angelTask.getJdhAngelTaskExtVo().getPatientName());
                    taskDto.setPatientGender( GenderEnum.getDescOfType(angelTask.getJdhAngelTaskExtVo().getPatientGender()));
                    taskDto.setPatientAge(Objects.toString(angelTask.getJdhAngelTaskExtVo().getPatientAge(), null));
                }
                angelTasks.add(taskDto);
            }
            nodeDto.setAngelTasks(angelTasks);
        }

        return nodeDto;
    }

    /**
     * 求和检测项目需要的时间
     * @param serviceItems
     * @param medicalPromises
     * @return
     */
    @Named("sumConsumeTime")
    default Integer sumConsumeTime(List<ServiceItemDto> serviceItems, List<MedicalPromiseDTO> medicalPromises) {
        logger.info("sumConsumeTime serviceItems={} medicalPromises={}", JSON.toJSONString(serviceItems),JSON.toJSONString(medicalPromises));
        if (CollectionUtils.isEmpty(medicalPromises) || CollectionUtils.isEmpty(serviceItems)){
            return 0;
        }
        Map<Long, ServiceItemDto> itemMap = serviceItems.stream().collect(Collectors.toMap(ServiceItemDto::getItemId, Function.identity(), (o, n)->o));
        int time = 0;
        for (MedicalPromiseDTO medicalPromise : medicalPromises) {
            ServiceItemDto serviceItemDto = itemMap.get(Long.valueOf(medicalPromise.getServiceItemId()));
            if (Objects.nonNull(serviceItemDto) && Objects.nonNull( serviceItemDto.getServiceDuration())){
                time += serviceItemDto.getServiceDuration();
            }
        }
        return time;
    }



    /**
     * 转换列表的taskDto信息
     * @param angelTasks
     * @return
     */
    default List<AngelTaskDto> convert2PageTaskDto(List<AngelTask> angelTasks){
        if(CollectionUtils.isEmpty(angelTasks)){
            return null;
        }
        List<AngelTaskDto> result = new ArrayList<>();
        for(AngelTask task : angelTasks){
            AngelTaskDto dto = new AngelTaskDto();
            JdhAngelTaskExtVo jdhAngelTaskExtVo = task.getJdhAngelTaskExtVo();
            if (Objects.nonNull(jdhAngelTaskExtVo)) {
                String patientName = jdhAngelTaskExtVo.getPatientName();
                dto.setPatientName(UserName.mask(patientName));
                dto.setPatientAge(String.valueOf(jdhAngelTaskExtVo.getPatientAge()));
                dto.setPatientGender(GenderEnum.getDescOfType(jdhAngelTaskExtVo.getPatientGender()));
            }
            result.add(dto);
        }
        return result;
    }

    List<AngelWorkDetailDto> toAngelWorkDetailDto(List<AngelWork> angelWorks);

    default AngelWorkItemDto convert2WorkItemDto(AngelWork angelWork, List<MedicalPromise> medicalPromises) {
        AngelWorkItemDto angelWorkItemDto = new AngelWorkItemDto();
        angelWorkItemDto.setVerticalCode(angelWork.getVerticalCode());
        angelWorkItemDto.setServiceType(angelWork.getServiceType());
        angelWorkItemDto.setWorkId(angelWork.getWorkId());
        angelWorkItemDto.setJdOrderId(angelWork.getJdOrderId());
        angelWorkItemDto.setPromiseId(angelWork.getPromiseId());
        angelWorkItemDto.setWorkType(angelWork.getWorkType());
        angelWorkItemDto.setAngelId(angelWork.getAngelId());
        angelWorkItemDto.setAngelName(angelWork.getAngelName());
        angelWorkItemDto.setAngelPin(angelWork.getAngelPin());
        angelWorkItemDto.setAngelPhone(angelWork.getAngelPhone());
        angelWorkItemDto.setStatus(angelWork.getWorkStatus());
        angelWorkItemDto.setStopStatus(angelWork.getStopStatus());
        angelWorkItemDto.setAngelStationId(angelWork.getAngelStationId());
        angelWorkItemDto.setWorkStartTime(angelWork.getWorkStartTime());
        angelWorkItemDto.setWorkEndTime(angelWork.getWorkEndTime());
        angelWorkItemDto.setPlanCallTime(angelWork.getPlanCallTime());
        angelWorkItemDto.setPlanOutTime(angelWork.getPlanOutTime());
        angelWorkItemDto.setPlanFinishTime(angelWork.getPlanFinishTime());

        if(CollectionUtils.isEmpty(medicalPromises)){
            return angelWorkItemDto;
        }
        List<WorkItemDto> itemDtoList = Lists.newArrayList();
        medicalPromises.forEach(item -> {
            WorkItemDto itemDto = new WorkItemDto();
            BeanUtil.copyProperties(item, itemDto);
            itemDtoList.add(itemDto);
        });
        angelWorkItemDto.setItemDtoList(itemDtoList);
        return angelWorkItemDto;
    }
}
