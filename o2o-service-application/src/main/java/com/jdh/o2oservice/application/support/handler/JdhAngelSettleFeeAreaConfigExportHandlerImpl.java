package com.jdh.o2oservice.application.support.handler;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.jdh.o2oservice.application.settlement.service.JdhAngelSettleConfigApplication;
import com.jdh.o2oservice.base.constatnt.NumConstant;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.core.domain.settlement.enums.SettleAggregateEnum;
import com.jdh.o2oservice.core.domain.settlement.repository.query.JdhAngelSettleAreaFeeQuery;
import com.jdh.o2oservice.core.domain.support.file.context.FileExportContext;
import com.jdh.o2oservice.core.domain.support.file.enums.FileExportTypeEnum;
import com.jdh.o2oservice.core.domain.support.file.service.AbstractFileExportHandler;
import com.jdh.o2oservice.export.settlement.dto.AngelSettlementConfigDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.BlockingQueue;

/**
 * JdhAngelSettleFeeAreaConfigExportHandlerImpl
 * 护士结算地区费项配置导出
 * <AUTHOR>
 * @date 2025/04/22
 */
@Slf4j
@Service
public class JdhAngelSettleFeeAreaConfigExportHandlerImpl extends AbstractFileExportHandler {


    /**
     * 商品接口信息
     */
    @Resource
    JdhAngelSettleConfigApplication jdhAngelSettleConfigApplication;

    /**
     * 获取map key
     *
     * @return
     */
    @Override
    public String getMapKey() {
        return FileExportTypeEnum.ANGEL_SETTLE_CONFIG_EXPORT.getType();
    }

    /**
     * 前置处理
     *
     * @param ctx
     */
    @Override
    protected void preHandle(FileExportContext ctx) {

    }

    /**
     * 各业务实现获取业务数据
     *
     * @param ctx ctx
     */
    @Override
    protected void getData(FileExportContext ctx) {
        Map<String, Object> queryParam = ctx.getQueryParam();
        JdhAngelSettleAreaFeeQuery pageRequest = JSON.parseObject(JSON.toJSONString(queryParam), JdhAngelSettleAreaFeeQuery.class);
        pageRequest.setPageSize(NumConstant.NUM_10000);
        log.info("JdhAngelSettleFeeAreaConfigExportHandlerImpl getData queryParam:{}",JSON.toJSONString(queryParam));
        boolean hasNextPage = true;
        int pageNum = 1;
        BlockingQueue<Map<String, Object>> queue = ctx.getQueue();
        do {
            pageRequest.setPageNum(pageNum);
            PageDto<AngelSettlementConfigDto> pageDto = jdhAngelSettleConfigApplication.queryAngelSettleConfigPage(pageRequest);
            if(pageNum == pageDto.getTotalPage()){
                hasNextPage = false;
            }
            List<AngelSettlementConfigDto> list = pageDto.getList();
            if(CollUtil.isNotEmpty(list)){
                for (AngelSettlementConfigDto itemDto : list) {
                    Map<String, Object> column = new HashMap<>();
                    column.put(SettleAggregateEnum.SETTLE_FEE_AREA_CONFIG.getCode(), itemDto);
                    queue.add(column);
                }
            }
            pageNum++;
        }while (hasNextPage);
        log.info("JdhAngelSettleFeeAreaConfigExportHandlerImpl getData 结束");

    }
}
