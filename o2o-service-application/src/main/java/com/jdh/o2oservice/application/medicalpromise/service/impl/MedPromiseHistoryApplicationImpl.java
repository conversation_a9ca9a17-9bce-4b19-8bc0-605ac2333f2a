package com.jdh.o2oservice.application.medicalpromise.service.impl;

import com.jdh.o2oservice.application.medicalpromise.convert.MedPromiseHistoryConvert;
import com.jdh.o2oservice.application.medicalpromise.service.MedPromiseHistoryApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.core.domain.medpromise.model.MedPromiseHistory;
import com.jdh.o2oservice.core.domain.medpromise.query.MedPromiseHistoryQuery;
import com.jdh.o2oservice.core.domain.medpromise.repository.db.MedPromiseHistoryRepository;
import com.jdh.o2oservice.export.medicalpromise.dto.MedPromiseHistoryDTO;
import com.jdh.o2oservice.export.medicalpromise.query.MedPromiseHistoryRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description: 检验单历史记录流转
 * @Author: wangpengfei144
 * @Date: 2024/5/3
 */
@Service
@Slf4j
public class MedPromiseHistoryApplicationImpl implements MedPromiseHistoryApplication {

    /**
     * 检测单历史仓储层
     */
    @Autowired
    private MedPromiseHistoryRepository medPromiseHistoryRepository;


    /**
     * 查询检测单历史列表
     * @param medPromiseHistoryRequest 医疗承诺历史请求对象
     * @return 测单历史列表
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.medicalpromise.service.impl.MedPromiseHistoryApplicationImpl.queryMedPromiseHistoryList")
    public List<MedPromiseHistoryDTO> queryMedPromiseHistoryList(MedPromiseHistoryRequest medPromiseHistoryRequest) {
//        AssertUtils.nonNull(medPromiseHistoryRequest.getMedicalPromiseId(), MedPromiseErrorCode.PARAM_NULL);
        MedPromiseHistoryQuery medPromiseHistoryQuery = MedPromiseHistoryConvert.INSTANCE.convert(medPromiseHistoryRequest);
        List<MedPromiseHistory> medPromiseHistories = medPromiseHistoryRepository.queryMedPromiseHistoryList(medPromiseHistoryQuery);
        return MedPromiseHistoryConvert.INSTANCE.convert(medPromiseHistories);
    }
}
