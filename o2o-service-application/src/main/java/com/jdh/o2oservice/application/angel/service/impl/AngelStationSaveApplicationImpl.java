package com.jdh.o2oservice.application.angel.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Sets;
import com.jdh.o2oservice.application.angel.convert.MapApplicationConverter;
import com.jdh.o2oservice.application.angel.service.AngelApplication;
import com.jdh.o2oservice.application.angel.service.AngelStationSaveApplication;
import com.jdh.o2oservice.base.enums.RedisKeyEnum;
import com.jdh.o2oservice.base.event.Event;
import com.jdh.o2oservice.base.event.EventCoordinator;
import com.jdh.o2oservice.base.event.factory.EventFactory;
import com.jdh.o2oservice.base.exception.ArgumentsException;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode;
import com.jdh.o2oservice.base.util.AssertUtils;
import com.jdh.o2oservice.base.util.RedisLockUtil;
import com.jdh.o2oservice.base.util.TrimUtil;
import com.jdh.o2oservice.base.util.ValidateParamUtil;
import com.jdh.o2oservice.core.domain.angel.context.AngelStationSaveContext;
import com.jdh.o2oservice.core.domain.angel.context.AngelStationStartStopContext;
import com.jdh.o2oservice.core.domain.angel.context.LayerSaveContext;
import com.jdh.o2oservice.core.domain.angel.context.MapSaveContext;
import com.jdh.o2oservice.core.domain.angel.enums.*;
import com.jdh.o2oservice.core.domain.angel.event.AngelStationEventBody;
import com.jdh.o2oservice.core.domain.angel.factory.JdhStationAngelManFactory;
import com.jdh.o2oservice.core.domain.angel.factory.JdhStationSkuManFactory;
import com.jdh.o2oservice.core.domain.angel.factory.JdhStationSkuRelFactory;
import com.jdh.o2oservice.core.domain.angel.model.*;
import com.jdh.o2oservice.core.domain.angel.repository.db.*;
import com.jdh.o2oservice.core.domain.angel.repository.query.*;
import com.jdh.o2oservice.core.domain.angel.service.JdhMapDomainService;
import com.jdh.o2oservice.core.domain.product.model.JdhSku;
import com.jdh.o2oservice.core.domain.product.model.JdhSkuIdentifier;
import com.jdh.o2oservice.core.domain.product.repository.db.JdhSkuRepository;
import com.jdh.o2oservice.core.domain.provider.bo.StoreInfoBo;
import com.jdh.o2oservice.core.domain.provider.model.JdhStoreTransferStation;
import com.jdh.o2oservice.core.domain.provider.repository.db.JdhStoreTransferStationRepository;
import com.jdh.o2oservice.core.domain.provider.rpc.ProviderStoreExportServiceRpc;
import com.jdh.o2oservice.export.angel.cmd.*;
import com.jdh.o2oservice.export.angel.dto.JdhAngelDto;
import com.jdh.o2oservice.export.angel.query.AngelRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * @ClassName:GisMapApplicationImpl
 * @Description: 地图服务实现
 *
 * @Author: yaoqinghai
 * @Date: 2024/4/26 11:43
 * @Vserion: 1.0
 **/
@Service
@Slf4j
public class AngelStationSaveApplicationImpl implements AngelStationSaveApplication {

    @Resource
    private JdhMapDomainService jdhMapDomainService;

    @Resource
    private JdhStationRepository jdhStationRepository;

    @Resource
    private JdhStationAngelManRepository jdhStationAngelManRepository;

    @Resource
    private JdhStationSkuManRepository jdhStationSkuManRepository;

    @Resource
    private JdhStationSkuRelRepository jdhStationSkuRelRepository;

    @Resource
    private JdhMapRepository jdhMapRepository;

    @Resource
    private JdhLayerRepository jdhLayerRepository;

    @Resource
    private AngelApplication angelApplication;

    @Resource
    private RedisLockUtil redisLockUtil;

    @Resource
    private ProviderStoreExportServiceRpc providerStoreExportServiceRpc;

    @Resource
    private EventCoordinator eventCoordinator;

    @Resource
    private JdhSkuRepository jdhSkuRepository;

    @Resource
    JdhStoreTransferStationRepository jdhStoreTransferStationRepository;


    /**
     * 创建地图
     *
     * @param mapSaveCmd
     * @return
     */
    @Override
    public Boolean createMap(MapSaveCmd mapSaveCmd) {
        String errMsg = ValidateParamUtil.paramValidation(mapSaveCmd);
        if(StringUtils.isNotBlank(errMsg)){
            log.error("[AngelStationSaveApplicationImpl.createMap],创建地图参数异常!mapSaveCmd={}", JSON.toJSONString(mapSaveCmd));
            throw new ArgumentsException(BusinessErrorCode.ILLEGAL_ARG_ERROR);
        }
        MapSaveContext mapSaveContext = MapSaveContext.builder().mapName(mapSaveCmd.getMapName()).build();
        return jdhMapDomainService.createMap(mapSaveContext);
    }

    /**
     * 创建图层
     *
     * @param layerSaveCmd
     * @return
     */
    @Override
    public Boolean saveLayer(LayerSaveCmd layerSaveCmd) {
        String errMsg = ValidateParamUtil.paramValidation(layerSaveCmd);
        if(StringUtils.isNotBlank(errMsg)){
            log.error("[AngelStationSaveApplicationImpl.saveLayer],创建图层参数异常!layerSaveCmd={}", JSON.toJSONString(layerSaveCmd));
            throw new ArgumentsException(BusinessErrorCode.ILLEGAL_ARG_ERROR);
        }
        LayerSaveContext layerSaveContext = MapApplicationConverter.ins.convertToLayerSaveContext(layerSaveCmd);
        return jdhMapDomainService.saveLayer(layerSaveContext);
    }

    /**
     * @param angelStationSaveCmd
     * @return
     */
    @Override
    public Boolean saveStationElement(AngelStationSaveCmd angelStationSaveCmd) {
        String lockKey = RedisKeyEnum.getRedisKey(RedisKeyEnum.CREATE_ANGEL_STATION_KEY, angelStationSaveCmd.getAngelStationName());
        if(!redisLockUtil.tryLock(lockKey, "1", RedisKeyEnum.CREATE_ANGEL_STATION_KEY.getExpireTime(), RedisKeyEnum.CREATE_ANGEL_STATION_KEY.getExpireTimeUnit())){
            log.error("[AngelStationSaveApplicationImpl.saveStationAngel],创建服务站分布式锁获取失败!");
            throw new BusinessException(AngelErrorCode.DOING_WAIT_MOMENT);
        }

        AssertUtils.hasText(angelStationSaveCmd.getAngelStationName(), "服务站名称不能为空");
        AssertUtils.nonNull(angelStationSaveCmd.getFenceShapeType(), "围栏类型不能为空");
        if(angelStationSaveCmd.getFenceShapeType()==null||angelStationSaveCmd.getFenceShapeType().equals(FenceShapeTypeEnum.CIRCLE.getType())){
            AssertUtils.nonNull(angelStationSaveCmd.getFenceRangeRadius(), "服务站半径未输入");
            AssertUtils.hasText(angelStationSaveCmd.getFenceRangeCenterLat(), "服务站纬度为空");
            AssertUtils.hasText(angelStationSaveCmd.getFenceRangeCenterLng(), "服务站经度为空");
            if(angelStationSaveCmd.getFenceRangeRadius()< 5 || angelStationSaveCmd.getFenceRangeRadius() > 50){
                throw new BusinessException(AngelErrorCode.ANGEL_STATUS_RADIOS_ERROR);
            }
        }else{
            AssertUtils.nonNull(angelStationSaveCmd.getFenceBoundaryList(), "围栏坐标集合不能为空");
        }

        if(Objects.isNull(angelStationSaveCmd.getNurseNum()) && CollectionUtils.isNotEmpty(angelStationSaveCmd.getNurseInventoryReadjustCmdList())) {
            throw new BusinessException(AngelErrorCode.MAX_NURSE_INVENTORY_HAS_READJUST);
        }

        if(CollectionUtils.isEmpty(angelStationSaveCmd.getAngelTypes())){
            throw new ArgumentsException(BusinessErrorCode.ILLEGAL_ARG_ERROR, "服务资源类型不能为空");
        }

        // 启用检测是否存在接驳点，并查询接驳点是否存在
        if (angelStationSaveCmd.getJdTransferStationId() != null) {
            JdhStoreTransferStation jdhStoreTransferStation = jdhStoreTransferStationRepository.query(JdhStoreTransferStation.builder().jdStationId(angelStationSaveCmd.getJdTransferStationId()).build());
            if (jdhStoreTransferStation == null) {
                throw new BusinessException(AngelErrorCode.ANGEL_STATION_NOT_EXIST, "服务站关联实验室接驳点已下架");
            }
        }

        //统一去除字符串空格
        TrimUtil.trimStringFields(angelStationSaveCmd);

        //检查服务站名称是否已经存在
        StationDbQuery stationDbQuery = StationDbQuery.builder().stationName(angelStationSaveCmd.getAngelStationName())
                .notInStationIds(Objects.nonNull(angelStationSaveCmd.getAngelStationId()) ? Sets.newHashSet(angelStationSaveCmd.getAngelStationId()) : null)
                .build();
        List<JdhStation> stationList = jdhStationRepository.findAccurateList(stationDbQuery);
        if(CollectionUtils.isNotEmpty(stationList)){
            throw new BusinessException(AngelErrorCode.ANGEL_STATION_EXIST_ALREADY);
        }

        JdhLayerDbQuery jdhLayerDbQuery = JdhLayerDbQuery.builder().modeType(LayerModeTypeEnum.FULL_TIME.getType()).build();
        List<JdhLayer> layerList = jdhLayerRepository.findList(jdhLayerDbQuery);

        if(CollectionUtils.isEmpty(layerList)){
            log.error("[AngelStationSaveApplicationImpl.saveStationElement],创建服务站参数异常!angelStationSaveCmd={}", JSON.toJSONString(angelStationSaveCmd));
            throw new BusinessException(AngelErrorCode.LAYER_NOT_EXIST);
        }

        JdhMapDbQuery jdhMapDbQuery = JdhMapDbQuery.builder().mapId(Sets.newHashSet(layerList.get(0).getMapId())).build();
        List<JdhMap> jdhMapList = jdhMapRepository.findList(jdhMapDbQuery);
        if(CollectionUtils.isEmpty(jdhMapList)){
            log.error("[AngelStationSaveApplicationImpl.saveStationElement],创建服务站参数异常!angelStationSaveCmd={}", JSON.toJSONString(angelStationSaveCmd));
            throw new BusinessException(AngelErrorCode.MAP_NOT_EXIST);
        }
        angelStationSaveCmd.setLayerId(layerList.get(0).getLayerId());
        angelStationSaveCmd.setMapId(jdhMapList.get(0).getMapId());
        AngelStationSaveContext angelStationSaveContext = MapApplicationConverter.ins.convertToAngelStationSaveContext(angelStationSaveCmd);
        angelStationSaveContext.setPartStationFlag(false);

        if(angelStationSaveCmd.getStationId()!=null){
            //查询实验室信息,获取实验室联系人和电话
            StoreInfoBo storeInfoBo = providerStoreExportServiceRpc.queryByStoreId(angelStationSaveCmd.getStationId());
            if(storeInfoBo!=null){
                angelStationSaveContext.setContactName(StringUtils.defaultIfBlank(storeInfoBo.getContactName(),"张灿"));
                angelStationSaveContext.setContactPhone(StringUtils.defaultIfBlank(storeInfoBo.getContactPhone(),"***********"));
            }else{
                //兜底处理
                angelStationSaveContext.setContactName("张灿");
                angelStationSaveContext.setContactPhone("***********");
            }
        }

        log.info("[angelStationSaveContext={}]", JSON.toJSONString(angelStationSaveContext));
        return jdhMapDomainService.saveStationElement(angelStationSaveContext);
    }

    /**
     * 绑定服务站站长
     *
     * @param bindAngelMasterCmd
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean bindStationMaster(BindAngelMasterCmd bindAngelMasterCmd) {
//        AssertUtils.isNotEmpty(bindAngelMasterCmd.getAngelStationId(), "未选中服务站");
        AssertUtils.hasText(bindAngelMasterCmd.getStationMaster(), "未输入站长信息");

        if("yaoqinghai".equals(bindAngelMasterCmd.getStationMaster())){
            bindAngelMasterCmd.setStationMaster("guopeiyu7");
        }

        List<JdhStation> list;
        if(CollectionUtils.isNotEmpty(bindAngelMasterCmd.getAngelStationId())){
            StationDbQuery stationDbQuery = StationDbQuery.builder().stationIds(bindAngelMasterCmd.getAngelStationId()).build();
            list = jdhStationRepository.findList(stationDbQuery);
        }else {
            list = jdhStationRepository.findNoMasterList();
        }
        if(CollectionUtils.isEmpty(list)){
            log.error("[AngelStationSaveApplicationImpl.bindStationMaster],服务站信息不存在!bindAngelMasterCmd={}", JSON.toJSONString(bindAngelMasterCmd));
            throw new BusinessException(AngelErrorCode.ANGEL_STATION_NOT_EXIST);
        }

        AtomicInteger saveNum = new AtomicInteger();
        list.stream().forEach(station -> {
            JdhStation updateAngelStation = JdhStation.builder()
                    .id(station.getId())
                    .angelStationId(station.getAngelStationId())
                    .stationMaster(bindAngelMasterCmd.getStationMaster())
                    .version(station.getVersion())
                    .build();

            saveNum.addAndGet(jdhStationRepository.updateStation(updateAngelStation));
        });

        if(saveNum.get() != list.size()){
            log.error("[AngelStationSaveApplicationImpl.bindStationMaster],绑定站长失败!executeSize = {}, batchSize={}", saveNum.get(), list.size());
            throw new BusinessException(AngelErrorCode.BIND_ANGEL_STATION_MASTER_ERROR);
        }
        return true;
    }

    /**
     * 启停服务站
     *
     * @param startStopAngelStationCmd
     * @return
     */
    @Override
    public Boolean startOrStop(StartStopAngelStationCmd startStopAngelStationCmd) {
        String errMsg = ValidateParamUtil.paramValidation(startStopAngelStationCmd);
        if(StringUtils.isNotBlank(errMsg)){
            log.error("[AngelStationSaveApplicationImpl.bindStationMaster],启停服务站参数异常!startStopAngelStationCmd={}", JSON.toJSONString(startStopAngelStationCmd));
            throw new ArgumentsException(BusinessErrorCode.ILLEGAL_ARG_ERROR, errMsg);
        }

        JdhStation jdhStation = jdhStationRepository.find(JdhStationIdentifier.builder().angelStationId(startStopAngelStationCmd.getAngelStationId()).build());
        if(Objects.isNull(jdhStation)){
            log.error("[AngelStationSaveApplicationImpl.bindStationMaster],服务站信息不存在!startStopAngelStationCmd={}", JSON.toJSONString(startStopAngelStationCmd));
            throw new BusinessException(AngelErrorCode.ANGEL_STATION_NOT_EXIST, errMsg);
        }
        // 启用检测是否存在接驳点，并查询接驳点是否存在
        if (AngelStationStatusEnum.ALIVE.getCode().equals(startStopAngelStationCmd.getStationStatus()) && jdhStation.getJdTransferStationId() != null) {
            JdhStoreTransferStation jdhStoreTransferStation = jdhStoreTransferStationRepository.query(JdhStoreTransferStation.builder().jdStationId(jdhStation.getJdTransferStationId()).build());
            if (jdhStoreTransferStation == null) {
                throw new BusinessException(AngelErrorCode.ANGEL_STATION_NOT_EXIST, "服务站关联实验室接驳点已下架");
            }
        }

        //启停判断围栏数据是否存在
        AngelStationStartStopContext angelStationStartStopContext = new AngelStationStartStopContext();
        angelStationStartStopContext.setMapId(jdhStation.getMapId());
        angelStationStartStopContext.setAngelStationStatus(startStopAngelStationCmd.getStationStatus());
        angelStationStartStopContext.setAngelStationName(jdhStation.getAngelStationName());
        angelStationStartStopContext.setAngelStationId(jdhStation.getAngelStationId());
        angelStationStartStopContext.setFenceId(jdhStation.getFenceId());
        angelStationStartStopContext.setElementGeometry(jdhStation.getFenceBoundaryList());
        angelStationStartStopContext.setFenceRangeRadius(jdhStation.getFenceRangeRadius());
        angelStationStartStopContext.setLayerId(jdhStation.getLayerId());
        String newFenceId = jdhMapDomainService.startOrStopElement(angelStationStartStopContext);

        jdhStation.setStationStatus(startStopAngelStationCmd.getStationStatus());
        jdhStation.setFenceId(newFenceId);
        boolean updateSuc = jdhStationRepository.updateStation(jdhStation) > 0;
        if(updateSuc){
            log.info("[AngelStationSaveApplicationImpl.bindStationMaster],发送状态变更消息!");
            jdhStation.fillChangeBitMap(jdhStation.STATUS_INDEX);
            AngelStationEventBody eventBody = new AngelStationEventBody();
            eventBody.setPosition(jdhStation.getChangeBitMap());
            eventBody.setStationId(jdhStation.getAngelStationId());
            Event event = EventFactory.newDefaultEvent(jdhStation, AngelStationEventTypeEnum.ANGEL_STATION_MODIFY, eventBody);
            eventCoordinator.publish(event);
        }
        return updateSuc;
    }

    /**
     * 服务站解绑资源
     *
     * @param angelStationUnbindCmd
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean unBind(AngelStationUnbindCmd angelStationUnbindCmd) {
        String errMsg = ValidateParamUtil.paramValidation(angelStationUnbindCmd);
        if(StringUtils.isNotBlank(errMsg)){
            log.error("[AngelStationSaveApplicationImpl.bindStationMaster],服务站解绑资源参数异常!startStopAngelStationCmd={}", JSON.toJSONString(angelStationUnbindCmd));
            throw new ArgumentsException(BusinessErrorCode.ILLEGAL_ARG_ERROR, errMsg);
        }

        JdhStationAngelManQuery manQuery = new JdhStationAngelManQuery();
        manQuery.setAngelIdSet(Objects.nonNull(angelStationUnbindCmd.getAngelId()) ? Sets.newHashSet(angelStationUnbindCmd.getAngelId()) : null);
        manQuery.setAngelStationId(angelStationUnbindCmd.getAngelStationId());
        List<JdhStationAngelMan> jdhStationAngelManList = jdhStationAngelManRepository.findList(manQuery);
        if(CollectionUtils.isEmpty(jdhStationAngelManList)){
            log.error("[AngelStationSaveApplicationImpl.bindStationMaster],服务站资源信息不存在!angelStationUnbindCmd={}", JSON.toJSONString(angelStationUnbindCmd));
            throw new BusinessException(AngelErrorCode.STATION_ANGEL_NOT_EXIST);
        }

        //将服务者服务站删掉
        AngelBindStationCmd angelBindStationCmd = new AngelBindStationCmd();
        angelBindStationCmd.setStationId(angelStationUnbindCmd.getAngelStationId());
        angelBindStationCmd.setAngelIdList(Lists.newArrayList(angelStationUnbindCmd.getAngelId()));
        angelApplication.batchUnBindStation(angelBindStationCmd);

        return jdhStationAngelManRepository.remove(jdhStationAngelManList.get(0)) > 0 ? true : false;
    }

    /**
     * 保存服务者资源信息
     *
     * @param stationAngelSaveCmd
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveStationAngel(StationAngelSaveCmd stationAngelSaveCmd) {
        String lockKey = RedisKeyEnum.getRedisKey(RedisKeyEnum.ANGEL_STATION_BIND_ANGEL_KEY, stationAngelSaveCmd.getAngelStationId());
        if(!redisLockUtil.tryLock(lockKey, "1", RedisKeyEnum.ANGEL_STATION_BIND_ANGEL_KEY.getExpireTime(), RedisKeyEnum.ANGEL_STATION_BIND_ANGEL_KEY.getExpireTimeUnit())){
            log.error("[AngelStationSaveApplicationImpl.saveStationAngel],服务站绑定资源分布式锁获取失败!");
            throw new BusinessException(AngelErrorCode.DOING_WAIT_MOMENT);
        }

        String errMsg = ValidateParamUtil.paramValidation(stationAngelSaveCmd);
        if(StringUtils.isNotBlank(errMsg)){
            log.error("[AngelStationSaveApplicationImpl.bindStationMaster],保存服务者资源信息参数异常!stationAngelSaveCmd={}", JSON.toJSONString(stationAngelSaveCmd));
            throw new ArgumentsException(BusinessErrorCode.ILLEGAL_ARG_ERROR, errMsg);
        }
        //去除空格
        TrimUtil.trimStringFields(stationAngelSaveCmd);

        //检查资源是否已经绑定过服务站
        List<AngelInfo> angelInfoListList = stationAngelSaveCmd.getAngelInfoListList();
        Set<Long> angelIdSet = angelInfoListList.stream().map(AngelInfo::getAngelId).collect(Collectors.toSet());
        JdhStationAngelManQuery manQuery = new JdhStationAngelManQuery();
        manQuery.setAngelIdSet(angelIdSet);
        List<JdhStationAngelMan> list = jdhStationAngelManRepository.findList(manQuery);
        if(CollectionUtils.isNotEmpty(list)){
            log.error("[AngelStationSaveApplicationImpl.saveStationAngel],服务者已经绑定了服务站!stationAngelSaveCmd={}", JSON.toJSONString(stationAngelSaveCmd));
            throw new BusinessException(AngelErrorCode.STATION_ANGEL_NOT_EXIST);
        }

        //检查服务站
        JdhStation jdhStation =
                jdhStationRepository.find(JdhStationIdentifier.builder().angelStationId(stationAngelSaveCmd.getAngelStationId()).build());
        if(Objects.isNull(jdhStation) || !Objects.equals(jdhStation.getStationStatus(), AngelStationStatusEnum.ALIVE.getCode())){
            log.error("[AngelStationSaveApplicationImpl.bindStationMaster],服务站信息不存在!stationAngelSaveCmd={}", JSON.toJSONString(stationAngelSaveCmd));
            throw new BusinessException(AngelErrorCode.ANGEL_STATION_NOT_EXIST);
        }
        List<JdhStationAngelMan> stationAngelManList = Lists.newArrayList();
        angelInfoListList.stream().forEach(angel -> {
            AngelRequest angelRequest = new AngelRequest();
            angelRequest.setAngelId(angel.getAngelId());
            JdhAngelDto jdhAngelDto = angelApplication.queryByAngelInfo(angelRequest);
            JdhStationAngelMan angelMan = JdhStationAngelManFactory.create(jdhStation, angel.getAngelId(), jdhAngelDto.getAngelName(), jdhAngelDto.getPhone(), jdhAngelDto.getIdCard(), stationAngelSaveCmd.getOperator());
            stationAngelManList.add(angelMan);
        });

        //批量绑定服务站的接口
        AngelBindStationCmd angelBindStationCmd = new AngelBindStationCmd();
        angelBindStationCmd.setStationId(stationAngelSaveCmd.getAngelStationId());
        angelBindStationCmd.setOperator(stationAngelSaveCmd.getOperator());
        angelBindStationCmd.setAngelIdList(angelInfoListList.stream().map(AngelInfo::getAngelId).collect(Collectors.toList()));
        angelApplication.batchBindStation(angelBindStationCmd);

        //服务站绑定资源
        return jdhStationAngelManRepository.batchSave(stationAngelManList) > 0 ? true : false;
    }

    /**
     * 服务站解绑sku
     *
     * @param angelStationUnbindSkuCmd
     * @return
     */
    @Override
    public Boolean skuUnbind(AngelStationUnbindSkuCmd angelStationUnbindSkuCmd) {
        String errMsg = ValidateParamUtil.paramValidation(angelStationUnbindSkuCmd);
        if(StringUtils.isNotBlank(errMsg)){
            log.error("[AngelStationSaveApplicationImpl.bindStationMaster],服务站解绑sku参数异常!startStopAngelStationCmd={}", JSON.toJSONString(angelStationUnbindSkuCmd));
            throw new ArgumentsException(BusinessErrorCode.ILLEGAL_ARG_ERROR, errMsg);
        }

        JdhStationSkuManQuery manQuery = new JdhStationSkuManQuery();
        manQuery.setSkuIdSet(Objects.nonNull(angelStationUnbindSkuCmd.getSkuId()) ? Sets.newHashSet(angelStationUnbindSkuCmd.getSkuId()) : null);
        manQuery.setAngelStationId(angelStationUnbindSkuCmd.getAngelStationId());
        List<JdhStationSkuMan> stationSkuManList = jdhStationSkuManRepository.findList(manQuery);
        if(CollectionUtils.isEmpty(stationSkuManList)){
            log.error("[AngelStationSaveApplicationImpl.bindStationMaster],服务站sku信息不存在!angelStationUnbindCmd={}", JSON.toJSONString(stationSkuManList));
            throw new BusinessException(AngelErrorCode.STATION_ANGEL_NOT_EXIST);
        }

        //解绑商品的服务站的管理关系
        JdhStationSkuRel rel  = JdhStationSkuRel.builder()
                .stationId(angelStationUnbindSkuCmd.getAngelStationId())
                .skuId(angelStationUnbindSkuCmd.getSkuId())
                .build();
        jdhStationSkuRelRepository.remove(rel);
        return jdhStationSkuManRepository.remove(stationSkuManList.get(0)) > 0 ? true : false;
    }

    /**
     * 保存服务站商品信息
     *
     * @param stationSkuSaveCmd
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveStationSku(StationSkuSaveCmd stationSkuSaveCmd) {
        String lockKey = RedisKeyEnum.getRedisKey(RedisKeyEnum.ANGEL_STATION_BIND_SKU_KEY, stationSkuSaveCmd.getAngelStationId());
        if(!redisLockUtil.tryLock(lockKey, "1", RedisKeyEnum.ANGEL_STATION_BIND_ANGEL_KEY.getExpireTime(), RedisKeyEnum.ANGEL_STATION_BIND_ANGEL_KEY.getExpireTimeUnit())){
            log.error("[AngelStationSaveApplicationImpl.saveStationAngel],服务站绑定资源分布式锁获取失败!");
            throw new BusinessException(AngelErrorCode.DOING_WAIT_MOMENT);
        }
        //统一去前后空格
        TrimUtil.trimStringFields(stationSkuSaveCmd);

        String errMsg = ValidateParamUtil.paramValidation(stationSkuSaveCmd);
        if(StringUtils.isNotBlank(errMsg)){
            log.error("[AngelStationSaveApplicationImpl.bindStationMaster],保存服务者商品信息参数异常!stationSkuSaveCmd={}", JSON.toJSONString(stationSkuSaveCmd));
            throw new ArgumentsException(BusinessErrorCode.ILLEGAL_ARG_ERROR, errMsg);
        }

        //检查资源是否已经绑定过服务站
        List<StationSKuInfo> angelStationSkuList = stationSkuSaveCmd.getAngelStationSkuList();
        Set<Long> skuIdSet = angelStationSkuList.stream().map(StationSKuInfo::getSkuId).collect(Collectors.toSet());
        if(skuIdSet.size() != angelStationSkuList.size()) {
            log.error("[AngelStationSaveApplicationImpl.saveStationSku],存在重复的sku!stationSkuSaveCmd={}", JSON.toJSONString(stationSkuSaveCmd));
            throw new BusinessException(AngelErrorCode.ANGEL_SKU_REPEAT);
        }

        //检查商品信息是否存在
        List<JdhSku> jdhSkuList = skuIdSet.stream().map(item -> {
            JdhSku jdhSku = new JdhSku();
            jdhSku.setSkuId(item);
            return jdhSku;
        }).collect(Collectors.toList());
        List<JdhSku> jdhSkus = jdhSkuRepository.queryMultiSku(jdhSkuList);
        if(CollectionUtils.isEmpty(jdhSkus) || jdhSkus.size() != skuIdSet.size()) {
            log.error("[StationApplicationImpl.querySkuDetail],查询商品异常!angelStationSkuQuery={}", JSON.toJSONString(stationSkuSaveCmd));
            throw new BusinessException(AngelErrorCode.SKU_HAS_NOT_EXIST_ERROR);
        }

        JdhStationSkuManQuery manQuery = new JdhStationSkuManQuery();
        manQuery.setSkuIdSet(skuIdSet);
        manQuery.setAngelStationId(stationSkuSaveCmd.getAngelStationId());
        List<JdhStationSkuMan> list = jdhStationSkuManRepository.findList(manQuery);
        if(CollectionUtils.isNotEmpty(list)){
            log.error("[AngelStationSaveApplicationImpl.saveStationAngel]，sku已经绑定了服务站!stationSkuSaveCmd={}", JSON.toJSONString(stationSkuSaveCmd));
            throw new BusinessException(AngelErrorCode.ANGEL_SKU_REPEAT);
        }

        //检查服务站
        JdhStation jdhStation =
                jdhStationRepository.find(JdhStationIdentifier.builder().angelStationId(stationSkuSaveCmd.getAngelStationId()).build());
        if(Objects.isNull(jdhStation) || !Objects.equals(jdhStation.getStationStatus(), AngelStationStatusEnum.ALIVE.getCode())){
            log.error("[AngelStationSaveApplicationImpl.bindStationMaster],服务站信息不存在!stationSkuSaveCmd={}", JSON.toJSONString(stationSkuSaveCmd));
            throw new BusinessException(AngelErrorCode.ANGEL_STATION_NOT_EXIST);
        }
        List<JdhStationSkuMan> stationSkuManList = Lists.newArrayList();
        List<JdhStationSkuRel> stationSkuRelList = Lists.newArrayList();
        Integer onOffShelf = stationSkuSaveCmd.getOnOffShelf() == null ? 1 : stationSkuSaveCmd.getOnOffShelf();
        angelStationSkuList.stream().forEach(sku -> {
            JdhStationSkuMan jdhStationSkuMan = JdhStationSkuManFactory.create(jdhStation, sku.getSkuId(), sku.getSkuName(), stationSkuSaveCmd.getOperator());
            if (sku.getOnOffShelf() != null) {
                jdhStationSkuMan.setOnOffShelf(sku.getOnOffShelf());
            } else {
                jdhStationSkuMan.setOnOffShelf(onOffShelf);
            }
            stationSkuManList.add(jdhStationSkuMan);

            JdhStationSkuRel jdhStationSkuRel = JdhStationSkuRelFactory.create(jdhStation.getAngelStationId(), sku.getSkuId(), sku.getSkuName(), stationSkuSaveCmd.getOperator());
            if (sku.getOnOffShelf() != null) {
                jdhStationSkuRel.setOnOffShelf(sku.getOnOffShelf());
            } else {
                jdhStationSkuRel.setOnOffShelf(onOffShelf);
            }
            stationSkuRelList.add(jdhStationSkuRel);
        });
        jdhStationSkuRelRepository.batchSave(stationSkuRelList);
        return jdhStationSkuManRepository.batchSave(stationSkuManList) > 0 ? true : false;
    }

    /**
     * 商品绑定到服务站
     *
     * @param stationSaveCmd
     * @return
     */
    @Override
    public Boolean bindSkuToAngelStation(SkuToStationSaveCmd stationSaveCmd) {
        AssertUtils.nonNull(stationSaveCmd, "参数异常");
        AssertUtils.nonNull(stationSaveCmd.getSkuId(), "商品编码不能为空");
        AssertUtils.hasText(stationSaveCmd.getOperator(), "商品编码不能为空");
        AssertUtils.hasText(stationSaveCmd.getAngelStationSaveCmdList(), "服务站信息不能为空");

        String lockKey = RedisKeyEnum.getRedisKey(RedisKeyEnum.ANGEL_STATION_BIND_SKU_KEY, stationSaveCmd.getSkuId());
        if(!redisLockUtil.tryLock(lockKey, "1", RedisKeyEnum.ANGEL_STATION_BIND_SKU_KEY.getExpireTime(), RedisKeyEnum.ANGEL_STATION_BIND_SKU_KEY.getExpireTimeUnit())){
            log.error("[AngelStationSaveApplicationImpl.bindSkuToAngelStation],服务站绑定资源分布式锁获取失败!");
            throw new BusinessException(AngelErrorCode.DOING_WAIT_MOMENT);
        }
        //统一去前后空格
        TrimUtil.trimStringFields(stationSaveCmd);


        //检查资源是否已经绑定过服务站
        String stationListStr = stationSaveCmd.getAngelStationSaveCmdList();
        List<AngelStationSaveCmd> angelStationSaveCmdList = JSON.parseArray(stationListStr, AngelStationSaveCmd.class);
        Set<Long> angelStationIds = angelStationSaveCmdList.stream().map(AngelStationSaveCmd::getAngelStationId).collect(Collectors.toSet());
        if(angelStationIds.size() != angelStationSaveCmdList.size()) {
            log.error("[AngelStationSaveApplicationImpl.bindSkuToAngelStation],存在重复的服务站!stationSkuSaveCmd={}", JSON.toJSONString(angelStationSaveCmdList));
            throw new BusinessException(AngelErrorCode.ANGEL_STATION_EXIST_ALREADY);
        }

        //检查商品信息是否存在
        JdhSku jdhSku = jdhSkuRepository.find(new JdhSkuIdentifier(stationSaveCmd.getSkuId()));
        if(Objects.isNull(jdhSku)) {
            log.error("[StationApplicationImpl.bindSkuToAngelStation],查询商品异常!angelStationSkuQuery={}", JSON.toJSONString(stationSaveCmd));
            throw new BusinessException(AngelErrorCode.SKU_HAS_NOT_EXIST_ERROR);
        }

        JdhStationSkuManQuery manQuery = new JdhStationSkuManQuery();
        manQuery.setSkuIdSet(Sets.newHashSet(stationSaveCmd.getSkuId()));
        manQuery.setAngelStationIds(angelStationIds);
        List<JdhStationSkuMan> list = jdhStationSkuManRepository.findList(manQuery);
        if(CollectionUtils.isNotEmpty(list)){
            log.error("[AngelStationSaveApplicationImpl.bindSkuToAngelStation]，sku已经绑定了服务站!stationSkuSaveCmd={}", JSON.toJSONString(stationSaveCmd));
            throw new BusinessException(AngelErrorCode.ANGEL_SKU_REPEAT);
        }

        //检查服务站
        StationDbQuery stationDbQuery = new StationDbQuery();
        stationDbQuery.setStationIds(angelStationIds);
        stationDbQuery.setAngelStationStatus(AngelStationStatusEnum.ALIVE.getCode());
        List<JdhStation> jdhStationList = jdhStationRepository.findList(stationDbQuery);
        if(CollectionUtils.isEmpty(jdhStationList)){
            log.error("[AngelStationSaveApplicationImpl.bindSkuToAngelStation],服务站信息不存在!stationSkuSaveCmd={}", JSON.toJSONString(stationSaveCmd));
            throw new BusinessException(AngelErrorCode.ANGEL_STATION_NOT_EXIST);
        }
        List<JdhStationSkuMan> stationSkuManList = Lists.newArrayList();
        List<JdhStationSkuRel> stationSkuRelList = Lists.newArrayList();
        Integer onOffShelf = stationSaveCmd.getOnOffShelf() == null ? 1 : stationSaveCmd.getOnOffShelf();
        jdhStationList.stream().forEach(angelStation -> {
            JdhStationSkuMan jdhStationSkuMan = JdhStationSkuManFactory.create(angelStation, stationSaveCmd.getSkuId(), stationSaveCmd.getSkuName(), stationSaveCmd.getOperator());
            jdhStationSkuMan.setOnOffShelf(onOffShelf);
            stationSkuManList.add(jdhStationSkuMan);

            JdhStationSkuRel jdhStationSkuRel = JdhStationSkuRelFactory.create(angelStation.getAngelStationId(), stationSaveCmd.getSkuId(), stationSaveCmd.getSkuName(), stationSaveCmd.getOperator());
            jdhStationSkuRel.setOnOffShelf(onOffShelf);
            stationSkuRelList.add(jdhStationSkuRel);
        });
        jdhStationSkuRelRepository.batchSave(stationSkuRelList);
        return jdhStationSkuManRepository.batchSave(stationSkuManList) > 0 ? true : false;
    }

    /**
     * 更新服务站商品信息
     *
     * @param stationSkuUpdateCmd
     * @return
     */
    @Override
    public Boolean updateOnOffShelf(StationSkuUpdateCmd stationSkuUpdateCmd) {
        AssertUtils.nonNull(stationSkuUpdateCmd, "参数异常");
        AssertUtils.nonNull(stationSkuUpdateCmd.getSkuId(), "商品编码不能为空");
        AssertUtils.nonNull(stationSkuUpdateCmd.getAngelStationId(), "站点编码不能为空");
        AssertUtils.nonNull(stationSkuUpdateCmd.getOnOffShelf(), "上下架状态不能为空");

        jdhStationSkuRelRepository.updateOnOffShelf(JdhStationSkuRel.builder().skuId(stationSkuUpdateCmd.getSkuId()).stationId(stationSkuUpdateCmd.getAngelStationId()).onOffShelf(stationSkuUpdateCmd.getOnOffShelf()).build());
        return jdhStationSkuManRepository.updateOnOffShelf(JdhStationSkuMan.builder().skuId(stationSkuUpdateCmd.getSkuId()).stationId(stationSkuUpdateCmd.getAngelStationId()).onOffShelf(stationSkuUpdateCmd.getOnOffShelf()).build()) > 0;
    }


}
