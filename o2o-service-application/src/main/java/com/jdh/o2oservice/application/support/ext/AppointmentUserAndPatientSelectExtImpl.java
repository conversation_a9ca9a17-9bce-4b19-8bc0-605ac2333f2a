package com.jdh.o2oservice.application.support.ext;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.jd.medicine.base.common.util.StringUtil;
import com.jdh.o2oservice.base.enums.YnStatusEnum;
import com.jdh.o2oservice.base.model.DomainEnum;
import com.jdh.o2oservice.base.util.EntityUtil;
import com.jdh.o2oservice.core.domain.medpromise.enums.MedPromiseAggregateEnum;
import com.jdh.o2oservice.core.domain.medpromise.model.MedicalPromise;
import com.jdh.o2oservice.core.domain.medpromise.model.MedicalPromiseIdentifier;
import com.jdh.o2oservice.core.domain.medpromise.repository.db.MedicalPromiseRepository;
import com.jdh.o2oservice.core.domain.promise.enums.PromiseAggregateEnum;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromise;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromiseIdentifier;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromisePatient;
import com.jdh.o2oservice.core.domain.promise.repository.db.PromiseRepository;
import com.jdh.o2oservice.core.domain.promise.repository.db.VoucherRepository;
import com.jdh.o2oservice.core.domain.support.basic.model.PhoneNumber;
import com.jdh.o2oservice.core.domain.support.reach.enums.ReachUserTypeEnum;
import com.jdh.o2oservice.core.domain.support.reach.ext.ReachServiceSelectUserExt;
import com.jdh.o2oservice.core.domain.support.reach.ext.dto.ReachUser;
import com.jdh.o2oservice.core.domain.support.reach.ext.param.SelectReachUserParam;
import com.jdh.o2oservice.infrastructure.repository.db.dao.JdhPromisePatientPoMapper;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhPromisePatientPo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;

/**
 * 查询promise预约人和被服务人，根据手机号去重
 * @author: yangxiyu
 * @date: 2024/4/18 5:06 下午
 * @version: 1.0
 */
@Component
@Slf4j
public class AppointmentUserAndPatientSelectExtImpl implements ReachServiceSelectUserExt {

    /**
     *
     */
    @Resource
    private PromiseRepository promiseRepository;

    @Resource
    private VoucherRepository voucherRepository;
    @Resource
    private MedicalPromiseRepository medicalPromiseRepository;
    @Resource
    private JdhPromisePatientPoMapper jdhPromisePatientPoMapper;

    /**
     * 查询到家服务触达人群
     * @param param
     * @return
     */
    @Override
    public List<ReachUser> selectUsers(SelectReachUserParam param) {
        List<ReachUser> list = Lists.newArrayList();
        if(DomainEnum.PROMISE.getCode().equals(param.getDomainCode())&& PromiseAggregateEnum.VOUCHER.getCode().equals(param.getAggregateCode())){


        }else if(DomainEnum.PROMISE.getCode().equals(param.getDomainCode())&& PromiseAggregateEnum.PROMISE.getCode().equals(param.getAggregateCode())){
            Long promiseId = Long.valueOf(param.getAggregateId());
            JdhPromise promise = promiseRepository.find(new JdhPromiseIdentifier(promiseId));

            List<PhoneNumber> phoneNumbers = EntityUtil.getFiledDefaultNull(promise.getPatients(), JdhPromisePatient::getPhoneNumber);
            List<String> phones = EntityUtil.getFiledDefaultNull(phoneNumbers, PhoneNumber::getPhone);
            if (CollectionUtils.isNotEmpty(phones)){
                Set<String> phoneSet = Sets.newHashSet(phones);
                for (String phone : phoneSet) {
                    ReachUser reachUser = new ReachUser();
                    reachUser.setPhone(phone);
                    reachUser.setUserType(ReachUserTypeEnum.PATIENT.getCode());
                    list.add(reachUser);
                }

                if (!phones.contains(promise.getAppointmentPhone())){
                    ReachUser reachUser = new ReachUser();
                    reachUser.setPhone(promise.getAppointmentPhone());
                    reachUser.setUserType(ReachUserTypeEnum.APPOINTMENT_USER.getCode());
                    list.add(reachUser);
                }
            }else {
                ReachUser reachUser = new ReachUser();
                reachUser.setPhone(promise.getAppointmentPhone());
                reachUser.setUserType(ReachUserTypeEnum.APPOINTMENT_USER.getCode());
                list.add(reachUser);
            }

            // 检测单是获取履约单预约人和当前检测单被检测人
        }else if(DomainEnum.MED_PROMISE.getCode().equals(param.getDomainCode())&& MedPromiseAggregateEnum.MED_PROMISE.getCode().equals(param.getAggregateCode())){
            Long medicalPromiseId = Long.valueOf(param.getAggregateId());

            MedicalPromise medicalPromise = medicalPromiseRepository.find(new MedicalPromiseIdentifier(medicalPromiseId));
            Long promisePatientId = medicalPromise.getPromisePatientId();

            LambdaQueryWrapper<JdhPromisePatientPo> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper.eq(JdhPromisePatientPo::getPromisePatientId, promisePatientId)
                    .eq(JdhPromisePatientPo::getYn, YnStatusEnum.YES.getCode());
            JdhPromisePatientPo patientPo = jdhPromisePatientPoMapper.selectOne(queryWrapper);
            String phone = patientPo.getUserPhone();

            ReachUser reachUser = new ReachUser();
            reachUser.setPhone(phone);
            reachUser.setUserType(ReachUserTypeEnum.PATIENT.getCode());
            list.add(reachUser);
            JdhPromise jdhPromise = promiseRepository.find(new JdhPromiseIdentifier(medicalPromise.getPromiseId()));
            if (!StringUtil.equals(jdhPromise.getAppointmentPhone(), phone)){
                ReachUser appointmentUser = new ReachUser();
                appointmentUser.setPhone(phone);
                appointmentUser.setUserType(ReachUserTypeEnum.APPOINTMENT_USER.getCode());
                list.add(appointmentUser);
            }
        }
        log.info("AppointmentUserAndPatientSelectExtImpl->selectUsers list={}", JSON.toJSONString(list));
        return list;
    }

    @Override
    public String functionId() {
        return "appointmentUserAndPatientSelect";
    }
}
