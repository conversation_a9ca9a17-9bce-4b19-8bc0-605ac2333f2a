package com.jdh.o2oservice.application.support.price;

import cn.hutool.core.convert.Convert;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.core.domain.support.basic.enums.PricingServiceFactObjectEnum;
import com.jdh.o2oservice.core.domain.support.basic.rpc.AddressRpc;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.BaseAddressBo;
import com.jdh.o2oservice.core.domain.support.price.context.PricingServiceCalculateContext;
import com.jdh.o2oservice.core.domain.support.price.enums.PricingServiceErrorCode;
import com.jdh.o2oservice.core.domain.support.price.handler.PricingServiceFactObjectHandler;
import com.jdh.o2oservice.export.promise.dto.PromiseDto;
import com.jdh.o2oservice.export.promise.dto.PromiseStationDto;
import com.jdh.o2oservice.export.trade.dto.AddressInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @ClassName JdhAddressFactObjectHandler
 * @Description
 * <AUTHOR>
 * @Date 2025/5/6 08:28
 **/
@Component
@Slf4j
public class JdhAddressFactObjectHandler extends AbstractFactObjectHandler {

    /**
     * 地址服务
     */
    @Resource
    private AddressRpc addressRpc;

    /**
     * 履约单数据
     */
    @Resource
    private JdhPromiseFactObjectHandler promiseFactObjectHandler;

    /**
     * 获取事实对象
     * @param context
     * @return
     */
    @Override
    public Object getData(PricingServiceCalculateContext context) {
        boolean containsKey = context.getFactObjectMap().containsKey(getMapKey());
        if (containsKey) {
            return context.getFactObjectMap().get(getMapKey());
        }
        //前置依赖履约单数据，先从上下文中获取，没有则调用接口获取，并放入上下文
        Object factObject = promiseFactObjectHandler.getFactObject(context);
        if (Objects.isNull(factObject)) {
            throw new BusinessException(PricingServiceErrorCode.USER_PROMISE_NOT_EXIST);
        }
        PromiseDto promiseDto = Convert.convert(PromiseDto.class, factObject);
        PromiseStationDto store = promiseDto.getStore();

        if (Objects.isNull(store) || StringUtils.isBlank(store.getStoreAddr())) {
            throw new BusinessException(PricingServiceErrorCode.USER_ADDRESS_NOT_EXIST);
        }
        String storeAddr = store.getStoreAddr();
        BaseAddressBo jdAddressFromAddress = addressRpc.getJDAddressFromAddress(storeAddr.trim());
        if(Objects.isNull(jdAddressFromAddress)){
            throw new BusinessException(PricingServiceErrorCode.USER_ADDRESS_AREA_NOT_EXIST);
        }

        AddressInfoDTO addressInfo = new AddressInfoDTO();
        addressInfo.setProvinceId(jdAddressFromAddress.getProvinceCode());
        addressInfo.setProvinceName(jdAddressFromAddress.getProvinceName());
        addressInfo.setCityId(jdAddressFromAddress.getCityCode());
        addressInfo.setCityName(jdAddressFromAddress.getCityName());
        addressInfo.setCountyId(jdAddressFromAddress.getDistrictCode());
        addressInfo.setCountyName(jdAddressFromAddress.getDistrictName());
        addressInfo.setTownId(jdAddressFromAddress.getTownCode());
        addressInfo.setTownName(jdAddressFromAddress.getTownName());
        addressInfo.setAddressDetail(storeAddr);
        context.getFactObjectMap().put(getMapKey(), addressInfo);
        return addressInfo;
    }

    /**
     * 获取map key
     * @return
     */
    @Override
    public String getMapKey() {
        return PricingServiceFactObjectEnum.ADDRESS_INFO.getCode();
    }
}