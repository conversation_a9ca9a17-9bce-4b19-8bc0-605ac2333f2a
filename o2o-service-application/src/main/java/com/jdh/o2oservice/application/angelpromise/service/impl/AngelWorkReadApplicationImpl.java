package com.jdh.o2oservice.application.angelpromise.service.impl;

import com.google.common.collect.Lists;
import com.jd.fastjson.JSON;
import com.jd.medicine.base.common.annotation.LogAndUmp;
import com.jdh.o2oservice.application.angelpromise.convert.AngelPromiseWorkApplicationConverter;
import com.jdh.o2oservice.application.angelpromise.service.AngelWorkReadApplication;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelWork;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelWorkRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.query.AngelWorkDBQuery;
import com.jdh.o2oservice.core.domain.medpromise.model.MedicalPromise;
import com.jdh.o2oservice.core.domain.medpromise.query.MedicalPromiseListQuery;
import com.jdh.o2oservice.core.domain.medpromise.repository.db.MedicalPromiseRepository;
import com.jdh.o2oservice.export.angelpromise.dto.AngelWorkItemDto;
import com.jdh.o2oservice.export.angelpromise.query.AngelWorkItemQuery;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @ClassName AngelWorkReadApplicationImpl
 * @Description
 * <AUTHOR>
 * @Date 2025/5/27 21:46
 */
@Service
@Slf4j
public class AngelWorkReadApplicationImpl implements AngelWorkReadApplication {

    @Resource
    private AngelWorkRepository angelWorkRepository;

    @Resource
    private MedicalPromiseRepository medicalPromiseRepository;

    /**
     * 查询工单及工单项目信息
     *
     * @param angelWorkItemQuery
     * @return
     */
    @Override
    @LogAndUmp
    public List<AngelWorkItemDto> queryAngelWorkItemList(AngelWorkItemQuery angelWorkItemQuery) {
        //查询工单信息
        AngelWorkDBQuery angelWorkDBQuery = new AngelWorkDBQuery();
        angelWorkDBQuery.setWorkIds(StringUtils.isNotBlank(angelWorkItemQuery.getWorkId()) ? Lists.newArrayList(Long.valueOf(angelWorkItemQuery.getWorkId())) : null);
        angelWorkDBQuery.setPromiseId(StringUtils.isNotBlank(angelWorkItemQuery.getPromiseId()) ? Long.valueOf(angelWorkItemQuery.getPromiseId()) : null);
        List<AngelWork> angelWorkList = angelWorkRepository.findList(angelWorkDBQuery);
        if(CollectionUtils.isEmpty(angelWorkList)) {
            log.error("AngelWorkReadApplicationImpl -> queryAngelWorkItemList, 没有查询到工单信息!angelWorkItemQuery={}", JSON.toJSONString(angelWorkItemQuery));
            return null;
        }
        AngelWork angelWork = angelWorkList.get(0);

        //查询工单下的项目信息
        Long promiseId = angelWork.getPromiseId();
        if(Objects.isNull(promiseId)) {
            log.error("AngelWorkReadApplicationImpl -> queryAngelWorkItemList, 履约单id为空!angelWorkItemQuery={}", JSON.toJSONString(angelWorkItemQuery));
            return null;
        }
        MedicalPromiseListQuery medicalPromiseListQuery = new MedicalPromiseListQuery();
        medicalPromiseListQuery.setPromiseId(promiseId);
        medicalPromiseListQuery.setInvalid(Boolean.FALSE);
        medicalPromiseListQuery.setFreezeQuery(Boolean.FALSE);
        List<MedicalPromise> medicalPromises = medicalPromiseRepository.queryMedicalPromiseList(medicalPromiseListQuery);
        //检测单根据服务项目id去重
        Map<String, MedicalPromise> itemIdEntityMap = Optional.ofNullable(medicalPromises).map(List::stream).orElseGet(Stream::empty).collect(Collectors.toMap(MedicalPromise::getServiceItemId, Function.identity(), (k1, k2) -> k1));
        List<MedicalPromise> distinctMedItemList = itemIdEntityMap.values().stream().collect(Collectors.toList());
        //组装返回工单项目返回数据
        AngelWorkItemDto angelWorkItemDto = AngelPromiseWorkApplicationConverter.INS.convert2WorkItemDto(angelWork, distinctMedItemList);
        return Lists.newArrayList(angelWorkItemDto);
    }
}
