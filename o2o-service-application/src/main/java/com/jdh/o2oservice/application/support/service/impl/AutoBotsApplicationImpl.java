package com.jdh.o2oservice.application.support.service.impl;

import cn.hutool.json.JSONUtil;
import com.jd.medicine.base.common.util.CollectionUtil;
import com.jdh.o2oservice.application.support.service.AutoBotsApplication;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.common.enums.AutoBotsSceneEnum;
import com.jdh.o2oservice.core.domain.support.autobos.bo.*;
import com.jdh.o2oservice.core.domain.support.autobos.factory.AutoBotsProcessorFactory;
import com.jdh.o2oservice.core.domain.support.autobos.model.AutoBotsRecord;
import com.jdh.o2oservice.core.domain.support.autobos.repository.AutoBotsProcessor;
import com.jdh.o2oservice.core.domain.support.autobos.repository.AutoBotsRecordRepository;
import com.jdh.o2oservice.core.domain.support.autobos.rpc.AutoBotsRpc;
import com.jdh.o2oservice.core.domain.support.basic.enums.SupportErrorCode;
import com.jdh.o2oservice.export.support.command.AutoBotsCmd;
import com.jdh.o2oservice.infrastructure.rpc.autobots.AutoBotsConvert;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description
 * @date 2025/5/15
 */
@Service
@Slf4j
public class AutoBotsApplicationImpl implements AutoBotsApplication {

    /**
     * autoBotsRpc
     */
    @Autowired
    private AutoBotsRpc autoBotsRpc;

    /**
     * 自动化机器人命令记录仓库。
     */
    @Resource
    private AutoBotsRecordRepository autoBotsRecordRepository;

    /**
     * 自动化机器人处理器工厂，用于生成和管理不同类型的自动化机器人处理器。
     */
    @Autowired
    private AutoBotsProcessorFactory autoBotsProcessorFactory;

    /**
     * 发送智能问答请求，同一次会话traceId一致，reqId每次需要是新的
     *
     * @param agentId           代表要搜索的agent的唯一标识符。
     * @param token             用于验证请求的授权令牌。
     * @param autoBotsRequestBO 包含搜索条件的对象。
     * @return 搜索结果的AutoBotsResultBO对象。
     */
    @Override
    public AutoBotsResultBO searchAiRequest(String agentId, String token, AutoBotsRequestBO autoBotsRequestBO) {


        List<AutoBotsRecord> autoBotsRecords = autoBotsRecordRepository.queryAutoBotsRecordList(
                AutoBotsRequestQueryBO.builder()
                        .traceId(autoBotsRequestBO.getTraceId())
                        .reqId(autoBotsRequestBO.getReqId())
                        .build()
        );
        if (CollectionUtil.isNotEmpty(autoBotsRecords)){
            // 抛异常,同一个会话，每次问答都要生成一个新的reqId
            throw new BusinessException(SupportErrorCode.AUTO_BOTS_SEARCH_REQ_REPEAT);
        }

        //保存调用记录
        AutoBotsRecord autoBotsRecord = new AutoBotsRecord();
        autoBotsRecord.setErp(autoBotsRequestBO.getErp());
        autoBotsRecord.setExtendId(autoBotsRequestBO.getExtendId());
        autoBotsRecord.setExtendIdType(autoBotsRequestBO.getExtendIdType());
        autoBotsRecord.setTraceId(autoBotsRequestBO.getTraceId());
        autoBotsRecord.setReqId(autoBotsRequestBO.getReqId());
        autoBotsRecord.setCreateUser(autoBotsRequestBO.getErp());
        autoBotsRecord.setCreateTime(new Date());
        autoBotsRecord.setUpdateUser(autoBotsRequestBO.getErp());
        autoBotsRecord.setUpdateTime(new Date());
        autoBotsRecord.setScene(autoBotsRequestBO.getScene());
        autoBotsRecord.setKeywordText(autoBotsRequestBO.getKeyword());
        Long save = autoBotsRecordRepository.save(autoBotsRecord);

        //调用
        AutoBotsResultBO autoBotsResultBO = autoBotsRpc.searchAiRequest(agentId, token, autoBotsRequestBO);
        if (Objects.nonNull(autoBotsResultBO) && Objects.nonNull(save)){
            autoBotsRecord.setId(save);
            autoBotsRecord.setStatus(autoBotsResultBO.getStatus());
            autoBotsRecordRepository.save(autoBotsRecord);
        }


        return autoBotsResultBO;
    }

    /**
     * 获取智能问答结果，入参需要与searchAiRequest一致
     *
     * @param agentId           代表要搜索的agent的唯一标识符。
     * @param token             用于验证请求的授权令牌。
     * @param autoBotsRequestBO 包含搜索条件的对象。
     * @return 搜索结果的AutoBotsResultBO对象。
     */
    @Override
    public AutoBotsResultBO searchAiResult(String agentId, String token, AutoBotsRequestBO autoBotsRequestBO) {
        return autoBotsRpc.searchAiResult(agentId, token, autoBotsRequestBO);
    }

    /**
     * 执行工作流程。
     *
     * @param agentId 代理人ID。
     * @param token   访问令牌。
     * @param request 工作流请求对象。
     * @return 工作流执行结果。
     */
    @Override
    public AutoBotsResultBO runWorkflow(String agentId, String token, AutoBotsWfRequestBO request) {

        List<AutoBotsRecord> autoBotsRecords = autoBotsRecordRepository.queryAutoBotsRecordList(
                AutoBotsRequestQueryBO.builder()
                        .traceId(request.getTraceId())
                        .build()
        );
        if (CollectionUtil.isNotEmpty(autoBotsRecords)){
            //抛异常,每次调用工作流都要生成一个新的traceId
            throw new BusinessException(SupportErrorCode.AUTO_BOTS_TRACE_REQ_REPEAT);
        }

        //保存调用记录
        AutoBotsRecord autoBotsRecord = new AutoBotsRecord();
        autoBotsRecord.setErp(request.getErp());
        autoBotsRecord.setExtendId(request.getExtendId());
        autoBotsRecord.setExtendIdType(request.getExtendIdType());
        autoBotsRecord.setTraceId(request.getTraceId());
        autoBotsRecord.setWorkflowId(request.getWorkflowId());
        Long save = autoBotsRecordRepository.save(autoBotsRecord);


        AutoBotsResultBO autoBotsResultBO = autoBotsRpc.runWorkflow(agentId, token, request);
        if (Objects.nonNull(autoBotsResultBO)){
            autoBotsRecord.setId(save);
            autoBotsRecord.setStatus(autoBotsResultBO.getStatus());
            autoBotsRecordRepository.save(autoBotsRecord);
        }

        return autoBotsResultBO;
    }

    /**
     * 获取工作流结果
     *
     * @param agentId 代理人ID
     * @param token   认证令牌
     * @param request 自动机请求对象
     * @return 自动机结果对象
     */
    @Override
    public AutoBotsResultBO getWorkflowResult(String agentId, String token, AutoBotsWfRequestBO request) {
        return autoBotsRpc.getWorkflowResult(agentId, token, request);
    }

    /**
     * 发送智能问答请求，同一次会话traceId一致，reqId每次需要是新的,直到全部结果返回
     *
     * @param agentId           代表要搜索的agent的唯一标识符。
     * @param token             用于验证请求的授权令牌。
     * @param autoBotsRequestBO 包含搜索条件的对象。
     * @return 搜索结果的AutoBotsResultBO对象。
     */
    @Override
    public AutoBotsResultBO searchAiRequestTillComplete(String agentId, String token, AutoBotsRequestBO autoBotsRequestBO) {

        AutoBotsResultBO response = searchAiRequest(agentId, token, autoBotsRequestBO);

        if (Objects.nonNull(response)){


            String status = response.getStatus();
            if ("loading".equals(status)){
                // 等待

                response = searchAiResult(agentId, token, autoBotsRequestBO);
                status = response.getStatus();

                while ("running".equals(status)) {
                    response = searchAiResult(agentId, token, autoBotsRequestBO);
                    log.info("searchAiRequestTillComplete->智能问答获取结果：{}" , JSONUtil.toJsonStr(response));
                    status = response.getStatus();
                    // 休眠一段时间，否则调用量太多会触发限流
                    try {
                        Thread.sleep(1000);
                    }catch (Exception e){
                        log.info("searchAiRequestTillComplete->休眠异常：{}" , e.getMessage());
                    }
                }


            }else if ("no".equals(status)) {
                //大模型无法回复
                log.info("searchAiRequestTillComplete->大模型无法回复");



            }else if("running".equals(status)){

                while ("running".equals(status)) {
                    response = searchAiResult(agentId, token, autoBotsRequestBO);
                    log.info("searchAiRequestTillComplete->智能问答获取结果 running,running：{}" , JSONUtil.toJsonStr(response));
                    status = response.getStatus();
                    // 休眠一段时间，否则调用量太多会触发限流
                    try{
                        Thread.sleep(1000);
                    }catch (Exception e){

                    }
                }

            }


        }


        return response;
    }

    /**
     * 执行工作流程，直到全部结果返回
     *
     * @param agentId 代理人ID。
     * @param token   访问令牌。
     * @param request 工作流请求对象。
     * @return 工作流执行结果。
     */
    @Override
    public AutoBotsResultBO runWorkflowTillComplete(String agentId, String token, AutoBotsWfRequestBO request) {
        return null;
    }


    /**
     * 保存自动化机器人命令
     *
     * @param cmd 自动化机器人命令对象
     * @return 保存操作是否成功
     */
    @Override
    public Boolean saveAutoBots(AutoBotsCmd cmd) {


        return null;
    }

    /**
     * 处理AutoBotsResult的回调函数
     *
     * @param autoBotsResult 自动机器人执行结果
     * @return 是否成功处理结果
     */
    @Override
    public Boolean callback(AutoBotsResultBO autoBotsResult) {

        log.info("AutoBotsApplicationImpl->callback,autoBotsResult={}", JSONUtil.toJsonStr(autoBotsResult));
        AutoBotsRecord autoBotsRecord = autoBotsRecordRepository.queryAutoBotsRecord(AutoBotsRequestQueryBO.builder().traceId(autoBotsResult.getTraceId()).build());
        if (Objects.isNull(autoBotsRecord)){
            return false;
        }
        log.info("AutoBotsApplicationImpl->callback,autoBotsRecord={}", JSONUtil.toJsonStr(autoBotsRecord));


        AutoBotsProcessor dispatchRuleProcessor = autoBotsProcessorFactory.createDispatchRuleProcessor(AutoBotsSceneEnum.DIGITAL_REPORT_ANALYSE.getScene());
        if (Objects.isNull(dispatchRuleProcessor)){
            return false;
        }


        AutoBotsCallBackContext context = AutoBotsConvert.INSTANCE.convert(autoBotsResult, autoBotsRecord);
        log.info("AutoBotsApplicationImpl->callback,context={}", JSONUtil.toJsonStr(context));
        dispatchRuleProcessor.callBack(context);
        return Boolean.TRUE;
    }


}
