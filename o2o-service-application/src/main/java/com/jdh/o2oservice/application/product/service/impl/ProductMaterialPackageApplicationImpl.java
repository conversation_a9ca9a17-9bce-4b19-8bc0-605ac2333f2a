package com.jdh.o2oservice.application.product.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.jdh.o2oservice.application.product.convert.ProductMaterialPackageApplicationConvertor;
import com.jdh.o2oservice.application.product.service.ProductMaterialPackageApplication;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.model.DomainEnum;
import com.jdh.o2oservice.base.util.AssertUtils;
import com.jdh.o2oservice.base.util.BeanUtil;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.core.domain.product.enums.ProductAggregateEnum;
import com.jdh.o2oservice.core.domain.product.enums.ProductErrorCode;
import com.jdh.o2oservice.core.domain.product.model.JdhMaterialPackage;
import com.jdh.o2oservice.core.domain.product.model.JdhMaterialPackageIdentifier;
import com.jdh.o2oservice.core.domain.product.repository.db.JdhMaterialPackageRepository;
import com.jdh.o2oservice.core.domain.settlement.bo.ExternalDomainFeeConfigSaveBo;
import com.jdh.o2oservice.core.domain.settlement.bo.SettlementFeeDetailSaveBo;
import com.jdh.o2oservice.core.domain.settlement.bo.query.SettlementConfigDomainQuery;
import com.jdh.o2oservice.core.domain.settlement.context.SettlementConfigContext;
import com.jdh.o2oservice.core.domain.settlement.enums.SettlementSubjectSubTypeEnum;
import com.jdh.o2oservice.core.domain.settlement.enums.SettlementSubjectTypeEnum;
import com.jdh.o2oservice.core.domain.settlement.model.ExternalDomainFeeConfig;
import com.jdh.o2oservice.core.domain.settlement.model.JdhSettlementFeeDetailConfig;
import com.jdh.o2oservice.core.domain.settlement.service.SettlementConfigDomainService;
import com.jdh.o2oservice.core.domain.support.basic.rpc.SkuInfoRpc;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.RpcSkuBO;
import com.jdh.o2oservice.core.domain.support.basic.enums.JdOrderFeeTypeEnum;
import com.jdh.o2oservice.export.product.cmd.CreateJdhMaterialPackageCmd;
import com.jdh.o2oservice.export.product.cmd.UpdateJdhMaterialPackageCmd;
import com.jdh.o2oservice.export.product.dto.JdhMaterialPackageDto;
import com.jdh.o2oservice.export.product.query.JdhMaterialPackageRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 商品耗材包接口
 *
 * <AUTHOR>
 * @date 2024/04/29
 */
@Service
@Slf4j
public class ProductMaterialPackageApplicationImpl implements ProductMaterialPackageApplication {

    /**
     * 耗材包仓储
     */
    @Resource
    JdhMaterialPackageRepository jdhMaterialPackageRepository;

    /**
     * 结算配置
     */
    @Resource
    private SettlementConfigDomainService settlementConfigDomainService;

    /**
     * skuInfoRpc
     */
    @Autowired
    private SkuInfoRpc skuInfoRpc;

    /**
     * 保存耗材
     *
     * @param cmd
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean save(CreateJdhMaterialPackageCmd cmd) {
        log.info("ProductMaterialPackageApplicationImpl -> save, cmd={}", JSONUtil.toJsonStr(cmd));
        AssertUtils.nonNull(cmd, ProductErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("CreateJdhMaterialPackageCmd"));
        checkValid(null, cmd.getMaterialPackageName(), cmd.getSkuId());
        JdhMaterialPackage jdhMaterialPackage = ProductMaterialPackageApplicationConvertor.INSTANCE.createCmdToModel(cmd);
        jdhMaterialPackage.setCreateUser(cmd.getErp());
        jdhMaterialPackage.setUpdateUser(cmd.getErp());
        int count = jdhMaterialPackageRepository.save(jdhMaterialPackage);
        //保存结算价格相关信息
        SettlementConfigContext context = new SettlementConfigContext();
        context.setExternalDomainFeeConfigList(Lists.newArrayList(
                //自营护士结算价
                ExternalDomainFeeConfigSaveBo.builder().domainCode(DomainEnum.PRODUCT).aggregateCode(ProductAggregateEnum.PRODUCT_MATERIAL).aggregateId(String.valueOf(jdhMaterialPackage.getMaterialPackageId())).settlementSubjectType(SettlementSubjectTypeEnum.NURSE).settlementSubjectSubType(SettlementSubjectSubTypeEnum.FULL_TIME).detailConfigList(Lists.newArrayList(SettlementFeeDetailSaveBo.builder().feeType(JdOrderFeeTypeEnum.MATERIAL_FEE.getType()).feeAmount(cmd.getSelfAngelSettlementPrice()).build())).build(),
                //兼职护士结算价
                ExternalDomainFeeConfigSaveBo.builder().domainCode(DomainEnum.PRODUCT).aggregateCode(ProductAggregateEnum.PRODUCT_MATERIAL).aggregateId(String.valueOf(jdhMaterialPackage.getMaterialPackageId())).settlementSubjectType(SettlementSubjectTypeEnum.NURSE).settlementSubjectSubType(SettlementSubjectSubTypeEnum.PART_TIME).detailConfigList(Lists.newArrayList(SettlementFeeDetailSaveBo.builder().feeType(JdOrderFeeTypeEnum.MATERIAL_FEE.getType()).feeAmount(cmd.getPartAngelSettlementPrice()).build())).build()
                ));
        settlementConfigDomainService.batchSaveExternalDomainFeeConfig(context);
        return count > 0;
    }

    /**
     * 更新耗材
     *
     * @param cmd
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean update(UpdateJdhMaterialPackageCmd cmd) {
        log.info("ProductMaterialPackageApplicationImpl -> update, cmd={}", JSONUtil.toJsonStr(cmd));
        AssertUtils.nonNull(cmd, ProductErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("UpdateJdhMaterialPackageCmd"));
        checkValid(cmd.getMaterialPackageId(), cmd.getMaterialPackageName(), cmd.getSkuId());
        JdhMaterialPackage jdhMaterialPackage = ProductMaterialPackageApplicationConvertor.INSTANCE.updateCmdToModel(cmd);
        JdhMaterialPackage snapshot = jdhMaterialPackageRepository.find(JdhMaterialPackageIdentifier.builder().materialPackageId(cmd.getMaterialPackageId()).build());
        jdhMaterialPackage.setUpdateUser(cmd.getErp());
        BeanUtil.copyProperties(jdhMaterialPackage, snapshot, true);
        int count = jdhMaterialPackageRepository.update(snapshot);
        //保存结算价格相关信息
        SettlementConfigContext context = new SettlementConfigContext();
        context.setExternalDomainFeeConfigList(Lists.newArrayList(
                //自营护士结算价
                ExternalDomainFeeConfigSaveBo.builder().domainCode(DomainEnum.PRODUCT).aggregateCode(ProductAggregateEnum.PRODUCT_MATERIAL).aggregateId(String.valueOf(jdhMaterialPackage.getMaterialPackageId())).settlementSubjectType(SettlementSubjectTypeEnum.NURSE).settlementSubjectSubType(SettlementSubjectSubTypeEnum.FULL_TIME).detailConfigList(Lists.newArrayList(SettlementFeeDetailSaveBo.builder().feeType(JdOrderFeeTypeEnum.MATERIAL_FEE.getType()).feeAmount(cmd.getSelfAngelSettlementPrice()).build())).build(),
                //兼职护士结算价
                ExternalDomainFeeConfigSaveBo.builder().domainCode(DomainEnum.PRODUCT).aggregateCode(ProductAggregateEnum.PRODUCT_MATERIAL).aggregateId(String.valueOf(jdhMaterialPackage.getMaterialPackageId())).settlementSubjectType(SettlementSubjectTypeEnum.NURSE).settlementSubjectSubType(SettlementSubjectSubTypeEnum.PART_TIME).detailConfigList(Lists.newArrayList(SettlementFeeDetailSaveBo.builder().feeType(JdOrderFeeTypeEnum.MATERIAL_FEE.getType()).feeAmount(cmd.getPartAngelSettlementPrice()).build())).build()
        ));
        settlementConfigDomainService.batchSaveExternalDomainFeeConfig(context);
        return count > 0;
    }

    /**
     * 查询耗材
     *
     * @param request
     * @return
     */
    @Override
    public JdhMaterialPackageDto query(JdhMaterialPackageRequest request) {
        log.info("ProductMaterialPackageApplicationImpl -> query, request={}", JSONUtil.toJsonStr(request));
        JdhMaterialPackage jdhMaterialPackage = jdhMaterialPackageRepository.query(ProductMaterialPackageApplicationConvertor.INSTANCE.requestToModel(request));
        if (jdhMaterialPackage == null) {
            return null;
        }
        JdhMaterialPackageDto jdhMaterialPackageDto = ProductMaterialPackageApplicationConvertor.INSTANCE.modelToDto(jdhMaterialPackage);
        if (jdhMaterialPackage.getSkuId() != null) {
            RpcSkuBO crsSku = skuInfoRpc.getCrsSkuBoBySkuId(String.valueOf(jdhMaterialPackage.getSkuId()));
            if (crsSku != null) {
                jdhMaterialPackageDto.setSkuName(crsSku.getSkuName());
            }
        }
        //查询配置的结算价格数据
        List<ExternalDomainFeeConfig> externalDomainFeeConfigs = settlementConfigDomainService.queryExternalDomainFeeConfig(SettlementConfigDomainQuery.builder()
                        .domainCode(DomainEnum.PRODUCT)
                        .aggregateCode(ProductAggregateEnum.PRODUCT_MATERIAL)
                        .aggregateId(String.valueOf(jdhMaterialPackage.getMaterialPackageId()))
                        .settlementSubjectType(SettlementSubjectTypeEnum.NURSE)
                        .build());
        if (CollectionUtils.isNotEmpty(externalDomainFeeConfigs)) {
            //按照子类型分组（全兼职）
            Map<String, List<ExternalDomainFeeConfig>> subTypeMap = externalDomainFeeConfigs.stream().collect(Collectors.groupingBy(ExternalDomainFeeConfig::getSettlementSubjectSubType));

            // 提取结算价格
            BigDecimal selfAngelPrice = extractSettlementPrice(subTypeMap, SettlementSubjectSubTypeEnum.FULL_TIME, JdOrderFeeTypeEnum.MATERIAL_FEE);
            BigDecimal partAngelPrice = extractSettlementPrice(subTypeMap, SettlementSubjectSubTypeEnum.PART_TIME, JdOrderFeeTypeEnum.MATERIAL_FEE);

            jdhMaterialPackageDto.setSelfAngelSettlementPrice(selfAngelPrice);
            jdhMaterialPackageDto.setPartAngelSettlementPrice(partAngelPrice);
        }
        return jdhMaterialPackageDto;
    }

    /**
     * 查询耗材
     *
     * @param request
     * @return
     */
    @Override
    public List<JdhMaterialPackageDto> queryList(List<JdhMaterialPackageRequest> request) {
        log.info("ProductMaterialPackageApplicationImpl -> queryList, request={}", JSONUtil.toJsonStr(request));
        List<JdhMaterialPackage>  list = jdhMaterialPackageRepository.queryList(ProductMaterialPackageApplicationConvertor.INSTANCE.requestToModel(request));
        List<JdhMaterialPackageDto> jdhMaterialPackageDtos = CollectionUtils.isEmpty(list) ? new ArrayList<>() : ProductMaterialPackageApplicationConvertor.INSTANCE.modelToDto(list);
        //查询配置的结算价格数据
        List<ExternalDomainFeeConfig> externalDomainFeeConfigs = settlementConfigDomainService.queryExternalDomainFeeConfig(SettlementConfigDomainQuery.builder()
                .domainCode(DomainEnum.PRODUCT)
                .aggregateCode(ProductAggregateEnum.PRODUCT_MATERIAL)
                .aggregateIdList(jdhMaterialPackageDtos.stream().map(JdhMaterialPackageDto::getMaterialPackageId).map(String::valueOf).collect(Collectors.toList()))
                .settlementSubjectType(SettlementSubjectTypeEnum.NURSE)
                .build());
        if (CollectionUtils.isNotEmpty(externalDomainFeeConfigs)) {
            //按照耗材ID分组
            Map<String, List<ExternalDomainFeeConfig>> materialId2Map = externalDomainFeeConfigs.stream().collect(Collectors.groupingBy(ExternalDomainFeeConfig::getAggregateId));
            jdhMaterialPackageDtos.forEach(jdhMaterialPackageDto -> {
                //按照子类型分组（全兼职）
                Map<String, List<ExternalDomainFeeConfig>> subTypeMap = materialId2Map.getOrDefault(String.valueOf(jdhMaterialPackageDto.getMaterialPackageId()), Collections.emptyList())
                        .stream().collect(Collectors.groupingBy(ExternalDomainFeeConfig::getSettlementSubjectSubType));
                // 提取结算价格
                BigDecimal selfAngelPrice = extractSettlementPrice(subTypeMap, SettlementSubjectSubTypeEnum.FULL_TIME, JdOrderFeeTypeEnum.MATERIAL_FEE);
                BigDecimal partAngelPrice = extractSettlementPrice(subTypeMap, SettlementSubjectSubTypeEnum.PART_TIME, JdOrderFeeTypeEnum.MATERIAL_FEE);

                jdhMaterialPackageDto.setSelfAngelSettlementPrice(selfAngelPrice);
                jdhMaterialPackageDto.setPartAngelSettlementPrice(partAngelPrice);
            });
        }
        return jdhMaterialPackageDtos;
    }

    /**
     * 分页查询耗材
     *
     * @param request
     * @return
     */
    @Override
    public PageDto<JdhMaterialPackageDto> queryPage(JdhMaterialPackageRequest request) {
        log.info("ProductMaterialPackageApplicationImpl -> queryPage, request={}", JSONUtil.toJsonStr(request));
        Page<JdhMaterialPackage> page = jdhMaterialPackageRepository.queryPage(ProductMaterialPackageApplicationConvertor.INSTANCE.requestToModel(request));
        PageDto<JdhMaterialPackageDto> pageDto = new PageDto<>();
        pageDto.setTotalPage(page.getPages());
        pageDto.setPageNum(page.getCurrent());
        pageDto.setPageSize(page.getSize());
        pageDto.setTotalCount(page.getTotal());
        if (CollUtil.isEmpty(page.getRecords())) {
            pageDto.setList(Collections.emptyList());
            log.info("ProductMaterialPackageApplicationImpl.queryPage, pageDto={}", JSON.toJSONString(pageDto));
            return pageDto;
        }
        pageDto.setList(ProductMaterialPackageApplicationConvertor.INSTANCE.modelToDto(page.getRecords()));
        log.info("ProductMaterialPackageApplicationImpl.queryPage, pageDto={}", JSON.toJSONString(pageDto));
        //查询配置的结算价格数据
        List<ExternalDomainFeeConfig> externalDomainFeeConfigs = settlementConfigDomainService.queryExternalDomainFeeConfig(SettlementConfigDomainQuery.builder()
                .domainCode(DomainEnum.PRODUCT)
                .aggregateCode(ProductAggregateEnum.PRODUCT_MATERIAL)
                .aggregateIdList(pageDto.getList().stream().map(JdhMaterialPackageDto::getMaterialPackageId).map(String::valueOf).collect(Collectors.toList()))
                .settlementSubjectType(SettlementSubjectTypeEnum.NURSE)
                .build());
        if (CollectionUtils.isNotEmpty(externalDomainFeeConfigs)) {
            //按照耗材ID分组
            Map<String, List<ExternalDomainFeeConfig>> materialId2Map = externalDomainFeeConfigs.stream().collect(Collectors.groupingBy(ExternalDomainFeeConfig::getAggregateId));
            pageDto.getList().forEach(jdhMaterialPackageDto -> {
                //按照子类型分组（全兼职）
                Map<String, List<ExternalDomainFeeConfig>> subTypeMap = materialId2Map.getOrDefault(String.valueOf(jdhMaterialPackageDto.getMaterialPackageId()), Collections.emptyList())
                        .stream().collect(Collectors.groupingBy(ExternalDomainFeeConfig::getSettlementSubjectSubType));
                // 提取结算价格
                BigDecimal selfAngelPrice = extractSettlementPrice(subTypeMap, SettlementSubjectSubTypeEnum.FULL_TIME, JdOrderFeeTypeEnum.MATERIAL_FEE);
                BigDecimal partAngelPrice = extractSettlementPrice(subTypeMap, SettlementSubjectSubTypeEnum.PART_TIME, JdOrderFeeTypeEnum.MATERIAL_FEE);

                jdhMaterialPackageDto.setSelfAngelSettlementPrice(selfAngelPrice);
                jdhMaterialPackageDto.setPartAngelSettlementPrice(partAngelPrice);
            });
        }
        return pageDto;
    }

    /**
     * 查询耗材
     *
     * @param request
     * @return
     */
    @Override
    public JdhMaterialPackageDto checkSku(JdhMaterialPackageRequest request) {
        return checkSku(request.getMaterialPackageId(), request.getSkuId());
    }

    /**
     * 添加更新校验是否存在数据
     *
     * @param materialPackageId
     * @param packageName
     * @param skuId
     */
    private void checkValid(Long materialPackageId, String packageName, Long skuId) {
        // 校验名称
        if (StringUtils.isNotBlank(packageName)) {
            JdhMaterialPackageRequest jdhMaterialPackageRequest = new JdhMaterialPackageRequest();
            jdhMaterialPackageRequest.setMaterialPackageName(packageName.trim());
            JdhMaterialPackage queryResult = jdhMaterialPackageRepository.query(ProductMaterialPackageApplicationConvertor.INSTANCE.requestToModel(jdhMaterialPackageRequest));
            if (queryResult == null) {
                return;
            }
            // 更新
            if (materialPackageId != null) {
                // 不是同一条数据报错
                if (!queryResult.getMaterialPackageId().equals(materialPackageId)) {
                    throw new BusinessException(ProductErrorCode.PRODUCT_MATERIAL_PACKAGE_EXIST);
                }
            } else {
                throw new BusinessException(ProductErrorCode.PRODUCT_MATERIAL_PACKAGE_EXIST);
            }
        }
        checkSku(materialPackageId, skuId);
    }

    /**
     * 校验sku
     *
     * @param materialPackageId
     * @param skuId
     * @return
     */
    private JdhMaterialPackageDto checkSku(Long materialPackageId, Long skuId) {
        try {
            if (skuId != null) {
                // 查询是否已存在SKU
                JdhMaterialPackageRequest jdhMaterialPackageRequest = new JdhMaterialPackageRequest();
                jdhMaterialPackageRequest.setSkuId(skuId);
                JdhMaterialPackage queryResult = jdhMaterialPackageRepository.query(ProductMaterialPackageApplicationConvertor.INSTANCE.requestToModel(jdhMaterialPackageRequest));
                if (queryResult == null) {
                    RpcSkuBO crsSku = skuInfoRpc.getCrsSkuBoBySkuId(String.valueOf(skuId));
                    if (crsSku == null || StringUtils.isBlank(crsSku.getSkuId()) || StringUtils.isBlank(crsSku.getSkuName())) {
                        throw new BusinessException(ProductErrorCode.PRODUCT_SKU_NOT_EXIST_CHECK);
                    }
                    JdhMaterialPackageDto jdhMaterialPackageDto = new JdhMaterialPackageDto();
                    jdhMaterialPackageDto.setSkuId(skuId);
                    jdhMaterialPackageDto.setSkuName(crsSku.getSkuName());
                    return jdhMaterialPackageDto;
                }

                // 保存不允许重复sku
                if (materialPackageId == null) {
                    throw new BusinessException(ProductErrorCode.PRODUCT_MATERIAL_PACKAGE_SKU_EXIST);
                } else {
                    if (!materialPackageId.equals(queryResult.getMaterialPackageId())) {
                        throw new BusinessException(ProductErrorCode.PRODUCT_MATERIAL_PACKAGE_SKU_EXIST);
                    }
                }
            }
            return null;
        } catch (BusinessException e) {
            log.error("ProductMaterialPackageApplicationImpl#checkSku exception", e);
            throw e;
        } catch (Exception e) {
            log.error("ProductMaterialPackageApplicationImpl#checkSku exception", e);
            throw new BusinessException(ProductErrorCode.PRODUCT_MATERIAL_PACKAGE_SKU_EXIST);
        }
    }

    /**
     *
     * @param subTypeMap
     * @param subTypeEnum
     * @return
     */
    private BigDecimal extractSettlementPrice(Map<String, List<ExternalDomainFeeConfig>> subTypeMap, SettlementSubjectSubTypeEnum subTypeEnum, JdOrderFeeTypeEnum feeTypeEnum) {
        if (CollectionUtils.isNotEmpty(subTypeMap.get(subTypeEnum.getType()))) {
            List<ExternalDomainFeeConfig> externalDomainFeeConfigs = subTypeMap.get(subTypeEnum.getType());
            JdhSettlementFeeDetailConfig feeDetailConfig = externalDomainFeeConfigs.get(0).getDetailConfigListByFeeType(feeTypeEnum.getType().toString());
            return Objects.nonNull(feeDetailConfig) ? feeDetailConfig.getFeeAmount() : null;
        }
        return null;
    }
}
