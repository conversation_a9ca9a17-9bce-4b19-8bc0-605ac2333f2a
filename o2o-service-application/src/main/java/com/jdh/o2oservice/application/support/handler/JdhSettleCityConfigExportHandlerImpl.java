package com.jdh.o2oservice.application.support.handler;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.jdh.o2oservice.application.settlement.service.JdServiceCityAngelSettleApplication;
import com.jdh.o2oservice.application.settlement.service.JdhAngelSettleAdjustApplication;
import com.jdh.o2oservice.base.constatnt.NumConstant;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.core.domain.settlement.enums.SettleAggregateEnum;
import com.jdh.o2oservice.core.domain.settlement.repository.query.CityAngelSettlementPageQuery;
import com.jdh.o2oservice.core.domain.support.file.context.FileExportContext;
import com.jdh.o2oservice.core.domain.support.file.enums.FileExportTypeEnum;
import com.jdh.o2oservice.core.domain.support.file.service.AbstractFileExportHandler;
import com.jdh.o2oservice.export.settlement.dto.AngelSettlementDto;
import com.jdh.o2oservice.export.settlement.dto.CityAngelSettlementConfigDto;
import com.jdh.o2oservice.export.settlement.query.AngelSettleQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.BlockingQueue;

/**
 * JdhSettleCityConfigExportHandlerImpl
 * 护士结算城市配置导出
 * <AUTHOR>
 * @date 2025/04/22
 */
@Slf4j
@Service
public class JdhSettleCityConfigExportHandlerImpl extends AbstractFileExportHandler {


    /**
     * 商品接口信息
     */
    @Resource
    JdServiceCityAngelSettleApplication jdServiceCityAngelSettleApplication;

    /**
     * 获取map key
     *
     * @return
     */
    @Override
    public String getMapKey() {
        return FileExportTypeEnum.ANGEL_SETTLE_CITY_CONFIG_EXPORT.getType();
    }

    /**
     * 前置处理
     *
     * @param ctx
     */
    @Override
    protected void preHandle(FileExportContext ctx) {

    }

    /**
     * 各业务实现获取业务数据
     *
     * @param ctx ctx
     */
    @Override
    protected void getData(FileExportContext ctx) {
        Map<String, Object> queryParam = ctx.getQueryParam();
        CityAngelSettlementPageQuery pageRequest = JSON.parseObject(JSON.toJSONString(queryParam), CityAngelSettlementPageQuery.class);
        pageRequest.setPageSize(NumConstant.NUM_10000);
        log.info("JdhSettleCityConfigExportHandlerImpl getData queryParam:{}",JSON.toJSONString(queryParam));
        boolean hasNextPage = true;
        int pageNum = 1;
        BlockingQueue<Map<String, Object>> queue = ctx.getQueue();
        do {
            pageRequest.setPageNum(pageNum);
            PageDto<CityAngelSettlementConfigDto> pageDto = jdServiceCityAngelSettleApplication.queryCityAngelSettlementPage(pageRequest);
            if(pageNum == pageDto.getTotalPage()){
                hasNextPage = false;
            }
            List<CityAngelSettlementConfigDto> list = pageDto.getList();
            if(CollUtil.isNotEmpty(list)){
                for (CityAngelSettlementConfigDto itemDto : list) {
                    Map<String, Object> column = new HashMap<>();
                    column.put(SettleAggregateEnum.SETTLE_CITY_CONFIG.getCode(), itemDto);
                    queue.add(column);
                }
            }
            pageNum++;
        }while (hasNextPage);
        log.info("JdhSettleCityConfigExportHandlerImpl getData 结束");

    }
}
