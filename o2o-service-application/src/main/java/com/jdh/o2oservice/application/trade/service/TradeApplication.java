package com.jdh.o2oservice.application.trade.service;

import com.jdh.o2oservice.export.promise.cmd.SubmitAppointmentDraftCmd;
import com.jdh.o2oservice.export.trade.cmd.CalcTradeServiceFeeCmd;
import com.jdh.o2oservice.export.trade.dto.*;
import com.jdh.o2oservice.export.trade.query.*;

import java.util.List;

/**
 * @author: yangxiyu
 * @date: 2024/1/4 2:25 下午
 * @version: 1.0
 */
public interface TradeApplication {


    /**
     * 获取预约草稿数据结算页url
     * @param cmd
     * @return
     */
    String saveDraft(SubmitAppointmentDraftCmd cmd);

    /**
     * 获取订单下预约单列表
     * @param request
     * @return
     */
    List<OrderPromiseDto> queryOrderPromiseByList(OrderPromiseListRequest request);


    /**
     * 结算页用户行为
     *
     * @param orderUserActionParam 入参
     * @return OrderUserActionDTO
     */
    OrderUserActionDTO executeAction(OrderUserActionParam orderUserActionParam);

    /**
     * 提交订单
     *
     * @param submitOrderParam 提单参数
     * @return SubmitOrderDTO
     */
    SubmitOrderDTO submitOrder(SubmitOrderParam submitOrderParam);

    /**
     * 订单支付收银台
     * @param orderPayUrlParam
     * @return
     */
    String getOrderPayUrl(OrderPayUrlParam orderPayUrlParam);

    /**
     * 取消待支付订单
     * @param cancelOrderParam
     * @return
     */
    Boolean cancelPayOrder(CancelOrderParam cancelOrderParam);

    /**
     * 查询订单详情
     */
    JdOrderDTO getOrderDetail(OrderDetailParam orderDetailParam);
    /**
     * 查询订单详情
     */
    JdOrderDTO getOrderSettleDetail(OrderDetailParam orderDetailParam);

    /**
     * 查询订单详情
     */
    JdOrderDTO queryJdOrderDTO(OrderDetailParam orderDetailParam);

    /**
     * 可约时段
     * @param param
     * @return
     */
    AvaiableAppointmentTimeDTO queryAvaiableAppointmentTime(AvaiableAppointmentTimeParam param);

    /**
     * 计算服务费
     *
     * @param cmd cmd
     * @return {@link List}<{@link TradeServiceFeeInfoDTO}>
     */
    List<TradeServiceFeeInfoDTO> calcServiceFee(CalcTradeServiceFeeCmd cmd);

    /**
     * 申请退款
     * @param param
     * @return
     */
    Boolean xfylOrderRefund(RefundOrderParam param);
    /**
     * 获取订单退款原因列表
     * @return
     */
    List<OrderCancelReasonDto> getOrderCancelReasons(RefundOrderParam param);

    /**
     * findCompleteOrder
     *
     * @param request 请求
     * @return {@link CompleteOrderDto}
     */
    CompleteOrderDto findCompleteOrder(CompleteOrderRequest request);

    /**
     * 查询运营端退款提示信息
     *
     * @param param param
     * @return {@link List}<{@link ManRefundTipsInfoDto}>
     */
    ManRefundTipsInfoDto queryManRefundTipsInfo(ManRefundTipsParam param);

    /**
     *
     * @param param
     * @return
     */
    Boolean xfylOrderRefund2Knight(RefundOrderKnightParam param);

    /**
     * 查询自定义订单列表
     * @param param
     * @return
     */
    List<CustomOrderInfoDTO> queryCustomOrderList(QueryCustomOrderListParam param);

    /**
     * 收单
     * @param request
     * @return
     */
    Integer receiveOrder(ReceiveOrderRequest request);

    /**
     * 订单拉完成
     * @param orderId
     * @return
     */
    Boolean reviseOrderFinishState(Long orderId);

    /**
     * 查询用户服务过的历史护士
     * @param param
     * @return
     */
    List<ServiceHistoryAngelDTO> queryServiceHistoryAngel(QueryServiceHistoryAngelParam param);
    {
    "userPin": "test_user_pin",
    "orderId": 317863298262,
    "remarkParam": {
        "remark": "请护士准时到达"
    },
    "partnerSource": 1,
    "saleChannelId": "XFYL_APP",
    "partnerSourceOrderId": "EXT_ORDER_123456",
    "patientParamList": [
        {
            "patientId": 173394062999926,
            "name": "张三",
            "phone": "13800138000",
            "credentialType": 1,
            "credentialNo": "110101199001011234",
            "birthday": "1990-01-01",
            "gender": 1
        }
    ],
    "appointmentTimeParam": {
        "appointmentStartTime": "2024-12-01 09:00",
        "appointmentEndTime": "2024-12-01 12:00",
        "dateType": 2,
        "isImmediately": false
    },
    "intendedNurse": {
        "recommendType": 2,
        "angelId": 12345,
        "invitationCode": "INVITE_CODE_123"
    }
    }
    /**
     * 查询意向服务者
     * @param param
     * @return
     */
    IntendedAngelDTO queryIntendedAngel(QueryIntendedAngelParam param);

    /**
     * 主站订单详情楼层
     * @param param
     * @return
     */
    CustomOrderDetailFloorDTO queryCustomOrderDetailFloor(CustomOrderDetailParam param);

    /**
     * 收单并提交预约
     * @param param
     * @return
     */
    Boolean receiveOrderAndAppointment(ReceiveOrderAndAppointmentParam param);

    /**
     * 校验是否可以提单并预约
     * @param param
     * @return
     */
    Boolean checkSubmitOrderAndAppointment(CheckSubmitOrderAndAppointmentParam param);
}
