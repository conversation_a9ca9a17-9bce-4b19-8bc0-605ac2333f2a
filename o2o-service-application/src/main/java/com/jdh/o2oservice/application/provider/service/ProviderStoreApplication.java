package com.jdh.o2oservice.application.provider.service;

import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.core.domain.provider.model.JdhStoreTransferStation;
import com.jdh.o2oservice.export.laboratory.cmd.AddQuickMerchantStoreRequest;
import com.jdh.o2oservice.export.laboratory.cmd.AppointmentMigrationRequest;
import com.jdh.o2oservice.export.laboratory.cmd.UpdateQuickMerchantStoreRequest;
import com.jdh.o2oservice.export.laboratory.dto.JdhStoreTransferStationDto;
import com.jdh.o2oservice.export.laboratory.dto.QueryMedicalPromisePageResponse;
import com.jdh.o2oservice.export.laboratory.dto.QueryMerchantStoreDetailResponse;
import com.jdh.o2oservice.export.laboratory.query.QueryMedicalPromisePageRequest;
import com.jdh.o2oservice.export.laboratory.query.QueryMerchantMultiStoreRequest;
import com.jdh.o2oservice.export.laboratory.query.QueryMerchantStoreDetailByParamRequest;
import com.jdh.o2oservice.export.laboratory.query.QueryMerchantStoreListByParamRequest;
import com.jdh.o2oservice.export.provider.cmd.*;
import com.jdh.o2oservice.export.provider.dto.JdhStationServiceItemRelDto;
import com.jdh.o2oservice.export.provider.dto.StationAddressDto;
import com.jdh.o2oservice.export.provider.dto.StoreInfoDto;
import com.jdh.o2oservice.export.provider.query.JdhStationServiceItemRelRequest;
import com.jdh.o2oservice.export.provider.query.StationAddressRequest;
import com.jdh.o2oservice.export.provider.query.StoreInfoRequest;

import java.util.List;
import java.util.Map;

/**
 * 供应商门店集合类
 *
 * <AUTHOR>
 * @date 2024/04/28
 */
public interface ProviderStoreApplication {

    /**
     * 查询门店信息
     *
     * @param request req
     * @return dto
     */
    StoreInfoDto queryStationInfo(StoreInfoRequest request);

    /**
     * 创建门店项目信息
     *
     * @param cmd cmd
     * @return dto
     */
    Boolean addStationServiceItemRel(JdhStationServiceItemRelCreateCmd cmd);

    /**
     * 更新门店项目信息
     *
     * @param cmd cmd
     * @return dto
     */
    Boolean updateStationServiceItemRel(JdhStationServiceItemRelUpdateCmd cmd);

    /**
     * 分页查询门店项目信息
     *
     * @param request req
     * @return dto
     */
    PageDto<JdhStationServiceItemRelDto> queryStationServiceItemRelPage(JdhStationServiceItemRelRequest request);

    /**
     * 查询门店项目信息列表
     *
     * @param request req
     * @return dto
     */
    List<JdhStationServiceItemRelDto> queryStationServiceItemRelList(JdhStationServiceItemRelRequest request);

    /**
     * 查询门店项目信息
     *
     * @param request req
     * @return dto
     */
    JdhStationServiceItemRelDto queryStationServiceItemRel(JdhStationServiceItemRelRequest request);

    /**
     * 更新门店项目信息
     *
     * @param cmd cmd
     * @return dto
     */
    Boolean deleteStationServiceItemRel(JdhStationServiceItemRelDeleteCmd cmd);

    /**
     * 根据实验室名称查询实验室列表
     * @param listLaboratoryByStoreNameCmd
     * @return
     */
    List<StoreInfoDto> listLaboratoryByStoreName(ListLaboratoryByStoreNameCmd listLaboratoryByStoreNameCmd);

    /**
     * 异步导入项目列表
     *
     * @param cmd cmd
     * @return true
     */
    Boolean importServiceItem(StationServiceItemImportCmd cmd);

    /**
     * 查询已开通省市
     * @param param
     * @return
     */
    List<StationAddressDto> queryStationAddress(StationAddressRequest param);

    /**
     * 根据实验室名称查询实验室列表
     * @param param
     * @return
     */
    List<StoreInfoDto> listStationByAddress(StationAddressRequest param);

    /**
     * 创建实验室
     * @param addQuickMerchantStoreRequest
     * @return
     */
    Boolean addQuickMerchantStore(AddQuickMerchantStoreRequest addQuickMerchantStoreRequest);

    /**
     * 编辑实验室
     * @param updateQuickMerchantStoreRequest
     * @return
     */
    Boolean updateQuickMerchantStore(UpdateQuickMerchantStoreRequest updateQuickMerchantStoreRequest);

    /**
     * 查询实验室详情
     * @param queryMerchantStoreDetailByParamRequest
     * @return
     */
    QueryMerchantStoreDetailResponse queryMerchantStoreDetailByParam(QueryMerchantStoreDetailByParamRequest queryMerchantStoreDetailByParamRequest);

    /**
     * 查询实验室列表分页
     * @param queryMerchantStoreListByParamRequest
     * @return
     */
    PageDto<QueryMerchantStoreDetailResponse> queryMerchantStoreListByParam(QueryMerchantStoreListByParamRequest queryMerchantStoreListByParamRequest);

    /**
     * 实验室迁移配置
     * @param appointmentMigrationRequest
     * @return
     */
    Boolean appointmentMigration(AppointmentMigrationRequest appointmentMigrationRequest);

    /**
     * 查询检测单列表
     * @param queryMedicalPromisePageRequest
     * @return
     */
    PageDto<QueryMedicalPromisePageResponse> queryMedicalPromisePage(QueryMedicalPromisePageRequest queryMedicalPromisePageRequest);

    /**
     * 查询单个实验室接驳点数据
     * @return
     */
    List<JdhStoreTransferStationDto> queryStoreTransferStation(QueryMerchantStoreDetailByParamRequest request);

    /**
     * 查询多个实验室接驳点数据
     * @return
     */
    Map<String, List<JdhStoreTransferStationDto>> queryMultiStoreTransferStation(QueryMerchantMultiStoreRequest request );
}