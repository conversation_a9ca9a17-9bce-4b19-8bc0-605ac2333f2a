package com.jdh.o2oservice.application.support.price;

import cn.hutool.core.convert.Convert;
import com.jdh.o2oservice.application.trade.service.JdOrderApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.core.domain.support.basic.enums.PricingServiceFactObjectEnum;
import com.jdh.o2oservice.core.domain.support.price.context.PricingServiceCalculateContext;
import com.jdh.o2oservice.core.domain.support.price.handler.PricingServiceFactObjectHandler;
import com.jdh.o2oservice.core.domain.trade.model.JdOrder;
import com.jdh.o2oservice.export.promise.dto.PromiseDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @ClassName JdhOrderFactObjectHandler
 * @Description
 * <AUTHOR>
 * @Date 2025/5/19 13:25
 **/
@Component
@Slf4j
public class JdhOrderFactObjectHandler extends AbstractFactObjectHandler {

    /**
     * 护士服务
     */
    @Resource
    private JdOrderApplication jdOrderApplication;

    /**
     * 履约单数据
     */
    @Resource
    private JdhPromiseFactObjectHandler promiseFactObjectHandler;

    /**
     * 获取事实对象
     * @param context
     * @return
     */
    @Override
    public Object getData(PricingServiceCalculateContext context) {
        boolean containsKey = context.getFactObjectMap().containsKey(getMapKey());
        if (containsKey) {
            return context.getFactObjectMap().get(getMapKey());
        }
        Long orderId = context.getOrderId();
        //如果入参未传订单号，从履约单中取
        if (Objects.isNull(orderId)) {
            //依赖履约单数据，先从上下文中获取，没有则调用接口获取，并放入上下文
            Object factObject = promiseFactObjectHandler.getFactObject(context);
            if (Objects.isNull(factObject)) {
                context.getFactObjectMap().put(getMapKey(), null);
                return null;
            }
            PromiseDto promiseDto = Convert.convert(PromiseDto.class, factObject);
            orderId = StringUtils.isNotBlank(promiseDto.getSourceVoucherId()) ? Long.valueOf(promiseDto.getSourceVoucherId()) : null;
        }
        if (Objects.isNull(orderId)) {
            context.getFactObjectMap().put(getMapKey(), null);
            return null;
        }
        JdOrder jdOrder = jdOrderApplication.queryJdOrderAndItemExt(orderId);
        context.getFactObjectMap().put(getMapKey(), jdOrder);
        return jdOrder;
    }

    /**
     * 获取map key
     * @return
     */
    @Override
    public String getMapKey() {
        return PricingServiceFactObjectEnum.ORDER_INFO.getCode();
    }
}