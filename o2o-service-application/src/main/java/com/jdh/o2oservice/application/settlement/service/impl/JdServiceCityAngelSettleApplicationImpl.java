package com.jdh.o2oservice.application.settlement.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jdh.o2oservice.application.settlement.listener.ImportAngelSettleCityConfigListener;
import com.jdh.o2oservice.application.settlement.service.JdServiceCityAngelSettleApplication;
import com.jdh.o2oservice.application.support.service.FileManageApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.ducc.model.fee.CityLevelSettlementCoefficientConfig;
import com.jdh.o2oservice.base.enums.CityLevelEnum;
import com.jdh.o2oservice.base.enums.ThreadPoolConfigEnum;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode;
import com.jdh.o2oservice.base.factory.ExecutorPoolFactory;
import com.jdh.o2oservice.base.factory.GenerateIdFactory;
import com.jdh.o2oservice.base.util.AssertUtils;
import com.jdh.o2oservice.base.util.SpringUtil;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.core.domain.settlement.enums.SettleErrorCode;
import com.jdh.o2oservice.core.domain.settlement.model.*;
import com.jdh.o2oservice.core.domain.settlement.repository.db.JdhSettlementCityLevelConfigRepository;
import com.jdh.o2oservice.core.domain.settlement.repository.query.CityAngelSettlementPageQuery;
import com.jdh.o2oservice.core.domain.support.basic.rpc.AddressRpc;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.BaseAddressBo;
import com.jdh.o2oservice.core.domain.support.file.enums.FileExportTypeEnum;
import com.jdh.o2oservice.core.domain.support.file.enums.FileOperationStatusEnum;
import com.jdh.o2oservice.core.domain.support.file.model.JdhFile;
import com.jdh.o2oservice.core.domain.support.file.model.JdhFileIdentifier;
import com.jdh.o2oservice.core.domain.support.file.model.JdhImportExportTask;
import com.jdh.o2oservice.core.domain.support.file.repository.db.JdhFileRepository;
import com.jdh.o2oservice.core.domain.support.file.repository.db.JdhFileTaskRepository;
import com.jdh.o2oservice.core.domain.support.file.service.FileManageService;
import com.jdh.o2oservice.export.product.dto.StandardItemDTO;
import com.jdh.o2oservice.export.settlement.cmd.AngelSettleCityConfigFileCmd;
import com.jdh.o2oservice.export.settlement.cmd.CityAngelSettlementConfigCmd;
import com.jdh.o2oservice.export.settlement.dto.CityAngelSettlementConfigDto;
import com.jdh.o2oservice.export.support.dto.FilePreSignedUrlDto;
import com.jdh.o2oservice.infrastructure.repository.db.convert.JdhAngelSettlementPoConvert;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * JdServiceCityAngelSettleApplication
 * 城市级别护士结算配置
 * <AUTHOR>
 * @version 2025/4/21 12:15
 **/
@Service
@Slf4j
public class JdServiceCityAngelSettleApplicationImpl implements JdServiceCityAngelSettleApplication {

    /**
     * ebs处理器管理
     */
    @Resource
    private JdhSettlementCityLevelConfigRepository jdhSettlementCityLevelConfigRepository;
    /** */
    @Resource
    private DuccConfig duccConfig;
    /**
     * 全局id
     */
    @Resource
    private GenerateIdFactory generateIdFactory;
    /**
     * 文件服务仓
     */
    @Resource
    JdhFileRepository jdhFileRepository;
    /** */
    @Resource
    private JdhFileTaskRepository jdhFileTaskRepository;
    /**
     * 文件服务
     */
    @Resource
    private FileManageApplication fileManageApplication;
    /**
     *
     */
    @Autowired
    private ExecutorPoolFactory executorPoolFactory;
    /**
     * 服务者
     */
    @Resource
    FileManageService fileManageService;
    /**
     * 地址服务
     */
    @Autowired
    private AddressRpc addressRpc;

    /**
     * 当前配置
     */
    private static String ACTIVE;
    /**
     * 初始注入maven环境
     */
    @Value("${spring.profiles.active}")
    public void setActive(String value){
        ACTIVE = value;
    }


    /**
     * 分页护士结算城市配置
     *
     * @param cityAngelSettlementPageQuery
     * @return
     */
    @Override
    @LogAndAlarm
    public PageDto<CityAngelSettlementConfigDto> queryCityAngelSettlementPage(CityAngelSettlementPageQuery cityAngelSettlementPageQuery) {
        Page<JdhSettlementCityLevelConfig> page = jdhSettlementCityLevelConfigRepository.queryPage(cityAngelSettlementPageQuery);
        return this.entity2PageDto(page);
    }

    /**
     * 新建城市级别配置
     * @param cityAngelSettlementConfigCmd
     * @return
     */
    @Override
    public boolean saveCityAngelSettlement(CityAngelSettlementConfigCmd cityAngelSettlementConfigCmd) {
        CityAngelSettlementPageQuery cityAngelSettlementPageQuery = new CityAngelSettlementPageQuery();
        cityAngelSettlementPageQuery.setProvinceCode(cityAngelSettlementConfigCmd.getProvinceCode());
        cityAngelSettlementPageQuery.setCityCode(cityAngelSettlementConfigCmd.getCityCode());
        JdhSettlementCityLevelConfig entity = jdhSettlementCityLevelConfigRepository.queryJdhCityLevelConfig(cityAngelSettlementPageQuery);
        if(Objects.nonNull(entity) && !cityAngelSettlementConfigCmd.getCover()) {
            throw new BusinessException(BusinessErrorCode.ANGEL_FEE_ADDR_EXSIT);
        }

        JdhSettlementCityLevelConfig jdhSettlementCityLevelConfig = getJdhSettlementCityLevelConfig(cityAngelSettlementConfigCmd);
        if(!cityAngelSettlementConfigCmd.getCover()){
            return jdhSettlementCityLevelConfigRepository.save(jdhSettlementCityLevelConfig) > 0;
        }else{
            jdhSettlementCityLevelConfig.setCityConfigId(entity.getCityConfigId());
            return jdhSettlementCityLevelConfigRepository.update(jdhSettlementCityLevelConfig) > 0;
        }
    }

    /**
     *
     * @param cityAngelSettlementConfigCmd
     * @return
     */
    private JdhSettlementCityLevelConfig getJdhSettlementCityLevelConfig(CityAngelSettlementConfigCmd cityAngelSettlementConfigCmd){
        JdhSettlementCityLevelConfig jdhSettlementCityLevelConfig = new JdhSettlementCityLevelConfig();
        jdhSettlementCityLevelConfig.setCityConfigId(generateIdFactory.getId());
        jdhSettlementCityLevelConfig.setProvinceCode(cityAngelSettlementConfigCmd.getProvinceCode());
        jdhSettlementCityLevelConfig.setProvinceName(cityAngelSettlementConfigCmd.getProvinceName());
        jdhSettlementCityLevelConfig.setCityCode(cityAngelSettlementConfigCmd.getCityCode());
        jdhSettlementCityLevelConfig.setCityName(cityAngelSettlementConfigCmd.getCityName());
        jdhSettlementCityLevelConfig.setDestCode(StringUtils.isNotBlank(jdhSettlementCityLevelConfig.getCityCode()) ? jdhSettlementCityLevelConfig.getCityCode() : jdhSettlementCityLevelConfig.getProvinceCode());
        jdhSettlementCityLevelConfig.setLevel(cityAngelSettlementConfigCmd.getLevel());
        jdhSettlementCityLevelConfig.setCreateUser(cityAngelSettlementConfigCmd.getOperator());
        jdhSettlementCityLevelConfig.setUpdateUser(cityAngelSettlementConfigCmd.getOperator());
        jdhSettlementCityLevelConfig.setBranch(ACTIVE);
        return jdhSettlementCityLevelConfig;
    }

    /**
     * 更新城市级别配置
     * @param cityAngelSettlementConfigCmd
     * @return
     */
    @Override
    public boolean updateCityAngelSettlement(CityAngelSettlementConfigCmd cityAngelSettlementConfigCmd) {
        AssertUtils.nonNull(cityAngelSettlementConfigCmd.getCityConfigId(), "城市级别配置id不能为空");
        JdhSettlementCityLevelConfig jdhSettlementCityLevelConfig = new JdhSettlementCityLevelConfig();
        jdhSettlementCityLevelConfig.setCityConfigId(cityAngelSettlementConfigCmd.getCityConfigId());
        jdhSettlementCityLevelConfig.setLevel(cityAngelSettlementConfigCmd.getLevel());
        jdhSettlementCityLevelConfig.setUpdateUser(cityAngelSettlementConfigCmd.getOperator());
        return jdhSettlementCityLevelConfigRepository.update(jdhSettlementCityLevelConfig) > 0;
    }

    /**
     * @param angelSettleCityConfigFileCmd
     * @return
     */
    @Override
    public boolean batchSubmitCityConfig(AngelSettleCityConfigFileCmd angelSettleCityConfigFileCmd) {
        JdhFile jdhFile = jdhFileRepository.find(JdhFileIdentifier.builder().fileId(angelSettleCityConfigFileCmd.getFileId()).build());
        AssertUtils.nonNull(jdhFile,"上传非法文件");
        JdhImportExportTask task = bulidJdhImportExportTask(jdhFile,angelSettleCityConfigFileCmd.getApplyErp());
        jdhFileTaskRepository.add(task);
        angelSettleCityConfigFileCmd.setTaskId(task.getTaskId());

        CompletableFuture.runAsync(() -> excelAllSheetAnalysis(angelSettleCityConfigFileCmd,jdhFile),
                executorPoolFactory.get(ThreadPoolConfigEnum.DEFAULT));
        return Boolean.TRUE;
    }

    /**
     * @param cityName
     * @return
     */
    @Override
    public BaseAddressBo getProvinceName(String cityName) {
        BaseAddressBo jdAddressFromAddress = this.getBaseAddressBo(cityName);
        return jdAddressFromAddress;
    }

    /**
     *
     * @param cityAngelSettlementPageQuery
     * @return
     */
    @Override
    public Boolean exportCityLevelConfig(CityAngelSettlementPageQuery cityAngelSettlementPageQuery) {
        Integer settlementCount = jdhSettlementCityLevelConfigRepository.queryCityConfigCount(cityAngelSettlementPageQuery);
        if(Objects.nonNull(settlementCount) && settlementCount == CommonConstant.ZERO){
            throw new BusinessException(SettleErrorCode.EXPORT_SETTLE_DATA_ZERO);
        }
        if(Objects.nonNull(settlementCount) && settlementCount > FileExportTypeEnum.ANGEL_SETTLE_EXPORT.getMaxCount()){
            throw new BusinessException(SettleErrorCode.EXPORT_SETTLE_DATA_OUT);
        }
        //1、构建上下文
        Map<String, Object> ctx = BeanUtil.beanToMap(cityAngelSettlementPageQuery);
        ctx.put("scene", FileExportTypeEnum.ANGEL_SETTLE_CITY_CONFIG_EXPORT.getType());
        ctx.put("userPin",cityAngelSettlementPageQuery.getUserPin());
        ctx.put("operationType", FileExportTypeEnum.ANGEL_SETTLE_CITY_CONFIG_EXPORT.getType());
        //2、调用通用文件导入能力
        return fileManageApplication.export(ctx);
    }

    /**
     * @param cityLevel
     * @return
     */
    @Override
    public CityLevelSettlementCoefficientConfig getCityLevelSettlementCoefficientConfig(String cityLevel) {
        Map<String, CityLevelSettlementCoefficientConfig> angelServiceCoefficientConfigMap = duccConfig.getCityLevelSettlementCoefficientConfigMap();
        CityLevelSettlementCoefficientConfig result = angelServiceCoefficientConfigMap.get(cityLevel);
        return result;
    }

    /**
     * entity2PageDto
     *
     * @param itemPage itemPage
     * @return {@link PageDto}<{@link StandardItemDTO}>
     */
    private PageDto<CityAngelSettlementConfigDto> entity2PageDto(Page<JdhSettlementCityLevelConfig> itemPage){
        PageDto<CityAngelSettlementConfigDto> dto = new PageDto<>();
        if(Objects.nonNull(itemPage)){
            dto.setTotalPage(itemPage.getPages());
            dto.setPageNum(itemPage.getCurrent());
            dto.setPageSize(itemPage.getSize());
            dto.setTotalCount(itemPage.getTotal());
            Map<String, CityLevelSettlementCoefficientConfig> angelServiceCoefficientConfigMap = duccConfig.getCityLevelSettlementCoefficientConfigMap();
            List<JdhSettlementCityLevelConfig> records = itemPage.getRecords();
            if(CollUtil.isNotEmpty(records)){
                List<CityAngelSettlementConfigDto> list = new ArrayList<>();
                records.forEach(jdhSettlementCityLevelConfig ->{
                    list.add(entity2Dto(jdhSettlementCityLevelConfig,angelServiceCoefficientConfigMap));
                });
                dto.setList(list);
            }
        }
        return dto;
    }

    /**
     * 实体2 dto
     *
     * @param entity 实体
     * @return {@link CityAngelSettlementConfigDto}
     */
    private CityAngelSettlementConfigDto entity2Dto(JdhSettlementCityLevelConfig entity, Map<String, CityLevelSettlementCoefficientConfig> angelServiceCoefficientConfigMap){
        CityAngelSettlementConfigDto dto = new CityAngelSettlementConfigDto();
        dto.setCityConfigId(entity.getCityConfigId());
        dto.setProvinceCode(entity.getProvinceCode());
        dto.setProvinceName(entity.getProvinceName());
        dto.setCityCode(entity.getCityCode());
        dto.setCityName(entity.getCityName());
        dto.setLevel(CityLevelEnum.getDescOfCityLevel(entity.getLevel()));
        dto.setOperator(entity.getUpdateUser());
        CityLevelSettlementCoefficientConfig coefficientConfig = angelServiceCoefficientConfigMap.get(entity.getLevel());
        if (Objects.nonNull(coefficientConfig)) {
            dto.setSelfServiceCoefficient(coefficientConfig.getSelfServiceCoefficient());
            dto.setSidelineServiceCoefficient(coefficientConfig.getSidelineServiceCoefficient());
            dto.setPlatformServiceCoefficient(coefficientConfig.getPlatformServiceCoefficient());
            dto.setPlatformSubsidyCoefficient(coefficientConfig.getPlatformSubsidyCoefficient());
        }
        return dto;
    }


    /**
     * 创建导入文件任务
     * @param jdhFile
     * @param userPin
     * @return
     */
    public static JdhImportExportTask bulidJdhImportExportTask(JdhFile jdhFile,String userPin){
        JdhImportExportTask task = new JdhImportExportTask();
        Long taskId = SpringUtil.getBean(GenerateIdFactory.class).getId();
        task.setTaskId(taskId);
        task.setOperatorId(userPin);
        task.setScene(FileExportTypeEnum.ANGEL_SETTLE_CITY_CONFIG_EXPORT.getType());
        task.setOperationType(FileExportTypeEnum.ANGEL_SETTLE_CITY_CONFIG_UPLOAD.getType());
        task.setStatus(FileOperationStatusEnum.PROCESSING.getStatus());
        task.setFileName(jdhFile.getFileName() + taskId);
        task.setFileUrl(jdhFile.getFilePath());
        task.setExpireTime(CommonConstant.EXPIRE_TIME);
        return task;
    }

    /**
     *
     * @param angelSettleCityConfigFileCmd
     * @param jdhFile
     */
    private void excelAllSheetAnalysis(AngelSettleCityConfigFileCmd angelSettleCityConfigFileCmd, JdhFile jdhFile) {
        JdhImportExportTask task = new JdhImportExportTask();
        task.setTaskId(angelSettleCityConfigFileCmd.getTaskId());
        try{
            InputStream inputStream = fileManageService.get(jdhFile.getFilePath());
            ImportAngelSettleCityConfigListener readListener = new ImportAngelSettleCityConfigListener(angelSettleCityConfigFileCmd.getApplyErp());
            EasyExcelFactory.read(inputStream, ImportAngelSettleCityConfig.class, readListener).sheet().doRead();
            //
            FilePreSignedUrlDto filePreSignedUrlDto = readListener.getFilePreSignedUrlDto();
            if (readListener.getIsFail()) {
                JdhFile jdhFailFile = jdhFileRepository.find(JdhFileIdentifier.builder().fileId(filePreSignedUrlDto.getFileId()).build());
                task.setFileUrl(jdhFailFile.getFilePath());
                task.setStatus(FileOperationStatusEnum.FAIL.getStatus());
            }else{
                List<ImportAngelSettleCityConfig> succList = CollUtil.newArrayList(readListener.getSuccMap().values());
                log.info("[JdServiceCityAngelSettleApplicationImpl.excelAllSheetAnalysis] succList={}",succList);
                if(CollUtil.isNotEmpty(succList)){
                    batchSaveCityConfig(succList,angelSettleCityConfigFileCmd.getApplyErp());
                }
                task.setStatus(FileOperationStatusEnum.SUCCESS.getStatus());
            }
        }catch (Exception e){
            log.error("[JdServiceCityAngelSettleApplicationImpl.excelAllSheetAnalysis] error=",e);
            String failReason = e.getMessage().substring(0,255);
            task.setFailMsg(failReason);
            task.setStatus(FileOperationStatusEnum.FAIL.getStatus());
        }
        jdhFileTaskRepository.update(task);
    }


    /**
     * 保存调账 + 发起审批
     * @param succList
     * @param applyErp
     */
    private void batchSaveCityConfig(List<ImportAngelSettleCityConfig> succList,String applyErp){
        List<JdhSettlementCityLevelConfig> addJdhSettlementCityLevelConfigList = new ArrayList<>();
        List<JdhSettlementCityLevelConfig> updateJdhSettlementCityLevelConfigList = new ArrayList<>();
        for(ImportAngelSettleCityConfig request : succList){
            JdhSettlementCityLevelConfig jdhSettlementCityLevelConfig = JdhAngelSettlementPoConvert.INSTANCE.convertToCityLevelConfig(request);
            jdhSettlementCityLevelConfig.setCreateUser(applyErp);
            jdhSettlementCityLevelConfig.setUpdateUser(applyErp);
            Long cityConfigId = generateIdFactory.getId();
            jdhSettlementCityLevelConfig.setCityConfigId(cityConfigId);
            jdhSettlementCityLevelConfig.setLevel(request.getLevel());
            jdhSettlementCityLevelConfig.setBranch(ACTIVE);
            jdhSettlementCityLevelConfig.setDestCode(StringUtils.isNotBlank(jdhSettlementCityLevelConfig.getCityCode()) ? jdhSettlementCityLevelConfig.getCityCode() : jdhSettlementCityLevelConfig.getProvinceCode());
            CityAngelSettlementPageQuery cityAngelSettlementPageQuery = new CityAngelSettlementPageQuery();
            cityAngelSettlementPageQuery.setProvinceCode(request.getProvinceCode());
            cityAngelSettlementPageQuery.setCityCode(request.getCityCode());
            JdhSettlementCityLevelConfig entity = jdhSettlementCityLevelConfigRepository.queryJdhCityLevelConfig(cityAngelSettlementPageQuery);
            if(Objects.nonNull(entity)){
                jdhSettlementCityLevelConfig.setCityConfigId(entity.getCityConfigId());
                updateJdhSettlementCityLevelConfigList.add(jdhSettlementCityLevelConfig);
            }else{
                addJdhSettlementCityLevelConfigList.add(jdhSettlementCityLevelConfig);
            }
        }
        if(CollUtil.isNotEmpty(addJdhSettlementCityLevelConfigList)){
            jdhSettlementCityLevelConfigRepository.batchSaveJdhCityLevelConfig(addJdhSettlementCityLevelConfigList);
        }
        if(CollUtil.isNotEmpty(updateJdhSettlementCityLevelConfigList)){
            for(JdhSettlementCityLevelConfig jdhSettlementCityLevelConfig : updateJdhSettlementCityLevelConfigList){
                jdhSettlementCityLevelConfigRepository.update(jdhSettlementCityLevelConfig);
            }
        }
    }

    /**
     *
     * @param storeAddr
     * @return
     */
    private BaseAddressBo getBaseAddressBo(String storeAddr){
        BaseAddressBo jdAddressFromAddress = addressRpc.getJDAddressFromAddress(storeAddr.trim());
        return jdAddressFromAddress;
    }
}
