package com.jdh.o2oservice.application.angelpromise.ability.impl;
import com.alibaba.fastjson.JSON;
import com.jdh.o2oservice.application.angelpromise.ability.AngelWorkFinishServiceAbility;
import com.jdh.o2oservice.application.angelpromise.service.AngelServiceRecordApplication;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.processor.MapAutowiredKey;
import com.jdh.o2oservice.common.enums.BusinessModeEnum;
import com.jdh.o2oservice.common.enums.ServiceTypeEnum;
import com.jdh.o2oservice.core.domain.angelpromise.bo.AngelTaskExtStateBo;
import com.jdh.o2oservice.core.domain.angelpromise.context.AngelTaskExtStatusContext;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelBizExtStatusEnum;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelPromiseBizErrorCode;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelTask;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelWork;
import com.jdh.o2oservice.export.angelpromise.query.AngelServiceRecordQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class HomeCareAngelWorkFinishServiceAbility implements AngelWorkFinishServiceAbility,MapAutowiredKey {

    @Resource
    private AngelServiceRecordApplication angelServiceRecordApplication;

    @Override
    public AngelTaskExtStatusContext execute(AngelWork angelWork, List<AngelTask> angelTaskList) {
        log.info("HomeCareAngelWorkFinishServiceAbility execute angelWork={} angelTaskList={}", JSON.toJSONString(angelWork), JSON.toJSONString(angelTaskList));
        AngelServiceRecordQuery serviceRecordConfigQuery = AngelServiceRecordQuery.builder().workId(angelWork.getWorkId()).build();
        // 校验是否开启了护理单配置
        Boolean serviceRecordConfigFlag = angelServiceRecordApplication.checkAngelServiceRecordConfig(serviceRecordConfigQuery);
        if (serviceRecordConfigFlag){
            AngelServiceRecordQuery serviceRecordFinishQuery = AngelServiceRecordQuery.builder().workId(angelWork.getWorkId()).build();
            // 校验护理单是否已完成
            Boolean serviceRecordFinishFlag = angelServiceRecordApplication.checkAngelServiceRecordFinish(serviceRecordFinishQuery);
            if (!serviceRecordFinishFlag){
                throw new BusinessException(AngelPromiseBizErrorCode.ANGEL_SERVICE_RECORD_NOT_SUBMIT);
            }
        }

        // 更新业务状态
        List<AngelTaskExtStateBo> taskExtStateBoList = angelTaskList.stream()
                .map(task -> AngelTaskExtStateBo.builder()
                        .taskId(task.getTaskId())
                        .taskExtStatus(AngelBizExtStatusEnum.CARE_SERVICE_FINISH.getType()).build())
                .collect(Collectors.toList());
        AngelTaskExtStatusContext statusContext = AngelTaskExtStatusContext.builder()
                .workId(angelWork.getWorkId())
                .angelTaskExtStateBoList(taskExtStateBoList)
                .build();
        log.info("HomeCareAngelWorkFinishServiceAbility execute statusContext={}", JSON.toJSONString(statusContext));
        return statusContext;
    }

    @Override
    public String getMapKey() {
        return "angelWorkFinishService"+"_"+BusinessModeEnum.ANGEL_CARE.getCode() + "_" +ServiceTypeEnum.CARE.getServiceType();
    }
}
