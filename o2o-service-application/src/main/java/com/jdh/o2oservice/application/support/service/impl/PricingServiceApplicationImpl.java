package com.jdh.o2oservice.application.support.service.impl;

import cn.hutool.core.lang.TypeReference;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.jdh.o2oservice.application.support.OperationLogApplication;
import com.jdh.o2oservice.application.support.PricingServiceExtApplication;
import com.jdh.o2oservice.application.support.convert.PricingServiceApplicationConverter;
import com.jdh.o2oservice.application.support.service.PricingServiceApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.ducc.model.price.PricingServiceFormula;
import com.jdh.o2oservice.core.domain.support.basic.enums.PricingServiceFactObjectEnum;
import com.jdh.o2oservice.core.domain.support.basic.enums.PricingServiceSceneEnum;
import com.jdh.o2oservice.core.domain.support.price.PricingServiceFormulaParser;
import com.jdh.o2oservice.core.domain.support.price.context.PricingServiceCalculateContext;
import com.jdh.o2oservice.core.domain.support.price.factory.PricingServiceFeeFactory;
import com.jdh.o2oservice.core.domain.support.price.model.PricingServiceFee;
import com.jdh.o2oservice.export.angel.dto.JdhAngelDto;
import com.jdh.o2oservice.export.product.dto.JdhMaterialPackageDto;
import com.jdh.o2oservice.export.support.command.OperationLogCmd;
import com.jdh.o2oservice.export.support.command.PricingServiceCalculateCmd;
import com.jdh.o2oservice.export.support.dto.PricingServiceCalculateResultDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

/**
 * @ClassName PricingServiceApplicationImpl
 * @Description
 * <AUTHOR>
 * @Date 2025/4/29 16:43
 **/
@Service
@Slf4j
public class PricingServiceApplicationImpl implements PricingServiceApplication, PricingServiceExtApplication {

    /**
     * duccConfig
     */
    @Resource
    private DuccConfig duccConfig;

    /**
     * pricingServiceFeeFactory
     */
    @Resource
    private PricingServiceFeeFactory pricingServiceFeeFactory;

    /**
     * operationLogApplication
     */
    @Resource
    private OperationLogApplication operationLogApplication;

    /**
     * 计算价格
     * @param cmd
     * @return
     */
    @Override
    @LogAndAlarm
    public BigDecimal calculatePrice(PricingServiceCalculateCmd cmd) {
        PricingServiceCalculateResultDto pricingServiceCalculateResultDto = calculatePriceForDetail(cmd);
        return Objects.isNull(pricingServiceCalculateResultDto) ? null : pricingServiceCalculateResultDto.getTotalPrice();
    }

    /**
     * 计算价格，返回详细信息
     * @param cmd
     * @return
     */
    @Override
    @LogAndAlarm
    public PricingServiceCalculateResultDto calculatePriceForDetail(PricingServiceCalculateCmd cmd) {
        if (Objects.isNull(cmd) || StringUtils.isBlank(cmd.getScene())) {
            return null;
        }
        PricingServiceSceneEnum scene = PricingServiceSceneEnum.getByScene(cmd.getScene());
        if (Objects.isNull(scene)) {
            return null;
        }
        Map<String, PricingServiceFormula> pricingServiceFormulaConfigMap = duccConfig.getPricingServiceFormulaConfigMap();
        if (Objects.isNull(pricingServiceFormulaConfigMap) || !pricingServiceFormulaConfigMap.containsKey(cmd.getScene())) {
            return null;
        }
        //cmd转换为上下文对象
        PricingServiceCalculateContext context = PricingServiceApplicationConverter.INSTANCE.cmd2CalculateContext(cmd);
        if (Objects.isNull(context.getFactObjectMap())) {
            context.setFactObjectMap(new HashMap<>());
        }

        //获取场景价格计算公式
        PricingServiceFormula pricingServiceFormula = pricingServiceFormulaConfigMap.get(context.getScene());
        log.info("PricingServiceApplicationImpl -> calculatePriceForDetail 获取公式:{}, pricingServiceFormula={}", pricingServiceFormula.getFormulaName(), JSON.toJSONString(pricingServiceFormula));
        //获取公式中用到的费项列表
        Set<String> feeConfigIds = PricingServiceFormulaParser.extractFeeConfigId(pricingServiceFormula.getExpression());
        //获取对应的费项实体
        List<PricingServiceFee> pricingServiceFeeSet = pricingServiceFeeFactory.getPricingServiceFee(feeConfigIds, pricingServiceFormula.getSubtractFeeCodeList());
        log.info("PricingServiceApplicationImpl -> calculatePriceForDetail 获取费项 pricingServiceFeeSet:{}", JSON.toJSONString(pricingServiceFeeSet));
        //费项实体各自计算对应金额
        if (CollectionUtils.isNotEmpty(pricingServiceFeeSet)) {
            log.info("PricingServiceApplicationImpl -> calculatePriceForDetail 费项实体各自计算对应金额 事实对象集合:{}", JSON.toJSONString(context.getFactObjectMap()));
            pricingServiceFeeSet.forEach(pricingServiceFee -> pricingServiceFee.calculateFeeAmount(context, true));
        }
        log.info("PricingServiceApplicationImpl -> calculatePriceForDetail context:{}", JSONUtil.toJsonStr(context));
        operationLogApplication.insertAsyncToLocalDB(OperationLogCmd.builder().bizSceneKey(scene.getScene()).bizSceneDesc(scene.getDescription())
                .bizUnionId(String.valueOf(cmd.getPromiseId())).operateType(4).param(JSONUtil.toJsonStr(cmd)).result(JSONUtil.toJsonStr(context)).build());
        //计算公式总金额
        BigDecimal totalPrice = PricingServiceFormulaParser.calculateFormulaAmount(pricingServiceFormula.getExpression(), pricingServiceFeeSet);
        return PricingServiceCalculateResultDto.builder().totalPrice(totalPrice).feeAmountMap(context.getFeeAmountMap())
                .skuServiceAmountMap(context.getFactObject(PricingServiceFactObjectEnum.SKU_SERVICE_AMOUNT_MAP.getCode(), new TypeReference<Map<Long,BigDecimal>>() {}))
                .materialFeeConfig(context.getFactObject(PricingServiceFactObjectEnum.MATERIAL_FEE_CONFIG.getCode(), new TypeReference<Map<Long, BigDecimal>>() {}))
                .historySettleAmount(context.getFactObject(PricingServiceFactObjectEnum.HISTORY_SETTLE_AMOUNT.getCode(), BigDecimal.class))
                .build();
    }

    /**
     * 根据护士全兼职标签批量计算价格，返回详细信息
     * @param cmd
     * @return
     */
    @Override
    @LogAndAlarm
    public Map<Integer, PricingServiceCalculateResultDto> batchCalculatePriceForDetailByAngelJobNature(PricingServiceCalculateCmd cmd) {
        if (Objects.isNull(cmd) || StringUtils.isBlank(cmd.getScene()) || CollectionUtils.isEmpty(cmd.getJobNatureList())) {
            return null;
        }
        PricingServiceSceneEnum scene = PricingServiceSceneEnum.getByScene(cmd.getScene());
        if (Objects.isNull(scene)) {
            return null;
        }
        Map<String, PricingServiceFormula> pricingServiceFormulaConfigMap = duccConfig.getPricingServiceFormulaConfigMap();
        if (Objects.isNull(pricingServiceFormulaConfigMap) || !pricingServiceFormulaConfigMap.containsKey(cmd.getScene())) {
            return null;
        }
        Map<Integer, PricingServiceCalculateResultDto> result = new HashMap<>();
        //cmd转换为上下文对象
        PricingServiceCalculateContext context = PricingServiceApplicationConverter.INSTANCE.cmd2CalculateContext(cmd);
        if (Objects.isNull(context.getFactObjectMap())) {
            context.setFactObjectMap(new HashMap<>());
        }
        //获取场景价格计算公式
        PricingServiceFormula pricingServiceFormula = pricingServiceFormulaConfigMap.get(context.getScene());
        log.info("PricingServiceApplicationImpl -> batchCalculatePriceForDetailByAngelJobNature 获取公式:{}, pricingServiceFormula={}", pricingServiceFormula.getFormulaName(), JSON.toJSONString(pricingServiceFormula));
        //获取公式中用到的费项列表
        Set<String> feeConfigIds = PricingServiceFormulaParser.extractFeeConfigId(pricingServiceFormula.getExpression());
        //获取对应的费项实体
        List<PricingServiceFee> pricingServiceFeeSet = pricingServiceFeeFactory.getPricingServiceFee(feeConfigIds, pricingServiceFormula.getSubtractFeeCodeList());
        log.info("PricingServiceApplicationImpl -> batchCalculatePriceForDetailByAngelJobNature 获取费项 pricingServiceFeeSet:{}", JSON.toJSONString(pricingServiceFeeSet));

        //按照职级计算价格
        for (Integer jobNature : cmd.getJobNatureList()) {
            try {
                //组装护士数据放入事实对象
                JdhAngelDto jdhAngelDto = new JdhAngelDto();
                jdhAngelDto.setJobNature(jobNature);
                context.getFactObjectMap().put(PricingServiceFactObjectEnum.ANGEL_INFO.getCode(), jdhAngelDto);
                //费项实体各自计算对应金额
                if (CollectionUtils.isNotEmpty(pricingServiceFeeSet)) {
                    log.info("PricingServiceApplicationImpl -> batchCalculatePriceForDetailByAngelJobNature 费项实体各自计算对应金额 事实对象集合:{}", JSON.toJSONString(context.getFactObjectMap()));
                    pricingServiceFeeSet.forEach(pricingServiceFee -> pricingServiceFee.calculateFeeAmount(context, true));
                }
                log.info("PricingServiceApplicationImpl -> batchCalculatePriceForDetailByAngelJobNature context:{}", JSONUtil.toJsonStr(context));
                //计算公式总金额
                BigDecimal totalPrice = PricingServiceFormulaParser.calculateFormulaAmount(pricingServiceFormula.getExpression(), pricingServiceFeeSet);
                PricingServiceCalculateResultDto resultDto = PricingServiceCalculateResultDto.builder().totalPrice(totalPrice).feeAmountMap(new HashMap<>(context.getFeeAmountMap()))
                        .skuServiceAmountMap(context.getFactObject(PricingServiceFactObjectEnum.SKU_SERVICE_AMOUNT_MAP.getCode(), new TypeReference<Map<Long,BigDecimal>>() {}))
                        .materialFeeConfig(context.getFactObject(PricingServiceFactObjectEnum.MATERIAL_FEE_CONFIG.getCode(), new TypeReference<Map<Long, BigDecimal>>() {}))
                        .build();
                result.put(jobNature, resultDto);
                operationLogApplication.insertAsyncToLocalDB(OperationLogCmd.builder().bizSceneKey(scene.getScene()).bizSceneDesc(scene.getDescription())
                        .bizUnionId(String.valueOf(cmd.getPromiseId())).operateType(4).param(JSONUtil.toJsonStr(cmd)).result(JSONUtil.toJsonStr(context)).build());
                //重置计算的费项，遍历计算下一个
                context.resetCalculateResult(false);
            } catch (Exception e) {
                log.error("PricingServiceApplicationImpl -> batchCalculatePriceForDetailByAngelJobNature error", e);
            }
        }
        return result;
    }

    /**
     * 计算价格，返回详细信息
     *
     * @param cmd
     * @return
     */
    @Override
    @LogAndAlarm
    public PricingServiceCalculateResultDto calculatePriceExtForDetail(PricingServiceCalculateCmd cmd) {
        return this.calculatePriceForDetail(cmd);
    }
}