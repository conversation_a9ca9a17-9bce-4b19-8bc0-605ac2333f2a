package com.jdh.o2oservice.application.support.service;

import com.jdh.o2oservice.export.support.query.SoundExtRequest;

/**
 * <AUTHOR>
 * @description
 * @date 2025/5/28
 */
public interface SoundExtApplication {


    /**
     * 根据 SoundExtRequest 对象查询扩展声音参数。
     * @param request SoundExtRequest 对象，包含查询条件。
     * @return 扩展声音参数对象。
     */
    Object querySoundExtParam(SoundExtRequest request);


}
