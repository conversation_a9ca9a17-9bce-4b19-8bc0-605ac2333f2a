package com.jdh.o2oservice.application.via.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.google.common.collect.Maps;
import com.googlecode.aviator.AviatorEvaluator;
import com.jd.i18n.order.dict.FieldKeyEnum;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jd.medicine.base.common.util.StringUtil;
import com.jd.purchase.domain.assist.SkuRelationInfo;
import com.jd.purchase.domain.old.bean.Cart;
import com.jd.purchase.domain.old.bean.Order;
import com.jd.purchase.domain.old.bean.SKU;
import com.jd.purchase.utils.serializer.helper.SerializersHelper;
import com.jdh.o2oservice.application.angel.service.AngelApplication;
import com.jdh.o2oservice.application.angelpromise.service.AngelServiceRecordApplication;
import com.jdh.o2oservice.application.angelpromise.service.AngelWorkApplication;
import com.jdh.o2oservice.application.dispatch.service.DispatchApplication;
import com.jdh.o2oservice.application.medicalpromise.MedicalPromiseApplication;
import com.jdh.o2oservice.application.medicalpromise.service.MedPromiseHistoryApplication;
import com.jdh.o2oservice.application.promise.VoucherApplication;
import com.jdh.o2oservice.application.promise.service.PromiseApplication;
import com.jdh.o2oservice.application.support.util.OrderTypeDicUtil;
import com.jdh.o2oservice.application.trade.service.TradeApplication;
import com.jdh.o2oservice.application.via.ManViaApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.constatnt.NumConstant;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.enums.OrderStatusEnum;
import com.jdh.o2oservice.base.enums.PartnerSourceEnum;
import com.jdh.o2oservice.base.enums.SendpayValueEnum;
import com.jdh.o2oservice.base.enums.ThreadPoolConfigEnum;
import com.jdh.o2oservice.base.factory.ExecutorPoolFactory;
import com.jdh.o2oservice.base.util.TimeFormat;
import com.jdh.o2oservice.base.util.TimeUtils;
import com.jdh.o2oservice.common.enums.*;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelShipStatusEnum;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelWorkStatusEnum;
import com.jdh.o2oservice.core.domain.dispatch.enums.DispatchEventTypeEnum;
import com.jdh.o2oservice.core.domain.dispatch.model.JdhDispatch;
import com.jdh.o2oservice.core.domain.dispatch.model.JdhDispatchDetail;
import com.jdh.o2oservice.core.domain.dispatch.repository.db.DispatchRepository;
import com.jdh.o2oservice.core.domain.dispatch.repository.query.DispatchRepQuery;
import com.jdh.o2oservice.core.domain.medpromise.enums.MedPromiseEventTypeEnum;
import com.jdh.o2oservice.core.domain.promise.model.JdhVoucher;
import com.jdh.o2oservice.core.domain.promise.model.JdhVoucherIdentifier;
import com.jdh.o2oservice.core.domain.promise.repository.db.VoucherRepository;
import com.jdh.o2oservice.core.domain.provider.model.Provider;
import com.jdh.o2oservice.core.domain.provider.model.ProviderIdentifier;
import com.jdh.o2oservice.core.domain.provider.repository.db.ProviderRepository;
import com.jdh.o2oservice.core.domain.report.model.MedicalReport;
import com.jdh.o2oservice.core.domain.report.repository.db.MedicalReportRepository;
import com.jdh.o2oservice.core.domain.support.basic.enums.JdhDispatchStatusEnum;
import com.jdh.o2oservice.core.domain.support.basic.enums.JdhFreezeEnum;
import com.jdh.o2oservice.core.domain.support.vertical.model.JdhVerticalBusiness;
import com.jdh.o2oservice.core.domain.support.vertical.repository.VerticalBusinessRepository;
import com.jdh.o2oservice.core.domain.support.via.configcenter.ManViaConfigRepository;
import com.jdh.o2oservice.core.domain.support.via.configcenter.ViaConfigRepository;
import com.jdh.o2oservice.core.domain.support.via.model.*;
import com.jdh.o2oservice.core.domain.trade.context.JdOrderFullPageContext;
import com.jdh.o2oservice.core.domain.trade.enums.JdOrderExtTypeEnum;
import com.jdh.o2oservice.core.domain.trade.model.JdOrder;
import com.jdh.o2oservice.core.domain.trade.model.JdOrderFull;
import com.jdh.o2oservice.core.domain.trade.model.JdOrderIdentifier;
import com.jdh.o2oservice.core.domain.trade.repository.db.JdOrderRepository;
import com.jdh.o2oservice.core.domain.trade.rpc.OrderInfoRpc;
import com.jdh.o2oservice.core.domain.trade.vo.JdOrderExtendVo;
import com.jdh.o2oservice.core.domain.trade.vo.JdSkuRelationInfoVo;
import com.jdh.o2oservice.core.domain.trade.vo.OrderAppointmentInfoValueObject;
import com.jdh.o2oservice.core.domain.user.auth.uim.context.UimLoginContext;
import com.jdh.o2oservice.core.domain.user.auth.uim.context.UimRoleContext;
import com.jdh.o2oservice.core.domain.user.auth.uim.context.UimUserContext;
import com.jdh.o2oservice.export.angel.dto.JdhAngelDto;
import com.jdh.o2oservice.export.angel.query.AngelRequest;
import com.jdh.o2oservice.export.angelpromise.dto.*;
import com.jdh.o2oservice.export.angelpromise.query.AngelServiceRecordQuery;
import com.jdh.o2oservice.export.angelpromise.query.AngelWorkDetailForManRequest;
import com.jdh.o2oservice.export.dispatch.dto.JdhDispatchHistoryDto;
import com.jdh.o2oservice.export.dispatch.query.DispatchHistoryListRequest;
import com.jdh.o2oservice.export.medicalpromise.dto.MedPromiseHistoryDTO;
import com.jdh.o2oservice.export.medicalpromise.dto.MedicalPromiseDTO;
import com.jdh.o2oservice.export.medicalpromise.query.MedPromiseHistoryRequest;
import com.jdh.o2oservice.export.medicalpromise.query.MedicalPromiseListRequest;
import com.jdh.o2oservice.export.medicalpromise.query.MedicalPromiseRequest;
import com.jdh.o2oservice.export.promise.dto.PromiseDto;
import com.jdh.o2oservice.export.promise.dto.PromiseServiceDetailDto;
import com.jdh.o2oservice.export.promise.query.PromiseIdRequest;
import com.jdh.o2oservice.export.promise.query.PromiseListRequest;
import com.jdh.o2oservice.export.support.dto.ManViaCompletePromiseTagDto;
import com.jdh.o2oservice.export.support.dto.ManViaPromiseServiceDto;
import com.jdh.o2oservice.export.trade.dto.JdOrderDTO;
import com.jdh.o2oservice.export.trade.dto.JdOrderExtDTO;
import com.jdh.o2oservice.export.trade.dto.JdOrderItemDTO;
import com.jdh.o2oservice.export.trade.query.OrderDetailParam;
import com.jdh.o2oservice.export.via.dto.ManViaFloorDto;
import com.jdh.o2oservice.export.via.dto.ViaCompletePromiseDto;
import com.jdh.o2oservice.export.via.dto.ViaTimeLineItemDto;
import com.jdh.o2oservice.export.via.query.ViaCompletePromiseRequest;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 运营端 via application
 *
 * <AUTHOR>
 * @date 2024/05/02
 */
@Slf4j
@Service
public class ManViaApplicationImpl implements ManViaApplication, InitializingBean {

    /**
     * medicalPromiseApplication
     */
    @Autowired
    private MedicalPromiseApplication medicalPromiseApplication;

    /**
     * promiseApplication
     */
    @Autowired
    private PromiseApplication promiseApplication;

    /**
     * angelWorkApplication
     */
    @Autowired
    private AngelWorkApplication angelWorkApplication;

    /**
     * tradeApplication
     */
    @Autowired
    private TradeApplication tradeApplication;

    /**
     * angelApplication
     */
    @Autowired
    private AngelApplication angelApplication;

    /**
     * executorPoolFactory
     */
    @Resource
    private ExecutorPoolFactory executorPoolFactory;

    /**
     * medPromiseHistoryApplication
     */
    @Autowired
    private MedPromiseHistoryApplication medPromiseHistoryApplication;

    /**
     * dispatchApplication
     */
    @Autowired
    private DispatchApplication dispatchApplication;

    /**
     * verticalBusinessRepository
     */
    @Autowired
    private VerticalBusinessRepository verticalBusinessRepository;

    /**
     * viaConfigRepository
     */
    @Autowired
    private ViaConfigRepository viaConfigRepository;

    /**
     * medicalReportRepository
     */
    @Autowired
    private MedicalReportRepository medicalReportRepository;

    /**
     * providerRepository
     */
    @Autowired
    private ProviderRepository providerRepository;

    /**
     * dispatchRepository
     */
    @Autowired
    private DispatchRepository dispatchRepository;

    /**
     * 运营端状态映射 - 自检测类
     */
    private static final String SELF_TEST_STATUS_MAPPING = "manVia_selfTest_StatusMapping";

    /**
     * 运营端状态映射 - angel检测类
     */
    private static final String ANGEL_TEST_STATUS_MAPPING = "manVia_angelTest_StatusMapping";

    /**
     * 运营端状态映射 - angel护理类
     */
    private static final String ANGEL_CARE_STATUS_MAPPING = "manVia_angelCare_StatusMapping";

    /**
     * 已有采样盒的 - 自检测类
     */
    private static final String SELF_TEST_PRE_SAMPLE_STATUS_MAPPING = "man_Via_preSample_selfTest_StatusMapping";

    /**
     * jdOrderRepository
     */
    @Autowired
    private JdOrderRepository jdOrderRepository;

    /**
     * man
     */
    @Resource
    ManViaConfigRepository manViaConfigRepository;


    @Autowired
    private DuccConfig duccConfig;

    /**
     * voucherRepository
     */
    @Resource
    private VoucherRepository voucherRepository;

    /**
     * orderInfoRpc
     */
    @Resource
    private OrderInfoRpc orderInfoRpc;

    /**
     * 订单类型枚举
     */
    @Resource
    OrderTypeDicUtil orderTypeDicUtil;

    @Resource
    private AngelServiceRecordApplication angelServiceRecordApplication;

    /**
     * 初始化，预热一下表达式编译工作
     */
    @Override
    public void afterPropertiesSet() throws Exception {
        log.info("ManViaApplicationImpl 启动预加载 aviator表达式编译 开始");
        Arrays.asList(SELF_TEST_STATUS_MAPPING,
                ANGEL_TEST_STATUS_MAPPING,
                ANGEL_CARE_STATUS_MAPPING,
                SELF_TEST_PRE_SAMPLE_STATUS_MAPPING).forEach(ele -> {
            try {
                log.info("ManViaApplicationImpl 启动预加载 aviator表达式编译 scene:{}",ele);
                ViaConfig viaConfig = viaConfigRepository.find(ViaConfigIdentifier.builder().scene(ele).build());
                if(Objects.nonNull(viaConfig) && CollUtil.isNotEmpty(viaConfig.getStatusMapping())){
                    viaConfig.getStatusMapping().forEach(viaStatusMapping -> {
                        log.info("ManViaApplicationImpl 启动预加载 aviator表达式:{}",viaStatusMapping.getStatusExpression());
                        AviatorEvaluator.compile(viaStatusMapping.getStatusExpression(),Boolean.TRUE);
                    });
                }
            }catch (Exception e){
                log.error("ManViaApplicationImpl init error",e);
            }
        });
        log.info("ManViaApplicationImpl 启动预加载 aviator表达式编译 完成");
    }

    /**
     * 查询完整履约信息
     *
     * @param request 请求
     * @return {@link ViaCompletePromiseDto}
     */
    @Override
    public ViaCompletePromiseDto queryCompletePromise(ViaCompletePromiseRequest request) {
        if(request != null && request.getMedPromiseId() == 0) {
            return queryOrderNoMedicalPromiseId(request.getOrderId());
        }

        ViaCompletePromiseDto result = ViaCompletePromiseDto.builder().tagInfo(ManViaCompletePromiseTagDto.builder().build()).build();

        //服务单信息
        MedicalPromiseDTO medicalPromiseDto = medicalPromiseApplication.queryMedicalPromise(MedicalPromiseRequest.builder()
                .medicalPromiseId(request.getMedPromiseId())
                .patientDetail(Boolean.TRUE)
                .itemDetail(Boolean.TRUE)
                .reportDetail(Boolean.TRUE)
                .build());
        result.setMedicalPromiseDto(medicalPromiseDto);
        //状态映射规则
        List<ViaStatusMapping> statusMapping = findStatusMapping(medicalPromiseDto);
        //businessMode
        BusinessModeEnum businessModeEnum = findBusinessMode(medicalPromiseDto.getVerticalCode());
        log.info("ManViaApplicationImpl queryCompletePromise businessModeEnum:{},medicalPromiseDto:{},statusMapping:{}",businessModeEnum,JSON.toJSONString(medicalPromiseDto),JSON.toJSONString(statusMapping));
        if (Arrays.asList(BusinessModeEnum.ANGEL_TEST.getCode(),BusinessModeEnum.ANGEL_TEST_NO_LABORATORY.getCode(),BusinessModeEnum.ANGEL_CARE.getCode()).contains(businessModeEnum.getCode())){
            medicalPromiseDto.setRecordingDetails(true);
        }

        List<CompletableFuture<Void>> firstFutures = new ArrayList<>();

        //查promise
        firstFutures.add(CompletableFuture.runAsync(() -> fillPromiseDto(medicalPromiseDto.getPromiseId(),result), executorPoolFactory.get(ThreadPoolConfigEnum.MAN_VIA_DETAIL_HAND_POOL)));

        //查work angel
        firstFutures.add(CompletableFuture.runAsync(() -> fillAngelAndWork(result, false), executorPoolFactory.get(ThreadPoolConfigEnum.MAN_VIA_DETAIL_HAND_POOL)));

        //查订单
        firstFutures.add(CompletableFuture.runAsync(() -> fillOrder(request.getOrderId(),result), executorPoolFactory.get(ThreadPoolConfigEnum.MAN_VIA_DETAIL_HAND_POOL)));

        //时间轴
        firstFutures.add(CompletableFuture.runAsync(() -> fillTimeLine(result, businessModeEnum, false), executorPoolFactory.get(ThreadPoolConfigEnum.MAN_VIA_DETAIL_HAND_POOL)));

        CompletableFuture.allOf(firstFutures.toArray(new CompletableFuture[0])).join();
        log.info("ManViaApplicationImpl queryCompletePromise firstFutures end result:{}", JSON.toJSONString(result));

        //处理展示状态
        List<CompletableFuture<Void>> secondFutures = new ArrayList<>();
        secondFutures.add(CompletableFuture.runAsync(() -> fillViaInfo(result,businessModeEnum,statusMapping),executorPoolFactory.get(ThreadPoolConfigEnum.MAN_VIA_DETAIL_HAND_POOL)));

        //订单下全部服务单信息
        //精准营养，屏蔽列表
        if(!VerticalEnum.isNonOrderVertical(medicalPromiseDto.getVerticalCode())){
            secondFutures.add(CompletableFuture.runAsync(() -> fillAllMedPromise(result,statusMapping), executorPoolFactory.get(ThreadPoolConfigEnum.MAN_VIA_DETAIL_HAND_POOL)));
        }
        CompletableFuture.allOf(secondFutures.toArray(new CompletableFuture[0])).join();
        log.info("ManViaApplicationImpl queryCompletePromise secondFutures end result:{}", JSON.toJSONString(result));
        fillPageElement(request.getPageCode(), result);
        return result;
    }

    /**
     * 查询完整B2b promise信息
     * @param request
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.via.impl.ManViaApplicationImpl.queryCompleteB2bPromise")
    public ViaCompletePromiseDto queryCompleteB2bPromise(ViaCompletePromiseRequest request) {
        ViaCompletePromiseDto result = ViaCompletePromiseDto.builder().tagInfo(ManViaCompletePromiseTagDto.builder().build()).build();

        //服务单信息
        List<MedicalPromiseDTO> medicalPromiseList = medicalPromiseApplication.queryMedicalPromiseList(MedicalPromiseRequest.builder()
                .promiseId(request.getPromiseId())
                .build());
        log.info("ManViaApplicationImpl queryCompleteB2bPromise medicalPromiseList={}", JSON.toJSONString(medicalPromiseList));
        result.setMedicalPromiseDtoList(medicalPromiseList);

        PromiseDto promiseDto = promiseApplication.findByPromiseId(PromiseIdRequest.builder().promiseId(request.getPromiseId()).build());
        log.info("ManViaApplicationImpl queryCompleteB2bPromise promiseDto={}", JSON.toJSONString(promiseDto));
        result.setPromiseDto(promiseDto);
        List<CompletableFuture<Void>> firstFutures = new ArrayList<>();

        //查promise
        firstFutures.add(CompletableFuture.runAsync(() -> fillPromiseDto(request.getPromiseId(),result), executorPoolFactory.get(ThreadPoolConfigEnum.MAN_VIA_DETAIL_HAND_POOL)));
        log.info("ManViaApplicationImpl queryCompleteB2bPromise fillPromiseDto result={}", JSON.toJSONString(result));

        //查work angel
        firstFutures.add(CompletableFuture.runAsync(() -> fillAngelAndWork(result, true), executorPoolFactory.get(ThreadPoolConfigEnum.MAN_VIA_DETAIL_HAND_POOL)));
        log.info("ManViaApplicationImpl queryCompleteB2bPromise fillAngelAndWork result={}", JSON.toJSONString(result));

        //businessMode
        BusinessModeEnum businessModeEnum = findBusinessMode(promiseDto.getVerticalCode());

        //时间轴
        firstFutures.add(CompletableFuture.runAsync(() -> fillTimeLine(result, businessModeEnum, true), executorPoolFactory.get(ThreadPoolConfigEnum.MAN_VIA_DETAIL_HAND_POOL)));
        log.info("ManViaApplicationImpl queryCompleteB2bPromise fillTimeLine result={}", JSON.toJSONString(result));

        CompletableFuture.allOf(firstFutures.toArray(new CompletableFuture[0])).join();

        fillPageElement(request.getPageCode(), result);
        log.info("ManViaApplicationImpl queryCompleteB2bPromise firstFutures end result:{}", JSON.toJSONString(result));
        return result;
    }

    /**
     * 页面
     *
     * @param pageCode pageCode
     * @return vo
     */
    @Override
    public ManViaConfig findPageFloor(String pageCode) {
        ManViaConfig manViaConfig = manViaConfigRepository.findPageFloor(pageCode);
        if (manViaConfig == null) {
            return null;
        }
        UimUserContext uimUserContext = UimUserContext.get();
        return filterManViaConfig(manViaConfig, uimUserContext);
    }

    /**
     * 页面楼层
     *
     * @param pageCode  pageCode
     * @param floorCode floorCode
     * @return vo
     */
    @Override
    public ManViaFloorConfig findPageFloor(String pageCode, String floorCode) {
        ManViaFloorConfig manViaFloorConfig = manViaConfigRepository.findPageFloor(pageCode, floorCode);
        if (manViaFloorConfig == null) {
            return null;
        }
        UimUserContext uimUserContext = UimUserContext.get();
        return filterManViaFloorConfig(manViaFloorConfig, uimUserContext);
    }

    /**
     * 组装楼层元素信息
     *
     * @param pageCode code
     * @param result ret
     */
    private void fillPageElement(String pageCode, ViaCompletePromiseDto result){
        try {
            Map<String, Object> param = new HashMap<>();
            param.put("medicalPromiseDto", result.getMedicalPromiseDto());
            param.put("promiseDto", result.getPromiseDto());
            param.put("jdOrderDto", result.getJdOrderDto());
            param.put("angelShipDto", result.getAngelShipDto());
            param.put("angelDto", result.getAngelDto());
            param.put("angelWorkDto", result.getAngelWorkDto());
            ManViaConfig manViaConfig = findPageFloor(pageCode);
            if (manViaConfig == null || CollUtil.isEmpty(manViaConfig.getFloorConfigList())) {
                return;
            }
            List<ManViaFloorDto> manViaFloorElementConfigs = new ArrayList<>();
            // 本期展示不处理是否展示楼层等逻辑,运营端楼层未配置化
            for (ManViaFloorConfig manViaFloorConfig : manViaConfig.getFloorConfigList()) {
                if (manViaFloorConfig == null || CollUtil.isEmpty(manViaFloorConfig.getFloorElementConfigList())) {
                    continue;
                }
                for (ManViaFloorElementConfig manViaFloorElementConfig : manViaFloorConfig.getFloorElementConfigList()) {
                    if (StringUtils.isNotBlank(manViaFloorElementConfig.getShowExpression())) {
                        if ((boolean) AviatorEvaluator.compile(manViaFloorElementConfig.getShowExpression(), Boolean.TRUE).execute(param)) {
                            toManViaFloorDto(manViaFloorElementConfigs, manViaFloorConfig, manViaFloorElementConfig);
                        }
                    } else {
                        toManViaFloorDto(manViaFloorElementConfigs, manViaFloorConfig, manViaFloorElementConfig);
                    }
                }
            }
            result.setFloorConfigList(manViaFloorElementConfigs);
        } catch (Exception e) {
            log.error("ManViaApplicationImpl queryCompletePromise error",e);
        }
    }

    /**
     * 组装数据
     *
     * @param manViaFloorElementConfigs conf
     * @param manViaFloorConfig conf
     * @param manViaFloorElementConfig conf
     */
    private void toManViaFloorDto(List<ManViaFloorDto> manViaFloorElementConfigs, ManViaFloorConfig manViaFloorConfig, ManViaFloorElementConfig manViaFloorElementConfig) {
        ManViaFloorDto manViaFloorDto = new ManViaFloorDto();
        manViaFloorDto.setKey(manViaFloorConfig.getKey());
        manViaFloorDto.setName(manViaFloorConfig.getName());
        manViaFloorDto.setElementKey(manViaFloorElementConfig.getKey());
        manViaFloorDto.setElementName(manViaFloorElementConfig.getName());
        manViaFloorDto.setElementType(manViaFloorElementConfig.getType());
        manViaFloorDto.setElementClickAction(manViaFloorElementConfig.getClickAction());
        manViaFloorDto.setElementClickUrl(manViaFloorElementConfig.getClickUrl());
        manViaFloorElementConfigs.add(manViaFloorDto);
    }

    /**
     * 组装展示信息
     *
     * @param result           结果
     * @param businessModeEnum
     * @param statusMapping
     */
    private void fillViaInfo(ViaCompletePromiseDto result, BusinessModeEnum businessModeEnum, List<ViaStatusMapping> statusMapping){
        log.info("fillViaInfo start");
        ManViaCompletePromiseTagDto tagInfo = result.getTagInfo();
        MedicalPromiseDTO medicalPromiseDto = result.getMedicalPromiseDto();
        AngelWorkDetailForManDto angelWorkDto = result.getAngelWorkDto();
        ViaStatusMapping hitStatus = findViaStatusDesc(result.getJdOrderDto().getOrderStatus(),
                result.getPromiseDto().getPromiseStatus(),result.getMedicalPromiseDto().getStatus(),
                result.getMedicalPromiseDto().getFreeze(),Objects.isNull(angelWorkDto) ? null : angelWorkDto.getWorkStatus(),statusMapping);
        tagInfo.setStatusDesc(Objects.isNull(hitStatus) ? null : hitStatus.getStatusDesc());
        tagInfo.setViaStatus(Objects.isNull(hitStatus) ? null : hitStatus.getViaStatus());

        String serviceType = findServiceType(businessModeEnum);
        tagInfo.setServiceTypeDesc(serviceType);

        JdOrderDTO jdOrderDto = result.getJdOrderDto();
        tagInfo.setPartnerSource(jdOrderDto.getPartnerSource());
        // JDH_XFYL(0, "消费医疗"),JDH_NETDIAG(1, "互医"),
        String partnerSourceDesc = "";
        if(PartnerSourceEnum.JDH_XFYL.getCode().equals(jdOrderDto.getPartnerSource())){
            partnerSourceDesc = "C端交易";
        }else if(PartnerSourceEnum.JDH_NETDIAG.getCode().equals(jdOrderDto.getPartnerSource())){
            partnerSourceDesc = "互医检验单";
        }else if(PartnerSourceEnum.JDH_HOMEDIAG.getCode().equals(jdOrderDto.getPartnerSource())){
            partnerSourceDesc = "家医检验单";
        }else if(PartnerSourceEnum.OUT_PINGAN.getCode().equals(jdOrderDto.getPartnerSource())){
            partnerSourceDesc = "平安健康";
        }
        tagInfo.setPartnerSourceDesc(partnerSourceDesc);

        //出报告状态
        if(NumConstant.NUM_1.equals(medicalPromiseDto.getReportStatus())){
            tagInfo.setReportStatus(Boolean.TRUE);
            MedicalReport medicalReport = queryMedicalReport(medicalPromiseDto.getMedicalPromiseId());
            tagInfo.setReportDate(Objects.isNull(medicalReport) ? null : medicalReport.getReportTime());
        }

        //实验室检测状态
        if(Objects.nonNull(medicalPromiseDto.getCheckTime())){
            tagInfo.setCheckStatus(Boolean.TRUE);
            tagInfo.setCheckDate(medicalPromiseDto.getCheckTime());
        }

        //服务商名称
        if(Objects.nonNull(medicalPromiseDto.getProviderId())){
            Provider provider = queryProviderInfo(medicalPromiseDto.getProviderId());
            tagInfo.setProviderName(Objects.isNull(provider) ? null : provider.getChannelName());
        }

        //是否赠品
        String extend = jdOrderDto.getExtend();
        tagInfo.setIsGiftOrder(Boolean.FALSE);
        if(StrUtil.isNotBlank(extend)){
            JdOrderExtendVo orderExtendVo = JSON.parseObject(extend, JdOrderExtendVo.class);
            if (NumConstant.NUM_2.equals(orderExtendVo.getWareType())) {
                tagInfo.setIsGiftOrder(Boolean.TRUE);
            }
        }

        //是否包含加项商品
        tagInfo.setIsIncludeAddSkuOrder(Boolean.FALSE);
        //有父单，取父单  处理加项合管逻辑的影响
        Long parentId = jdOrderDto.getParentId();
        if(Objects.nonNull(parentId) && parentId > 0){
            OrderDetailParam orderDetailParam = new OrderDetailParam();
            orderDetailParam.setOrderId(parentId.toString());
            JdOrderDTO parentOrderDetail = tradeApplication.getOrderDetail(orderDetailParam);
            //看父单
            if (parentOrderDetail != null && NumConstant.NUM_1.equals(parentOrderDetail.getHasAdded())) {
                tagInfo.setIsIncludeAddSkuOrder(Boolean.TRUE);
            }
        }else{
            //看子单
            if (NumConstant.NUM_1.equals(jdOrderDto.getHasAdded())) {
                tagInfo.setIsIncludeAddSkuOrder(Boolean.TRUE);
            }
        }

        //订单类型
        if(Objects.nonNull(jdOrderDto.getOrderType())){
            tagInfo.setOrderTypeDesc(jdOrderDto.getOrderType() + "号订单");
        }

        //是否已有采样盒
        List<JdOrderExtDTO> jdOrderExtList = jdOrderDto.getJdOrderExtList();
        if(CollUtil.isNotEmpty(jdOrderExtList)){
            try {
                for (JdOrderExtDTO jdOrderExtDTO : jdOrderExtList) {
                    if (StrUtil.isNotBlank(jdOrderExtDTO.getExtType()) && JdOrderExtTypeEnum.APPOINTMENT_INFO.getExtType().equals(jdOrderExtDTO.getExtType())) {
                        OrderAppointmentInfoValueObject orderAppointmentInfo = JsonUtil.parseObject(jdOrderExtDTO.getExtContext(),OrderAppointmentInfoValueObject.class);
                        tagInfo.setPreSampleFlag(orderAppointmentInfo.getPreSampleFlag());
                    }
                }
            } catch (Exception e) {
                //ignore
            }

        }
        log.info("fillViaInfo end");
    }

    /**
     * queryProviderInfo
     *
     * @param providerId providerId
     * @return {@link Provider}
     */
    private Provider queryProviderInfo(Long providerId){
        try {
            Provider provider = providerRepository.find(ProviderIdentifier.builder().channelNo(providerId).build());
            log.info("ManViaApplicationImpl queryProviderInfo provider:{}", JSON.toJSONString(provider));
            return provider;
        }catch (Exception e){
            log.error("ManViaApplicationImpl queryProviderInfo error",e);
            return null;
        }
    }

    /**
     * queryMedicalReport
     *
     * @param medPromiseId medPromiseId
     * @return {@link MedicalReport}
     */
    private MedicalReport queryMedicalReport(Long medPromiseId){
        try {
            MedicalReport medicalReport = medicalReportRepository.getByMedicalPromiseId(medPromiseId);
            log.info("ManViaApplicationImpl queryMedicalReport medicalReport:{}", JSON.toJSONString(medicalReport));
            return medicalReport;
        }catch (Exception e){
            log.error("ManViaApplicationImpl queryMedicalReport error",e);
            return null;
        }
    }

    /**
     * findViaStatusDesc
     *
     * @param
     */
    private ViaStatusMapping findViaStatusDesc(Integer orderStatus,
                                               Integer promiseStatus,
                                               Integer medPromiseStatus,
                                               Integer medPromiseFreeze,
                                               Integer angelWorkStatus,
                                               List<ViaStatusMapping> statusMapping) {
        Map<String, Object> param = new HashMap<>();
        param.put("orderStatus",orderStatus);
        param.put("promiseStatus",promiseStatus);
        param.put("medPromiseStatus",medPromiseStatus);
        param.put("medPromiseFreeze",medPromiseFreeze);
        param.put("angelWorkStatus",angelWorkStatus);

        log.info("findViaStatusDesc param:{}",JSON.toJSONString(param));
        ViaStatusMapping hitViaStatus = null;
        if(CollUtil.isNotEmpty(statusMapping)){
            for (ViaStatusMapping viaStatusMapping : statusMapping) {
                if ((boolean) AviatorEvaluator.compile(viaStatusMapping.getStatusExpression(),Boolean.TRUE).execute(param)) {
                    hitViaStatus =  viaStatusMapping;
                    break;
                }
            }
        }
        return hitViaStatus;
    }

    /**
     * 查找状态映射
     *
     * @param medicalPromiseDto medicalPromiseDto
     * @return {@link List}<{@link ViaStatusMapping}>
     */
    private List<ViaStatusMapping> findStatusMapping(MedicalPromiseDTO medicalPromiseDto){
        BusinessModeEnum businessModeEnum = findBusinessMode(medicalPromiseDto.getVerticalCode());
        JdhVoucher jdhVoucher = voucherRepository.find(JdhVoucherIdentifier.builder().voucherId(medicalPromiseDto.getVoucherId()).build());
        String viaStatusKey;
        switch (Objects.requireNonNull(businessModeEnum)){
            case SELF_TEST_TRANSPORT:
            case SELF_TEST:
                //已有采样盒
                if(Boolean.TRUE.equals(jdhVoucher.getExtend().getPreSampleFlag())) {
                    viaStatusKey = SELF_TEST_PRE_SAMPLE_STATUS_MAPPING;
                }else{
                    viaStatusKey = SELF_TEST_STATUS_MAPPING;
                }
                break;
            case ANGEL_TEST:
                viaStatusKey = ANGEL_TEST_STATUS_MAPPING;
                break;
            case ANGEL_TEST_NO_LABORATORY:
                viaStatusKey = ANGEL_TEST_STATUS_MAPPING;
                break;
            case ANGEL_CARE:
                viaStatusKey = ANGEL_CARE_STATUS_MAPPING;
                break;
            default:
                viaStatusKey = "";
                break;
        }
        ViaConfig viaConfig = viaConfigRepository.find(ViaConfigIdentifier.builder().scene(viaStatusKey).build());
        return viaConfig.getStatusMapping();
    }

    /**
     * findBusinessMode
     *
     * @param verticalCode 垂直代码
     * @return {@link BusinessModeEnum}
     */
    private BusinessModeEnum findBusinessMode(String verticalCode){
        JdhVerticalBusiness jdhVerticalBusiness = verticalBusinessRepository.find(verticalCode);
        return BusinessModeEnum.getEnumByCode(jdhVerticalBusiness.getBusinessModeCode());
    }

    /**
     * 查找服务类型
     *
     * @param businessModeEnum businessModeEnum
     * @return {@link String}
     */
    private String findServiceType(BusinessModeEnum businessModeEnum){
        switch (Objects.requireNonNull(businessModeEnum)){
            case SELF_TEST:
                return "骑手上门检测服务";
            case ANGEL_TEST:
                return "护士上门检测服务";
            case ANGEL_TEST_NO_LABORATORY:
                return "护士上门检测服务";
            case ANGEL_CARE:
                return "护士上门护理服务";
            case SELF_TEST_TRANSPORT:
                return "快递寄送检测";
            default:
                return "到店服务";
        }
    }

    /**
     * 填充时间线
     *
     * @param result       结果
     */
    private void fillTimeLine(ViaCompletePromiseDto result, BusinessModeEnum businessModeEnum, Boolean b2bFlag) {
        try {
            Long promiseId = null;
            Long medPromiseId = null;
            AngelWorkDetailForManDto angelWorkDetailForManDto = null;
            List<MedPromiseHistoryDTO> medPromiseHistoryList = null;
            if (b2bFlag){
                promiseId = result.getPromiseDto().getPromiseId();
                List<Long> medicalPromiseIdList = result.getMedicalPromiseDtoList().stream().map(MedicalPromiseDTO::getMedicalPromiseId).collect(Collectors.toList());
                angelWorkDetailForManDto = findAngelWorkDetailForManDto(promiseId, null);
                medPromiseHistoryList = medPromiseHistoryApplication.queryMedPromiseHistoryList(MedPromiseHistoryRequest.builder().medicalPromiseIds(new HashSet<>(medicalPromiseIdList)).build());
            }else {
                promiseId = result.getMedicalPromiseDto().getPromiseId();
                medPromiseId = result.getMedicalPromiseDto().getMedicalPromiseId();
                angelWorkDetailForManDto = findAngelWorkDetailForManDto(promiseId, medPromiseId);
                medPromiseHistoryList = findMedPromiseHistoryList(medPromiseId);
            }
            List<JdhDispatchHistoryDto> dispatchHistoryDtoList = findJdhDispatchHistoryDtoList(promiseId);
            JdhDispatch dispatch = findDispatch(promiseId);
            if(Objects.nonNull(angelWorkDetailForManDto) && CollUtil.isNotEmpty(angelWorkDetailForManDto.getShipDtoList())){
                AngelShipDto angelShipDto = angelWorkDetailForManDto.getShipDtoList().get(0);
                result.setAngelShipDto(angelShipDto);
                DeliveryTypeEnum deliveryTypeEnum = DeliveryTypeEnum.getEnumByType(angelShipDto.getType());
                String desc = Objects.isNull(deliveryTypeEnum) ? "" : deliveryTypeEnum.getDesc();
                if (Objects.equals(DeliveryTypeEnum.THIRD_DELIVERY.getType(),angelShipDto.getType())){
                    ThirdShipSupplierDTO thirdShipSupplierDTO = JsonUtil.parseArray(duccConfig.getThirdShipSupplier(), ThirdShipSupplierDTO.class).stream().filter(p -> Objects.equals(p.getThirdShipType(), angelShipDto.getAngelShipExtDTO().getThirdShipType())).findFirst().orElse(null);
                    if (Objects.nonNull(thirdShipSupplierDTO)){
                        desc = desc + "-"+ thirdShipSupplierDTO.getThirdShipName();
                    }
                }
                result.getTagInfo().setDeliveryTypeDesc(desc);
            }
            result.setShipTimeLine(buildshipTimeLine(angelWorkDetailForManDto));
            result.setPromiseTimeLine(buildPromiseTimeLine(result,angelWorkDetailForManDto,medPromiseHistoryList,dispatchHistoryDtoList,dispatch, businessModeEnum));
        }catch (Exception e){
            log.error("ManViaApplicationImpl fillTimeLine error",e);
        }
    }

    /**
     * 寻找派单模型
     *
     * @param promiseId promiseId
     * @return {@link JdhDispatch}
     */
    private JdhDispatch findDispatch(Long promiseId){
        try {
            JdhDispatch dispatch = dispatchRepository.findValidDispatch(DispatchRepQuery.builder().promiseId(promiseId).build());
            log.info("ManViaApplicationImpl findDispatch dispatch:{}", JSON.toJSONString(dispatch));
            return dispatch;
        }catch (Exception e){
            log.error("ManViaApplicationImpl findDispatch error",e);
            return null;
        }
    }

    /**
     * findJdhDispatchHistoryDtoList
     *
     * @param promiseId promiseId
     * @return {@link List}<{@link JdhDispatchHistoryDto}>
     */
    private List<JdhDispatchHistoryDto> findJdhDispatchHistoryDtoList(Long promiseId){
        try {
            List<JdhDispatchHistoryDto> dispatchHistoryDtos = dispatchApplication.queryDispatchHistoryListByPromiseId(DispatchHistoryListRequest.builder().promiseId(promiseId).build());
            log.info("ManViaApplicationImpl fillTimeLine dispatchHistoryDtos:{}", JSON.toJSONString(dispatchHistoryDtos));
            return dispatchHistoryDtos;
        }catch (Exception e){
            log.error("ManViaApplicationImpl findJdhDispatchHistoryDtoList error",e);
            return null;
        }
    }

    /**
     * findMedPromiseHistoryList
     *
     * @param medPromiseId medPromiseId
     * @return {@link List}<{@link MedPromiseHistoryDTO}>
     */
    private List<MedPromiseHistoryDTO> findMedPromiseHistoryList(Long medPromiseId){

        try {
            List<MedPromiseHistoryDTO> medPromiseHistoryList = medPromiseHistoryApplication.queryMedPromiseHistoryList(MedPromiseHistoryRequest.builder().medicalPromiseId(medPromiseId).build());
            log.info("ManViaApplicationImpl findMedPromiseHistoryList medPromiseHistoryList:{}", JSON.toJSONString(medPromiseHistoryList));
            return medPromiseHistoryList;
        }catch (Exception e){
            log.error("ManViaApplicationImpl findMedPromiseHistoryList error",e);
            return null;
        }
    }

    /**
     * findAngelWorkDetailForManDto
     *
     * @param promiseId    promiseId
     * @param medPromiseId medPromiseId
     * @return {@link AngelWorkDetailForManDto}
     */
    private AngelWorkDetailForManDto findAngelWorkDetailForManDto(Long promiseId,Long medPromiseId){
        try {
            AngelWorkDetailForManRequest manQuery = new AngelWorkDetailForManRequest();
            manQuery.setPromiseId(promiseId);
            manQuery.setMedPromiseId(medPromiseId);
            AngelWorkDetailForManDto angelWorkDetailForManDto = angelWorkApplication.queryAngelWorkForMan(manQuery);
            log.info("ManViaApplicationImpl findAngelWorkDetailForManDto angelWorkDetailForManDto:{}", JSON.toJSONString(angelWorkDetailForManDto));
            return angelWorkDetailForManDto;
        }catch (Exception e){
            log.error("ManViaApplicationImpl findAngelWorkDetailForManDto error",e);
            return null;
        }
    }

    /**
     * fillAllMedPromise
     *
     * @param result    result
     * @param statusMapping    statusMapping
     */
    private void fillAllMedPromise(ViaCompletePromiseDto result,List<ViaStatusMapping> statusMapping) {
        try {
            List<Long> promiseIdList = result.getPromiseList().stream().map(PromiseDto::getPromiseId).collect(Collectors.toList());
            log.info("ManViaApplicationImpl fillAllMedPromise 开始");
            List<MedicalPromiseDTO> allMedicalPromiseList = medicalPromiseApplication.queryMedicalPromiseList(MedicalPromiseListRequest
                    .builder()
//                    .promiseId(result.getMedicalPromiseDto().getPromiseId())
                    .promiseIdList(promiseIdList)
                    .reportDetail(Boolean.TRUE)
                    .patientDetail(Boolean.TRUE)
                    .itemDetail(Boolean.TRUE)
                    .build());
            log.info("ManViaApplicationImpl fillAllMedPromise allMedicalPromiseList:{}", JSON.toJSONString(allMedicalPromiseList));
            if(CollUtil.isNotEmpty(allMedicalPromiseList)){
                //PromiseDto promiseDto = promiseApplication.findByPromiseId(PromiseIdRequest.builder().promiseId(promiseId).build());
                PromiseDto promiseDto = result.getPromiseDto();
                log.info("ManViaApplicationImpl fillAllMedPromise promiseDto:{}", JSON.toJSONString(promiseDto));
                //List<JdOrderDTO> orderDetailList = tradeApplication.getOrderDetailList(OrderDetailParam.builder().orderId(promiseDto.getSourceVoucherId()).build());
                JdOrderDTO orderDto = result.getJdOrderDto();
                log.info("ManViaApplicationImpl fillAllMedPromise orderDto:{}", JSON.toJSONString(orderDto));
                AngelWorkDetailForManDto angelWorkDto = result.getAngelWorkDto();
                log.info("ManViaApplicationImpl fillAllMedPromise angelWorkDto:{}", JSON.toJSONString(angelWorkDto));
                Set<Long> medPromiseIdList = allMedicalPromiseList.stream().map(MedicalPromiseDTO::getMedicalPromiseId).collect(Collectors.toSet());

                List<MedPromiseHistoryDTO> medPromiseHistoryList = medPromiseHistoryApplication.queryMedPromiseHistoryList(MedPromiseHistoryRequest.builder().medicalPromiseIds(medPromiseIdList).build());
                Map<Long, List<MedPromiseHistoryDTO>> medPromiseHistoryMap = medPromiseHistoryList.stream().collect(Collectors.groupingBy(MedPromiseHistoryDTO::getMedicalPromiseId));

                List<ManViaPromiseServiceDto> mergeList = new ArrayList<>();
                List<Long> reportedMedPromiseList = new ArrayList<>();

                List<Long> currOrderServiceIdList = orderDto.getJdOrderItemList().stream().map(JdOrderItemDTO::getSkuId).collect(Collectors.toList());

                //过滤非当前订单的。C端一定全是，互医的不一定（只看当前子单的）
                Iterator<MedicalPromiseDTO> iterator = allMedicalPromiseList.iterator();
                log.info("ManViaApplicationImpl fillAllMedPromise 开始循环处理每个单据状态");
                while (iterator.hasNext()){
                    MedicalPromiseDTO medicalPromiseDTO = iterator.next();
                    if(!currOrderServiceIdList.contains(medicalPromiseDTO.getServiceId())){
                        iterator.remove();
                    }else{
                        ManViaPromiseServiceDto ele = new ManViaPromiseServiceDto();
                        BeanUtils.copyProperties(medicalPromiseDTO,ele);
                        ele.setOrderId(orderDto.getOrderId());
                        ele.setSkuId(orderDto.getJdOrderItemList().get(0).getSkuId().toString());
                        ele.setSkuName(orderDto.getJdOrderItemList().get(0).getSkuName());
                        PromiseDto currPromise = promiseApplication.findByPromiseId(PromiseIdRequest.builder().promiseId(medicalPromiseDTO.getPromiseId()).build());
                        ViaStatusMapping hitStatus = findViaStatusDesc(orderDto.getOrderStatus(),currPromise.getPromiseStatus()
                                ,medicalPromiseDTO.getStatus(),medicalPromiseDTO.getFreeze(),Objects.isNull(angelWorkDto) ? null : angelWorkDto.getWorkStatus(),statusMapping);
                        ele.setStatusDesc(Objects.isNull(hitStatus) ? null : hitStatus.getStatusDesc());
                        ele.setViaStatus(Objects.isNull(hitStatus) ? null : hitStatus.getViaStatus());
                        if (CollectionUtils.isNotEmpty(hitStatus.getBtnList())){
                            ele.setBtnCodes(hitStatus.getBtnList().stream().map(ViaBtnInfo::getCode).collect(Collectors.toList()));
                        }
                        if(NumConstant.NUM_1.equals(medicalPromiseDTO.getReportStatus())){
                            reportedMedPromiseList.add(medicalPromiseDTO.getMedicalPromiseId());
                        }
                        ele.setOrderCreateTime(orderDto.getCreateTime());
                        ele.setAppointmentDate(Objects.isNull(currPromise.getAppointmentTime()) ? null : currPromise.getAppointmentTime().getAppointmentStartTime());
                        ele.setPromiseId(currPromise.getPromiseId());

                        //获取服务完成时间 & 采样时间
                        List<MedPromiseHistoryDTO> historyDTOList = medPromiseHistoryMap.get(medicalPromiseDTO.getMedicalPromiseId());
                        if(CollUtil.isNotEmpty(historyDTOList)){
                            List<MedPromiseHistoryDTO> sortedHistoryList = historyDTOList.stream().sorted((o1, o2) -> o2.getCreateTime().compareTo(o1.getCreateTime())).collect(Collectors.toList());
                            // 服务完成时间
                            for (MedPromiseHistoryDTO historyDTO : sortedHistoryList) {
                                if(MedicalPromiseStatusEnum.COMPLETED.getStatus().equals(historyDTO.getAfterStatus())){
                                    ele.setCompleteTime(historyDTO.getCreateTime());
                                    break;
                                }
                            }
                            // 采样时间
                            for (MedPromiseHistoryDTO historyDTO : sortedHistoryList) {
                                if(MedicalPromiseStatusEnum.COLLECTED.getStatus().equals(historyDTO.getAfterStatus())){
                                    ele.setCollectionTime(historyDTO.getCreateTime());
                                    break;
                                }
                            }
                        }
                        mergeList.add(ele);
                    }
                }
                result.setAllMedicalPromiseList(mergeList);
                log.info("ManViaApplicationImpl fillAllMedPromise 循环处理每个单据状态结束");

                //处理报告时间
                Map<Long, MedicalReport> reportedMap = new HashMap<>();
                log.info("ManViaApplicationImpl fillAllMedPromise reportedMedPromiseList:{}",JSON.toJSONString(reportedMedPromiseList));
                if(CollUtil.isNotEmpty(reportedMedPromiseList)){
                    List<MedicalReport> byMedicalPromiseIdList = medicalReportRepository.getByMedicalPromiseIdList(reportedMedPromiseList);
                    reportedMap = byMedicalPromiseIdList.stream().collect(Collectors.toMap(MedicalReport::getMedicalPromiseId, Function.identity()));
                }
                log.info("ManViaApplicationImpl fillAllMedPromise reportedMap:{}",JSON.toJSONString(reportedMap));

                for (ManViaPromiseServiceDto manViaPromiseServiceDto : mergeList) {
                    MedicalReport medicalReport = reportedMap.get(manViaPromiseServiceDto.getMedicalPromiseId());
                    if(Objects.nonNull(medicalReport)){
                        manViaPromiseServiceDto.setReportDate(medicalReport.getReportTime());
                    }
                }

                log.info("ManViaApplicationImpl fillAllMedPromise 开始对检测单进行归堆");
                try {
                    List<ManViaPromiseServiceDto> resultAllMedPromiseList = new ArrayList<>();
                    //C端订单 按人归堆、互医订单 按orderId归堆
                    if(PartnerSourceEnum.JDH_XFYL.getCode().equals(orderDto.getPartnerSource())){
                        Map<Long, List<ManViaPromiseServiceDto>> patientGroup = mergeList.stream().collect(Collectors.groupingBy(ManViaPromiseServiceDto::getPromisePatientId));
                        patientGroup.forEach((promisePatientId,data) -> {
                            boolean canSelectOrder = true;
                            if(OrderStatusEnum.ORDER_REFUNDING.getStatus().equals(orderDto.getOrderStatus()) || OrderStatusEnum.ORDER_REFUND.getStatus().equals(orderDto.getOrderStatus())){
                                canSelectOrder = false;
                            }
                            boolean canSelectPatient = true;
                            for (ManViaPromiseServiceDto ele : data) {
                                if(JdhFreezeEnum.FREEZE.getStatus().equals(ele.getFreeze())
                                        || MedicalPromiseStatusEnum.INVALID.getStatus().equals(ele.getStatus())
                                ){
                                    canSelectPatient = false;
                                }
                            }
                            resultAllMedPromiseList.add(ManViaPromiseServiceDto.builder()
                                    .orderId(orderDto.getOrderId())
                                    .promiseId(data.get(0).getPromiseId())
                                    .canSelect(canSelectOrder && canSelectPatient)
                                    .promisePatientId(promisePatientId)
                                    .data(data)
                                    .build());
                        });
                    }else if(PartnerSourceEnum.JDH_NETDIAG.getCode().equals(orderDto.getPartnerSource()) || PartnerSourceEnum.OUT_HOSPITAL_PAID_GUIDANCE_A.getCode().equals(orderDto.getPartnerSource())){
                        Map<Long, List<ManViaPromiseServiceDto>> orderGroup = mergeList.stream().collect(Collectors.groupingBy(ManViaPromiseServiceDto::getOrderId));
                        orderGroup.forEach((orderId,data) -> {
                            boolean canSelect = true;
                            if(OrderStatusEnum.ORDER_REFUNDING.getStatus().equals(orderDto.getOrderStatus()) || OrderStatusEnum.ORDER_REFUND.getStatus().equals(orderDto.getOrderStatus())){
                                canSelect = false;
                            }
                            resultAllMedPromiseList.add(ManViaPromiseServiceDto.builder()
                                    .orderId(orderId)
                                    .promiseId(data.get(0).getPromiseId())
                                    .promisePatientId(data.get(0).getPromisePatientId())
                                    .canSelect(canSelect)
                                    .data(data)
                                    .build());
                        });
                    }
                    log.info("ManViaApplicationImpl fillAllMedPromise 结束");
                    if(CollUtil.isNotEmpty(resultAllMedPromiseList)){
                        result.setAllMedicalPromiseList(resultAllMedPromiseList);
                    }
                }catch (Exception e){
                    log.error("ManViaApplicationImpl fillAllMedPromise 将medPromise进行归堆 error",e);
                }

            }
        }catch (Exception e){
            log.error("ManViaApplicationImpl fillAllMedPromise error",e);
        }
    }

    /**
     * queryPromiseDto
     *
     * @param promiseId promiseId
     * @return {@link PromiseDto}
     */
    private void fillPromiseDto(Long promiseId,ViaCompletePromiseDto result){
        try {
            PromiseDto promiseDto = promiseApplication.findByPromiseId(PromiseIdRequest.builder().promiseId(promiseId).build());
            log.info("ManViaApplicationImpl fillPromiseDto promiseDto:{}", JSON.toJSONString(promiseDto));
            result.setPromiseDto(promiseDto);

            List<PromiseDto> promiseList = promiseApplication.findByPromiseList(PromiseListRequest.builder().sourceVoucherIdList(Collections.singletonList(promiseDto.getSourceVoucherId())).build());
            log.info("ManViaApplicationImpl fillPromiseDto promiseList:{}", JSON.toJSONString(promiseList));
            result.setPromiseList(promiseList);
        }catch (Exception e){
            log.error("ManViaApplicationImpl fillPromiseDto error",e);
        }
    }

    /**
     * 查询订单
     *
     * @param orderId 订单id
     * @return {@link JdOrderDTO}
     */
    private void fillOrder(Long orderId,ViaCompletePromiseDto result){
        try {
            MedicalPromiseDTO medicalPromiseDto = result.getMedicalPromiseDto();
            JdOrderDTO jdOrderDto = tradeApplication.getOrderDetail(OrderDetailParam.builder().orderId(orderId.toString()).build());
            if(VerticalEnum.isNonOrderVertical(medicalPromiseDto.getVerticalCode())){
                JdOrderDTO mockJdOrderDto = new JdOrderDTO();
                mockJdOrderDto.setOrderId(orderId);
                mockJdOrderDto.setPartnerSource(PartnerSourceEnum.JDH_XFYL.getCode());
                mockJdOrderDto.setOrderStatus(MedicalPromiseStatusEnum.INVALID.getStatus().equals(medicalPromiseDto.getStatus()) ? OrderStatusEnum.ORDER_REFUND.getStatus() : OrderStatusEnum.ORDER_PAID.getStatus());
                JdOrderItemDTO jdOrderItemDTO = new JdOrderItemDTO();
                jdOrderItemDTO.setOrderId(orderId);
                jdOrderItemDTO.setSkuId(medicalPromiseDto.getServiceId());
                mockJdOrderDto.setJdOrderItemList(CollectionUtil.toList(jdOrderItemDTO));
                result.setJdOrderDto(mockJdOrderDto);
                return;
            }
            //拆单订单
            if(OrderStatusEnum.ORDER_SPLIT.getStatus().equals(jdOrderDto.getOrderStatus())){
                List<JdOrder> childOrderList = jdOrderRepository.findOrderListByParentId(JdOrder.builder().parentId(jdOrderDto.getOrderId()).build());
                log.info("ManViaApplicationImpl fillOrder childOrderList:{}", JSON.toJSONString(childOrderList));
                for (JdOrder jdOrder : childOrderList) {
                    JdOrderDTO childOrderDto = tradeApplication.getOrderDetail(OrderDetailParam.builder().orderId(jdOrder.getOrderId().toString()).build());
                    //如果是当前子单
                    if (medicalPromiseDto.getServiceId().equals(childOrderDto.getJdOrderItemList().get(0).getSkuId())) {
                        jdOrderDto = childOrderDto;
                        break;
                    }
                }
            }
            log.info("ManViaApplicationImpl fillOrder jdOrderDto:{}", JSON.toJSONString(jdOrderDto));
            result.setJdOrderDto(jdOrderDto);
            result.setAddedItemOrderRefundTips(getAddedItemOrderTips(jdOrderDto));
        }catch (Exception e){
            log.error("ManViaApplicationImpl fillOrder error",e);
        }
    }

    /**
     * 含加项的订单在退款部分的提示语
     * @param jdOrderDto
     * @return
     */
    private String getAddedItemOrderTips(JdOrderDTO jdOrderDto){
        log.info("ManViaApplicationImpl.getAddedItemOrderTips.jdOrderDto={}",JSON.toJSONString(jdOrderDto));
        if(Objects.isNull(jdOrderDto)){
            return null;
        }
        // 如果当前订单是父单，且不包含加项
        if(jdOrderDto.getParentId().intValue()==0&& Objects.isNull(jdOrderDto.getHasAdded()) || jdOrderDto.getHasAdded().intValue() == 0) {
            return null;
        }
        List<JdOrderItemDTO> orderItemList = jdOrderDto.getJdOrderItemList();
        // 当前订单是否为加项订单
        boolean isAddedItemOrder = false;
        for (JdOrderItemDTO jdOrderItemDTO : orderItemList) {
            if(Objects.nonNull(jdOrderItemDTO.getIsAdded()) && jdOrderItemDTO.getIsAdded().intValue() == 1){
                isAddedItemOrder = true;
                break;
            }
        }
        StringBuilder tips = new StringBuilder();
        List<JdOrder> brotherOrder = getBrotherOrder(jdOrderDto);
        log.info("ManViaApplicationImpl.getAddedItemOrderTips.brotherOrder={}",JSON.toJSONString(brotherOrder));
        // 当前订单是加项订单
        if(isAddedItemOrder){
            tips.append("注意：该订单为加项订单，请注意是否退关联主品订单；主品订单号：");
            try {
               JdOrder mainOrder = getMainOrder(jdOrderDto);
                tips.append(mainOrder.getOrderId());
            }catch (Exception e){
                log.error("ManViaApplicationImpl.getAddedItemOrderTips has error",e);
            }
        } else {
            tips.append("注意：该订单为主品订单，请注意是否退关联加项订单；加项订单号：");
            tips.append(brotherOrder.stream().map(s -> String.valueOf(s.getOrderId())).collect(Collectors.joining("、")));
        }
        tips.append("；");
        log.info("ManViaApplicationImpl.getAddedItemOrderTips.tips={}",JSON.toJSONString(tips));
        return tips.toString();
    }

    /**
     * 获取当前加项订单的主品订单
     * @param jdOrderDto
     * @return
     */
    private JdOrder getMainOrder(JdOrderDTO jdOrderDto) {
        if (Objects.isNull(jdOrderDto)) {
            return null;
        }
        if (Objects.isNull(jdOrderDto.getParentId()) || jdOrderDto.getParentId().intValue() == 0) {
            return null;
        }
        List<JdOrder> orders = jdOrderRepository.findOrderListByParentId(JdOrder.builder().parentId(jdOrderDto.getParentId()).build());
        log.info("ManViaApplicationImpl.getMainOrder.orders={}",JSON.toJSONString(orders));
        JdOrder mainOrder = orders.stream().filter(jdOrder -> HasAddedEnum.NOT_HAS_ADDED.getValue().equals(jdOrder.getHasAdded())).findFirst().get();
        log.info("ManViaApplicationImpl.getMainOrder.mainOrder={}",JSON.toJSONString(mainOrder));
        return mainOrder;
    }

    /**
     * 获取同父单下的兄弟订单(不包含自己)
     * @param jdOrderDto
     * @return
     */
    private List<JdOrder> getBrotherOrder(JdOrderDTO jdOrderDto){
        if(Objects.isNull(jdOrderDto)){
            return Collections.emptyList();
        }
        if(Objects.isNull(jdOrderDto.getParentId()) || jdOrderDto.getParentId() == 0){
            return Collections.emptyList();
        }
        List<JdOrder> childOrders = jdOrderRepository.findOrderListByParentId(JdOrder.builder().parentId(jdOrderDto.getParentId()).build());
        List<JdOrder> brotherOrders = childOrders.stream().filter(childOrder -> !Objects.equals(jdOrderDto.getOrderId(), childOrder.getOrderId())).collect(Collectors.toList());
        return brotherOrders;
    }

    /**
     * 查服务者履约和服务者信息
     *
     * @param result    结果
     */
    private void fillAngelAndWork(ViaCompletePromiseDto result, Boolean b2bFlag){
        try {
            Long promiseId = null;
            Long medPromiseId = null;
            AngelWorkDetailForManDto angelWorkDetailForManDto = null;
            if (b2bFlag){
                promiseId = result.getPromiseDto().getPromiseId();
                angelWorkDetailForManDto = findAngelWorkDetailForManDto(promiseId, null);
            }else {
                promiseId = result.getMedicalPromiseDto().getPromiseId();
                medPromiseId = result.getMedicalPromiseDto().getMedicalPromiseId();
                angelWorkDetailForManDto = findAngelWorkDetailForManDto(promiseId, medPromiseId);
            }
            if(Objects.isNull(angelWorkDetailForManDto)){
                result.getTagInfo().setUploadServiceRecord(Boolean.FALSE);
            }else{
                List<String> clothingPicUrls = angelWorkDetailForManDto.getClothingPicUrls();
                List<String> medicalWastePicUrls = angelWorkDetailForManDto.getMedicalWastePicUrls();
                List<String> serviceRecordPicUrls = angelWorkDetailForManDto.getServiceRecordPicUrls();
                List<String> electSignaturePicUrls = angelWorkDetailForManDto.getElectSignaturePicUrls();
                List<String> letterOfConsentPicUrls = angelWorkDetailForManDto.getLetterOfConsentPicUrls();
                List<String> visitRecordPicUrls = angelWorkDetailForManDto.getVisitRecordPicUrls();

                //检查是否有护理单
                AngelServiceRecordQuery cmd = new AngelServiceRecordQuery();
                cmd.setWorkId(Long.valueOf(angelWorkDetailForManDto.getWorkId()));
                List<AngelTaskDetailDto> angelTaskDetailDtoList = new ArrayList<>();
                if(angelServiceRecordApplication.checkAngelServiceRecordConfig(cmd)){
                    for (AngelTaskDetailDto angelTaskDetailDto : angelWorkDetailForManDto.getAngelTaskDetailDtoList()) {
                        //按照患者id做一层过滤，防止多个患者查询重复
                        if(angelTaskDetailDto.getPatientId().equals(result.getMedicalPromiseDto().getPromisePatientId().toString())){
                            angelTaskDetailDtoList.add(angelTaskDetailDto);
                        }
                    }
                }


                if(CollUtil.isNotEmpty(clothingPicUrls)
                        || CollUtil.isNotEmpty(medicalWastePicUrls)
                        || CollUtil.isNotEmpty(serviceRecordPicUrls)
                        || CollUtil.isNotEmpty(electSignaturePicUrls)
                        || CollUtil.isNotEmpty(letterOfConsentPicUrls)
                        || CollUtil.isNotEmpty(angelTaskDetailDtoList)
                        || CollUtil.isNotEmpty(visitRecordPicUrls)
                ){

                    result.getTagInfo().setUploadServiceRecord(Boolean.TRUE);


                    if(CollUtil.isNotEmpty(clothingPicUrls)){
                        result.getTagInfo().setClothingFileList(clothingPicUrls);
                    }

                    if(CollUtil.isNotEmpty(medicalWastePicUrls)){
                        result.getTagInfo().setWasteDestroyFileList(medicalWastePicUrls);
                    }

                    if(CollUtil.isNotEmpty(serviceRecordPicUrls)){
                        result.getTagInfo().setServiceRecordFileList(serviceRecordPicUrls);
                    }
                    if(CollUtil.isNotEmpty(electSignaturePicUrls)){
                        result.getTagInfo().setElectSignatureFileList(electSignaturePicUrls);
                    }
                    if(CollUtil.isNotEmpty(letterOfConsentPicUrls)){
                        result.getTagInfo().setLetterOfConsentFileList(letterOfConsentPicUrls);
                    }
                    if(CollUtil.isNotEmpty(angelTaskDetailDtoList)){
                        AngelTaskDetailDto angelTaskDetailDto = angelTaskDetailDtoList.get(0);
                        if(angelTaskDetailDto.getTaskId()!=null){
                            result.getTagInfo().setTaskId(angelTaskDetailDto.getTaskId().toString());
                        }
                    }
                    if(CollUtil.isNotEmpty(visitRecordPicUrls)){
                        result.getTagInfo().setVisitRecordFiles(visitRecordPicUrls);
                    }

                }else{
                    result.getTagInfo().setUploadServiceRecord(Boolean.FALSE);
                }
            }
            if(Objects.nonNull(angelWorkDetailForManDto) && Objects.nonNull(angelWorkDetailForManDto.getAngelId())){
                JdhAngelDto jdhAngelDto = angelApplication.queryByAngelInfo(AngelRequest.builder().angelId(Long.parseLong(angelWorkDetailForManDto.getAngelId())).build());
                log.info("ManViaApplicationImpl fillAngelAndWork jdhAngelDto:{}", JSON.toJSONString(jdhAngelDto));
                result.setAngelDto(jdhAngelDto);
            }
            result.setAngelWorkDto(angelWorkDetailForManDto);
        }catch (Exception e){
            log.error("ManViaApplicationImpl fillAngelAndWork error",e);
        }
    }

    /**
     * 履约时间线
     *
     * @return {@link List}<{@link ViaTimeLineItemDto}>
     */
    private List<ViaTimeLineItemDto> buildPromiseTimeLine(ViaCompletePromiseDto viaCompletePromiseDto
            , AngelWorkDetailForManDto angelWorkDetailForManDto, List<MedPromiseHistoryDTO> medPromiseHistoryList
            , List<JdhDispatchHistoryDto> dispatchHistoryDtos, JdhDispatch dispatch, BusinessModeEnum businessModeEnum){
        try {
            ManViaCompletePromiseTagDto tagInfo = viaCompletePromiseDto.getTagInfo();
            //是否露出派单按钮
            boolean showDispatch = false;
            //1 - 重派  2- 定向派
            Integer dispatchBtnType = NumConstant.NUM_2;
            List<TimeLineTempElement> tempList = new ArrayList<>();
            //处理派单按钮逻辑，护士上门和护理才有重新派单按钮
            if((Objects.equals(businessModeEnum, BusinessModeEnum.ANGEL_CARE) || Objects.equals(businessModeEnum, BusinessModeEnum.ANGEL_TEST) || Objects.equals(businessModeEnum, BusinessModeEnum.ANGEL_TEST_NO_LABORATORY))
                    && Objects.nonNull(dispatch)){
                showDispatch = true;
                Integer dispatchStatus = dispatch.getDispatchStatus();
                if(JdhDispatchStatusEnum.DISPATCH_INVALID.getStatus().equals(dispatchStatus)){
                    showDispatch = false;
                }else if(JdhDispatchStatusEnum.DISPATCH_RECEIVED.getStatus().equals(dispatchStatus)){
                    dispatchBtnType = NumConstant.NUM_1;
                }
            }

            //检测单历史记录
            if(CollUtil.isNotEmpty(medPromiseHistoryList)){
                medPromiseHistoryList.forEach(ele -> {
                    if(!MedicalPromiseStatusEnum.WAIT_COLLECTED.getStatus().equals(ele.getAfterStatus())
                        && !MedPromiseEventTypeEnum.MED_PROMISE_SETTLEMENT_COMPLETE.getCode().equals(ele.getEventCode())){
                        tempList.add(TimeLineTempElement.builder()
                                .label("(服务单)" + MedicalPromiseStatusEnum.getDescByStatus(ele.getAfterStatus()))
                                .date(ele.getCreateTime())
                                .build());
                    }

                });
            }

            //服务者工单历史记录
            if(Objects.nonNull(angelWorkDetailForManDto)){
                List<AngelWorkHistoryDto> workHistoryDtoList = angelWorkDetailForManDto.getWorkHistoryDtoList();
                if(CollUtil.isNotEmpty(workHistoryDtoList)){
                    List<Integer> workHistoryStatusList = new ArrayList<>();
                    workHistoryDtoList.forEach(ele -> {
                        if(!AngelWorkStatusEnum.INIT.getType().equals(ele.getAfterStatus())){
                            tempList.add(TimeLineTempElement
                                    .builder()
                                    .label("(服务者)" + Objects.requireNonNull(AngelWorkStatusEnum.getEnumByCode(ele.getAfterStatus())).getDesc())
                                    .date(ele.getCreateTime())
                                    .build());
                            workHistoryStatusList.add(ele.getAfterStatus());
                        }
                    });
                    //如果有服务中，不露出派单
                    if(workHistoryStatusList.contains(AngelWorkStatusEnum.SERVICING.getType())){
                        showDispatch = false;
                    }
                }
            }

            // 派单记录
            if(CollUtil.isNotEmpty(dispatchHistoryDtos)){
                dispatchHistoryDtos.forEach(ele -> {

                    //label
                    String label = "(派单)" + ele.getEventDesc();
                    Map<String,String> extendInfo = Maps.newHashMap();
                    //派单成功
                    if (Objects.equals(JdhDispatchStatusEnum.DISPATCH_COMPLETED.getStatus(),ele.getAfterStatus())){
                        JdhDispatch jdhDispatch = JsonUtil.parseObject(ele.getExtend(), JdhDispatch.class);
                        extendInfo.put("dispatchId", String.valueOf(jdhDispatch.getDispatchId()));
                        extendInfo.put("dispatchRound", String.valueOf(jdhDispatch.getDispatchRound()));

                    }

                    String operator = StringUtil.equals(CommonConstant.SYSTEM,ele.getOperator()) ? "系统" : ele.getOperator();
                    //护士已接单or指定派单

                    if (StringUtil.equals(DispatchEventTypeEnum.DISPATCH_REDISPATCH.getCode(),ele.getEventCode())){
                            label = label + " by " + operator;
                    }

                    //已接单
                    if (Objects.equals(JdhDispatchStatusEnum.DISPATCH_RECEIVED.getStatus(),ele.getAfterStatus())){
                        JdhDispatch jdhDispatch = JsonUtil.parseObject(ele.getExtend(), JdhDispatch.class);
                        List<JdhDispatchDetail> angelDetailList = jdhDispatch.getAngelDetailList();
                        JdhDispatchDetail jdhDispatchDetail = org.apache.commons.collections4.CollectionUtils.isEmpty(angelDetailList) ? null : angelDetailList.stream().filter(p -> Objects.equals(JdhDispatchStatusEnum.DISPATCH_RECEIVED.getStatus(), p.getDispatchDetailStatus())).findFirst().orElse(null);

                        //护士已接单
                        if (StringUtil.equals(DispatchEventTypeEnum.DISPATCH_RECEIVED.getCode(),ele.getEventCode())){
                            if (Objects.nonNull(jdhDispatchDetail)){
                                label = label + " - "+ jdhDispatchDetail.getAngelName()+"："+jdhDispatchDetail.getAngelId().toString();
                            }
                        }
                        //指定派单
                        if (StringUtil.equals(DispatchEventTypeEnum.DISPATCH_TARGET_DISPATCH.getCode(),ele.getEventCode())){
                            if (Objects.nonNull(jdhDispatchDetail)){
                                label = label + " - "+ jdhDispatchDetail.getAngelName()+"："+jdhDispatchDetail.getAngelId().toString() + " by "+ operator;
                            }
                        }
                        //常规派单轮派单接单
                        if (StringUtil.equals(DispatchEventTypeEnum.DISPATCH_ASSIGN_SUCCESS_RECEIVED.getCode(),ele.getEventCode())){
                            if (Objects.nonNull(jdhDispatchDetail)){
                                label = label + " - "+ jdhDispatchDetail.getAngelName()+"："+jdhDispatchDetail.getAngelId().toString() + " by "+ operator;
                            }
                        }
                    }

                    tempList.add(TimeLineTempElement
                            .builder()
                            .label(label)
                            .date(ele.getCreateTime())
                            .extendInfo(extendInfo)
                            .eventCode(ele.getEventCode())
                            .build());
                });
            }

            //合并返回
            List<ViaTimeLineItemDto> result = new ArrayList<>();
            if(CollUtil.isNotEmpty(tempList)){
                List<TimeLineTempElement> sortedList = tempList.stream().sorted((o1, o2) -> o2.getDate().compareTo(o1.getDate())).collect(Collectors.toList());
                sortedList.forEach(ele -> {
                    result.add(ViaTimeLineItemDto.builder()
                            .label(ele.getLabel())
                            .time(TimeUtils.dateTimeToStr(ele.getDate(),TimeFormat.LONG_PATTERN_LINE))
                            .eventCode(ele.getEventCode())
                            .extendInfo(ele.getExtendInfo())
                            .build());
                });
            }



            tagInfo.setShowDispatch(showDispatch);
            tagInfo.setDispatchBtnType(showDispatch ? dispatchBtnType : null);
            return result;
        }catch (Exception e){
            log.error("ManViaApplicationImpl buildAngelPromiseTimeLine error",e);
            return null;
        }
    }


    /**
     * 运单时间线
     *
     * @return {@link List}<{@link ViaTimeLineItemDto}>
     */
    private List<ViaTimeLineItemDto> buildshipTimeLine(AngelWorkDetailForManDto angelWorkDetailForManDto){
        if (Objects.isNull(angelWorkDetailForManDto)){
            return null;
        }
        try {
            if(Objects.isNull(angelWorkDetailForManDto)){
                return null;
            }
            List<AngelShipDto> shipDtoList = angelWorkDetailForManDto.getShipDtoList();
            if(CollUtil.isNotEmpty(shipDtoList)){
                List<AngelShipHistoryDto> shipHistoryDtoList = shipDtoList.get(0).getShipHistoryDtoList();
                if(CollUtil.isNotEmpty(shipHistoryDtoList)){
                    List<AngelShipHistoryDto> sortedList = shipHistoryDtoList.stream().sorted((o1, o2) -> o2.getCreateTime().compareTo(o1.getCreateTime())).collect(Collectors.toList());
                    List<ViaTimeLineItemDto> result = new ArrayList<>();
                    sortedList.forEach(ele ->
                        result.add(ViaTimeLineItemDto.builder()
                            .label(Objects.requireNonNull(AngelShipStatusEnum.matchShipStatusEnum(ele.getAfterStatus())).getShipStatusDesc())
                            .time(TimeUtils.dateTimeToStr(ele.getCreateTime(), TimeFormat.LONG_PATTERN_LINE))
                            .build()));
                    return result;
                }
            }
            return null;
        }catch (Exception e){
            log.error("ManViaApplicationImpl buildshipTimeLine error",e);
            return null;
        }
    }

    /**
     * 过滤楼层按钮相关权限
     * @param manViaConfig config
     * @param uimUserContext context
     * @return config
     */
    private ManViaConfig filterManViaConfig(ManViaConfig manViaConfig, UimUserContext uimUserContext) {
        if (manViaConfig == null) {
            return null;
        }
        // 无用户上下文,返回空
        if (uimUserContext == null || (uimUserContext.getLoginContext() == null && uimUserContext.getRoleListContext() == null)) {
            return null;
        }
        if (CollUtil.isNotEmpty(manViaConfig.getFloorConfigList())) {
            List<ManViaFloorConfig> manViaFloorConfigList = new ArrayList<>();
            for (ManViaFloorConfig manViaFloorConfig : manViaConfig.getFloorConfigList()) {
                ManViaFloorConfig manFloorConfig = filterManViaFloorConfig(manViaFloorConfig, uimUserContext);
                if (manFloorConfig != null) {
                    manViaFloorConfigList.add(manFloorConfig);
                }
            }
            manViaConfig.setFloorConfigList(manViaFloorConfigList);
            return manViaConfig;
        } else {
            // 未配置权限，默认全部
            if (CollUtil.isEmpty(manViaConfig.getAccessErp()) && CollUtil.isEmpty(manViaConfig.getAccessRoleCode())) {
                return manViaConfig;
            }
            // 配置了erp权限
            if (CollUtil.isNotEmpty(manViaConfig.getAccessErp())) {
                // 无法获取用户erp,不返回数据
                if (StringUtils.isBlank(uimUserContext.getLoginContext().getPin())) {
                    return null;
                }
                if (manViaConfig.getAccessErp().contains(uimUserContext.getLoginContext().getPin())) {
                    return manViaConfig;
                }
            }
            // 配置了角色权限
            if (CollUtil.isNotEmpty(manViaConfig.getAccessRoleCode())) {
                // 无法获取用户角色列表,不返回数据
                if (CollUtil.isEmpty(uimUserContext.getRoleListContext())) {
                    return null;
                }
                // 判断用户角色是否包含配置的角色
                if (!org.apache.commons.collections4.CollectionUtils.intersection(manViaConfig.getAccessRoleCode(), uimUserContext.getRoleListContext().stream().map(UimRoleContext::getRoleCode).collect(Collectors.toList())).isEmpty()) {
                    return manViaConfig;
                }
            }
            return null;
        }
    }

    /**
     * 过滤楼层按钮相关权限
     * @param manViaFloorConfig config
     * @param uimUserContext context
     * @return config
     */
    private ManViaFloorConfig filterManViaFloorConfig(ManViaFloorConfig manViaFloorConfig, UimUserContext uimUserContext) {
        if (manViaFloorConfig == null) {
            return null;
        }
        // 无用户上下文,返回空
        if (uimUserContext == null || (uimUserContext.getLoginContext() == null && uimUserContext.getRoleListContext() == null)) {
            return null;
        }
        if (CollUtil.isNotEmpty(manViaFloorConfig.getFloorElementConfigList())) {
            List<ManViaFloorElementConfig> floorElementConfigList = new ArrayList<>();
            for (ManViaFloorElementConfig manViaFloorElementConfig : manViaFloorConfig.getFloorElementConfigList()) {
                // 未配置权限，默认全部
                if (CollUtil.isEmpty(manViaFloorElementConfig.getAccessErp()) && CollUtil.isEmpty(manViaFloorElementConfig.getAccessRoleCode())) {
                    floorElementConfigList.add(manViaFloorElementConfig);
                    continue;
                }
                // 配置了erp权限
                if (CollUtil.isNotEmpty(manViaFloorElementConfig.getAccessErp())) {
                    // 无法获取用户erp,不返回数据
                    if (StringUtils.isBlank(uimUserContext.getLoginContext().getPin())) {
                        continue;
                    }
                    if (manViaFloorElementConfig.getAccessErp().contains(uimUserContext.getLoginContext().getPin())) {
                        floorElementConfigList.add(manViaFloorElementConfig);
                        continue;
                    }
                }
                // 配置了角色权限
                if (CollUtil.isNotEmpty(manViaFloorElementConfig.getAccessRoleCode())) {
                    // 无法获取用户角色列表,不返回数据
                    if (CollUtil.isEmpty(uimUserContext.getRoleListContext())) {
                        continue;
                    }
                    // 判断用户角色是否包含配置的角色
                    if (!org.apache.commons.collections4.CollectionUtils.intersection(manViaFloorElementConfig.getAccessRoleCode(), uimUserContext.getRoleListContext().stream().map(UimRoleContext::getRoleCode).collect(Collectors.toList())).isEmpty()) {
                        floorElementConfigList.add(manViaFloorElementConfig);
                        continue;
                    }
                }
            }
            manViaFloorConfig.setFloorElementConfigList(floorElementConfigList);
            return manViaFloorConfig;
        } else {
            // 未配置权限，默认全部
            if (CollUtil.isEmpty(manViaFloorConfig.getAccessErp()) && CollUtil.isEmpty(manViaFloorConfig.getAccessRoleCode())) {
                return manViaFloorConfig;
            }
            // 配置了erp权限
            if (CollUtil.isNotEmpty(manViaFloorConfig.getAccessErp())) {
                // 无法获取用户erp,不返回数据
                if (StringUtils.isBlank(uimUserContext.getLoginContext().getPin())) {
                    return null;
                }
                if (manViaFloorConfig.getAccessErp().contains(uimUserContext.getLoginContext().getPin())) {
                    return manViaFloorConfig;
                }
            }
            // 配置了角色权限
            if (CollUtil.isNotEmpty(manViaFloorConfig.getAccessRoleCode())) {
                // 无法获取用户角色列表,不返回数据
                if (CollUtil.isEmpty(uimUserContext.getRoleListContext())) {
                    return null;
                }
                // 判断用户角色是否包含配置的角色
                if (!org.apache.commons.collections4.CollectionUtils.intersection(manViaFloorConfig.getAccessRoleCode(), uimUserContext.getRoleListContext().stream().map(UimRoleContext::getRoleCode).collect(Collectors.toList())).isEmpty()) {
                    return manViaFloorConfig;
                }
            }
            return null;
        }
    }

    /**
     * 由于ES docid为检验单id，会存在订单已落库但是未同步到ES情况，导致查询订单查不到
     * @param orderId
     * @return
     */
    private ViaCompletePromiseDto queryOrderNoMedicalPromiseId(Long orderId) {
        if (orderId == null) {
            return null;
        }
        JdOrderDTO jdOrderDto = tradeApplication.getOrderDetail(OrderDetailParam.builder().orderId(orderId.toString()).build());
        if (jdOrderDto != null) {
            ViaCompletePromiseDto viaCompletePromiseDto = new ViaCompletePromiseDto();
            viaCompletePromiseDto.setJdOrderDto(jdOrderDto);
            ManViaCompletePromiseTagDto manViaCompletePromiseTagDto = new ManViaCompletePromiseTagDto();
            String orderTypeDesc = orderTypeDicUtil.getOrderTypeDesc(jdOrderDto.getOrderType());
            manViaCompletePromiseTagDto.setOrderTypeDesc("订单类型:" + jdOrderDto.getOrderType() + ",订单类型描述:" + (orderTypeDesc == null ? "未知" : orderTypeDesc));
            manViaCompletePromiseTagDto.setStatusDesc("订单落库(未生成服务单)");
            viaCompletePromiseDto.setTagInfo(manViaCompletePromiseTagDto);
            return viaCompletePromiseDto;
        } else {
            // 1.查到订单大数据
            Map<String,Object> dataMap = orderInfoRpc.getOrderData(orderId);
            if (CollUtil.isEmpty(dataMap)) {
                log.info("TradeApplicationImpl handleGiftOrder 通过父订单查到订单大数据为空,不执行");
                return null;
            }
            String cartxml = Objects.toString(dataMap.get(FieldKeyEnum.V_CARTXML.getFieldName()));
            String orderxml = Objects.toString(dataMap.get(FieldKeyEnum.V_ORDERXML.getFieldName()));
            // 2.解压缩
            cartxml = com.jd.orderver.component.utils.ZipUtils.gunzip(cartxml);
            orderxml = com.jd.orderver.component.utils.ZipUtils.gunzip(orderxml);
            // 3.按照返回 serializationType 进行反序列化成对象
            String serializationType = Objects.toString(dataMap.get(FieldKeyEnum.V_CBDFLAG.getFieldName()));
            Cart serializeCart = SerializersHelper.ofString(cartxml, Cart.class, serializationType);
            Order serializeOrder = SerializersHelper.ofString(orderxml, Order.class, serializationType);
            if (serializeOrder == null) {
                return null;
            }
            ViaCompletePromiseDto viaCompletePromiseDto = new ViaCompletePromiseDto();
            JdOrderDTO orderDTO = new JdOrderDTO();
            orderDTO.setOrderId(serializeOrder.getOrderId());
            orderDTO.setParentId(serializeOrder.getParentId());
            orderDTO.setOrderStatus(serializeOrder.getStatus());
            orderDTO.setOrderType(serializeOrder.getOrderType());
            ManViaCompletePromiseTagDto manViaCompletePromiseTagDto = new ManViaCompletePromiseTagDto();
            String orderTypeDesc = orderTypeDicUtil.getOrderTypeDesc(serializeOrder.getOrderType());
            manViaCompletePromiseTagDto.setOrderTypeDesc("订单类型:" + serializeOrder.getOrderType() + ",订单类型描述:" + (orderTypeDesc == null ? "未知" : orderTypeDesc));
            if (serializeOrder.getOrderType() == 0 && StringUtils.isNotBlank(serializeOrder.getSendPayMap())) {
                Map<String, String> sendPayDict = JSON.parseObject(serializeOrder.getSendPayMap(), new TypeReference< Map<String, String> >(){});
                if (CollUtil.isNotEmpty(sendPayDict) && sendPayDict.containsKey(SendpayValueEnum.SEND_PAY_1254_1.getSendPayStr()) && sendPayDict.get(SendpayValueEnum.SEND_PAY_1254_1.getSendPayStr()).equals(SendpayValueEnum.SEND_PAY_1254_1.getSendPayValue())) {
                    manViaCompletePromiseTagDto.setServiceTypeDesc(ServiceTypeNewEnum.TRANSPORT_TEST.getDesc());
                }
            } else {
                manViaCompletePromiseTagDto.setServiceTypeDesc("未知");
            }
            manViaCompletePromiseTagDto.setStatusDesc("订单未落库(非到家服务订单或订单异常)");
            orderDTO.setUserPin(serializeOrder.getPin());
            MedicalPromiseDTO medicalPromiseDTO = new MedicalPromiseDTO();
            medicalPromiseDTO.setUserPin(serializeOrder.getPin());
            viaCompletePromiseDto.setMedicalPromiseDto(medicalPromiseDTO);
            viaCompletePromiseDto.setTagInfo(manViaCompletePromiseTagDto);
            List<SKU> skuList = serializeCart.findAllSkus();
            if (CollUtil.isNotEmpty(skuList)) {
                List<JdOrderItemDTO> jdOrderItemDTOList = new ArrayList<>();;
                for(SKU sku : skuList) {
                    JdOrderItemDTO jdOrderItemDTO = new JdOrderItemDTO();
                    jdOrderItemDTO.setSkuId(sku.getId());
                    jdOrderItemDTO.setSkuName(sku.getName());
                    jdOrderItemDTOList.add(jdOrderItemDTO);
                }
                orderDTO.setJdOrderItemList(jdOrderItemDTOList);
            }
            viaCompletePromiseDto.setJdOrderDto(orderDTO);
            return viaCompletePromiseDto;
        }
    }

    /**
     * 时间线临时元素
     *
     * <AUTHOR>
     * @date 2024/05/24
     */
    @Builder
    @Data
    static class TimeLineTempElement{
        /**
         * 标签
         */
        private String label;
        /**
         * 时间
         */
        private Date date;
        /**
         * 扩展信息
         */
        private Map<String,String> extendInfo;
        /**
         * 事件编码
         */
        private String eventCode;
    }
}
