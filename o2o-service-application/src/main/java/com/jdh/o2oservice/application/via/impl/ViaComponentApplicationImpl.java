package com.jdh.o2oservice.application.via.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.beust.jcommander.internal.Lists;
import com.google.common.collect.Sets;
import com.jdh.o2oservice.application.provider.service.ProviderStoreApplication;
import com.jdh.o2oservice.application.via.ViaComponentApplication;
import com.jdh.o2oservice.application.via.util.UavUtil;
import com.jdh.o2oservice.base.enums.JdhStoreTransferStationTypeEnum;
import com.jdh.o2oservice.base.enums.YnStatusEnum;
import com.jdh.o2oservice.base.util.EntityUtil;
import com.jdh.o2oservice.base.util.GeoDistanceUtil;
import com.jdh.o2oservice.base.util.TimeUtils;
import com.jdh.o2oservice.common.enums.DeliveryTypeEnum;
import com.jdh.o2oservice.common.enums.MedicalPromiseStatusEnum;
import com.jdh.o2oservice.common.enums.PromiseGoAggregateStatusEnum;
import com.jdh.o2oservice.common.enums.UavErrorResultEnum;
import com.jdh.o2oservice.core.domain.angel.repository.db.JdhStationRepository;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelShipStatusEnum;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelWorkTypeEnum;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelShipHistory;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelWork;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelShipHistoryRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelShipRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelWorkRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.query.AngelShipHistoryDBQuery;
import com.jdh.o2oservice.core.domain.angelpromise.repository.query.AngelWorkDBQuery;
import com.jdh.o2oservice.core.domain.angelpromise.service.AngelShipDomainService;
import com.jdh.o2oservice.core.domain.medpromise.model.MedPromiseDeliveryStep;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromise;
import com.jdh.o2oservice.core.domain.promise.model.PromiseAppointmentTime;
import com.jdh.o2oservice.core.domain.promise.model.PromiseStation;
import com.jdh.o2oservice.core.domain.promise.repository.db.PromiseRepository;
import com.jdh.o2oservice.core.domain.support.basic.model.Birthday;
import com.jdh.o2oservice.core.domain.support.basic.rpc.AddressRpc;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.GisPointBo;
import com.jdh.o2oservice.core.domain.support.promisego.rpc.PromiseGoRpcService;
import com.jdh.o2oservice.core.domain.support.promisego.rpc.bo.PromisegoRequestAddress;
import com.jdh.o2oservice.core.domain.support.promisego.rpc.bo.PromisegoRequestAppointmentTime;
import com.jdh.o2oservice.core.domain.support.promisego.rpc.bo.UserPromisegoBo;
import com.jdh.o2oservice.core.domain.support.promisego.rpc.bo.UserPromisegoRequestBo;
import com.jdh.o2oservice.core.domain.support.vertical.model.JdhVerticalBusiness;
import com.jdh.o2oservice.core.domain.support.vertical.repository.VerticalBusinessRepository;
import com.jdh.o2oservice.core.domain.support.via.configcenter.ViaConfigRepository;
import com.jdh.o2oservice.core.domain.support.via.enums.PromiseAggregateStatusEnum;
import com.jdh.o2oservice.core.domain.support.via.model.ViaConfig;
import com.jdh.o2oservice.core.domain.support.via.model.ViaConfigIdentifier;
import com.jdh.o2oservice.core.domain.support.via.model.ViaStatusMapping;
import com.jdh.o2oservice.application.via.common.ViaComponentDomainService;
import com.jdh.o2oservice.export.laboratory.dto.JdhStoreTransferStationDto;
import com.jdh.o2oservice.export.laboratory.query.QueryMerchantStoreDetailByParamRequest;
import com.jdh.o2oservice.export.medicalpromise.dto.MedicalPromiseDTO;
import com.jdh.o2oservice.export.promise.dto.BirthdayDto;
import com.jdh.o2oservice.export.promise.dto.PromiseDistanceDto;
import com.jdh.o2oservice.export.promise.dto.PromisePatientDto;
import com.jdh.o2oservice.export.promise.dto.UserNameDto;
import com.jdh.o2oservice.export.promise.query.PromiseIdRequest;
import com.jdh.o2oservice.export.via.query.ViaPromiseDistanceRequest;
import com.jdh.o2oservice.ext.ship.enums.StanderAngelShipStatusEnum;
import com.jdh.o2oservice.ext.ship.reponse.DeliveryOrderDetailResponse;
import com.jdh.o2oservice.infrastructure.repository.db.convert.MedicalPromiseConvert;
import com.jdh.o2oservice.infrastructure.repository.db.convert.PromiseConverter;
import com.jdh.o2oservice.infrastructure.repository.db.dao.JdOrderPoMapper;
import com.jdh.o2oservice.infrastructure.repository.db.dao.JdhMedicalPromisePoMapper;
import com.jdh.o2oservice.infrastructure.repository.db.dao.JdhPromisePatientPoMapper;
import com.jdh.o2oservice.infrastructure.repository.db.dao.JdhPromisePoMapper;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdOrderPo;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhMedicalPromisePo;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhPromisePatientPo;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhPromisePo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/4 15:50
 */
@Component
@Slf4j
public class ViaComponentApplicationImpl implements ViaComponentApplication {
    /**
     * viaConfigRepository
     */
    @Resource
    private ViaConfigRepository viaConfigRepository;
    /**
     * jdhPromiseRepository
     */
    @Resource
    private PromiseRepository promiseRepository;
    @Resource
    private JdhPromisePoMapper jdhPromisePoMapper;
    @Resource
    private JdOrderPoMapper jdOrderPoMapper;
    @Resource
    private ViaComponentDomainService viaComponentDomainService;
    @Resource
    private AngelWorkRepository angelWorkRepository;
    @Resource
    private VerticalBusinessRepository verticalBusinessRepository;
    @Resource
    private ProviderStoreApplication providerStoreApplication;
    @Resource
    private AngelShipDomainService angelShipDomainService;
    @Resource
    private AddressRpc addressRpc;
    @Resource
    private JdhMedicalPromisePoMapper jdhMedicalPromisePoMapper;
    @Resource
    private JdhPromisePatientPoMapper jdhPromisePatientPoMapper;
    /**
     * promiseGoRpcService
     */
    @Resource
    private PromiseGoRpcService promiseGoRpcService;

    /**
     * 获取服务者距离信息，三个环节会展示地图，派单中、上门阶段、送检阶段；需要判断阶段是否发生了变化，返回前端refresh字段，前端根据refresh判断
     * 是否刷新页面。
     *
     *
     * @param request
     * @return
     */
    @Override
    public PromiseDistanceDto queryPromiseDistance(ViaPromiseDistanceRequest request) {
        // 查询配置
        ViaConfig viaConfig = viaConfigRepository.find(ViaConfigIdentifier.builder().scene(request.getScene()).build());
        // 查询订单
        LambdaQueryWrapper<JdOrderPo> orderWrapper = Wrappers.lambdaQuery();
        orderWrapper.eq(JdOrderPo::getOrderId, Long.valueOf(request.getOrderId()));
        orderWrapper.eq(JdOrderPo::getYn, YnStatusEnum.YES.getCode());
        JdOrderPo orderPo = jdOrderPoMapper.selectOne(orderWrapper);
        // 查询promise
        LambdaQueryWrapper<JdhPromisePo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(JdhPromisePo::getPromiseId, Long.valueOf(request.getPromiseId()));
        queryWrapper.eq(JdhPromisePo::getYn, YnStatusEnum.YES.getCode());
        JdhPromisePo promisePo = jdhPromisePoMapper.selectOne(queryWrapper);
        // 查询检测单
        LambdaQueryWrapper<JdhMedicalPromisePo> medicalPromisePoLambdaQueryWrapper = Wrappers.lambdaQuery();
        medicalPromisePoLambdaQueryWrapper
                .eq(JdhMedicalPromisePo::getPromiseId, Long.valueOf(request.getPromiseId()))
                .eq(JdhMedicalPromisePo::getYn, YnStatusEnum.YES.getCode());
        List<JdhMedicalPromisePo> medicalPromisePos = jdhMedicalPromisePoMapper.selectList(medicalPromisePoLambdaQueryWrapper);
        // ==>>>> 过滤statusMapping，获取当前状态对应的mapping和最新阶段
        ViaStatusMapping statusMapping = viaComponentDomainService.parseHomeTestMapping(
                EntityUtil.getFiledDefaultNull(orderPo, JdOrderPo::getOrderStatus),
                EntityUtil.getFiledDefaultNull(promisePo, JdhPromisePo::getPromiseStatus),
                EntityUtil.getFiledDefaultNull(medicalPromisePos , JdhMedicalPromisePo::getStatus),
                EntityUtil.getFiledDefaultNull(promisePo, e -> YnStatusEnum.convert(e.getImmediately())),
                null,
                viaConfig
        );
        // 获取业务身份
        JdhVerticalBusiness business = verticalBusinessRepository.find(promisePo.getVerticalCode());
        PromiseDistanceDto dto = new PromiseDistanceDto();
        AngelWorkDBQuery angelWorkDBQuery = new AngelWorkDBQuery();
        angelWorkDBQuery.setPromiseId(Long.valueOf(request.getPromiseId()));


        // 获取服务者信息，如果服务者存在，需要获取服务者经纬度。
        AngelWork angelWork = angelWorkRepository.findAngelWork(angelWorkDBQuery);
        DeliveryOrderDetailResponse shipDetail = null;
        if (Objects.nonNull(angelWork)) {
            log.info("ViaComponentApplicationImpl->queryPromiseDistance angelWork={}", JSON.toJSONString(angelWork));
            shipDetail = angelShipDomainService.getShipOrderDetailByWork(angelWork, business);
            log.info("ViaComponentApplicationImpl->queryPromiseDistance shipDetail={}", JSON.toJSONString(shipDetail));
            if (Objects.nonNull(shipDetail)) {
                dto.setAngelLat(shipDetail.getTransporterLat());
                dto.setAngelLng(shipDetail.getTransporterLng());
                dto.setShipType(shipDetail.getShipType());
            }
        }

        // 默认刷新页面，如果阶段一样则不刷新。
        if (StringUtils.equals(statusMapping.getAggregateStatus(), request.getAggregateStatus())){
            dto.setRefresh(Boolean.FALSE);
        }
        AngelWorkTypeEnum angelWorkTypeEnum = AngelWorkTypeEnum.matchType(business.getBusinessModeCode());
        String angelType = angelWorkTypeEnum != null ? angelWorkTypeEnum.getAngelType() : "";
        String freshStatus = statusMapping.getAggregateStatus();
        if (Objects.equals(freshStatus, PromiseAggregateStatusEnum.TO_HOME.getCode())){
            // 根据当前状态判断骑手是赶往用户家，还是赶往实验室
            // 解析服务者地址经纬度
            dto.setAngelDesc(angelType + "正在赶来");
            try {
                log.info("ViaComponentApplicationImpl->queryPromiseDistance 开始解析用户地址经纬度");
                GisPointBo gisPointBo = addressRpc.getLngLatByAddress(promisePo.getStoreAddr());
                log.info("ViaComponentApplicationImpl->queryPromiseDistance gisPointBo={}", JSON.toJSONString(gisPointBo));
                dto.setPromiseLat(gisPointBo.getLatitude().toString());
                dto.setPromiseLng(gisPointBo.getLongitude().toString());

                if (StringUtils.isNotBlank(dto.getAngelLat()) && StringUtils.isNotBlank(dto.getAngelLng())) {
                    double angelLat = Double.parseDouble(dto.getAngelLat());
                    double angelLng = Double.parseDouble(dto.getAngelLng());
                    dto.setDistance(GeoDistanceUtil.calculateDistanceDynamicUnit(angelLat, angelLng, gisPointBo.getLatitude().doubleValue(), gisPointBo.getLongitude().doubleValue()));
                }
            }catch (Exception e){
                log.error("ViaComponentApplicationImpl->findPromiseDistance parse gisPointBo error", e);
            }
        }else if(Objects.equals(freshStatus, PromiseAggregateStatusEnum.TO_LAB.getCode())){
            // 无人机模式
            if (UavUtil.isUavFLow(com.jdh.o2oservice.application.medicalpromise.convert.MedicalPromiseConvert.INSTANCE.convert(MedicalPromiseConvert.INSTANCE.convert(medicalPromisePos)))) {
                buildUavDistance(dto, shipDetail, promisePo, medicalPromisePos, business);
            } else {
                // 当前阶段为送检阶段，获取实验室经纬度
                dto.setAngelDesc(angelType + "正在赶往实验室");
                try {
                    medicalPromisePos.stream().filter(e -> StringUtils.isNotBlank(e.getStationAddress())).findFirst()
                            .ifPresent((e) -> {
                                log.info("ViaComponentApplicationImpl->queryPromiseDistance 开始解析实验室经纬度");
                                GisPointBo stationGis = addressRpc.getLngLatByAddress(e.getStationAddress());
                                dto.setStationLng(stationGis.getLongitude().toString());
                                dto.setStationLat(stationGis.getLatitude().toString());
                            });
                    if (StringUtils.isNotBlank(dto.getAngelLat()) && StringUtils.isNotBlank(dto.getAngelLng())) {
                        double angelLat = Double.parseDouble(dto.getAngelLat());
                        double angelLng = Double.parseDouble(dto.getAngelLng());
                        double stationLat = Double.parseDouble(dto.getStationLat());
                        double stationLng = Double.parseDouble(dto.getStationLng());
                        dto.setDistance(GeoDistanceUtil.calculateDistanceDynamicUnit(angelLat, angelLng, stationLat, stationLng));
                    }
                }catch (Exception e){
                    log.error("ViaComponentApplicationImpl->findPromiseDistance parse stationGis error", e);
                }
            }

        }else if(Objects.equals(freshStatus, PromiseAggregateStatusEnum.DISPATCH.getCode())){
            log.info("ViaComponentApplicationImpl->queryPromiseDistance 开始解析用户地址经纬度");
            GisPointBo gisPointBo = addressRpc.getLngLatByAddress(promisePo.getStoreAddr());
            log.info("ViaComponentApplicationImpl->queryPromiseDistance gisPointBo={}", JSON.toJSONString(gisPointBo));
            // 前端依赖骑手经纬度展示地图点位信息，临时先把服务地址经纬度返回。后续文案展示在哪个点位由服务端返回
            dto.setPromiseLat(gisPointBo.getLatitude().toString());
            dto.setPromiseLng(gisPointBo.getLongitude().toString());
            dto.setPromiseDesc("正在匹配" + angelType);
        }
        // 非配送阶段不再展示经纬度信息
        if (!AngelShipStatusEnum.finishBeforeStatus().contains(shipDetail.getStatusCode())) {
            dto.setAngelLat(null);
            dto.setAngelLng(null);
        }
        return dto;
    }

    /**
     * 获取履约服务人信息
     * @param idRequest
     * @return
     */
    @Override
    public List<PromisePatientDto> queryPromisePatient(PromiseIdRequest idRequest) {

        // 查询检测单
        LambdaQueryWrapper<JdhMedicalPromisePo> medicalPromisePoLambdaQueryWrapper = Wrappers.lambdaQuery();
        medicalPromisePoLambdaQueryWrapper
                .eq(JdhMedicalPromisePo::getPromiseId, idRequest.getPromiseId())
                .notIn(JdhMedicalPromisePo::getStatus, MedicalPromiseStatusEnum.UN_BIND_STATUS)
                .eq(JdhMedicalPromisePo::getYn, YnStatusEnum.YES.getCode());
        List<JdhMedicalPromisePo> medicalPromisePos = jdhMedicalPromisePoMapper.selectList(medicalPromisePoLambdaQueryWrapper);
        if (CollectionUtils.isEmpty(medicalPromisePos)){
            return Collections.emptyList();
        }
        Map<Long, List<JdhMedicalPromisePo>> map = medicalPromisePos.stream().collect(Collectors.groupingBy(JdhMedicalPromisePo::getPromisePatientId));

        LambdaQueryWrapper<JdhPromisePatientPo> patientPoLambdaQueryWrapper = Wrappers.lambdaQuery();
        patientPoLambdaQueryWrapper
                .in(JdhPromisePatientPo::getPromisePatientId, map.keySet())
                .eq(JdhPromisePatientPo::getYn, YnStatusEnum.YES.getCode());
        List<JdhPromisePatientPo> promisePatientPos = jdhPromisePatientPoMapper.selectList(patientPoLambdaQueryWrapper);

        List<PromisePatientDto> patientDtos = Lists.newArrayList();
        for (JdhPromisePatientPo promisePatientPo : promisePatientPos) {
            PromisePatientDto dto = new PromisePatientDto();
            dto.setUserName(UserNameDto.builder().name(promisePatientPo.getUserName()).build());
            dto.setGender(promisePatientPo.getUserGender());
            Birthday birthday = new Birthday(promisePatientPo.getBirthday());
            dto.setBirthday(BirthdayDto.builder().birth(birthday.getBirth()).age(birthday.getAge()).build());

            List<MedicalPromiseDTO> medicalPromiseDetails = Lists.newArrayList();
            List<JdhMedicalPromisePo> specimens = map.get(promisePatientPo.getPromisePatientId());
            for (JdhMedicalPromisePo specimen : specimens) {
                MedicalPromiseDTO medicalPromiseDTO = MedicalPromiseDTO.builder()
                        .medicalPromiseId(specimen.getMedicalPromiseId())
                        .serviceItemName(specimen.getServiceItemName())
                        .build();
                medicalPromiseDetails.add(medicalPromiseDTO);
            }
            dto.setMedicalPromiseDetails(medicalPromiseDetails);
            String headerImage = viaComponentDomainService.queryPatientHeadImage(promisePatientPo.getUserGender(), birthday.getAge());
            dto.setPatientHeaderImage(headerImage);
            patientDtos.add(dto);
        }
        return patientDtos;
    }

    /**
     * 构建无人机地图信息
     */
    private void buildUavDistance(PromiseDistanceDto dto, DeliveryOrderDetailResponse shipDetail, JdhPromisePo promisePo, List<JdhMedicalPromisePo> medicalPromisePos, JdhVerticalBusiness business) {
        QueryMerchantStoreDetailByParamRequest queryMerchantStoreDetailByParamRequest = new QueryMerchantStoreDetailByParamRequest();
        queryMerchantStoreDetailByParamRequest.setJdStoreId(shipDetail.getJdStoreId());
        queryMerchantStoreDetailByParamRequest.setQueryTransferStation(true);
        List<JdhStoreTransferStationDto> jdhStoreTransferStationDtos = providerStoreApplication.queryStoreTransferStation(queryMerchantStoreDetailByParamRequest);
        JdhStoreTransferStationDto uav = null;
        JdhStoreTransferStationDto store = null;
        List<String> stationIds = new ArrayList<>();
        if (CollUtil.isNotEmpty(medicalPromisePos)) {
            List<MedPromiseDeliveryStep> allMedPromiseDeliverySteps = new ArrayList<>();
            for (JdhMedicalPromisePo medicalPromisePo : medicalPromisePos) {
                if (StringUtils.isBlank(medicalPromisePo.getDeliveryStepFlow())) {
                    continue;
                }
                List<MedPromiseDeliveryStep> medPromiseDeliverySteps = JSON.parseArray(medicalPromisePo.getDeliveryStepFlow(),MedPromiseDeliveryStep.class);
                allMedPromiseDeliverySteps.addAll(medPromiseDeliverySteps);
            }
            stationIds = allMedPromiseDeliverySteps.stream().map(MedPromiseDeliveryStep::getStartAddressId).collect(Collectors.toList());
        }
        for (JdhStoreTransferStationDto jdhStoreTransferStationDto : jdhStoreTransferStationDtos) {
            if (JdhStoreTransferStationTypeEnum.SELF_RECEIVE_SAMPLE.getType().equals(jdhStoreTransferStationDto.getStationType())) {
                store = jdhStoreTransferStationDto;
            } else if (JdhStoreTransferStationTypeEnum.UAV_RECEIVE_SAMPLE.getType().equals(jdhStoreTransferStationDto.getStationType())) {
                if (CollUtil.isEmpty(stationIds)) {
                    uav = jdhStoreTransferStationDto;
                    continue;
                }
                if (CollUtil.isNotEmpty(stationIds) && stationIds.contains(jdhStoreTransferStationDto.getJdStationId().toString())) {
                    uav = jdhStoreTransferStationDto;
                }
            }
        }

        // 当前节点为无人机
        if (DeliveryTypeEnum.UAV.getType().equals(shipDetail.getShipType())) {
            int totalPoint = 1000;
            List<double[]> lt = new ArrayList<>();
            if (store != null && uav != null) {
                // 将无人机起飞点和实验室直接直线距离切割成1000个点，根据时间进度匹配到第N个点
                lt = GeoDistanceUtil.getGeoEquidistantPoints(uav.getJdStationLatitude(), uav.getJdStationLongitude(), store.getJdStationLatitude(), store.getJdStationLongitude(), totalPoint);
            }

            if (AngelShipStatusEnum.WAITING_RECEIVE_GOODS.getShipStatus().equals(shipDetail.getStatusCode())) {
                dto.setAngelDesc("正在分配无人机");
                if (uav != null && uav.getJdStationLatitude() != null && uav.getJdStationLongitude() != null && store != null && store.getJdStationLatitude() != null && store.getJdStationLongitude() != null) {
                    double uavLat = uav.getJdStationLatitude();
                    double uavLng = uav.getJdStationLongitude();
                    double stationLat = store.getJdStationLatitude();
                    double stationLng = store.getJdStationLongitude();
                    dto.setAngelLat(uav.getJdStationLatitude().toString());
                    dto.setAngelLng(uav.getJdStationLongitude().toString());
                    dto.setStationLat(store.getJdStationLatitude().toString());
                    dto.setStationLng(store.getJdStationLongitude().toString());
                    dto.setDistance(GeoDistanceUtil.calculateDistanceDynamicUnit(uavLat, uavLng, stationLat, stationLng));
                }
            } else if (AngelShipStatusEnum.DELIVERING_GOODS.getShipStatus().equals(shipDetail.getStatusCode())) {
                dto.setAngelDesc("无人机正在赶往实验室");
            }
            // 异常情况导致无人机无法起飞，需要模拟线路
            if (shipDetail.getDeliveryOrderDetailFailResult() != null && UavErrorResultEnum.faultScene().contains(shipDetail.getDeliveryOrderDetailFailResult().getCode())) {
                // 获取预估送达时间
                UserPromisegoRequestBo userPromisegoRequestBo = UserPromisegoRequestBo.builder()
                        .aggregateStatus(PromiseGoAggregateStatusEnum.DELIVERING.getType())
                        .promiseId(promisePo.getPromiseId())
                        .businessMode(business.getBusinessModeCode())
                        .queryTermScript(Boolean.TRUE)
                        .deliveryType(shipDetail.getShipType())
                        .build();

                JdhPromise jdhPromise = PromiseConverter.INSTANCE.convert2ProviderPromise(promisePo);
                //时间
                PromiseAppointmentTime promiseAppointmentTime = jdhPromise.getAppointmentTime();
                if(Objects.nonNull(promiseAppointmentTime)){
                    PromisegoRequestAppointmentTime appointmentTime = new PromisegoRequestAppointmentTime();
                    appointmentTime.setDateType(promiseAppointmentTime.getDateType());
                    appointmentTime.setImmediately(promiseAppointmentTime.getIsImmediately());
                    appointmentTime.setAppointmentStartTime(promiseAppointmentTime.getAppointmentStartTime());
                    appointmentTime.setAppointmentEndTime(promiseAppointmentTime.getAppointmentEndTime());
                    userPromisegoRequestBo.setAppointmentTime(appointmentTime);
                }

                //地址
                PromiseStation promiseStation = jdhPromise.getStore();
                if(Objects.nonNull(promiseStation)){
                    PromisegoRequestAddress address = PromisegoRequestAddress.builder()
                            .provinceId(promiseStation.getProvinceCode())
                            .cityId(promiseStation.getCityCode())
                            .countyId(promiseStation.getDistrictCode())
                            .townId(promiseStation.getTownCode())
                            .provinceName(promiseStation.getProvinceName())
                            .cityName(promiseStation.getCityName())
                            .countyName(promiseStation.getDistrictName())
                            .townName(promiseStation.getTownName())
                            .fullAddress(promiseStation.getStoreAddr())
                            .build();
                    userPromisegoRequestBo.setAppointmentAddress(address);
                }

                UserPromisegoBo userPromisegoBo = promiseGoRpcService.queryUserPromisego(userPromisegoRequestBo);
                Date shipStartTime = shipDetail.getDeliveryOrderDetailFailResult().getStartTime();
                if (shipStartTime != null) {
                    Date shipEndTime = DateUtil.offsetMinute(shipStartTime, 25).toJdkDate();
                    if (userPromisegoBo != null && userPromisegoBo.getCurrScript() != null) {
                        shipEndTime = userPromisegoBo.getCurrScript().getEndTime();
                    }
                    Date now = new Date();
                    if (shipEndTime == null || shipEndTime.before(now)) {
                        shipEndTime = DateUtil.offsetMinute(now, 15).toJdkDate();
                    }
                    long curr = new Date().getTime() - shipStartTime.getTime();
                    long all = shipEndTime.getTime() - shipStartTime.getTime();
                    BigDecimal timeRate = BigDecimal.valueOf(curr).divide(BigDecimal.valueOf(all), 2, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100));
                    if (store != null && uav != null) {
                        // 将无人机起飞点和实验室直接直线距离切割成1000个点，根据时间进度匹配到第N个点
                        if (CollectionUtils.isNotEmpty(lt)) {
                            double[] latLng = lt.get(timeRate.intValue() * (totalPoint / 100));
                            if (latLng != null && latLng.length == 2) {
                                dto.setAngelLat(String.valueOf(latLng[0]));
                                dto.setAngelLng(String.valueOf(latLng[1]));
                            }
                        }
                    }
                } else {
                    buildUavWait(dto, lt);
                }
            }
            if (store != null) {
                dto.setStationLat(store.getJdStationLatitude() == null ? null : store.getJdStationLatitude().toString());
                dto.setStationLng(store.getJdStationLongitude() == null ? null : store.getJdStationLongitude().toString());
                if (StringUtils.isNotBlank(dto.getAngelLat()) && StringUtils.isNotBlank(dto.getAngelLng())
                    && StringUtils.isNotBlank(dto.getStationLat()) && StringUtils.isNotBlank(dto.getStationLng())) {
                    double angelLat = Double.parseDouble(dto.getAngelLat());
                    double angelLng = Double.parseDouble(dto.getAngelLng());
                    double stationLat = Double.parseDouble(dto.getStationLat());
                    double stationLng = Double.parseDouble(dto.getStationLng());
                    dto.setDistance(GeoDistanceUtil.calculateDistanceDynamicUnit(angelLat, angelLng, stationLat, stationLng));
                }
            }
        } else {
            AngelWorkTypeEnum angelWorkTypeEnum = AngelWorkTypeEnum.matchType(business.getBusinessModeCode());
            String angelType = angelWorkTypeEnum != null ? angelWorkTypeEnum.getAngelType() : "";
            //  非无人机节点展示无人机起飞地址
            if(uav != null){
                dto.setTransformStationLat(uav.getJdStationLatitude() != null ? uav.getJdStationLatitude().toString() : null);
                dto.setTransformStationLng(uav.getJdStationLongitude() != null ? uav.getJdStationLongitude().toString() : null);
                dto.setStationLat(null);
                dto.setStationLng(null);
                if (StringUtils.isNotBlank(dto.getAngelLat()) && StringUtils.isNotBlank(dto.getAngelLng())) {
                    double angelLat = Double.parseDouble(dto.getAngelLat());
                    double angelLng = Double.parseDouble(dto.getAngelLng());
                    double stationLat = uav.getJdStationLatitude();
                    double stationLng = uav.getJdStationLongitude();
                    dto.setDistance(GeoDistanceUtil.calculateDistanceDynamicUnit(angelLat, angelLng, stationLat, stationLng));
                }
            }
            dto.setAngelDesc(angelType + "正在赶往无人机站点");
        }
    }

    /**
     * 构建无人机兜底未起飞
     * @param dto
     * @param lt
     */
    private void buildUavWait(PromiseDistanceDto dto, List<double[]> lt) {
        // 无起飞时间,默认分配无人机中
        if (CollectionUtils.isNotEmpty(lt)) {
            double[] latLng = lt.get(0);
            if (latLng != null && latLng.length == 2) {
                dto.setAngelLat(String.valueOf(latLng[0]));
                dto.setAngelLng(String.valueOf(latLng[1]));
            }
        }
        dto.setAngelDesc("正在分配无人机");
    }

}
