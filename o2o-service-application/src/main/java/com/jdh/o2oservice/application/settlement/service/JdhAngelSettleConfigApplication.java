package com.jdh.o2oservice.application.settlement.service;

import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.core.domain.settlement.repository.query.JdhAngelSettleAreaFeeQuery;
import com.jdh.o2oservice.export.settlement.cmd.AngelSettleCityConfigFileCmd;
import com.jdh.o2oservice.export.settlement.cmd.AngelSettlementConfigCmd;
import com.jdh.o2oservice.export.settlement.dto.AngelSettlementConfigDto;

/**
 * JdhAngelSettleConfigApplication
 * 护士结算配置
 * <AUTHOR>
 * @version 2025/4/22 15:15
 **/
public interface JdhAngelSettleConfigApplication {

    /**
     * 分页护士结算配置
     * @param queryContext
     * @return
     */
    PageDto<AngelSettlementConfigDto> queryAngelSettleConfigPage(JdhAngelSettleAreaFeeQuery queryContext);

    /**
     * 保存护士结算配置
     * @param angelSettlementConfigCmd
     * @return
     */
    boolean saveAngelSettleConfig(AngelSettlementConfigCmd angelSettlementConfigCmd);

    /**
     * 更新护士结算配置
     * @param angelSettlementConfigCmd
     * @return
     */
    boolean updateAngelSettleConfig(AngelSettlementConfigCmd angelSettlementConfigCmd);

    /**
     * 删除护士结算配置
     * @param angelSettlementConfigCmd
     * @return
     */
    boolean deleteAngelSettlementConfig(AngelSettlementConfigCmd angelSettlementConfigCmd);

    /**
     *
     * @param angelSettleCityConfigFileCmd
     * @return
     */
    boolean batchSaveAngelSettleConfig(AngelSettleCityConfigFileCmd angelSettleCityConfigFileCmd);
    /**
     *
     * @param queryContext
     * @return
     */
    Boolean exportAngelSettleConfig(JdhAngelSettleAreaFeeQuery queryContext);

    /**
     * 获取某地区护士结算配置
     * @param queryContext
     * @return
     */
    AngelSettlementConfigDto getAngelSettleAreaConfig(JdhAngelSettleAreaFeeQuery queryContext);
}
