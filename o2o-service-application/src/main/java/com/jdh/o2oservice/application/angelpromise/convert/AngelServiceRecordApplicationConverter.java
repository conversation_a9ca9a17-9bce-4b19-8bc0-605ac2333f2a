package com.jdh.o2oservice.application.angelpromise.convert;
import com.jdh.o2oservice.export.angelpromise.dto.AngelServiceRecordQuestionGroupDto;
import com.jdh.o2oservice.export.product.dto.QuestionGroupDto;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
@Mapper
public interface AngelServiceRecordApplicationConverter {

    AngelServiceRecordApplicationConverter INS = Mappers.getMapper(AngelServiceRecordApplicationConverter.class);

    AngelServiceRecordQuestionGroupDto convertToServiceRecordQuestionGroupDto(QuestionGroupDto groupDto);
}
