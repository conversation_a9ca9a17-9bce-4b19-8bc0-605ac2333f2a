package com.jdh.o2oservice.application.support.service.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jd.medicine.base.common.util.StringUtil;
import com.jdh.o2oservice.application.support.service.SoundExtApplication;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelPromiseBizErrorCode;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelWork;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelWorkRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.query.AngelWorkDBQuery;
import com.jdh.o2oservice.export.support.query.SoundExtRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description
 * @date 2025/5/28
 */
@Service
@Slf4j
public class SoundExtApplicationImpl implements SoundExtApplication {



    /**
     * 自动注入的 AngelWorkApplication 实例，用于调用相关服务。
     */
    @Autowired
    private AngelWorkRepository angelWorkRepository;


    /**
     * 根据 SoundExtRequest 对象查询扩展声音参数。
     *
     * @param request SoundExtRequest 对象，包含查询条件。
     * @return 扩展声音参数对象。
     */
    @Override
    public Object querySoundExtParam(SoundExtRequest request) {

        if (StringUtil.equals("xfylWorkService", request.getScene())) {

            //1.verticalCode,serviceType,workId,promiseId,channelCode,scene

            AngelWork angelWork = angelWorkRepository.findAngelWork(AngelWorkDBQuery.builder().workIds(Lists.newArrayList(Long.valueOf(request.getBusinessId()))).build());
            //工单不存在
            if (Objects.isNull(angelWork)){
                throw new BusinessException(AngelPromiseBizErrorCode.ANGEL_WORK_NOT_EXIST);
            }

            Map<String,Object> result = Maps.newHashMap();
            result.put("verticalCode",angelWork.getVerticalCode());
            result.put("serviceType",angelWork.getServiceType());
            result.put("workId",angelWork.getWorkId());
            result.put("promiseId",angelWork.getPromiseId());
            result.put("tenantNo","XFYL000001");
            result.put("scene","workService");


            return result;
        }

        return null;
    }


}
