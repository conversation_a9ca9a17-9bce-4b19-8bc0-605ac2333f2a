package com.jdh.o2oservice.application.support.price;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.TypeReference;
import com.jdh.o2oservice.core.domain.support.basic.enums.PricingServiceFactObjectEnum;
import com.jdh.o2oservice.core.domain.support.price.context.PricingServiceCalculateContext;
import com.jdh.o2oservice.export.angel.dto.JdhAngelDto;
import com.jdh.o2oservice.export.medicalpromise.dto.MedicalPromiseDTO;
import com.jdh.o2oservice.export.product.dto.JdhSkuDto;
import com.jdh.o2oservice.export.product.dto.ServiceItemDto;
import com.jdh.o2oservice.export.settlement.dto.CityAngelSettlementConfigDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * @ClassName JdhSkuServicePriceMapHandler
 * @Description
 * <AUTHOR>
 * @Date 2025/5/20 22:19
 **/
@Component
@Slf4j
public class JdhSkuServicePriceMapHandler extends AbstractFactObjectHandler {

    /**
     * sku配置数据
     */
    @Resource
    private JdhSkuFeeConfigHandler jdhSkuFeeConfigHandler;

    /**
     * 项目配置数据
     */
    @Resource
    private JdhItemFeeConfigHandler jdhItemFeeConfigHandler;

    /**
     * 护士数据
     */
    @Resource
    private JdhAngelFactObjectHandler jdhAngelFactObjectHandler;

    /**
     * 检测单数据
     */
    @Resource
    private JdhMedicalPromiseFactObjectHandler medicalPromiseFactObjectHandler;

    /**
     * 城市结算数据
     */
    @Resource
    private JdhCityLevelFactObjectHandler cityLevelFactObjectHandler;

    /**
     * 获取事实对象
     * @param context
     * @return
     */
    @Override
    public Object getData(PricingServiceCalculateContext context) {
        boolean containsKey = context.getFactObjectMap().containsKey(getMapKey());
        if (containsKey) {
            return context.getFactObjectMap().get(getMapKey());
        }
        Object o = jdhSkuFeeConfigHandler.getFactObject(context);
        //如果sku不存在，则直接返回
        if (Objects.isNull(o)) {
            context.getFactObjectMap().put(getMapKey(), null);
            return null;
        }
        Map<Long, JdhSkuDto> skuMap =  Convert.convert(new TypeReference<Map<Long, JdhSkuDto>>(){}, o);
        //Map<Long, JdhSkuDto> skuMap = jdhSkuDtos.stream().collect(Collectors.toMap(JdhSkuDto::getSkuId, jdhSkuDto -> jdhSkuDto, (t, t2) -> t2));

        //2. 提取检测单数据
        Object factObject = medicalPromiseFactObjectHandler.getFactObject(context);
        if (Objects.isNull(factObject)) {
            context.getFactObjectMap().put(getMapKey(), null);
            return null;
        }
        List<MedicalPromiseDTO> medicalPromiseDTOList = Convert.convert(new TypeReference<List<MedicalPromiseDTO>>() {}, factObject);


        // 3.提取项目数据
        //前置依赖项目数据，先从上下文中获取，没有则调用接口获取，并放入上下文
        Object itemFeeConfigHandlerFactObject = jdhItemFeeConfigHandler.getFactObject(context);
        //没有项目数据，默认没有耗材，返回null
        if (Objects.isNull(itemFeeConfigHandlerFactObject)) {
            context.getFactObjectMap().put(getMapKey(), null);
            return null;
        }
        Map<Long, ServiceItemDto> id2ItemMap = Convert.convert(new TypeReference<Map<Long, ServiceItemDto>>(){}, itemFeeConfigHandlerFactObject );

        // 4.提取护士数据
        Object angel = jdhAngelFactObjectHandler.getFactObject(context);
        //没有护士数据，返回null
        if (Objects.isNull(angel)) {
            context.getFactObjectMap().put(getMapKey(), null);
            return null;
        }
        JdhAngelDto jdhAngelDto = Convert.convert(JdhAngelDto.class, angel);
        //没有护士全兼职标签数据，返回null
        if (Objects.isNull(jdhAngelDto.getJobNature())) {
            context.getFactObjectMap().put(getMapKey(), null);
            return null;
        }

        // 5.提取城市结算系数
        Object cityLevel = cityLevelFactObjectHandler.getFactObject(context);
        //没有护士数据，返回null
        if (Objects.isNull(cityLevel)) {
            context.getFactObjectMap().put(getMapKey(), null);
            return null;
        }
        CityAngelSettlementConfigDto settlementConfigDto = Convert.convert(CityAngelSettlementConfigDto.class, cityLevel);
        String coefficient = Objects.equals(jdhAngelDto.getJobNature(), 1) ? settlementConfigDto.getSelfServiceCoefficient() : Objects.equals(jdhAngelDto.getJobNature(), 0) ? settlementConfigDto.getSidelineServiceCoefficient() : null;
        //如果未配置护士结算系数，返回null
        if (StringUtils.isBlank(coefficient)) {
            context.getFactObjectMap().put(getMapKey(), null);
            return null;
        }

        //获取sku和item的关联关系
        Map<Long, Set<Long>> sku2ItemListMap = new HashMap<>();
        for (MedicalPromiseDTO medicalPromiseDTO : medicalPromiseDTOList) {
            if (Objects.isNull(medicalPromiseDTO.getServiceId()) || StringUtils.isBlank(medicalPromiseDTO.getServiceItemId())) {
                continue;
            }
            Set<Long> itemSet = sku2ItemListMap.getOrDefault(medicalPromiseDTO.getServiceId(), new HashSet<>());
            itemSet.add(Long.valueOf(medicalPromiseDTO.getServiceItemId()));
            sku2ItemListMap.put(medicalPromiseDTO.getServiceId(), itemSet);
        }

        //返回值
        Map<Long,BigDecimal> result = new HashMap<>();

        for (Map.Entry<Long, Set<Long>> entry : sku2ItemListMap.entrySet()) {
            //sku编号
            Long skuId = entry.getKey();
            JdhSkuDto jdhSkuDto = skuMap.get(skuId);
            if (Objects.isNull(jdhSkuDto)) {
                continue;
            }
            BigDecimal angelBasicSettlementPrice = jdhSkuDto.getAngelBasicSettlementPrice();
            //sku上没配置结算价，则根据属于该sku的检测单中存的项目ID计算项目价格
            if (Objects.isNull(angelBasicSettlementPrice)) {
                angelBasicSettlementPrice = BigDecimal.ZERO;
                Set<Long> itemSet = entry.getValue();
                if (CollectionUtils.isNotEmpty(itemSet)) {
                    for (Long itemId : itemSet) {
                        ServiceItemDto itemDto = id2ItemMap.get(itemId);
                        angelBasicSettlementPrice = angelBasicSettlementPrice.add(Objects.nonNull(itemDto) && Objects.nonNull(itemDto.getAngelBasicSettlementPrice()) ? itemDto.getAngelBasicSettlementPrice() : BigDecimal.ZERO);
                    }
                }
            }
            //乘以全兼职结算系数
            angelBasicSettlementPrice = angelBasicSettlementPrice.multiply(new BigDecimal(coefficient)).setScale(2, RoundingMode.HALF_UP);
            result.put(skuId, angelBasicSettlementPrice);
        }
        // 5. 计算最终结果
        //return fee.setScale(2, RoundingMode.HALF_UP);
        context.getFactObjectMap().put(getMapKey(), result);
        return result;
    }

    /**
     * 获取map key
     * @return
     */
    @Override
    public String getMapKey() {
        return PricingServiceFactObjectEnum.SKU_SERVICE_AMOUNT_MAP.getCode();
    }
}