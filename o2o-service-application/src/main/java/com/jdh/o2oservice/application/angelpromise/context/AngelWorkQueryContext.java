package com.jdh.o2oservice.application.angelpromise.context;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.googlecode.aviator.AviatorEvaluator;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jdh.o2oservice.application.angelpromise.convert.AngelPromiseApplicationConverter;
import com.jdh.o2oservice.application.angelpromise.convert.AngelPromiseTaskApplicationConverter;
import com.jdh.o2oservice.application.angelpromise.service.AngelWorkApplication;
import com.jdh.o2oservice.application.support.service.FileManageApplication;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.ducc.model.ShipTypeConfig;
import com.jdh.o2oservice.base.enums.YnStatusEnum;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.errorcode.SystemErrorCode;
import com.jdh.o2oservice.base.util.SpringUtil;
import com.jdh.o2oservice.base.util.TimeFormat;
import com.jdh.o2oservice.base.util.TimeUtils;
import com.jdh.o2oservice.common.enums.*;
import com.jdh.o2oservice.core.domain.angel.rpc.bo.NurseVisitPolicyBo;
import com.jdh.o2oservice.core.domain.angelpromise.bo.AngelModifyDateRuleDuccBo;
import com.jdh.o2oservice.core.domain.angelpromise.enums.*;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelShip;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelTask;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelWork;
import com.jdh.o2oservice.core.domain.angelpromise.vo.AngelVirtualStatusVo;
import com.jdh.o2oservice.core.domain.angelpromise.vo.JdhAngelWorkExtVo;
import com.jdh.o2oservice.core.domain.provider.model.JdhStationServiceItemRel;
import com.jdh.o2oservice.core.domain.support.basic.model.PhoneNumber;
import com.jdh.o2oservice.core.domain.support.basic.model.UserName;
import com.jdh.o2oservice.core.domain.support.basic.rpc.AddressRpc;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.DirectionResultBO;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.GisPointBo;
import com.jdh.o2oservice.core.domain.support.file.service.FileManageService;
import com.jdh.o2oservice.core.domain.support.file.service.FileManageService;
import com.jdh.o2oservice.core.domain.support.securitynumber.enums.SecurityNumberBizCallTypeEnum;
import com.jdh.o2oservice.core.domain.support.vertical.context.BusinessContext;
import com.jdh.o2oservice.export.angel.dto.AngelDto;
import com.jdh.o2oservice.export.angel.dto.AngelJobNatureDto;
import com.jdh.o2oservice.export.angelpromise.dto.*;
import com.jdh.o2oservice.export.angelpromise.query.AngelTrackQuery;
import com.jdh.o2oservice.export.medicalpromise.dto.MedicalPromiseDTO;
import com.jdh.o2oservice.export.product.cmd.ItemMaterialPackageRelCmd;
import com.jdh.o2oservice.export.product.dto.JdhSkuDto;
import com.jdh.o2oservice.export.product.dto.ServiceItemDto;
import com.jdh.o2oservice.export.promise.dto.PromiseDto;
import com.jdh.o2oservice.export.support.dto.CallRecordDetailDto;
import com.jdh.o2oservice.export.support.dto.CallRecordGroupDto;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.text.MessageFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author:lichen55
 * @createTime: 2024-04-18 09:36
 * @Description: 服务者工单查询（详情）上下文
 */
@Data
@Slf4j
public class AngelWorkQueryContext extends BusinessContext {

    /**
     * 护士服务工单
     */
    private AngelWork angelWork;

    /**
     * 工单详情业务状态导航栏
     */
    private List<AngelVirtualStatusVo> angelVirtualStatusList;

    /**
     * 检验单信息
     */
    private List<MedicalPromiseDTO> medicalPromiseDTOs;

    /**
     * 检验项信息
     */
    private Map<Long, ServiceItemDto> serviceItemMap;

    /**
     * 配置跳转地址信息
     */
    private Map<String, String> jumpUrlMap;

    private Map<Long, JdhSkuDto> longJdhSkuDtoMap;

    private Map<Long, JdhSkuDto> allLongJdhSkuDtoMap;

    /**
     * 骑行路径规划信息
     */
    private DirectionResultBO direction;

    /**
     * rpcAddress
     */
    private AddressRpc addressRpc;
    /**
     * 保险对象
     */
    private NurseVisitPolicyBo nurseVisitPolicyBo;

    /**
     * 投保地址(通天塔链接)
     */
    private static final String insureUrl = "https://pro.m.jd.com/mall/active/TP3pUmMysE5LE5cG8v8KHpppc5b/index.html?showhead=no";

    /**
     * 实验室对应项目配置
     */
    private Map<Long, JdhStationServiceItemRel> serviceItem2StationItemMap;

    /**
     * 服务者
     */
    private AngelJobNatureDto angelJobNatureDto;

    /**
     * 履约单
     */
    private PromiseDto promiseDto;
    /**
     * ducc
     */
    private DuccConfig duccConfig;
    /**
     * fileManageService
     */
    private FileManageService fileManageService;

    /**
     * 通话记录
     */
    private CallRecordGroupDto callRecordGroupDto;


    public AngelWorkQueryContext(){}

    public AngelWorkQueryContext(AngelWork angelWork, List<Integer> statusList, List<MedicalPromiseDTO> medicalPromises, List<ServiceItemDto> serviceItems, Map<String, String> jumpUrlMap, Map<Long, JdhSkuDto> longJdhSkuDtoMap, Map<Long, JdhSkuDto> allLongJdhSkuDtoMap, NurseVisitPolicyBo nurseVisitPolicyBo, Map<Long, JdhStationServiceItemRel> serviceItem2StationItemMap, DuccConfig duccConfig, FileManageService fileManageService){
        this.angelWork = angelWork;

        //构建虚拟状态导航栏信息
        if(CollectionUtils.isNotEmpty(statusList)){
            initAngelVirtualStatus(statusList, angelWork.getWorkType());
        }

        //构建检验单信息
        if(CollectionUtils.isNotEmpty(medicalPromises)){
            medicalPromiseDTOs = medicalPromises;
        }

        //构建检验项信息
        if(CollectionUtils.isNotEmpty(serviceItems)){
            serviceItemMap = serviceItems.stream().collect(Collectors.toMap(ServiceItemDto::getItemId, item -> item, (v1, v2) -> v2));
        }

        this.jumpUrlMap = MapUtils.isEmpty(jumpUrlMap) ? new HashMap<>() : jumpUrlMap;

        this.longJdhSkuDtoMap = longJdhSkuDtoMap;
        this.allLongJdhSkuDtoMap = allLongJdhSkuDtoMap;

        this.nurseVisitPolicyBo = nurseVisitPolicyBo;

        this.serviceItem2StationItemMap = serviceItem2StationItemMap;

        this.duccConfig = duccConfig;

        this.fileManageService = fileManageService;
    }

    /**
     * 初始化工单详情虚拟状态
     * @return
     */
    public List<AngelVirtualStatusVo> initAngelVirtualStatus(List<Integer> statusList, Integer workType){
        angelVirtualStatusList = new ArrayList<>();
        if(CollectionUtils.isEmpty(statusList)){
            return angelVirtualStatusList;
        }

        for(int sort=0; sort<statusList.size(); sort++){
            AngelVirtualStatusVo virtualStatusVo = new AngelVirtualStatusVo();
            AngelBizExtStatusEnum enumByCode = AngelBizExtStatusEnum.getEnumByShowStatus(statusList.get(sort), workType);
            virtualStatusVo.setVirtualStatusId(enumByCode.getShowStatus());
            virtualStatusVo.setVirtualStatusName(enumByCode.getDesc());
            virtualStatusVo.setSort(sort);
            virtualStatusVo.setStatus(AngelVirtualShowStatusEnum.WAIT.getType());
            angelVirtualStatusList.add(virtualStatusVo);
        }
        return angelVirtualStatusList;
    }

    /**
     * 校验是否需要计算骑行规划
     * @return
     */
    public Boolean checkIsQueryDirection(){
        Integer status = angelWork.getWorkStatus();
        return AngelWorkStatusEnum.WAIT_RECEIVE.getType().equals(status) || AngelWorkStatusEnum.RECEIVED.getType().equals(status)
                || AngelWorkStatusEnum.WAIT_SERVICE.getType().equals(status) || AngelWorkStatusEnum.SERVICING.getType().equals(status);
    }

    /**
     * 构建工单详情返回值
     * @return
     */
    public AngelWorkDto mergeWorkDto(){
        AngelWorkDto workDto = AngelPromiseApplicationConverter.instance.entity2WorkDto(angelWork);
        //虚拟状态导航栏
        takeAngelVirtualStatus();
        if(CollectionUtils.isNotEmpty(angelVirtualStatusList)){
            List<AngelVirtualStatusDto> virtualStatusDtos = JSONObject.parseArray(JSON.toJSONString(angelVirtualStatusList), AngelVirtualStatusDto.class);
            workDto.setAngelVirtualStatusList(virtualStatusDtos);
        }

        //保单
        workDto.setAngelWorkInsure(takeInsure());

        //订单
        workDto.setAngelWorkOrder(takeOrder());

        //实验室-样本
        workDto.setAngelWorkSpecimens(takeAngelWorkSpecimen(medicalPromiseDTOs));

        //任务-检验项
        workDto.setAngelTasks(takeTasks());

        //工单维度耗材包
        List<AngelWorkMaterialPackageDto> materialPackages = buildMaterialPackageDto(workDto);
        workDto.setAngelWorkMaterialPackages(materialPackages);

        //处理工单的不可用状态
        workDto.setDisabled(parserWorkNoAliveStatus());

        //工单详情运力楼层数据信息
        workDto.setCourierFloorInfoDto(buildCourierFloor(workDto));

        //服务记录楼层
        workDto.setAngelWorkServiceRecordDto(buildServiceRecord(workDto));

        //是否上传服务记录楼层
        workDto.setNeedServiceRecordFloor(buildNeedServiceRecord(workDto));

        //是否需要上传服务记录
        if(MapUtils.isNotEmpty(longJdhSkuDtoMap)){
            workDto.setNeedServiceRecord(longJdhSkuDtoMap.values().stream().anyMatch(item -> CollectionUtils.isNotEmpty(item.getServiceRecordType())));
        }else {
            workDto.setNeedServiceRecord(false);
        }

        //实验室接收指引
        workDto.setNeedAcceptGuideFloor(AngelWorkTypeEnum.NURSE.getType().equals(angelWork.getWorkType()) &&  AngelWorkStatusEnum.RECEIVED.getType().equals(angelWork.getWorkStatus()));

        workDto.setCanModifyDate(buildCanModifyDate());
        buildArrived(workDto);
        return workDto;
    }

    /**
     *
     * @return
     */
    private void buildArrived(AngelWorkDto workDto) {
        if (angelWork != null) {
            if (AngelWorkStatusEnum.WAIT_SERVICE.getType().equals(angelWork.getWorkStatus()) && angelWork.getJdhAngelWorkExtVo() != null && angelWork.getJdhAngelWorkExtVo().getAngelArrivedTime() != null) {
                workDto.setStatus(AngelWorkStatusEnum.ARRIVED.getType());
                workDto.setStatusDesc(AngelWorkStatusEnum.ARRIVED.getShowDesc());
            }
        }
    }

    /**
     *
     * @return
     */
    private boolean buildCanModifyDate() {
        if (angelWork != null) {
            Map<String, Object> expParam = new HashMap<>();
            expParam.put("angelWork", angelWork);
            AngelModifyDateRuleDuccBo angelModifyDateRuleDuccBo = JSON.parseObject(duccConfig.getAngelModifyDateRule(), AngelModifyDateRuleDuccBo.class);
            if (angelModifyDateRuleDuccBo != null && StringUtils.isNotBlank(angelModifyDateRuleDuccBo.getShowAngelModifyButtonExpress())) {
                Boolean ret = (Boolean) AviatorEvaluator.compile(angelModifyDateRuleDuccBo.getShowAngelModifyButtonExpress(), Boolean.TRUE).execute(expParam);
                return Boolean.TRUE.equals(ret);
            }
        }
        return false;
    }

    /**
     *
     * @param workDto
     * @return
     */
    private boolean buildNeedServiceRecord(AngelWorkDto workDto) {
        boolean showRecordFloor = Objects.equals(workDto.getStatus(), AngelWorkStatusEnum.DELIVERING.getType())
                || Objects.equals(workDto.getStatus(), AngelWorkStatusEnum.COMPLETED.getType());
        if(!showRecordFloor) {
            return false;
        }
        if(CollectionUtils.isEmpty(workDto.getClothingPicUrls())
                && CollectionUtils.isEmpty(workDto.getServiceRecordPicUrls())
                && CollectionUtils.isEmpty(workDto.getMedicalWastePicUrls())) {
            return true;
        }
        return false;
    }

    /**
     * 构建服务记录楼层
     * @return
     */
    private AngelWorkServiceRecordDto buildServiceRecord(AngelWorkDto workDto) {
        AngelWorkServiceRecordDto recordDto = new AngelWorkServiceRecordDto();
        recordDto.setServiceRecordPicUrls(workDto.getServiceRecordPicUrls());
        recordDto.setMedicalWastePicUrls(workDto.getMedicalWastePicUrls());
        recordDto.setClothingPicUrls(workDto.getClothingPicUrls());
        return recordDto;
    }

    /**
     * 构建运力楼层数据
     *
     * @return
     */
    private CourierFloorInfoDto buildCourierFloor(AngelWorkDto workDto) {
        if(Objects.isNull(angelWork)){
            return null;
        }
        //上门检测类
        boolean isInspect = AngelWorkTypeEnum.NURSE.getType().equals(angelWork.getWorkType());
        //已出门或服务中
        boolean isShow = AngelWorkStatusEnum.WAIT_SERVICE.getType().equals(angelWork.getWorkStatus())
                || AngelWorkStatusEnum.SERVICING.getType().equals(angelWork.getWorkStatus());

        if(!(isInspect && isShow)) {
            return null;
        }
        CourierFloorInfoDto courierFloor = new CourierFloorInfoDto();
        if(CollectionUtils.isNotEmpty(angelWork.getAngelShips())){
            int stationNum = 0;
            if(CollectionUtils.isNotEmpty(workDto.getAngelWorkSpecimens())){
                stationNum = workDto.getAngelWorkSpecimens().size();
            }
            courierFloor.setCourierStatus(CommonConstant.ZERO);
            courierFloor.setCourierFloorDesc(MessageFormat.format(AngelClientCourierFloorEnum.COURIER_CALLED.getCodeDesc(), stationNum));
            courierFloor.setBtnDesc(AngelClientCourierFloorEnum.COURIER_CALLED.getBtnDesc());
        }else {
            courierFloor.setCourierStatus(CommonConstant.ONE);
            courierFloor.setCourierFloorDesc(AngelClientCourierFloorEnum.COURIER_NO_CALLED.getCodeDesc());
            courierFloor.setBtnDesc(AngelClientCourierFloorEnum.COURIER_NO_CALLED.getBtnDesc());
        }
        return courierFloor;
    }

    /**
     * 处理工单的不用用状态
     *
     * @return
     */
    private Boolean parserWorkNoAliveStatus() {
        boolean workStatusNoAlive = AngelWorkStatusEnum.workStatusNoAlive(angelWork.getWorkStatus());
        boolean workInStop = AngelWorkStopStatusEnum.inStopStatus(angelWork.getStopStatus());
        return workStatusNoAlive || workInStop;
    }

    /**
     * 构建保单信息
     * @return
     */
    public AngelWorkInsureDto takeInsure(){
        if(this.nurseVisitPolicyBo==null||InsureStatusEnum.UNINSURED.getCode().equals(this.nurseVisitPolicyBo.getPolicyStatus())){
            return null;
        }
        InsureStatusEnum insureStatusEnum = InsureStatusEnum.getEnumByCode(this.nurseVisitPolicyBo.getPolicyStatus());
        if(insureStatusEnum==null){
            return null;
        }
        AngelWorkInsureDto insure = new AngelWorkInsureDto();
        insure.setInsureStatusDesc(insureStatusEnum.getShowDesc());
        insure.setInsureUrl(insureUrl);
        insure.setInsureId(this.nurseVisitPolicyBo.getPolicyId());
        insure.setInsureStatus(3);
        return insure;
    }

    /**
     * 构建订单信息
     * @return
     */
    public AngelWorkOrderDto takeOrder(){
        if(Objects.isNull(angelWork.getJdOrderId()) || Objects.isNull(angelWork.getJdhAngelWorkExtVo())){
            return null;
        }

        AngelWorkOrderDto order = new AngelWorkOrderDto();
        JdhAngelWorkExtVo jdhAngelWorkExtVo = angelWork.getJdhAngelWorkExtVo();

        //脱敏处理
        String appointName = jdhAngelWorkExtVo.getAngelOrder().getAppointName();

        if(Objects.nonNull(jdhAngelWorkExtVo.getAngelOrder())){
            order.setRemark(jdhAngelWorkExtVo.getAngelOrder().getOrderRemark());
            PhoneNumber phoneNumber = new PhoneNumber(jdhAngelWorkExtVo.getAngelOrder().getAppointPhone());
            order.setUserPhone(phoneNumber.mask());
            order.setUserPhoneEncrypt(phoneNumber.encrypt());
            order.setUserName(new UserName(appointName).mask());
        }
        //如果通话记录不为空，展示沟通记录
        if (Objects.nonNull(callRecordGroupDto) && MapUtils.isNotEmpty(callRecordGroupDto.getGroupMap()) && StringUtils.isNotBlank(jdhAngelWorkExtVo.getAngelOrder().getAppointPhone())) {
            List<CallRecordDetailDto> callRecordDetailDtos = callRecordGroupDto.getGroupMap().get(jdhAngelWorkExtVo.getAngelOrder().getAppointPhone());
            order.setNeedCallRecordSheet(CollectionUtils.isNotEmpty(callRecordDetailDtos));
        } else {
            order.setNeedCallRecordSheet(false);
        }
        order.setOrderId(angelWork.getJdOrderId());
        order.setServiceStartTime(Objects.isNull(angelWork.takeServiceStartTime()) ? "" : TimeUtils.dateTimeToStr(angelWork.takeServiceStartTime(), TimeFormat.DATE_PATTERN_MMdd_SIMPLE));
        order.setUserFullAddress(angelWork.takeUserFullAddress());
        order.setCreateTime(TimeUtils.dateTimeToStr(jdhAngelWorkExtVo.getAngelOrder().getCreateTime()!=null?jdhAngelWorkExtVo.getAngelOrder().getCreateTime():angelWork.getCreateTime(), TimeFormat.LONG_PATTERN_LINE));

        //写入骑行规划信息
        if(Objects.nonNull(direction)){
            double distanceInKm = direction.getDistance() / 1000.0;
            double roundedDistanceInKm = Math.round(distanceInKm * 10.0) / 10.0;
            order.setKmDistance(roundedDistanceInKm);
            order.setDuration(direction.getDuration());
        }

        if(MapUtils.isNotEmpty(longJdhSkuDtoMap)){
            AtomicReference<Integer> serviceDuration = new AtomicReference<>(0);
            longJdhSkuDtoMap.values().forEach(item -> {
                if(Objects.nonNull(item.getServiceDuration())){
                    serviceDuration.updateAndGet(v -> v + item.getServiceDuration());
                }
            });
            order.setDuration(serviceDuration.get().doubleValue() * angelWork.getAngelTasks().stream().filter(item -> item.checkValidStatus(true, true)).count());
        }

        AngelTask angelTask = angelWork.getAngelTasks().get(0);
        order.setUserLat(angelTask.getPatientAddressLat());
        order.setUserLng(angelTask.getPatientAddressLng());

        // 服务者打给预约人
        List<Integer> workStatusList = Arrays.asList(AngelWorkStatusEnum.RECEIVED.getType(),AngelWorkStatusEnum.WAIT_SERVICE.getType(),AngelWorkStatusEnum.SERVICING.getType());
        if (ServiceTypeEnum.TEST.getServiceType().equals(angelWork.getServiceType())){
            workStatusList = Arrays.asList(AngelWorkStatusEnum.RECEIVED.getType(),AngelWorkStatusEnum.WAIT_SERVICE.getType()
                    ,AngelWorkStatusEnum.SERVICING.getType(),AngelWorkStatusEnum.SERVICED.getType(),AngelWorkStatusEnum.DELIVERING.getType());
        }
        if (workStatusList.contains(angelWork.getWorkStatus())){
            AngelWorkCallDto angelWorkCall = new AngelWorkCallDto();
            angelWorkCall.setPromiseId(angelWork.getPromiseId());
            angelWorkCall.setBizCallType(SecurityNumberBizCallTypeEnum.ANGEL_TO_APPOINTMENT.getCode());
            order.setAngelWorkCall(angelWorkCall);
        }
        return order;
    }

    /**
     * 根据当前work与task补充导航栏状态
     */
    public void takeAngelVirtualStatus(){
        if(CollectionUtils.isEmpty(angelVirtualStatusList) || AngelWorkStatusEnum.CANCEL.getType().equals(angelWork.getWorkStatus())){
            return;
        }
        //过滤取消的任务，将待绑定条码的任务映射成待采样（页面不展示绑定条码这个虚拟状态，都当成采样处理）
        List<Integer> bizExtStatus = angelWork.getAngelTasks().stream()
                .filter(task -> task.checkValidStatus(true, true))
                .map(AngelTask::getBizExtStatus)
                .collect(Collectors.toList());
        if(CollectionUtils.isEmpty(bizExtStatus)){
            return ;
        }
        Integer minStatus = bizExtStatus.stream().min(Comparator.comparing(Function.identity())).get();

        log.info("[AngelPromiseApplicationImpl->queryAngelWorkDetailForPage] bizExtStatus:{}", JSONObject.toJSONString(bizExtStatus));
        for (int i = 0; i < angelVirtualStatusList.size(); i++) {
            AngelVirtualStatusVo angelVirtualStatus = angelVirtualStatusList.get(i);
            List<AngelBizExtStatusEnum> enumsByShowStatus = AngelBizExtStatusEnum.getEnumsByShowStatus(angelVirtualStatus.getVirtualStatusId());
            if(CollectionUtils.isEmpty(enumsByShowStatus)){
                angelVirtualStatus.setStatus(AngelVirtualShowStatusEnum.NOT_SHOW.getType());
                continue;
            }
            List<Integer> extStatusList = enumsByShowStatus.stream().map(item -> item.getType()).collect(Collectors.toList());
//            Integer minSort = extStatusList.stream().min(Comparator.comparing(Function.identity())).get();
            log.info("[AngelPromiseApplicationImpl->queryAngelWorkDetailForPage] extStatusList:{}, minSort={}", JSONObject.toJSONString(extStatusList), minStatus);
            AngelVirtualShowStatusEnum angelVirtualStatusEnum = extStatusList.contains(minStatus) ? AngelVirtualShowStatusEnum.SHOWING : AngelVirtualShowStatusEnum.COMPLETED;
            if(i == angelVirtualStatusList.size()-1 && angelVirtualStatusEnum.getType().equals(AngelVirtualShowStatusEnum.SHOWING.getType())){
                angelVirtualStatus.setStatus(AngelVirtualShowStatusEnum.COMPLETED.getType());
            }else {
                angelVirtualStatus.setStatus(angelVirtualStatusEnum.getType());
            }
            if(angelVirtualStatusEnum.getType().equals(AngelVirtualShowStatusEnum.SHOWING.getType())){
                return;
            }
        }
    }

    /**
     * 构建任务单信息
     * @return
     */
    public List<AngelTaskDto> takeTasks(){
        if(CollectionUtils.isEmpty(angelWork.getAngelTasks())){
            return null;
        }
        List<AngelTaskDto> angelTaskDtos = AngelPromiseTaskApplicationConverter.INS.convert2TaskDto(angelWork.getAngelTasks());

        boolean confirmFlag = false;
        if(MapUtils.isNotEmpty(allLongJdhSkuDtoMap)){
            confirmFlag = allLongJdhSkuDtoMap.values().stream().anyMatch(item -> CollectionUtils.isNotEmpty(item.getCustomerConfirmType()));
        }

        Map<String, String> id2PhoneMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(promiseDto.getPatients())){
            id2PhoneMap = promiseDto.getPatients().stream().filter(promisePatientDto -> Objects.nonNull(promisePatientDto.getPhoneNumber()) && StringUtils.isNotBlank(promisePatientDto.getPhoneNumber().getPhone()))
                    .collect(Collectors.toMap(promisePatientDto -> String.valueOf(promisePatientDto.getPromisePatientId()), promisePatientDto -> promisePatientDto.getPhoneNumber().getPhone(), (o, o2) -> o2));
        }

        for(AngelTaskDto task : angelTaskDtos){
            Map<Long, List<MedicalPromiseDTO>> patientMedicalPromiseMap = takeUserMedicalPromise();
            if(MapUtils.isNotEmpty(patientMedicalPromiseMap)){
                List<MedicalPromiseDTO> medicalPromiseDTOS = patientMedicalPromiseMap.get(Long.valueOf(task.getPatientId()));
                List<AngelWorkMedPromiseDto> angelWorkMedPromiseDtos = takeAngelWorkMedPromise(medicalPromiseDTOS);
                if(CollectionUtils.isNotEmpty(angelWorkMedPromiseDtos)){
                    task.setAngelWorkServiceItems(angelWorkMedPromiseDtos);
                }
            }
            task.setDisabled(parserTaskNoAliveStatus(task));

            //检查是否需要服务记录和核验信息
            if(MapUtils.isEmpty(longJdhSkuDtoMap)){
                continue;
            }
            task.setNeedConfirm(confirmFlag);

            //年龄处理
            if(StringUtils.isNotBlank(task.getPatientAge())){
                task.setPatientAge(task.getPatientAge().concat("岁"));
            }

            //项目服务时长
            AtomicReference<Integer> serviceDuration = new AtomicReference<>(0);
            longJdhSkuDtoMap.values().forEach(item -> {
                if(Objects.nonNull(item.getServiceDuration())){
                    serviceDuration.updateAndGet(v -> v + item.getServiceDuration());
                }
            });
            task.setServiceDuration(serviceDuration.get());

            //脱敏处理
            task.setPatientName(new UserName(task.getPatientName()).mask());

            // 服务者打给被服务人
            List<Integer> workStatusList = Arrays.asList(AngelWorkStatusEnum.RECEIVED.getType(),AngelWorkStatusEnum.WAIT_SERVICE.getType(),AngelWorkStatusEnum.SERVICING.getType());
            if (ServiceTypeEnum.TEST.getServiceType().equals(angelWork.getServiceType())){
                workStatusList = Arrays.asList(AngelWorkStatusEnum.RECEIVED.getType(),AngelWorkStatusEnum.WAIT_SERVICE.getType()
                        ,AngelWorkStatusEnum.SERVICING.getType(),AngelWorkStatusEnum.SERVICED.getType(),AngelWorkStatusEnum.DELIVERING.getType());
            }
            if (workStatusList.contains(angelWork.getWorkStatus())){
                AngelWorkCallDto angelWorkCall = new AngelWorkCallDto();
                angelWorkCall.setPromiseId(angelWork.getPromiseId());
                angelWorkCall.setBizCallType(SecurityNumberBizCallTypeEnum.ANGEL_TO_SERVICED.getCode());
                angelWorkCall.setPromisePatientId(task.getPatientId());
                task.setAngelWorkCall(angelWorkCall);
            }

            //如果通话记录不为空，展示沟通记录
            String phone = id2PhoneMap.get(task.getPatientId());
            if (Objects.nonNull(callRecordGroupDto) && MapUtils.isNotEmpty(callRecordGroupDto.getGroupMap()) && StringUtils.isNotBlank(phone)) {
                List<CallRecordDetailDto> callRecordDetailDtos = callRecordGroupDto.getGroupMap().get(phone);
                task.setNeedCallRecordSheet(CollectionUtils.isNotEmpty(callRecordDetailDtos));
            } else {
                task.setNeedCallRecordSheet(false);
            }
        }
        return angelTaskDtos;
    }

    /**
     * 处理任务单我失效的状态
     *
     * @param task
     * @return
     */
    private Boolean parserTaskNoAliveStatus(AngelTaskDto task) {
        boolean taskStatusNoAlive = AngelTaskStatusEnum.taskStatusNoAlive(task.getStatus());
        boolean taskInStop = AngelTaskStopStatusEnum.inStopStatus(task.getStopStatus());
        return taskStatusNoAlive || taskInStop;
    }

    /**
     * 构建检测信息列表
     * @param medicalPromiseDTOs
     * @return
     */
    public List<AngelWorkMedPromiseDto> takeAngelWorkMedPromise(List<MedicalPromiseDTO> medicalPromiseDTOs){
        if(CollectionUtils.isEmpty(medicalPromiseDTOs)){
            return null;
        }

        List<AngelWorkMedPromiseDto> result = new ArrayList<>();
        for(MedicalPromiseDTO promiseDTO : medicalPromiseDTOs){
            AngelWorkMedPromiseDto angelWorkMedPromise = convert2AngelWorkMedPromiseDto(promiseDTO);
            result.add(angelWorkMedPromise);
        }
        return result;
    }

    /**
     * 构建检测项信息
     * @return
     */
    private AngelWorkMedPromiseDto convert2AngelWorkMedPromiseDto(MedicalPromiseDTO medicalPromiseDTO){

        AngelWorkMedPromiseDto promiseDto = new AngelWorkMedPromiseDto();
        Long serviceItemId = Long.valueOf(medicalPromiseDTO.getServiceItemId());
        promiseDto.setMedicalPromiseId(medicalPromiseDTO.getMedicalPromiseId());
        promiseDto.setServiceItemId(serviceItemId);
        promiseDto.setServiceItemName(medicalPromiseDTO.getServiceItemName());
        promiseDto.setSpecimenCode(medicalPromiseDTO.getSpecimenCode());

        //填充耗材信息 和 检验时长
        if(MapUtils.isNotEmpty(serviceItemMap) && Objects.nonNull(serviceItemMap.get(serviceItemId))){
            ServiceItemDto serviceItemDto = serviceItemMap.get(serviceItemId);
            if(CollectionUtils.isNotEmpty(serviceItemDto.getMaterialList())){
                promiseDto.setMaterialNames(serviceItemDto.getMaterialList().stream().map(ItemMaterialPackageRelCmd::getMaterialPackageDetail).collect(Collectors.joining("、")));
                promiseDto.setMaterialPackageId(serviceItemDto.getMaterialList().stream().map(item -> String.valueOf(item.getMaterialPackageId())).collect(Collectors.joining("、")));
                promiseDto.setMaterialPackageName(serviceItemDto.getMaterialList().stream().map(ItemMaterialPackageRelCmd::getMaterialPackageName).collect(Collectors.joining("、")));
            }
            promiseDto.setPlanConsumeTime(String.valueOf(serviceItemDto.getInspectDuration()));
            //填充教程地址
            promiseDto.setTutorialPicUrl(serviceItemMap.get(serviceItemId).getTongUrl());
            JdhStationServiceItemRel serviceItemRel = serviceItem2StationItemMap.get(serviceItemId);
            if (Objects.nonNull(serviceItemRel)) {
                promiseDto.setSamplingWay(serviceItemRel.getSpecimenWay());
                promiseDto.setSampleType(serviceItemRel.getSpecimenType());
                promiseDto.setTestWay(serviceItemRel.getTestWay());
                promiseDto.setSimplePreserveDuration(serviceItemRel.getSpecimenPreserveDuration());
                promiseDto.setSimpleNum(serviceItemRel.getSpecimenNum());
                promiseDto.setServiceDuration(serviceItemRel.getServiceDuration());
                promiseDto.setSimplePreserveCondition(serviceItemRel.getSpecimenPreserveCondition());
                promiseDto.setTestDuration(serviceItemRel.getTestDuration());
                promiseDto.setServiceCondition(serviceItemRel.getServiceCondition());
                promiseDto.setRemark(serviceItemRel.getRemark());
                promiseDto.setStationId(serviceItemRel.getStationId());
            }
        }

        // 检测项目作废或者冻结，项目失效
        if (Objects.equals(medicalPromiseDTO.getFreeze(), YnStatusEnum.YES.getCode())){
            promiseDto.setDisabled(Boolean.TRUE);
        }else if (Objects.equals(medicalPromiseDTO.getStatus(), MedicalPromiseStatusEnum.INVALID.getStatus())){
            promiseDto.setDisabled(Boolean.TRUE);
        }else {
            promiseDto.setDisabled(Boolean.FALSE);
        }

        return promiseDto;
    }

    /**
     * 构建样本送检信息列表
     * @param medicalPromiseDTOs
     * @return
     */
    public List<AngelWorkSpecimenDto> takeAngelWorkSpecimen(List<MedicalPromiseDTO> medicalPromiseDTOs){
        if(CollectionUtils.isEmpty(medicalPromiseDTOs)){
            return null;
        }
        Map<String, List<MedicalPromiseDTO>> stationPromiseMap = medicalPromiseDTOs.stream()
                .filter(medical -> Objects.nonNull(medical.getStationId()))
                .collect(Collectors.groupingBy(MedicalPromiseDTO::getStationId));

        if(MapUtils.isEmpty(stationPromiseMap)){
            return null;
        }
        List<AngelWorkSpecimenDto> result = new ArrayList<>();
        Map<String, AngelShip> reciveShipMap = angelWork.takeReciveShipMap();

        //注入bean
        AngelWorkApplication angelWorkApplication = SpringUtil.getBean(AngelWorkApplication.class);
        log.info("[AngelWorkQueryContext -> takeAngelWorkSpecimen],reciveShipMap={}", JSON.toJSONString(reciveShipMap));
        for(Map.Entry<String, List<MedicalPromiseDTO>> entry : stationPromiseMap.entrySet()){
            AngelShip angelShip = reciveShipMap.get(entry.getKey());
            MedicalPromiseDTO medicalPromiseDTO = entry.getValue().get(0);
            log.info("[AngelWorkQueryContext -> takeAngelWorkSpecimen],ship={}", JSON.toJSONString(angelShip));
            if(Objects.nonNull(angelShip)){
                AngelTrackQuery angelTrackQuery = new AngelTrackQuery();
                angelTrackQuery.setShipId(angelShip.getShipId());
                angelTrackQuery.setShipStatus(angelShip.getShipStatus());
                angelTrackQuery.setPromiseId(medicalPromiseDTO.getPromiseId());
                angelTrackQuery.setAngelType(AngelTypeEnum.DELIVERY.getType());
                angelTrackQuery.setDeliveryType(angelShip.getType());
                angelTrackQuery.setAngelDetailType(AngelDetailTypeEnum.getTypeByDelivery(angelShip.getType()));
                AngelTrackDto transferTrack = angelWorkApplication.getTransferTrack(angelTrackQuery);
                angelShip.setTrackUrl(Objects.nonNull(transferTrack) ? transferTrack.getTrackUrl() : null);
            }
            List<String> specimenCodes = entry.getValue().stream().map(MedicalPromiseDTO::getSpecimenCode).collect(Collectors.toList());
            AngelWorkSpecimenDto specimenDto = convert2AngelWorkSpecimenDto(entry.getValue().get(0), specimenCodes, angelShip);
            result.add(specimenDto);
        }
        return result;
    }

    /**
     * 构建实验室-运单信息
     * @param stationPromise
     * @param specimenCodes
     * @return
     */
    private AngelWorkSpecimenDto convert2AngelWorkSpecimenDto(MedicalPromiseDTO stationPromise, List<String> specimenCodes, AngelShip ship){
        PhoneNumber phoneNumber = new PhoneNumber(angelWork.getAngelPhone());
        UserName userName = new UserName(angelWork.getAngelName());
        AngelWorkSpecimenDto specimenDto = new AngelWorkSpecimenDto();
        specimenDto.setReceiverId(stationPromise.getStationId());
        specimenDto.setReceiverName(stationPromise.getStationName());
        specimenDto.setReceiverPhone(new PhoneNumber(stationPromise.getStationPhone()).mask());
        specimenDto.setReceiverPhoneEncrypt(new PhoneNumber(stationPromise.getStationPhone()).encrypt());
        specimenDto.setReceiverFullAddress(stationPromise.getStationAddress());
        specimenDto.setSenderPhone(phoneNumber.mask());
        specimenDto.setSenderPhoneEncrypt(phoneNumber.encrypt());
        specimenDto.setSenderName(userName.mask());
        specimenDto.setSenderFullAddress(angelWork.getAngelTasks().get(0).getPatientFullAddress());

        if (StringUtils.isNotBlank(stationPromise.getStationId())){
            String guideUrl = duccConfig.getAcceptSampleGuideConfig().get(stationPromise.getStationId());
            if (StringUtils.isNotBlank(guideUrl)){
                String publicUrl = fileManageService.getPublicUrl(guideUrl, Boolean.TRUE, DateUtil.offsetMinute(new Date(), CommonConstant.NUMBER_THIRTY));
                specimenDto.setAcceptSampleGuide(publicUrl);
            }
        }

        if(Objects.isNull(addressRpc)){
            addressRpc = SpringUtil.getBean(AddressRpc.class);
            GisPointBo lngLatByAddress = addressRpc.getLngLatByAddress(stationPromise.getStationAddress());
            if(Objects.nonNull(lngLatByAddress)){
                specimenDto.setReceiverLat(lngLatByAddress.getLatitude().doubleValue());
                specimenDto.setReceiverLng(lngLatByAddress.getLongitude().doubleValue());
            }
        }

        if(CollectionUtils.isNotEmpty(specimenCodes)){
            String codeNames = specimenCodes.stream().filter(StringUtils::isNotBlank)
                    .map(code -> code.substring(Math.max(code.length() - 4, 0))).collect(Collectors.joining("、"));
            specimenDto.setSpecimenNames(codeNames);
        }

        if(Objects.nonNull(ship)){
            specimenDto.setShipId(ship.getShipId());
            specimenDto.setShipType(ship.getType());
            specimenDto.setOutShipNo(ship.getOutShipId());
            specimenDto.setTransferName(new UserName(ship.getTransferName()).mask());
            specimenDto.setTransferPhone(new PhoneNumber(ship.getTransferPhone()).mask());
            specimenDto.setTransferPhoneEncrypt(new PhoneNumber(ship.getTransferPhone()).encrypt());
            specimenDto.setTransferHeadImg(ship.getTransferHeadImg());
            if(AngelShipStatusEnum.showSendCodeStatus.contains(ship.getShipStatus())) {
                specimenDto.setSendCode(new PhoneNumber(ship.getSendCode()).encrypt());
            }
            specimenDto.setSenderFullAddress(ship.getSenderFullAddress());
            specimenDto.setSenderPhone(new PhoneNumber(ship.getSenderPhone()).mask());
            specimenDto.setSenderPhoneEncrypt(new PhoneNumber(ship.getSenderPhone()).encrypt());
            specimenDto.setSenderName(new UserName(ship.getSenderName()).mask());
            specimenDto.setShipStatus(ship.getShipStatus());
            specimenDto.setShipStatusDesc(AngelShipStatusEnum.getStatusDesc(ship.getShipStatus()));
            specimenDto.setTransferTrack(ship.getTrackUrl());

            if(!DeliveryTypeEnum.SELF_DELIVERY.getType().equals(ship.getType()) && !DeliveryTypeEnum.THIRD_DELIVERY.getType().equals(ship.getType())) {
                specimenDto.setTransferId(ship.getTransferId());
            }
        }else {
            //如果没有运单，则需要呼叫运单，判断下发运力类型
            //自营护士+护士上门检测项目+特定地区 ->下发三方运力、自送
            //其余下发平台运力、三方运力、自送
            Integer workAttribute = angelWork.getJdhAngelWorkExtVo().getWorkAttribute();
            String shipListType = Objects.nonNull(workAttribute)? String.valueOf(workAttribute) : "default";
            ShipTypeConfig shipTypeConfig = JsonUtil.parseObject(duccConfig.getCanSelectShipTypeConfig(), ShipTypeConfig.class);
            List<AngelDeliveryTypeDTO> angelDeliveryTypeDTOS = JsonUtil.parseArray(shipTypeConfig.getShipTypeMap().get(shipListType), AngelDeliveryTypeDTO.class);
            //如果是自营+上海+检测，则判断三方运力和自送是否可选

            Boolean canClick = Boolean.TRUE;

            AngelTask angelTask = angelWork.getAngelTasks().stream()
                    .filter(p -> AngelTaskStopStatusEnum.INIT.getStatus().equals(p.getStopStatus()))
                    .filter(p -> !Objects.equals(AngelBizExtStatusEnum.CHOOSE_DELIVERY_WAY.getType(), p.getBizExtStatus())).findFirst().orElse(null);
            if (Objects.nonNull(angelTask)){
                canClick = Boolean.FALSE;
            }
            if (canClick){
                if (CollectionUtils.isNotEmpty(specimenCodes)) {
                    List<String> empty = specimenCodes.stream().filter(StringUtils::isBlank).collect(Collectors.toList());
                    canClick = CollectionUtils.isEmpty(empty);
                } else {
                    canClick = Boolean.FALSE;
                }
            }

            if (!canClick) {
                for (AngelDeliveryTypeDTO angelDeliveryTypeDTO : angelDeliveryTypeDTOS) {
                    if (DeliveryTypeEnum.THIRD_DELIVERY.getType().equals(angelDeliveryTypeDTO.getDeliveryType()) || DeliveryTypeEnum.SELF_DELIVERY.getType().equals(angelDeliveryTypeDTO.getDeliveryType())) {
                        angelDeliveryTypeDTO.setClickYn(CommonConstant.ZERO);
                    }
                }
            }

            specimenDto.setShipTypeList(angelDeliveryTypeDTOS);
        }
        return specimenDto;
    }

    /**
     * 提取该工单对应的检验单信息（患者维度）
     * @return
     */
    private Map<Long, List<MedicalPromiseDTO>> takeUserMedicalPromise(){
        if(CollectionUtils.isEmpty(medicalPromiseDTOs)){
            return new HashMap<>();
        }
        return medicalPromiseDTOs.stream().collect(Collectors.groupingBy(MedicalPromiseDTO::getPromisePatientId));
    }

    /**
     * 构建工单维度耗材信息
     * @param workDto
     * @return
     */
    private List<AngelWorkMaterialPackageDto> buildMaterialPackageDto(AngelWorkDto workDto){
        if(CollectionUtils.isEmpty(workDto.getAngelTasks())){
            return null;
        }
        Map<String, AngelWorkMaterialPackageDto> cacheMap = new HashMap<>();
        for(AngelTaskDto task : workDto.getAngelTasks()){
            if(CollectionUtils.isNotEmpty(task.getAngelWorkServiceItems())){
                for(AngelWorkMedPromiseDto medPromiseDto : task.getAngelWorkServiceItems()){
                    if (Objects.nonNull(medPromiseDto.getMaterialPackageId())) {
                        AngelWorkMaterialPackageDto packageDto = cacheMap.get(medPromiseDto.getMaterialPackageId());
                        if (Objects.isNull(packageDto)) {
                            packageDto = new AngelWorkMaterialPackageDto();
                            packageDto.setMaterialPackageId(medPromiseDto.getMaterialPackageId());
                            packageDto.setMaterialPackageName(medPromiseDto.getMaterialPackageName());
                            packageDto.setMaterialPackageDetail(medPromiseDto.getMaterialNames());
                            packageDto.setMaterialPackageNum(1);
                            packageDto.setPlanConsumeTime(medPromiseDto.getPlanConsumeTime());
                            cacheMap.put(medPromiseDto.getMaterialPackageId(), packageDto);
                        } else {
                            packageDto.setMaterialPackageNum(packageDto.getMaterialPackageNum() + 1);
                        }
                    }
                }
            }
        }
        if(MapUtils.isEmpty(cacheMap)){
            return null;
        }

        return new ArrayList<>(cacheMap.values());
    }

}
