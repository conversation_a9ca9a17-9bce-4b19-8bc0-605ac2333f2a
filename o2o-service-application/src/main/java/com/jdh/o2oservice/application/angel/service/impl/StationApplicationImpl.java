package com.jdh.o2oservice.application.angel.service.impl;

import cn.hutool.core.lang.UUID;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.jd.jim.cli.Cluster;
import com.jdh.o2oservice.application.angel.convert.ModifyInventoryContextConvert;
import com.jdh.o2oservice.application.angel.convert.StationApplicationConverter;
import com.jdh.o2oservice.application.angel.service.AngelApplication;
import com.jdh.o2oservice.application.angel.service.StationApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.ducc.model.AngelStationInventoryConfig;
import com.jdh.o2oservice.base.enums.RedisKeyEnum;
import com.jdh.o2oservice.base.enums.ThreadPoolConfigEnum;
import com.jdh.o2oservice.base.enums.UmpKeyEnum;
import com.jdh.o2oservice.base.exception.ArgumentsException;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode;
import com.jdh.o2oservice.base.exception.errorcode.SystemErrorCode;
import com.jdh.o2oservice.base.factory.ExecutorPoolFactory;
import com.jdh.o2oservice.base.util.*;
import com.jdh.o2oservice.common.enums.AngelTypeEnum;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.core.domain.angel.context.*;
import com.jdh.o2oservice.core.domain.angel.enums.*;
import com.jdh.o2oservice.core.domain.angel.model.*;
import com.jdh.o2oservice.core.domain.angel.repository.db.*;
import com.jdh.o2oservice.core.domain.angel.repository.query.*;
import com.jdh.o2oservice.core.domain.angel.service.AngelStationInventoryDomainService;
import com.jdh.o2oservice.core.domain.angel.service.JdhMapDomainService;
import com.jdh.o2oservice.core.domain.angel.service.inventory.AngelStationInventoryHelper;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelPromiseBizErrorCode;
import com.jdh.o2oservice.core.domain.product.model.JdhSku;
import com.jdh.o2oservice.core.domain.product.model.JdhSkuIdentifier;
import com.jdh.o2oservice.core.domain.product.repository.db.JdhSkuRepository;
import com.jdh.o2oservice.core.domain.support.basic.rpc.AddressRpc;
import com.jdh.o2oservice.core.domain.support.basic.rpc.SkuInfoRpc;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.GisPointBo;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.RpcSkuBO;
import com.jdh.o2oservice.core.domain.support.gismap.AoiMapServiceRpc;
import com.jdh.o2oservice.core.domain.support.gismap.bo.QueryFenceBo;
import com.jdh.o2oservice.core.domain.support.gismap.response.QueryFenceResponse;
import com.jdh.o2oservice.export.angel.cmd.*;
import com.jdh.o2oservice.export.angel.dto.*;
import com.jdh.o2oservice.export.angel.query.*;
import com.jdh.o2oservice.export.ztools.query.QueryAngelStationRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.shardingsphere.elasticjob.lite.internal.schedule.JobRegistry;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.ParseException;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * @ClassName:StationApplicationImpl
 * @Description: 服务站application实现
 * @Author: yaoqinghai
 * @Date: 2024/4/22 23:45
 * @Vserion: 1.0
 **/
@Service
@Slf4j
public class StationApplicationImpl implements StationApplication {

    @Resource
    private JdhStationRepository jdhStationRepository;

    @Resource
    private JdhStationAngelManRepository jdhStationAngelManRepository;

    @Resource
    private JdhStationSkuManRepository jdhStationSkuManRepository;

    @Resource
    private JdhMapDomainService jdhMapDomainService;

    @Resource
    private AngelApplication angelApplication;

    @Resource
    private AddressRpc addressRpc;

    @Resource
    private SkuInfoRpc skuInfoRpc;

    @Resource
    private AoiMapServiceRpc aoiMapServiceRpc;

    @Resource
    private ExecutorPoolFactory executorPoolFactory;

    @Resource
    private AngelStationInventoryDomainService angelStationInventoryDomainService;

    @Resource
    private JdhSkuRepository jdhSkuRepository;

    @Resource
    private JdhInventoryPreemptionRecordRepository jdhInventoryPreemptionRecordRepository;

    @Resource
    private JdhInventoryRepository jdhInventoryRepository;

    @Resource
    private DuccConfig duccConfig;

    @Resource
    private RedisLockUtil redisLockUtil;

    @Resource
    private Cluster cluster;

    @Resource
    private AngelStationInventoryHelper angelStationInventoryHelper;

    @Resource
    private JdhInventoryReadjustRecordRepository jdhInventoryReadjustRecordRepository;

    @Resource
    private JdhStationSkuRelRepository jdhStationSkuRelRepository;


    /**
     * 查询服务站列表
     *
     * @param stationQuery
     * @return
     */
    @Override
    public List<AngelStationDto> queryJdhStation(StationQuery stationQuery) {
        if(Objects.isNull(stationQuery) || (CollectionUtils.isEmpty(stationQuery.getStationIds()) && StringUtils.isNotBlank(stationQuery.getStationName()))){
            throw new ArgumentsException(BusinessErrorCode.ILLEGAL_ARG_ERROR);
        }
        StationDbQuery stationDbQuery = StationDbQuery.builder()
                .stationIds(stationQuery.getStationIds())
                .stationName(stationQuery.getStationName())
                .stationMasters(stationQuery.getStationMasters())
                .build();
        return StationApplicationConverter.ins.convertToAngelStationDtoList(jdhStationRepository.findList(stationDbQuery));
    }

    /**
     * 地址查询服务站列表
     *
     * @param stationGeoQuery
     * @return
     */
    @Override
    public List<JdhStationDto> queryJdhStationGeo(StationGeoQuery stationGeoQuery) {
        AssertUtils.isNotEmpty(stationGeoQuery.getAddressDetailList(), "地址列表不能为空");
        AssertUtils.nonNull(stationGeoQuery.getSkuNo(), "商品编码不能为空");

        log.info("[StationApplicationImpl.queryJdhStationGeo],stationGeoQuery={}", JSON.toJSONString(stationGeoQuery));
        List<JdhStationDto> jdhStationDtoList = Lists.newArrayList();

        //查询服务站列表
        AngelStationAddressQueryContext queryContext = StationApplicationConverter.ins.convertToAngelStationAddressQueryContext(stationGeoQuery);
        queryContext.setModeType(AngelStationModeTypeEnum.FULL_TIME.getCode());
        log.info("[StationApplicationImpl.queryJdhStationGeo],queryContext={}", JSON.toJSONString(queryContext));
        Map<String, List<JdhStation>> stringListMap = jdhMapDomainService.queryAddressStationList(queryContext);

        //结果
        stringListMap.forEach((k, v) -> {
            if(CollectionUtils.isEmpty(v)){
                return;
            }
            List<AngelStationDto> jdhStationDtoList1 = StationApplicationConverter.ins.convertToAngelStationDtoList(v);
            JdhStationDto jdhStationDto = JdhStationDto.builder()
                    .stationDtoList(jdhStationDtoList1)
                    .addressId(k)
                    .build();
            jdhStationDtoList.add(jdhStationDto);
        });
        return jdhStationDtoList;
    }

    /**
     * 分页查询服务站列表
     *
     * @param request
     * @return
     */
    @Override
    public PageDto<AngelStationDto> queryAngelStationPage(AngelStationPageRequest request) {
        String errMsg = ValidateParamUtil.paramValidation(request);
        if(StringUtils.isNotBlank(errMsg)){
            log.error("[GisMapApplicationImpl.queryAngelStationPage]!request={}", JSON.toJSONString(request));
            throw new ArgumentsException(BusinessErrorCode.ILLEGAL_ARG_ERROR);
        }
        //运营端场景去空格
        TrimUtil.trimStringFields(request);

        if(StringUtils.isNotBlank(request.getAngelStationId()) && !NumberUtils.isNumber(request.getAngelStationId())){
            log.error("[StationApplicationImpl.queryAngelStationPage],服务站id输入格式不正确.request={}", JSON.toJSONString(request));
            throw new BusinessException(AngelPromiseBizErrorCode.ANGEL_STATION_ID_ERROR);
        }

        AngelStationPageQuery angelStationPageQuery = StationApplicationConverter.ins.convertToAngelStationPageQuery(request);
        angelStationPageQuery.setPageNum(request.getPageNum());
        angelStationPageQuery.setPageSize(request.getPageSize());
        angelStationPageQuery.setStationModeType(AngelStationModeTypeEnum.FULL_TIME.getCode());
        Page<JdhStation> pageList = jdhStationRepository.findPageList(angelStationPageQuery);
        if (pageList == null){
            return PageDto.getEmptyPage();
        }
        List<AngelStationDto> angelStationDtos = StationApplicationConverter.ins.convertToAngelStationDtoList(pageList.getRecords());
        PageDto<AngelStationDto> pageDto = new PageDto<>();
        pageDto.setList(angelStationDtos);
        pageDto.setTotalPage(pageList.getPages());
        pageDto.setPageNum(pageList.getCurrent());
        pageDto.setPageSize(pageList.getSize());
        pageDto.setTotalCount(pageList.getTotal());
        return pageDto;
    }

    /**
     * 根据地址查询经纬度
     *
     * @param request
     * @return
     */
    @Override
    public AngelStationLatAndLngDto queryAddressLatAndLng(AngelStationAddressRequest request) {
        String errMsg = ValidateParamUtil.paramValidation(request);
        if(StringUtils.isNotBlank(errMsg)){
            log.error("[StationApplicationImpl.queryAddressLatAndLng],查询地址的经纬度!request={}", JSON.toJSONString(request));
            throw new ArgumentsException(BusinessErrorCode.ILLEGAL_ARG_ERROR, errMsg);
        }
        //统一去前后空格
        TrimUtil.trimStringFields(request);
        GisPointBo lngLatByAddress = null;
        try{
            lngLatByAddress = addressRpc.getLngLatByAddress(request.getAddress());
        }catch (Exception exception){
            log.error("[StationApplicationImpl.queryAddressLatAndLng],查询经纬度失败!request={}", JSON.toJSONString(request));
            throw new BusinessException(AngelErrorCode.ANGEL_STATION_ADDRESS_ERROR);
        }
        if(Objects.isNull(lngLatByAddress)){
            log.error("[StationApplicationImpl.queryAddressLatAndLng],查询地址的经纬度数据异常!");
            throw BusinessException.asBusinessException(BusinessErrorCode.RPC_JSF_ERROR);
        }
        AngelStationLatAndLngDto latAndLngDto = new AngelStationLatAndLngDto();
        latAndLngDto.setAngelStationAddress(request.getAddress());
        latAndLngDto.setAngelStationLat(String.valueOf(lngLatByAddress.getLatitude()));
        latAndLngDto.setAngelStationLng(String.valueOf(lngLatByAddress.getLongitude()));
        return latAndLngDto;
    }

    /**
     * 服务站资源管理列表
     *
     * @param stationAngelPageRequest
     * @return
     */
    @Override
    public PageDto<StationAngelManDto> queryStationAngelPage(StationAngelPageRequest stationAngelPageRequest) {
        //统一去前后空格
        TrimUtil.trimStringFields(stationAngelPageRequest);

        if(StringUtils.isNotBlank(stationAngelPageRequest.getAngelId()) && !NumberUtils.isNumber(stationAngelPageRequest.getAngelId())){
            log.error("[StationApplicationImpl.queryStationAngelPage],服务站id输入格式不正确.stationAngelPageRequest={}", JSON.toJSONString(stationAngelPageRequest));
            throw new BusinessException(AngelPromiseBizErrorCode.ANGEL_ID_FORMAT_ERROR);
        }
        if(StringUtils.isNotBlank(stationAngelPageRequest.getAngelStationId()) && !NumberUtils.isNumber(stationAngelPageRequest.getAngelStationId())){
            log.error("[StationApplicationImpl.queryStationAngelPage],服务站id输入格式不正确.stationAngelPageRequest={}", JSON.toJSONString(stationAngelPageRequest));
            throw new BusinessException(AngelPromiseBizErrorCode.ANGEL_STATION_ID_ERROR);
        }

        JdhStationAngelManPageQuery pageQuery = StationApplicationConverter.ins.convertToJdhStationAngelManPageQuery(stationAngelPageRequest);
        Page<JdhStationAngelMan> stationAngelManPage = jdhStationAngelManRepository.findPageList(pageQuery);
        if (stationAngelManPage == null){
            return PageDto.getEmptyPage();
        }

        List<StationAngelManDto> stationAngelManDtos = StationApplicationConverter.ins.convertToStationAngelManDtoList(stationAngelManPage.getRecords());
        PageDto<StationAngelManDto> pageDto = new PageDto<>();
        pageDto.setList(stationAngelManDtos);
        pageDto.setTotalPage(stationAngelManPage.getPages());
        pageDto.setPageNum(stationAngelManPage.getCurrent());
        pageDto.setPageSize(stationAngelManPage.getSize());
        pageDto.setTotalCount(stationAngelManPage.getTotal());
        return pageDto;
    }

    /**
     * 分页查询服务站商品列表
     *
     * @param stationSkuPageRequest
     * @return
     */
    @Override
    public PageDto<StationSkuManDto> queryStationSkuPage(StationSkuPageRequest stationSkuPageRequest) {
        //统一去前后空格
        TrimUtil.trimStringFields(stationSkuPageRequest);
        if(StringUtils.isNotBlank(stationSkuPageRequest.getSkuId()) && !NumberUtils.isNumber(stationSkuPageRequest.getSkuId())){
            log.error("[StationApplicationImpl.queryStationSkuPage],服务站id输入格式不正确.stationSkuPageRequest={}", JSON.toJSONString(stationSkuPageRequest));
            throw new BusinessException(AngelPromiseBizErrorCode.SKU_ID_ERROR);
        }
        if(StringUtils.isNotBlank(stationSkuPageRequest.getAngelStationId()) && !NumberUtils.isNumber(stationSkuPageRequest.getAngelStationId())){
            log.error("[StationApplicationImpl.queryStationSkuPage],服务站id输入格式不正确.request={}", JSON.toJSONString(stationSkuPageRequest));
            throw new BusinessException(AngelPromiseBizErrorCode.ANGEL_STATION_ID_ERROR);
        }

        JdhStationSkuManPageQuery jdhStationSkuManPageQuery = StationApplicationConverter.ins.convertToJdhStationSkuManPageQuery(stationSkuPageRequest);
        Page<JdhStationSkuMan> skuManPage = jdhStationSkuManRepository.findPageList(jdhStationSkuManPageQuery);
        if (skuManPage == null){
            return PageDto.getEmptyPage();
        }

        List<StationSkuManDto> stationSkuManDtos = StationApplicationConverter.ins.convertToStationSkuManDto(skuManPage.getRecords());
        stationSkuManDtos.forEach(s -> {
            if (s.getOnOffShelf() != null) {
                if (s.getOnOffShelf() == 1) {
                    s.setOnOffShelfName("上架");
                } else {
                    s.setOnOffShelfName("下架");
                }
            }
        });
        PageDto<StationSkuManDto> pageDto = new PageDto<>();
        pageDto.setList(stationSkuManDtos);
        pageDto.setTotalPage(skuManPage.getPages());
        pageDto.setPageNum(skuManPage.getCurrent());
        pageDto.setPageSize(skuManPage.getSize());
        pageDto.setTotalCount(skuManPage.getTotal());
        return pageDto;
    }

    /**
     * 分页查询服务站商品列表-B端
     *
     * @param stationGeoForManQuery
     * @return
     */
    @Override
    @LogAndAlarm
    public List<JdhStationDto> queryJdhStationGeoForMan(StationGeoForManQuery stationGeoForManQuery) {
        AssertUtils.isNotEmpty(stationGeoForManQuery.getAddressDetailList(), "地址列表不能为空");

        List<JdhStationDto> jdhStationDtoList = Lists.newArrayList();
        //查询服务站列表
        AngelStationAddressQueryContext queryContext = StationApplicationConverter.ins.convertToAngelStationAddressQueryContextB(stationGeoForManQuery);
        queryContext.setModeType(AngelStationModeTypeEnum.FULL_TIME.getCode());
        Map<String, List<JdhStation>> stringListMap = jdhMapDomainService.queryAddressStationList(queryContext);

        //结果
        stringListMap.forEach((k, v) -> {
            if(CollectionUtils.isEmpty(v)){
                return;
            }
            List<AngelStationDto> jdhStationDtoList1 = StationApplicationConverter.ins.convertToAngelStationDtoList(v);
            JdhStationDto jdhStationDto = JdhStationDto.builder()
                    .stationDtoList(jdhStationDtoList1)
                    .addressId(k)
                    .build();
            jdhStationDtoList.add(jdhStationDto);
        });
        return jdhStationDtoList;
    }

    /**
     * 查询商品详情
     *
     * @param angelStationSkuQuery
     * @return
     */
    @Override
    public AngelStationSkuManDto querySkuDetail(AngelStationSkuQuery angelStationSkuQuery) {
        String errMsg = ValidateParamUtil.paramValidation(angelStationSkuQuery);
        if(StringUtils.isNotBlank(errMsg)){
            log.error("[StationApplicationImpl.querySkuDetail],查询商品详情异常!angelStationSkuQuery={}", JSON.toJSONString(angelStationSkuQuery));
            throw new ArgumentsException(BusinessErrorCode.ILLEGAL_ARG_ERROR, errMsg);
        }
        //统一去前后空格
        TrimUtil.trimStringFields(angelStationSkuQuery);

        //检查sku是否存在
        JdhSku jdhSku = jdhSkuRepository.find(new JdhSkuIdentifier(Long.valueOf(angelStationSkuQuery.getSkuId())));
        if(Objects.isNull(jdhSku)) {
            log.error("[StationApplicationImpl.querySkuDetail],查询商品异常!angelStationSkuQuery={}", JSON.toJSONString(angelStationSkuQuery));
            throw new BusinessException(AngelErrorCode.SKU_NOT_EXIST_ERROR);
        }

        Set<String> skuIdSet = Sets.newHashSet(angelStationSkuQuery.getSkuId());
        Map<String, RpcSkuBO> skuInfo = skuInfoRpc.getSkuInfo(skuIdSet);
        if(MapUtils.isEmpty(skuInfo)){
            log.error("[StationApplicationImpl.querySkuDetail],查询商品详情异常!angelStationSkuQuery={}", JSON.toJSONString(angelStationSkuQuery));
            throw new BusinessException(AngelErrorCode.SKU_NAME_IS_EMPTY);
        }
        RpcSkuBO rpcSkuBO = skuInfo.get(angelStationSkuQuery.getSkuId());
        if(StringUtils.isBlank(rpcSkuBO.getSkuName())){
            throw new BusinessException(AngelErrorCode.SKU_NAME_IS_EMPTY);
        }
        AngelStationSkuManDto skuManDto = new AngelStationSkuManDto();
        skuManDto.setSkuId(rpcSkuBO.getSkuId());
        skuManDto.setSkuName(rpcSkuBO.getSkuName());
        return skuManDto;
    }

    /**
     * 查询服务站列表
     *
     * @param request
     * @return
     */
    @Override
    public AngelStationDto queryAngelStationDetail(AngelStationDetailRequest request) {
        AssertUtils.nonNull(request.getAngelStationId(), "服务站id不能为空");

        //统一去前后空格
        TrimUtil.trimStringFields(request);

        StationDbQuery stationDbQuery = StationDbQuery.builder().stationIds(Sets.newHashSet(request.getAngelStationId())).build();
        List<JdhStation> list = jdhStationRepository.findList(stationDbQuery);
        if(CollectionUtils.isEmpty(list)){
            throw new BusinessException(AngelErrorCode.ANGEL_STATION_NOT_EXIST);
        }
        JdhInventoryReadjustQuery query = JdhInventoryReadjustQuery.builder()
                .angelStationId(list.get(0).getAngelStationId())
                .build();
        AngelStationDto angelStationDto = StationApplicationConverter.ins.convertToAngelStationDto(list.get(0));

        List<JdhInventoryReadjustRecord> readjustRecordList = jdhInventoryReadjustRecordRepository.findList(query);
        if(CollectionUtils.isEmpty(readjustRecordList)) {
            return angelStationDto;
        }
        List<JdhInventoryReadjustRecord> angelReadjustRecordList = readjustRecordList.stream().filter(item -> InventoryChannelEnum.RIDER_CHANNEL.getInventoryChannel().equals(item.getInventoryChannelNo())).collect(Collectors.toList());
        List<JdhInventoryReadjustRecord> nurseReadjustRecordList = readjustRecordList.stream().filter(item -> InventoryChannelEnum.NURSE_CHANNEL.getInventoryChannel().equals(item.getInventoryChannelNo())).collect(Collectors.toList());
        List<InventoryReadjustDto> inventoryAngelReadjustDtos = StationApplicationConverter.ins.convertToInventoryReadjustDtoList(angelReadjustRecordList);
        List<InventoryReadjustDto> inventoryNurseReadjustDtos = StationApplicationConverter.ins.convertToInventoryReadjustDtoList(nurseReadjustRecordList);
        if(Objects.nonNull(angelStationDto.getNurseNum()) && angelStationDto.getNurseNum().equals(CommonConstant.SUPPER_INVENTORY_NUM)) {
            //不限制库存时不漏出库存数量
            angelStationDto.setNurseNum(null);
        }

        angelStationDto.setInventoryReadjustDtoList(inventoryAngelReadjustDtos);
        angelStationDto.setInventoryNurseReadjustDtoList(inventoryNurseReadjustDtos);
        return angelStationDto;
    }

    /**
     * 服务站商品详情
     *
     * @param stationSkuDetailRequest
     * @return
     */
    @Override
    public StationSkuManDto queryStationSkuDetail(StationSkuDetailRequest stationSkuDetailRequest) {
        String errMsg = ValidateParamUtil.paramValidation(stationSkuDetailRequest);
        if(StringUtils.isNotBlank(errMsg)){
            log.error("[StationApplicationImpl.queryAngelStationList],查询服务站商品详情异常!stationSkuDetailRequest={}", JSON.toJSONString(stationSkuDetailRequest));
            throw new ArgumentsException(BusinessErrorCode.ILLEGAL_ARG_ERROR, errMsg);
        }
        //统一去前后空格
        TrimUtil.trimStringFields(stationSkuDetailRequest);

        JdhStationSkuManQuery manQuery = new JdhStationSkuManQuery();
        manQuery.setSkuIdSet(Sets.newHashSet(stationSkuDetailRequest.getSkuId()));
        manQuery.setAngelStationId(stationSkuDetailRequest.getAngelStationId());
        List<JdhStationSkuMan> list = jdhStationSkuManRepository.findList(manQuery);
        if(CollectionUtils.isEmpty(list)){
            throw new BusinessException(AngelErrorCode.SKU_NAME_IS_EMPTY);
        }

        return StationApplicationConverter.ins.convertToStationSkuManDto(list.get(0));
    }

    /**
     * 服务站资源详情
     *
     * @param detailRequest
     * @return
     */
    @Override
    public StationAngelManDto queryStationAngelDetail(StationAngelDetailRequest detailRequest) {
        String errMsg = ValidateParamUtil.paramValidation(detailRequest);
        if(StringUtils.isNotBlank(errMsg)){
            log.error("[StationApplicationImpl.queryAngelStationList],查询服务站资源详情异常!detailRequest={}", JSON.toJSONString(detailRequest));
            throw new ArgumentsException(BusinessErrorCode.ILLEGAL_ARG_ERROR, errMsg);
        }
        //统一去前后空格
        TrimUtil.trimStringFields(detailRequest);

        JdhStationAngelManQuery manQuery = new JdhStationAngelManQuery();
        manQuery.setAngelIdSet(Sets.newHashSet(detailRequest.getAngelId()));
        manQuery.setAngelStationId(detailRequest.getAngelStationId());
        List<JdhStationAngelMan> list = jdhStationAngelManRepository.findList(manQuery);
        if(CollectionUtils.isEmpty(list)){
            throw new BusinessException(AngelErrorCode.STATION_ANGEL_NOT_EXIST);
        }

        return StationApplicationConverter.ins.convertToStationAngelManDto(list.get(0));
    }

    /**
     * 分单查询围栏数据
     *
     * @param presortQuery
     * @return
     */
    @Override
    public String queryGeoList(PresortQuery presortQuery) {
        QueryFenceBo queryFenceBo = StationApplicationConverter.ins.convertToQueryFenceBo(presortQuery);
        List<QueryFenceResponse> queryFenceResponses = aoiMapServiceRpc.queryElementList(queryFenceBo);
        log.info("[StationApplicationImpl.queryGeoList],queryFenceResponses={}", JSON.toJSONString(queryFenceResponses));
        return JSON.toJSONString(queryFenceResponses);
    }

    /**
     * 地址查询服务者列表
     *
     * @param angelGeoQuery
     * @return
     */
    @Override
    public List<JdhAngelDto> queryAngelListByGeo(AngelGeoQuery angelGeoQuery) {
        log.info("[StationApplicationImpl.queryAngelListByGeo],angelGeoQuery={}", JSON.toJSONString(angelGeoQuery));
        String errMsg = ValidateParamUtil.paramValidation(angelGeoQuery);
        if(StringUtils.isNotBlank(errMsg)){
            log.error("[StationApplicationImpl.queryAngelListByGeo],查询服务站护士参数异常!angelGeoQuery={}, errMsg={}", JSON.toJSONString(angelGeoQuery), errMsg);
            throw new ArgumentsException(BusinessErrorCode.ILLEGAL_ARG_ERROR);
        }

        //查询全职、兼职的护士
        List<Integer> modeTypeList = Lists.newArrayList(AngelStationModeTypeEnum.FULL_TIME.getCode(), AngelStationModeTypeEnum.PART_TIME.getCode());//全职 、兼职
        //并行查询命中的服务站
        List<CompletableFuture<List<JdhAngelDto>>> futures = modeTypeList.stream()
                .map(modeType -> CompletableFuture.supplyAsync(() -> getAngelListByGeo(angelGeoQuery, modeType)
                        , executorPoolFactory.get(ThreadPoolConfigEnum.ADDRESS_GIS_HIT_POOL)))
                .collect(Collectors.toList());

        List<JdhAngelDto> result = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                .thenApply(v -> futures.stream()
                        .map(CompletableFuture::join)
                        .flatMap(List::stream)
                        .collect(Collectors.toList()))
                .join();

        log.info("[StationApplicationImpl.queryAngelListByGeo] end, result={}", JSON.toJSONString(result));
        // 返回结果
        return result;
    }

    /**
     * 商详页-根据sku查询骑手库存
     * @param queryBatchSkuInventoryRequest
     * @return
     */
    @Override
    @LogAndAlarm
    public SkuInventoryResultDto querySkuInventory(QueryBatchSkuInventoryRequest queryBatchSkuInventoryRequest) {
        //参数校验
        AssertUtils.nonNull(queryBatchSkuInventoryRequest,"参数不能为空");
        AssertUtils.isNotEmpty(queryBatchSkuInventoryRequest.getSkuNos(),"skuNos不能为空");
        AssertUtils.isNotEmpty(queryBatchSkuInventoryRequest.getAddressList(),"address不能为空");
        AssertUtils.nonNull(queryBatchSkuInventoryRequest.getInventoryChannelNo(),"库存渠道编码不能为空");

        long errorAddressCount = queryBatchSkuInventoryRequest.getAddressList().stream().filter(t->t.getAddressId()==null||StringUtils.isBlank(t.getFullAddress())).count();
        if(errorAddressCount>0){
            throw new BusinessException(AngelErrorCode.ANGEL_INVENTORY_CHECK_OP_PARAM_ERROR_MESSAGE.formatDescription("请先选择上门地址，再选择上门时间"));
        }
        SkuInventoryResultDto skuInventoryResultDto = SkuInventoryResultDto.builder().build();

        AngelStationAddressQueryContext queryContext = StationApplicationConverter.ins.convertToAngelStationAddressQueryContextC(queryBatchSkuInventoryRequest);
        if(queryBatchSkuInventoryRequest.getModeType()==null){
            //默认查全职
            queryContext.setModeType(AngelStationModeTypeEnum.FULL_TIME.getCode());
        }
        if(InventoryChannelEnum.RIDER_CHANNEL.getInventoryChannel().equals(queryBatchSkuInventoryRequest.getInventoryChannelNo())) {
            queryContext.setAngelType(AngelTypeEnum.DELIVERY.getType());
        } else if (InventoryChannelEnum.NURSE_CHANNEL.getInventoryChannel().equals(queryBatchSkuInventoryRequest.getInventoryChannelNo())
                || InventoryChannelEnum.NURSE_CARE_CHANNEL.getInventoryChannel().equals(queryBatchSkuInventoryRequest.getInventoryChannelNo())) {
            queryContext.setAngelType(AngelTypeEnum.NURSE.getType());
        } else {
            queryContext.setAngelType(null);
        }
        //查询地址围栏列表 k:地址id   v:服务站信息
        Map<String, List<JdhStation>> stringListMap = jdhMapDomainService.queryAddressStationList(queryContext);
        log.info("StationApplicationImpl.querySkuInventory stringListMap={}",JSON.toJSONString(stringListMap));
        if(stringListMap.isEmpty()){
            log.info("StationApplicationImpl.querySkuInventory stringListMap为空!!");
            return skuInventoryResultDto;
        }
        ///sku信息,维护可约时间段,未来可约天数
        List<JdhSku> jdhSkuList = jdhSkuRepository.queryMultiSku(queryBatchSkuInventoryRequest.getSkuNos().stream().map(t-> JdhSku.builder().skuId(t).build()).collect(Collectors.toList()));
        log.info("StationApplicationImpl.querySkuInventory jdhSkuList={}",JSON.toJSONString(jdhSkuList));
        //获取sku和服务站的服务重叠时间
        QueryBatchSkuTimeSlotContext queryBatchSkuTimeSlotContext = StationApplicationConverter.ins.convertToQueryBatchSkuTimeSlotContext(jdhSkuList,stringListMap.values().stream().flatMap(Collection::stream).collect(Collectors.toList()));
        QueryBatchSkuTimeSlotResult queryBatchSkuTimeSlotResult = angelStationInventoryDomainService.queryBatchSkuTimeSlot(queryBatchSkuTimeSlotContext);
        log.info("StationApplicationImpl.querySkuInventory queryBatchSkuTimeSlotResult={}",JSON.toJSONString(queryBatchSkuTimeSlotResult));

        if(queryBatchSkuTimeSlotResult==null){
            log.info("StationApplicationImpl.querySkuInventory queryBatchSkuTimeSlotResult为空!!");
            return skuInventoryResultDto;
        }
        //获取服务站库存明细
        QueryBatchAngelStationInventoryContext queryBatchAngelStationInventoryContext = StationApplicationConverter.ins.convertToQueryBatchAngelStationInventoryContext(queryBatchSkuTimeSlotResult,stringListMap.values().stream().flatMap(Collection::stream).collect(Collectors.toList()));
        queryBatchAngelStationInventoryContext.setInventoryChannelNo(queryBatchSkuInventoryRequest.getInventoryChannelNo());

        //库存打标
        AngelStationInventoryConfig angelStationInventoryConfig = duccConfig.getAngelStationInventoryConfig();
        AngelStationInventoryFlagContext context = new AngelStationInventoryFlagContext();
        context.setJdhAngelStationInfoBs(queryBatchAngelStationInventoryContext.getJdhAngelStationInfoBs());
        context.setInventoryChannelNo(queryBatchSkuInventoryRequest.getInventoryChannelNo());
        context.setAngelType(queryBatchSkuInventoryRequest.getAngelType());
        context.setAngelStationInventoryConfig(angelStationInventoryConfig);
        angelStationInventoryDomainService.handleInventoryFlag(context);

        log.info("StationApplicationImpl.querySkuInventory,queryBatchAngelStationInventoryContext={}", JSON.toJSONString(queryBatchAngelStationInventoryContext));
        QueryBatchAngelStationInventoryResult queryBatchAngelStationInventoryResult = angelStationInventoryDomainService.queryBatchAngelStationInventory(queryBatchAngelStationInventoryContext);
        log.info("StationApplicationImpl.querySkuInventory queryBatchAngelStationInventoryResult={}",JSON.toJSONString(queryBatchAngelStationInventoryResult));

        if(queryBatchAngelStationInventoryResult==null){
            log.info("StationApplicationImpl.querySkuInventory queryBatchAngelStationInventoryResult为空!!");
            return skuInventoryResultDto;
        }
        //给输出对象赋值
        this.enhanceDto(queryBatchAngelStationInventoryResult,skuInventoryResultDto);
        return skuInventoryResultDto;
    }

    /**
     * 字段赋值
     * @param queryBatchAngelStationInventoryResult
     * @param skuInventoryResultDto
     */
    private void enhanceDto(QueryBatchAngelStationInventoryResult queryBatchAngelStationInventoryResult,SkuInventoryResultDto skuInventoryResultDto){
        List<AngelStationDayInventoryDto> angelStationDayInventoryDtos = new ArrayList<>();
        if(CollectionUtils.isEmpty(queryBatchAngelStationInventoryResult.getAngelStationInventories())){
            return;
        }
        //消除服务站,返回时间槽对应的库存
        List<AngelStationDayInventory> angelStationDayInventories = new ArrayList<>();
        queryBatchAngelStationInventoryResult.getAngelStationInventories().forEach(t->{
            angelStationDayInventories.addAll(t.getAngelStationDayInventories());
        });
        if(CollectionUtils.isEmpty(angelStationDayInventories)){
            log.info("StationApplicationImpl.querySkuInventory angelStationDayInventories为空!!");
            return;
        }
        ///合并day维度 key:yyyy-MM-dd
        Map<String,List<AngelStationDayInventory>> maps = angelStationDayInventories.stream().collect(Collectors.groupingBy(AngelStationDayInventory::getScheduleDay));
        if(maps.isEmpty()){
            log.info("StationApplicationImpl.querySkuInventory maps为空!!");
            return;
        }
        maps.forEach((k,v)->{
            AngelStationDayInventoryDto angelStationDayInventoryDto = AngelStationDayInventoryDto.builder().build();
            angelStationDayInventoryDto.setScheduleDay(k);
            if(CollectionUtils.isEmpty(v)){
                log.info("StationApplicationImpl.querySkuInventory v为空!!");
                return;
            }
            ////合并day下的时间槽数据
            List<TimeSlotInventory> timeSlotInventoryList = v.stream().map(AngelStationDayInventory::getTimeSlotInventories).collect(Collectors.toList()).stream().flatMap(Collection::stream).collect(Collectors.toList());
            ////key: 09:00-10:00
            Map<String,List<TimeSlotInventory>> map2 = timeSlotInventoryList.stream().collect(Collectors.groupingBy(TimeSlotInventory::getTimeSpan));
            if(MapUtils.isEmpty(map2)){
                log.info("StationApplicationImpl.querySkuInventory map2为空!!");
                return;
            }
            List<TimeSlotInventoryDto> timeSlotInventoryDtoList = new ArrayList<>();
            map2.forEach((k1,v2)->{
                TimeSlotInventoryDto timeSlotInventoryDto = new TimeSlotInventoryDto();
                timeSlotInventoryDto.setTimeSpan(k1);
                timeSlotInventoryDto.setInventoryNum(v2.stream().mapToInt(TimeSlotInventory::getInventoryNum).sum());
                timeSlotInventoryDtoList.add(timeSlotInventoryDto);
            });
            angelStationDayInventoryDto.setTimeSlotInventories( timeSlotInventoryDtoList.stream().sorted(Comparator.comparing(TimeSlotInventoryDto::getTimeSpan)).collect(Collectors.toList()));
            angelStationDayInventoryDtos.add(angelStationDayInventoryDto);
        });
        skuInventoryResultDto.setAngelStationDayInventoryDtos(angelStationDayInventoryDtos.stream().sorted(Comparator.comparing(AngelStationDayInventoryDto::getScheduleDay)).collect(Collectors.toList()));
    }

    /**
     * 用户下单-查询sku绑定的服务站
     * @param querySkuAngelStationRequest
     * @return
     */
    @Override
    @LogAndAlarm
    public SkuAngelStationResultDto querySkuAngelStations(QuerySkuAngelStationRequest querySkuAngelStationRequest) {

        //参数校验
        AssertUtils.nonNull(querySkuAngelStationRequest,"参数不能为空");
        AssertUtils.isNotEmpty(querySkuAngelStationRequest.getSkuNos(),"skuNos不能为空");
        AssertUtils.isNotEmpty(querySkuAngelStationRequest.getAddressList(),"address不能为空");
        AssertUtils.nonNull(querySkuAngelStationRequest.getScheduleDay(),"预定日期不能为空");
        AssertUtils.nonNull(querySkuAngelStationRequest.getBookTimeSpan(),"预定时间不能为空");
        AssertUtils.nonNull(querySkuAngelStationRequest.getInventoryChannelNo(),"库存渠道编码不能为空");

        AngelStationAddressQueryContext queryContext = StationApplicationConverter.ins.convertToAngelStationAddressQueryContextD(querySkuAngelStationRequest);
        SkuAngelStationResultDto skuInventoryResultDto = SkuAngelStationResultDto.builder().build();

        if(querySkuAngelStationRequest.getModeType()==null){
            //默认查全职
            queryContext.setModeType(AngelStationModeTypeEnum.FULL_TIME.getCode());
        }

        //处理服务站类型
        Integer inventoryChannelNo = querySkuAngelStationRequest.getInventoryChannelNo();
        if(InventoryChannelEnum.RIDER_CHANNEL.getInventoryChannel().equals(inventoryChannelNo)) {
            queryContext.setAngelType(AngelTypeEnum.DELIVERY.getType());
        }else if(InventoryChannelEnum.NURSE_CHANNEL.getInventoryChannel().equals(inventoryChannelNo)){
            queryContext.setAngelType(AngelTypeEnum.NURSE.getType());
        }else if(InventoryChannelEnum.NURSE_CARE_CHANNEL.getInventoryChannel().equals(inventoryChannelNo)){
            queryContext.setAngelType(AngelTypeEnum.NURSE.getType());
        }

        //查询地址围栏列表 k:地址id   v:服务站信息
        Map<String, List<JdhStation>> stringListMap = jdhMapDomainService.queryAddressStationList(queryContext);
        log.info("StationApplicationImpl.querySkuAngelStations stringListMap={}",JSON.toJSONString(stringListMap));

        //获取sku和服务站的服务重叠时间
        List<JdhStation> jdhStationList = stringListMap.values().stream().flatMap(Collection::stream).collect(Collectors.toList());
        if(querySkuAngelStationRequest.getAngelStationId()!=null){
            jdhStationList = jdhStationList.stream().filter(t->querySkuAngelStationRequest.getAngelStationId().equals(t.getAngelStationId())).collect(Collectors.toList());
        }

        ///sku信息,维护可约时间段,未来可约天数
        List<JdhSku>  jdhSkuList = jdhSkuRepository.queryMultiSku(querySkuAngelStationRequest.getSkuNos().stream().map(t-> JdhSku.builder().skuId(t).build()).collect(Collectors.toList()));
        log.info("StationApplicationImpl.querySkuAngelStations jdhSkuList={}",JSON.toJSONString(jdhSkuList));

        QueryBatchSkuTimeSlotContext queryBatchSkuTimeSlotContext = StationApplicationConverter.ins.convertToQueryBatchSkuTimeSlotContext(jdhSkuList,jdhStationList);
        QueryBatchSkuTimeSlotResult queryBatchSkuTimeSlotResult = angelStationInventoryDomainService.queryBatchSkuTimeSlot(queryBatchSkuTimeSlotContext);
        log.info("StationApplicationImpl.querySkuAngelStations queryBatchSkuTimeSlotResult={}",JSON.toJSONString(queryBatchSkuTimeSlotResult));
        if(queryBatchSkuTimeSlotResult==null){
            log.info("StationApplicationImpl.querySkuAngelStations queryBatchSkuTimeSlotResult为空!!");
            return skuInventoryResultDto;
        }

        //获取服务站库存明细
        QueryBatchAngelStationInventoryContext queryBatchAngelStationInventoryContext = StationApplicationConverter.ins.convertToQueryBatchAngelStationInventoryContext(queryBatchSkuTimeSlotResult,jdhStationList);
        queryBatchAngelStationInventoryContext.setInventoryChannelNo(querySkuAngelStationRequest.getInventoryChannelNo());

        //库存打标
        AngelStationInventoryConfig angelStationInventoryConfig = duccConfig.getAngelStationInventoryConfig();
        AngelStationInventoryFlagContext context = new AngelStationInventoryFlagContext();
        context.setJdhAngelStationInfoBs(queryBatchAngelStationInventoryContext.getJdhAngelStationInfoBs());
        context.setInventoryChannelNo(querySkuAngelStationRequest.getInventoryChannelNo());
        context.setAngelType(querySkuAngelStationRequest.getAngelType());
        context.setAngelStationInventoryConfig(angelStationInventoryConfig);
        angelStationInventoryDomainService.handleInventoryFlag(context);

        QueryBatchAngelStationInventoryResult queryBatchAngelStationInventoryResult = angelStationInventoryDomainService.queryBatchAngelStationInventory(queryBatchAngelStationInventoryContext);
        log.info("StationApplicationImpl.querySkuAngelStations queryBatchAngelStationInventoryResult={}",JSON.toJSONString(queryBatchAngelStationInventoryResult));

        if(queryBatchAngelStationInventoryResult==null){
            log.info("StationApplicationImpl.querySkuAngelStations queryBatchAngelStationInventoryResult为空!!");
            return skuInventoryResultDto;
        }

        if(CollectionUtils.isEmpty(queryBatchAngelStationInventoryResult.getAngelStationInventories())){
            log.info("StationApplicationImpl.querySkuAngelStations queryBatchAngelStationInventoryResult.getAngelStationInventories()为空!!");
            return skuInventoryResultDto;
        }
        this.enhanceDto2(queryBatchAngelStationInventoryResult,querySkuAngelStationRequest,skuInventoryResultDto);
        return skuInventoryResultDto;
    }

    /**
     * 字段赋值
     * @param queryBatchAngelStationInventoryResult
     * @param queryBatchSkuInventoryRequest
     * @param skuInventoryResultDto
     */
    private void enhanceDto2(QueryBatchAngelStationInventoryResult queryBatchAngelStationInventoryResult,QuerySkuAngelStationRequest queryBatchSkuInventoryRequest,SkuAngelStationResultDto skuInventoryResultDto){
        List<SkuAngelStationDto> skuAngelStationDtos = new ArrayList<>();
        //预定开始时间
        LocalTime startTime = LocalTime.parse(queryBatchSkuInventoryRequest.getBookTimeSpan().split("-")[0], DateTimeFormatter.ofPattern("HH:mm"));
        //预定结束时间
        String endTimeTemp = queryBatchSkuInventoryRequest.getBookTimeSpan().split("-")[1];
        LocalTime endTime = LocalTime.parse("00:00".equals(endTimeTemp)?"23:59":endTimeTemp, DateTimeFormatter.ofPattern("HH:mm"));
        TimeIntervalIntersection.TimeInterval commonInterval = new TimeIntervalIntersection.TimeInterval(startTime,endTime);


        JdhInventoryPreemptionRecord jdhInventoryPreemptionRecord=null;
        if(StringUtils.isNotBlank(queryBatchSkuInventoryRequest.getBusinessId())&&queryBatchSkuInventoryRequest.getBusinessType()!=null){
            //businessId 和 businessType 都不为空,查库存单;一定绑定了库存单
            jdhInventoryPreemptionRecord = jdhInventoryPreemptionRecordRepository.findByBusinessId(queryBatchSkuInventoryRequest.getBusinessId(),
                    queryBatchSkuInventoryRequest.getBusinessType(),
                    Collections.singletonList(AngelStationInventoryStatusEnum.RELEASE.getType()),
                    queryBatchSkuInventoryRequest.getInventoryChannelNo());
        }
        JdhInventoryPreemptionRecord finalJdhInventoryPreemptionRecord = jdhInventoryPreemptionRecord;
        queryBatchAngelStationInventoryResult.getAngelStationInventories().forEach(t->{
            if(CollectionUtils.isEmpty(t.getAngelStationDayInventories())){
                log.info("StationApplicationImpl.querySkuAngelStations t.getAngelStationDayInventories()为空!!");
                return;
            }
            //通过预约day过滤出指定day的数据
            t.setAngelStationDayInventories(t.getAngelStationDayInventories().stream().filter(f->queryBatchSkuInventoryRequest.getScheduleDay().equals(f.getScheduleDay())).collect(Collectors.toList()));
            t.getAngelStationDayInventories().forEach(m->{
                //过滤出复合条件的库存数据,可能会跨时段
                m.setTimeSlotInventories(m.getTimeSlotInventories().stream().filter(n->{
                    LocalTime startTime1 = LocalTime.parse(n.getTimeSpan().split("-")[0], DateTimeFormatter.ofPattern(CommonConstant.HM));
                    LocalTime endTime1 = LocalTime.parse(n.getTimeSpan().split("-")[1], DateTimeFormatter.ofPattern(CommonConstant.HM));
                    TimeIntervalIntersection.TimeInterval interval = new TimeIntervalIntersection.TimeInterval(startTime1,endTime1);

                    if(startTime.toString().equals(endTime1.toString())){
                        return false;
                    }
                    if(endTime.toString().equals(startTime1.toString())){
                        return false;
                    }
                    //计算两个时段的交集
                    if(!commonInterval.intersection(interval).isPresent()){
                        return false;
                    }
                    return true;
                }).collect(Collectors.toList()));
                //时间段排序
                m.getTimeSlotInventories().sort(Comparator.comparing(TimeSlotInventory::getTimeSpan));
            });

            SkuAngelStationDto skuAngelStationDto = new SkuAngelStationDto();
            skuAngelStationDto.setAngelStationId(t.getAngelStationId());
            skuAngelStationDto.setJdTransferStationId(t.getJdTransferStationId());
            //维护实验室名称
            StationDto stationDto = new StationDto();
            stationDto.setStationName(t.getStationName());
            if(finalJdhInventoryPreemptionRecord !=null&& finalJdhInventoryPreemptionRecord.getAngelStationId()!=null&&finalJdhInventoryPreemptionRecord.getAngelStationId().equals(t.getAngelStationId())){
                //标记为 已预占
                skuAngelStationDto.setHavePreemption(Boolean.TRUE);
            }
            //维护实验室id
            stationDto.setStationId(t.getStationId());
            skuAngelStationDto.setStationDtoList(Arrays.asList(stationDto));
            SkuInventoryDto skuInventoryDto = new SkuInventoryDto();
            //上边逻辑已经按天过滤了数据,所以t.getAngelStationDayInventories()只会有一条数据,取0即可

            if(CollectionUtils.isNotEmpty(t.getAngelStationDayInventories())&&t.getAngelStationDayInventories().get(0)!=null&&CollectionUtils.isNotEmpty(t.getAngelStationDayInventories().get(0).getTimeSlotInventories())){
                //计算各时间段总库存
                skuInventoryDto.setInventoryNum(t.getAngelStationDayInventories()
                        .stream()
                        .map(AngelStationDayInventory::getTimeSlotInventories)
                        .collect(Collectors.toList())
                        .stream()
                        .flatMap(List::stream)
                        .mapToInt(TimeSlotInventory::getInventoryNum)
                        .sum());
                //合并时间段
                Integer length = t.getAngelStationDayInventories().get(0).getTimeSlotInventories().size();
                skuInventoryDto.setTimeSpan(t.getAngelStationDayInventories().get(0).getTimeSlotInventories().get(0).getTimeSpan().split("-")[0]+"-"+t.getAngelStationDayInventories().get(0).getTimeSlotInventories().get(length-1).getTimeSpan().split("-")[1]);
            }else {
                skuInventoryDto.setInventoryNum(CommonConstant.ZERO);
                skuInventoryDto.setTimeSpan(queryBatchSkuInventoryRequest.getBookTimeSpan());
            }
            skuAngelStationDto.setSkuInventoryDto(skuInventoryDto);
            skuAngelStationDtos.add(skuAngelStationDto);
        });
        skuInventoryResultDto.setSkuAngelStationDtos(skuAngelStationDtos);
    }

    /**
     * 预占-服务站库存
     * @param preemptionInventoryCmd
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.angel.service.impl.StationApplicationImpl.preemptionInventory")
    @Transactional
    public Long preemptionInventory(PreemptionInventoryCmd preemptionInventoryCmd) {
        //参数校验
        AssertUtils.nonNull(preemptionInventoryCmd,"参数不能为空");
        AssertUtils.nonNull(preemptionInventoryCmd.getPin(),"pin不能为空");
        AssertUtils.nonNull(preemptionInventoryCmd.getPreemptionNum(),"预占数量不能为空");
        AssertUtils.nonNull(preemptionInventoryCmd.getScheduleDay(),"预定日期不能为空");
        AssertUtils.nonNull(preemptionInventoryCmd.getScheduleTime(),"预定时间不能为空");
        AssertUtils.nonNull(preemptionInventoryCmd.getAngelStationId(),"服务站不能为空");
        AssertUtils.nonNull(preemptionInventoryCmd.getInventoryChannelNo(),"库存渠道编码不能为空");

        //校验时间
        try {
            AngelStationInventoryConfig angelStationInventoryConfig = duccConfig.getAngelStationInventoryConfig();
            if(BooleanUtils.isTrue(angelStationInventoryConfig.getAppointTimeSwitch())){
                Date scheduleDateTime = DateUtils.parseDate(preemptionInventoryCmd.getScheduleDay()+" "+preemptionInventoryCmd.getScheduleTime().split("-")[1]+":00",CommonConstant.YMDHMS);
                if(scheduleDateTime.before(new Date())){
                    throw new BusinessException(SystemErrorCode.PARAM_NULL_ERROR.formatDescription("不能预约已过去的时间!!!"));
                }
            }
        } catch (ParseException px) {
            log.error("StationApplicationImpl -> preemptionInventory, 库存检查时间失败，不影响库存预占继续执行", px);
            UmpUtil.showWarnMsg(UmpKeyEnum.STATION_INVENTORY_CHECK_TIME_ERROR);
//            throw new BusinessException(SystemErrorCode.PARAM_NULL_ERROR.formatDescription("时间转换异常!!!"));
        }

        PreemptionInventoryContext preemptionInventoryContext = StationApplicationConverter.ins.convertToPreemptionInventoryContext(preemptionInventoryCmd);
        return angelStationInventoryDomainService.preemptionInventory(preemptionInventoryContext);
    }

    /**
     * 确定-预约服务站库存
     * @param confirmPreemptionInventoryCmd
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.angel.service.impl.StationApplicationImpl.confirmPreemptionInventory")
    public Boolean confirmPreemptionInventory(ConfirmPreemptionInventoryCmd confirmPreemptionInventoryCmd) {
        //参数校验
        AssertUtils.nonNull(confirmPreemptionInventoryCmd,"参数不能为空");
        AssertUtils.nonNull(confirmPreemptionInventoryCmd.getInventoryId(),"库存单业务主键id不能为空");
        AssertUtils.nonNull(confirmPreemptionInventoryCmd.getBusinessId(),"业务数据主键id不能为空");
        AssertUtils.nonNull(confirmPreemptionInventoryCmd.getPin(),"操作人不能为空");

        if(!duccConfig.getAngelStationInventoryConfig().getAngelStationInventoryGlobalOpen()){
            log.info("StationApplicationImpl.confirmPreemptionInventory 不走库存逻辑");
            return true;
        }

        return angelStationInventoryDomainService.confirmPreemptionInventory(StationApplicationConverter.ins.convertToConfirmPreemptionInventoryContext(confirmPreemptionInventoryCmd));
    }

    /**
     * 释放-服务站库存
     * @param releaseInventoryCmd
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.angel.service.impl.StationApplicationImpl.releaseInventory")
    @Transactional
    public Boolean releaseInventory(ReleaseInventoryCmd releaseInventoryCmd) {
        //参数校验
        if(StringUtils.isBlank(releaseInventoryCmd.getBusinessId())&&releaseInventoryCmd.getInventoryId()==null&&releaseInventoryCmd.getBusinessType()==null){
            throw new ArgumentsException(SystemErrorCode.PARAM_NULL_ERROR.formatDescription("库存单号/业务id/业务字段类型不能同时为空!!!"));
        }
        //参数校验
        AssertUtils.nonNull(releaseInventoryCmd,"参数不能为空");
        AssertUtils.nonNull(releaseInventoryCmd.getPin(),"操作人不能为空");

        if(!duccConfig.getAngelStationInventoryConfig().getAngelStationInventoryGlobalOpen()){
            log.info("StationApplicationImpl.releaseInventory 不走库存逻辑");
            return true;
        }

        ReleaseInventoryContext releaseInventoryContext = StationApplicationConverter.ins.convertToReleaseInventoryContext(releaseInventoryCmd);
        return angelStationInventoryDomainService.releaseInventory(releaseInventoryContext);
    }

    /**
     * 扣减-服务站库存
     * @param reduceInventoryCmd
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.angel.service.impl.StationApplicationImpl.reduceInventory")
    @Transactional
    public Boolean reduceInventory(ReduceInventoryCmd reduceInventoryCmd) {
        //参数校验
        AssertUtils.nonNull(reduceInventoryCmd,"参数不能为空");
        AssertUtils.nonNull(reduceInventoryCmd.getPin(),"操作人不能为空");
        AssertUtils.nonNull(reduceInventoryCmd.getAngelStationId(),"服务站id不能为空!!!");
        AssertUtils.nonNull(reduceInventoryCmd.getBusinessId(),"业务id不能为空");
        AssertUtils.nonNull(reduceInventoryCmd.getBusinessType(),"业务类型不能为空");
        AssertUtils.nonNull(reduceInventoryCmd.getInventoryChannelNo(),"库存渠道编码不能为空");

        if(!duccConfig.getAngelStationInventoryConfig().getAngelStationInventoryGlobalOpen()){
            log.info("StationApplicationImpl.reduceInventory 不走库存逻辑");
            return true;
        }

        String lockKey = RedisKeyEnum.getRedisKey(RedisKeyEnum.ANGEL_STATION_INVENTORY_LOCK_KEY_PREFIX,reduceInventoryCmd.getBusinessType(),reduceInventoryCmd.getBusinessId(),AngelStationInventoryStatusEnum.RELEASE.getType());
        Boolean lock = redisLockUtil.tryLock(lockKey, UUID.fastUUID().toString(),  RedisKeyEnum.ANGEL_STATION_INVENTORY_LOCK_KEY_PREFIX.getExpireTime(), RedisKeyEnum.ANGEL_STATION_INVENTORY_LOCK_KEY_PREFIX.getExpireTimeUnit());

        if(!lock){
            throw new BusinessException(AngelErrorCode.REPEAT_DO);
        }

        try{
            JdhInventoryPreemptionRecord jdhInventoryPreemptionRecord = jdhInventoryPreemptionRecordRepository.findByBusinessId(reduceInventoryCmd.getBusinessId(),
                    reduceInventoryCmd.getBusinessType(),
                    Collections.singletonList(AngelStationInventoryStatusEnum.RELEASE.getType()),
                    reduceInventoryCmd.getInventoryChannelNo()
                    );
            if(jdhInventoryPreemptionRecord==null){
                throw new BusinessException(AngelErrorCode.ANGEL_INVENTORY_CHECK_OP_PARAM_ERROR_MESSAGE.formatDescription("扣减库存时,未查询到库存单!!!"));
            }
            if(!jdhInventoryPreemptionRecord.getAngelStationId().equals(reduceInventoryCmd.getAngelStationId())){
                //扣减的服务站和之前预定扣减的服务站不是同一个
                reduceNewAndReleaseOld(reduceInventoryCmd, jdhInventoryPreemptionRecord);
            }else{
                // 如果入参有日期和时间，与库存表中时间进行对比，不一致的，释放旧的库存，预占新的库存
                if (StringUtils.isNotBlank(reduceInventoryCmd.getScheduleDay())
                        && StringUtils.isNotBlank(reduceInventoryCmd.getScheduleTime())) {
                    List<TimeIntervalIntersection.TimeInterval> timeIntervalList = TimeIntervalIntersection.hitTimeIntervals(new TimeIntervalIntersection.TimeInterval(LocalTime.parse(reduceInventoryCmd.getScheduleTime().split("-")[0],DateTimeFormatter.ofPattern(CommonConstant.HM))
                            ,LocalTime.parse(reduceInventoryCmd.getScheduleTime().split("-")[1],DateTimeFormatter.ofPattern(CommonConstant.HM))));
                    boolean isSameDateTime = false;
                    // 同一天，继续判断是否为同一时段
                    if (jdhInventoryPreemptionRecord.getScheduleDay().equalsIgnoreCase(reduceInventoryCmd.getScheduleDay())) {
                        for (TimeIntervalIntersection.TimeInterval timeInterval : timeIntervalList) {
                            String scheduleTime = timeInterval.getStart().toString() + "-" + timeInterval.getEnd().toString();
                            if (jdhInventoryPreemptionRecord.getScheduleTime().equalsIgnoreCase(scheduleTime)) {
                                isSameDateTime = true;
                                break;
                            }
                        }
                    }
                    // 修改预约时间扣库存
                    if (Boolean.FALSE.equals(isSameDateTime)) {
                        reduceNewAndReleaseOld(reduceInventoryCmd, jdhInventoryPreemptionRecord);
                        return true;
                    } else {
                        // 如果时间一致，并且已经是扣减状态，幂等处理
                        if (AngelStationInventoryStatusEnum.REDUCE.getType().equals(jdhInventoryPreemptionRecord.getRecordStatus())) {
                            return true;
                        }
                    }
                }
                //扣减的服务站和之前预定扣减的服务站同一个;维护库存单状态和修改时间
                ReduceInventoryContext reduceInventoryContext = StationApplicationConverter.ins.convertToReduceInventoryContext(reduceInventoryCmd);
                ///此处明确扣减之前的库存单
                reduceInventoryContext.setInventoryId(jdhInventoryPreemptionRecord.getPreeRecordId());
                angelStationInventoryDomainService.reduceInventory(reduceInventoryContext);
            }
            return true;
        }finally {
            redisLockUtil.unLock(lockKey);
        }

    }

    /**
     * 修改-服务站库存
     *
     * @param reduceInventoryCmd
     * @return
     */
    @Override
    @LogAndAlarm
    @Transactional
    public Boolean modifyInventory(ModifyInventoryCmd reduceInventoryCmd) {
        if(!duccConfig.getAngelStationInventoryConfig().getAngelStationInventoryGlobalOpen()){
            log.info("StationApplicationImpl.modifyInventory 不走库存逻辑");
            return true;
        }

        //参数校验
        AssertUtils.hasText(reduceInventoryCmd.getBusinessId(), "业务号不能为空");
        AssertUtils.nonNull(reduceInventoryCmd.getAngelStationId(), "服务站不能为空");
        AssertUtils.nonNull(reduceInventoryCmd.getScheduleBeginTime(), "预约开始时间不能为空");
        AssertUtils.nonNull(reduceInventoryCmd.getScheduleEndTime(), "预约截止时间不能为空");
        AssertUtils.nonNull(reduceInventoryCmd.getModifyType(), "调整类型不能为空");
        AssertUtils.nonNull(reduceInventoryCmd.getInventoryChannelNo(), "渠道编码不能为空");
        AssertUtils.nonNull(reduceInventoryCmd.getModifyReemptionNum(), "调整数量不能为空");
        if(InventoryModifyTypeEnum.APPOINTMENT_TIME.getType().equals(reduceInventoryCmd.getModifyType())) {
            AssertUtils.nonNull(reduceInventoryCmd.getAfterScheduleBeginTime(), "预约开始时间不能为空");
            AssertUtils.nonNull(reduceInventoryCmd.getAfterScheduleEndTime(), "预约截止时间不能为空");
        }else if(InventoryModifyTypeEnum.ANGEL_STATION.getType().equals(reduceInventoryCmd.getModifyType())) {
            AssertUtils.nonNull(reduceInventoryCmd.getAfterAngelStationId(), "调整后服务站编码不能为空");
        }else if(InventoryModifyTypeEnum.INVENTORY_CHANNEL.getType().equals(reduceInventoryCmd.getModifyType())) {
            AssertUtils.nonNull(reduceInventoryCmd.getAfterInventoryChannelNo(), "调整后库存渠道不能为空");
        }else{
            throw new BusinessException(BusinessErrorCode.ILLEGAL_ARG_ERROR);
        }

        //分布式锁防重复扣减
        ModifyInventoryContext modifyInventoryContext = ModifyInventoryContextConvert.INS.convertToModifyInventoryContext(reduceInventoryCmd);
        String lockKey = RedisKeyEnum.getRedisKey(RedisKeyEnum.MODIFY_INVENTORY_PREFIX_KEY,reduceInventoryCmd.getBusinessId(), reduceInventoryCmd.getAngelStationId(), modifyInventoryContext.getScheduleDay(), modifyInventoryContext.getScheduleTime(), reduceInventoryCmd.getInventoryChannelNo());
        try{
            if(!redisLockUtil.tryLock(lockKey, "1", 10L)) {
                throw new BusinessException(SystemErrorCode.GENERATE_ID_PRODUCTION_ERROR);
            }

            //修改库存
            List<JdhInventoryPreemptionRecord> jdhInventoryPreemptionRecordList = angelStationInventoryDomainService.modifyInventory(modifyInventoryContext);
            if(CollectionUtils.isEmpty(jdhInventoryPreemptionRecordList)) {
                log.error("StationApplicationImpl -> modifyInventory, 没有扣减，modifyInventoryContext={}", JSON.toJSONString(modifyInventoryContext));
                throw new BusinessException(AngelErrorCode.ANGEL_INVENTORY_CHECK_OP_PARAM_ERROR_MESSAGE.formatDescription("库存扣减失败!!!"));
            }

            //预占记录
            PreemptionInventoryContext preemptionInventoryContext = new PreemptionInventoryContext();
            preemptionInventoryContext.setPin(modifyInventoryContext.getPin());
            preemptionInventoryContext.setScheduleTime(StringUtils.isNotBlank(modifyInventoryContext.getAfterScheduleTime()) ? modifyInventoryContext.getAfterScheduleTime() : modifyInventoryContext.getScheduleTime());
            preemptionInventoryContext.setScheduleDay(StringUtils.isNotBlank(modifyInventoryContext.getAfterScheduleDay()) ? modifyInventoryContext.getAfterScheduleDay() : modifyInventoryContext.getScheduleDay());
            preemptionInventoryContext.setAngelStationId(Objects.nonNull(modifyInventoryContext.getAngelStationId()) ? modifyInventoryContext.getAngelStationId() : modifyInventoryContext.getAngelStationId());
            preemptionInventoryContext.setPreemptionNum(modifyInventoryContext.getModifyReemptionNum());
            preemptionInventoryContext.setBusinessType(jdhInventoryPreemptionRecordList.get(0).getBusinessType());
            preemptionInventoryContext.setInventoryChannelNo(Objects.nonNull(modifyInventoryContext.getAfterInventoryChannelNo()) ? modifyInventoryContext.getAfterInventoryChannelNo() : modifyInventoryContext.getInventoryChannelNo());
            preemptionInventoryContext.setBusinessNo(jdhInventoryPreemptionRecordList.get(0).getBusinessNo());
            Long preeInventoryId = angelStationInventoryDomainService.preemptionInventory(preemptionInventoryContext);

            //处理库存扣减
            if(AngelStationInventoryStatusEnum.REDUCE.getType().equals(jdhInventoryPreemptionRecordList.get(0).getRecordStatus())) {
                ReduceInventoryContext reduceInventoryContext = new ReduceInventoryContext();
                reduceInventoryContext.setInventoryId(preeInventoryId);
                reduceInventoryContext.setBusinessId(jdhInventoryPreemptionRecordList.get(0).getBusinessNo());
                reduceInventoryContext.setBusinessType(jdhInventoryPreemptionRecordList.get(0).getBusinessType());
                reduceInventoryContext.setPin(reduceInventoryCmd.getPin());
                angelStationInventoryDomainService.reduceInventory(reduceInventoryContext);
            }
        }catch (Exception ex) {
            log.error("StationApplicationImpl -> modifyInventory,修改库存失败", ex);
            return false;
        }finally {
            redisLockUtil.unLock(lockKey);
        }
        return true;
    }

    /**
     * 初始化库存数据
     * @param initAngelStationInventoryCmd
     * @return
     */
    @Override
    public Boolean initAngelStationInventory(InitAngelStationInventoryCmd initAngelStationInventoryCmd) {
        angelStationInventoryHelper.initAngelStationInventory(initAngelStationInventoryCmd.getAngelStationId(), initAngelStationInventoryCmd.getInventoryChannelNo());
        return true;
    }

    /**
     * 查询服务站列表
     *
     * @param angelStationRequest
     * @return
     */
    @Override
    public List<AngelStationDto> queryAngelStationList(QueryAngelStationRequest angelStationRequest) {
        AngelStationPageQuery angelStationPageQuery = new AngelStationPageQuery();
        angelStationPageQuery.setAngelStationId(StringUtils.isNotBlank(angelStationRequest.getStationId()) ? Long.valueOf(angelStationRequest.getStationId()) : null);
        angelStationPageQuery.setAngelStationIdList(CollectionUtils.isNotEmpty(angelStationRequest.getAngelStationIdList()) ? angelStationRequest.getAngelStationIdList() : null);
        angelStationPageQuery.setAngelStationName(angelStationRequest.getAngelStationName());
        angelStationPageQuery.setStationModeType(angelStationRequest.getStationModeType());
        angelStationPageQuery.setPageNum(angelStationRequest.getPageNum());
        angelStationPageQuery.setPageSize(angelStationRequest.getPageSize());
        angelStationPageQuery.setStationId(angelStationRequest.getStationId2());
        angelStationPageQuery.setAngelStationStatus(angelStationRequest.getAngelStationStatus());
        angelStationPageQuery.setProvinceCode(angelStationRequest.getProvinceCode());
        angelStationPageQuery.setCityCode(angelStationRequest.getCityCode());
        angelStationPageQuery.setDistrictCode(angelStationRequest.getDistrictCode());
        Page<JdhStation> pageList = jdhStationRepository.findPageList(angelStationPageQuery);
        if(Objects.isNull(pageList)){
            return null;
        }
        return StationApplicationConverter.ins.convertToAngelStationDtoList(pageList.getRecords());
    }

    /**
     * job执行
     * @param executeJobCmd
     */
    @Override
    public void executorJob(ExecuteJobCmd executeJobCmd) {
        JobRegistry.getInstance().getJobScheduleController(executeJobCmd.getJobName()).triggerJob();
    }

    @Override
    public List<SkuAndStationRefDto> querySkuAndStationRef(QuerySkuAndStationRefRequest querySkuAndStationRefRequest) {
        log.info("[StationApplicationImpl.querySkuAndStationRef],querySkuAndStationRefRequest={}", JSON.toJSONString(querySkuAndStationRefRequest));
        QuerySkuAndStationRefContext querySkuAndStationRefContext = StationApplicationConverter.ins.convertToQuerySkuAndStationRefContext(querySkuAndStationRefRequest);
        Map<Long, List<JdhStationSkuRel>> map =  jdhMapDomainService.querySkuAndStationRef(querySkuAndStationRefContext);
        List<SkuAndStationRefDto> skuAndStationRefDtos =  StationApplicationConverter.ins.convertToSkuAndStationRefDtos(map);
        return skuAndStationRefDtos;
    }

    /**
     *
     * @param angelGeoQuery
     * @param modeType
     * @return
     */
    private List<JdhAngelDto> getAngelListByGeo(AngelGeoQuery angelGeoQuery, Integer modeType) {
        AngelStationAddressQueryContext queryContext = StationApplicationConverter.ins.convertToAngelStationAddressQueryContext(angelGeoQuery);
        queryContext.setModeType(modeType);
        if (Objects.equals(modeType, 2)) {
            queryContext.setSkuNos(Sets.newHashSet());
        }
        log.info("[StationApplicationImpl.queryAngelListByGeo],queryContext={}", JSON.toJSONString(queryContext));
        try {
            Map<String, List<JdhStation>> stringListMap = jdhMapDomainService.queryAddressStationList(queryContext);
            if (MapUtils.isEmpty(stringListMap)) {
                return Lists.newArrayList();
            }
            List<Long> angelStationIdList = stringListMap.values().stream()
                    .flatMap(Collection::stream)
                    .map(JdhStation::getAngelStationId)
                    .distinct()
                    .collect(Collectors.toList());
            AngelStationRequest request = new AngelStationRequest();
            request.setStationIdList(angelStationIdList);
            List<JdhAngelDto> list = angelApplication.queryByStationId(request);
            return CollectionUtils.isNotEmpty(list) ? list : Lists.newArrayList();
        } catch (Throwable e) {
            log.error("[StationApplicationImpl.queryAngelListByGeo] error", e);
        }
        return Lists.newArrayList();
    }


    /**
     * 扣减新库存，是否旧库存
     *
     */
    @Transactional
    public void reduceNewAndReleaseOld(ReduceInventoryCmd reduceInventoryCmd, JdhInventoryPreemptionRecord jdhInventoryPreemptionRecord) {
        log.info("StationApplicationImpl.reduceInventory reduceInventoryCmd={}, jdhInventoryPreemptionRecord={}", JSON.toJSONString(reduceInventoryCmd), JSON.toJSONString(jdhInventoryPreemptionRecord));
        ///预占库存1
        PreemptionInventoryCmd preemptionInventoryCmd = StationApplicationConverter.ins.convertToPreemptionInventoryCmd(reduceInventoryCmd,jdhInventoryPreemptionRecord);
        if (StringUtils.isNotBlank(reduceInventoryCmd.getScheduleDay()) && StringUtils.isNotBlank(reduceInventoryCmd.getScheduleTime())) {
            preemptionInventoryCmd.setScheduleDay(reduceInventoryCmd.getScheduleDay());
            preemptionInventoryCmd.setScheduleTime(reduceInventoryCmd.getScheduleTime());
        } else {
            if(StringUtils.isNotBlank(jdhInventoryPreemptionRecord.getPlanScheduleTime())){
                //如果表中记录了用户计划预约时间,则使用用户计划预约时间,支持跨时段
                preemptionInventoryCmd.setScheduleTime(jdhInventoryPreemptionRecord.getPlanScheduleTime());
            }
        }
        ///生成预占记录id
        log.info("StationApplicationImpl.reduceInventory preemptionInventoryCmd={}", JSON.toJSONString(preemptionInventoryCmd));
        Long inventoryId = this.preemptionInventory(preemptionInventoryCmd);
        ///确定预占库存2,完善订单号信息
        ConfirmPreemptionInventoryCmd confirmPreemptionInventoryCmd = new ConfirmPreemptionInventoryCmd();
        confirmPreemptionInventoryCmd.setInventoryId(inventoryId);
        confirmPreemptionInventoryCmd.setPin(reduceInventoryCmd.getPin());
        confirmPreemptionInventoryCmd.setBusinessId(reduceInventoryCmd.getBusinessId());
        log.info("StationApplicationImpl.reduceInventory confirmPreemptionInventoryCmd={}", JSON.toJSONString(confirmPreemptionInventoryCmd));
        this.confirmPreemptionInventory(confirmPreemptionInventoryCmd);
        ///扣减预占库存3,修改库存单状态
        ReduceInventoryContext reduceInventoryContext = StationApplicationConverter.ins.convertToReduceInventoryContext(reduceInventoryCmd);
        reduceInventoryContext.setInventoryId(inventoryId);
        angelStationInventoryDomainService.reduceInventory(reduceInventoryContext);
        ///释放之前预占的库存4
        ReleaseInventoryCmd releaseInventoryCmd = StationApplicationConverter.ins.convertToReleaseInventoryCmd(reduceInventoryCmd);
        ///此处明确释放之前的库存单,不能按订单号释放,会查出两条!!!
        releaseInventoryCmd.setInventoryId(jdhInventoryPreemptionRecord.getPreeRecordId());
        log.info("StationApplicationImpl.reduceInventory releaseInventoryCmd={}", JSON.toJSONString(releaseInventoryCmd));
        this.releaseInventory(releaseInventoryCmd);
    }

    /**
     * 按服务站过滤SKU
     * @param stationList
     * @param skuNos
     * @return
     */
    @Override
    public Map<Long, List<JdhStationSkuRel>> filterSkuByStation(List<JdhStation> stationList, Set<Long> skuNos) {
        Map<Long, List<JdhStationSkuRel>> skuIdGroup = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(skuNos)) {
            Set<Long> angelStationIds = stationList.stream().map(JdhStation::getAngelStationId).collect(Collectors.toSet());
            StationSkuRelDbQuery stationSkuRelDbQuery = StationSkuRelDbQuery.builder()
                    .angelStationIds(angelStationIds)
                    .skuIds(skuNos)
                    .build();
            List<JdhStationSkuRel> list = jdhStationSkuRelRepository.findListByEachSku(stationSkuRelDbQuery);
            if (CollectionUtils.isNotEmpty(list)) {
                skuIdGroup = list.stream().collect(Collectors.groupingBy(JdhStationSkuRel::getSkuId));
            }
        }
        return skuIdGroup;
    }

}
