package com.jdh.o2oservice.application.settlement.handler.angel.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.jdh.o2oservice.application.settlement.context.SettlementEbsContext;
import com.jdh.o2oservice.application.settlement.handler.angel.AngelSettlementHandler;
import com.jdh.o2oservice.application.settlement.handler.angel.AngelSettlementHandlerManager;
import com.jdh.o2oservice.application.settlement.service.SettlementEbsApplication;
import com.jdh.o2oservice.common.enums.EbsSettleMainBodyTypeEnum;
import com.jdh.o2oservice.common.enums.EbsSettleSplitTypeEnum;
import com.jdh.o2oservice.common.enums.EbsSettleTypeEnum;
import com.jdh.o2oservice.core.domain.settlement.enums.SettleItemTypeEnum;
import com.jdh.o2oservice.core.domain.settlement.enums.SettleTypeEnum;
import com.jdh.o2oservice.core.domain.settlement.model.AngelSettlementAndEbsDetail;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 护士上门（护士检测+护士服务）--订单的商品收入
 * <AUTHOR>
 * @date 2024
 */
@Slf4j
@Component
public class HomeAngelPlatformFeeInComeHandler implements AngelSettlementHandler {

    /**
     * 结算类型（收入、支出、冲收入） 必填
     */
    private EbsSettleTypeEnum ebsSettleTypeEnum = EbsSettleTypeEnum.INCOME;

    /**
     * 费项拆分维度（商品、商品项目、上门（时段）费项） 必填
     */
    private EbsSettleSplitTypeEnum ebsSettleSplitTypeEnum = EbsSettleSplitTypeEnum.PLATFORM_SERVICE_FEE;

    /**
     * 结算对象, 非必填默认JD
     */
    private EbsSettleMainBodyTypeEnum ebsSettleMainBodyTypeEnum = EbsSettleMainBodyTypeEnum.JD;
    /**
     * 结算推送ebs
     */
    @Autowired
    private SettlementEbsApplication settlementEbsApplication;

    /**
     * 结算前置计算构建对象
     *
     * @param angelSettlementAndEbsDetail
     * @return ebs
     */
    @Override
    public void prepareSettlementEbs(AngelSettlementAndEbsDetail angelSettlementAndEbsDetail) {
        log.info("HomeAngelPlatformFeeInComeHandler#prepareSettlementEbs settlementEbsContext={}", JSON.toJSONString(angelSettlementAndEbsDetail));
        SettlementEbsContext settlementEbsContext = new SettlementEbsContext();
        settlementEbsContext.setBusinessModeEnum(angelSettlementAndEbsDetail.getBusinessModeEnum());
        settlementEbsContext.setEbsSettleTypeEnum(ebsSettleTypeEnum);
        settlementEbsContext.setEbsSettleSplitTypeEnum(ebsSettleSplitTypeEnum);
        settlementEbsContext.setEbsSettleMainBodyTypeEnum(ebsSettleMainBodyTypeEnum);
        settlementEbsContext.setSendEbsData(angelSettlementAndEbsDetail.getSendEbsData());
        settlementEbsContext.setExtBusinessModel(angelSettlementAndEbsDetail);
        settlementEbsApplication.sendToEbs(settlementEbsContext);
    }

    /**
     * 结算前置计算构建对象
     *
     * @param angelSettlementAndEbsDetail
     */
    @Override
    public void afterEbsSend(AngelSettlementAndEbsDetail angelSettlementAndEbsDetail) {
        log.info("HomeAngelPlatformFeeInComeHandler#afterEbsSend settlementEbsContext={}", JSON.toJSONString(angelSettlementAndEbsDetail));

    }

    /**
     * 结算前置计算构建对象
     *
     * @return ebs
     */
    @Override
    public List<String> settlementSceneCode() {
        String angelTestIncome = EbsSettleSplitTypeEnum.PLATFORM_SERVICE_FEE.getType().toString() + SettleTypeEnum.INCOME.getType();
        return Stream.of(angelTestIncome).collect(Collectors.toList());
    }

    /**
     */
    @Override
    public void afterPropertiesSet() {
        if (CollUtil.isEmpty(this.settlementSceneCode())) {
            return;
        }
        for (String sceneCode : this.settlementSceneCode()) {
            AngelSettlementHandlerManager.register(sceneCode, this);
        }
    }
}
