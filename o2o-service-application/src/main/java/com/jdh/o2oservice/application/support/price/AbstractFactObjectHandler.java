package com.jdh.o2oservice.application.support.price;

import com.alibaba.fastjson.JSON;
import com.jd.ump.profiler.proxy.Profiler;
import com.jdh.o2oservice.base.exception.ArgumentsException;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.SystemException;
import com.jdh.o2oservice.core.domain.support.price.context.PricingServiceCalculateContext;
import com.jdh.o2oservice.core.domain.support.price.handler.PricingServiceFactObjectHandler;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.util.StopWatch;

import java.util.Objects;

/**
 * @ClassName AbstractFactObjectHandler
 * @Description
 * <AUTHOR>
 * @Date 2025/5/21 13:29
 **/
@Slf4j
public abstract class AbstractFactObjectHandler implements PricingServiceFactObjectHandler {

    /**
     * 获取事实对象
     * @param context
     * @return
     */
    @Override
    public Object getFactObject(PricingServiceCalculateContext context) {
        StopWatch stopWatch = new StopWatch();
        String handlerKey = this.getClass().getName();
        try {
            log.info("{},start", handlerKey);
            // 执行逻辑
            stopWatch.start();
            Object proceed = getData(context);
            stopWatch.stop();
            log.info("{},end,用时{}毫秒,result:{}", handlerKey, stopWatch.getTotalTimeMillis(), JSON.toJSONString(proceed));
            return proceed;
        } catch (BusinessException be) {
            log.error(handlerKey + ",businessException,", be);
            log.info(handlerKey + ",用时{}毫秒", stopWatch.getTotalTimeMillis());
            throw be;
        } catch (Throwable throwable) {
            log.error(handlerKey + ",throwable,", throwable);
            log.info(handlerKey + ",用时{}毫秒", stopWatch.getTotalTimeMillis());
            throw throwable;
        } finally {
            if (stopWatch.isRunning()) {
                stopWatch.stop();
            }
        }
    }

    protected abstract Object getData(PricingServiceCalculateContext context);
}