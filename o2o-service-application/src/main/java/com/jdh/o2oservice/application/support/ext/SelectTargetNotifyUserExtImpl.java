package com.jdh.o2oservice.application.support.ext;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.core.domain.support.reach.ext.ReachServiceSelectUserExt;
import com.jdh.o2oservice.core.domain.support.reach.ext.dto.ReachUser;
import com.jdh.o2oservice.core.domain.support.reach.ext.param.SelectReachUserParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @ClassName SelectTargetNotifyUserExtImpl
 * @Description
 * <AUTHOR>
 * @Date 2025/6/10 16:03
 **/
@Component
@Slf4j
public class SelectTargetNotifyUserExtImpl implements ReachServiceSelectUserExt {

    /**
     *
     */
    @Resource
    private DuccConfig duccConfig;

    /**
     * 圈定触达人员信息
     * @param param
     * @return
     */
    @Override
    public List<ReachUser> selectUsers(SelectReachUserParam param) {
        log.info("SelectTargetNotifyUserExtImpl -> selectUsers param={}", JSON.toJSONString(param));
        Map<String, JSONObject> targetNotifyUserConfigMap = duccConfig.getTargetNotifyUserConfigMap();
        log.info("SelectTargetNotifyUserExtImpl -> selectUsers targetNotifyUserConfigMap={}", JSON.toJSONString(targetNotifyUserConfigMap));
        if (MapUtil.isEmpty(targetNotifyUserConfigMap) || StringUtils.isBlank(param.getEventCode()) || Objects.isNull(param.getTemplateId())) {
            return new ArrayList<>();
        }
        //获取触达用户配置信息
        String key = param.getTemplateId() + "_" + param.getEventCode();
        log.info("SelectTargetNotifyUserExtImpl -> selectUsers key={}", key);

        JSONObject jsonObject = targetNotifyUserConfigMap.get(key);
        if (Objects.isNull(jsonObject) || CollectionUtils.isEmpty(jsonObject.getJSONArray("atUsers"))) {
            return new ArrayList<>();
        }
        //遍历组装返回值
        List<ReachUser> users = new ArrayList<>();

        JSONArray jsonArray = jsonObject.getJSONArray("atUsers");
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject object = jsonArray.getJSONObject(i);
            if (Objects.isNull(object)) {
                continue;
            }
            ReachUser user = new ReachUser();
            user.setApp(object.getString("app"));
            user.setPin(object.getString("pin"));
            user.setNickname(object.getString("nickname"));
            user.setUserType(object.getString("userType"));
            user.setPhone(object.getString("phone"));
            user.setExtend(object.getString("extend"));
            user.setGroupId(jsonObject.getString("groupId"));
            users.add(user);
        }
        //返回
        return users;
    }

    /**
     * 方法id
     * @return
     */
    @Override
    public String functionId() {
        return "selectTargetNotifyUser";
    }
}