package com.jdh.o2oservice.application.settlement.service;

import com.jdh.o2oservice.core.domain.medpromise.event.MedicalPromiseEventBody;
import com.jdh.o2oservice.core.domain.settlement.bo.ServerSettleAmountBo;
import com.jdh.o2oservice.core.domain.settlement.context.AngelServiceFinishSettlementContext;
import com.jdh.o2oservice.export.settlement.cmd.AngelInviteActivitySettlementCmd;
import com.jdh.o2oservice.export.trade.query.JdServiceSettleParam;

import java.util.Map;


/**
 * JdServiceSettleApplication 服务结算
 *
 * <AUTHOR>
 * @version 2024/5/8 23:15
 **/
public interface JdServiceSettleApplication {

    /**
     * 派单预估结算价
     * @param jdServiceSettleParam
     * @return
     */
    ServerSettleAmountBo getOrderSettleAmount(JdServiceSettleParam jdServiceSettleParam);

    /**
     * 护士完成单个服务开始结算
     * @param body
     */
    void angelServiceFinishSettleAndEbs(MedicalPromiseEventBody body);

    /**
     * 退款成功开始结算
     * @param context
     */
    void angelRefundSuccSettleAndEbs(AngelServiceFinishSettlementContext context);

    /**
     *
     */
    void orderFinishState(MedicalPromiseEventBody body);
    /**
     * 作废成功开始结算
     * @param context
     */
    void invalidVoucherSettleAndEbs(AngelServiceFinishSettlementContext context);

    /**
     *
     * @param promiseId
     * @return
     */
    String findAngelWorkSettleSnapshot(Long promiseId);

    /**
     * 护士邀请活动结算
     * @param cmd
     * @return
     */
    Boolean angelInviteActivitySettlement(AngelInviteActivitySettlementCmd cmd);

    /**
     * 清洗商品和项目结算价
     * @param ossKey
     * @return
     */
    Boolean cleanSkuItemSettlementPrice(String ossKey);

    /**
     * 派单预估结算价
     * @param jdServiceSettleParam
     * @return
     */
    Map<String, ServerSettleAmountBo> getOrderSettleAmountDiagram(JdServiceSettleParam jdServiceSettleParam);
}
