package com.jdh.o2oservice.application.angelpromise.ability.impl;

import com.alibaba.fastjson.JSON;
import com.jdh.o2oservice.application.angelpromise.ability.AngelWorkFinishServiceAbility;
import com.jdh.o2oservice.base.processor.MapAutowiredKey;
import com.jdh.o2oservice.common.enums.BusinessModeEnum;
import com.jdh.o2oservice.common.enums.ServiceTypeEnum;
import com.jdh.o2oservice.core.domain.angelpromise.context.AngelTaskExtStatusContext;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelTask;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelWork;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @ClassName HomeTestNoLaboratoryAngelWorkFinishServiceAbility
 * @Description
 * <AUTHOR>
 * @Date 2025/7/7 15:43
 **/
@Slf4j
@Service
public class HomeTestNoLaboratoryAngelWorkFinishServiceAbility implements AngelWorkFinishServiceAbility, MapAutowiredKey {

    /**
     * homeTestAngelWorkFinishServiceAbility
     */
    @Resource
    private HomeTestAngelWorkFinishServiceAbility homeTestAngelWorkFinishServiceAbility;

    @Override
    public AngelTaskExtStatusContext execute(AngelWork angelWork, List<AngelTask> angelTaskList) {
        log.info("HomeTestNoLaboratoryAngelWorkFinishServiceAbility execute forward, angelWork={} angelTaskList={}", JSON.toJSONString(angelWork), JSON.toJSONString(angelTaskList));
        return homeTestAngelWorkFinishServiceAbility.execute(angelWork, angelTaskList);
    }

    @Override
    public String getMapKey() {
        return "angelWorkFinishService"+"_"+ BusinessModeEnum.ANGEL_TEST_NO_LABORATORY.getCode() + "_" + ServiceTypeEnum.TEST.getServiceType();
    }
}
