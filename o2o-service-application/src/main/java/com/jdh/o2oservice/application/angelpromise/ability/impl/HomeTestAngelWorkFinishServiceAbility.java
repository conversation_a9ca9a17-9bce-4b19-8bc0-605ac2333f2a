package com.jdh.o2oservice.application.angelpromise.ability.impl;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Sets;
import com.jdh.o2oservice.application.angelpromise.ability.AngelWorkFinishServiceAbility;
import com.jdh.o2oservice.application.angelpromise.service.AngelServiceRecordApplication;
import com.jdh.o2oservice.base.event.EventCoordinator;
import com.jdh.o2oservice.base.event.factory.EventFactory;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.processor.MapAutowiredKey;
import com.jdh.o2oservice.common.enums.BusinessModeEnum;
import com.jdh.o2oservice.common.enums.ServiceTypeEnum;
import com.jdh.o2oservice.core.domain.angelpromise.bo.AngelTaskExtStateBo;
import com.jdh.o2oservice.core.domain.angelpromise.context.AngelTaskExtStatusContext;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelBizExtStatusEnum;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelPromiseBizErrorCode;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelShipStatusEnum;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelShip;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelTask;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelWork;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelShipRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.query.AngelShipDBQuery;
import com.jdh.o2oservice.core.domain.settlement.enums.SettleEventTypeEnum;
import com.jdh.o2oservice.export.angelpromise.query.AngelServiceRecordQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class HomeTestAngelWorkFinishServiceAbility implements AngelWorkFinishServiceAbility, MapAutowiredKey {

    @Resource
    private AngelServiceRecordApplication angelServiceRecordApplication;

    @Resource
    private AngelShipRepository angelShipRepository;

    @Resource
    private EventCoordinator eventCoordinator;

    @Override
    public AngelTaskExtStatusContext execute(AngelWork angelWork, List<AngelTask> angelTaskList) {
        log.info("HomeTestAngelWorkFinishServiceAbility execute angelWork={} angelTaskList={}", JSON.toJSONString(angelWork), JSON.toJSONString(angelTaskList));
        AngelServiceRecordQuery serviceRecordConfigQuery = AngelServiceRecordQuery.builder().workId(angelWork.getWorkId()).build();
        // 校验是否开启了护理单配置
        Boolean serviceRecordConfigFlag = angelServiceRecordApplication.checkAngelServiceRecordConfig(serviceRecordConfigQuery);
        if (serviceRecordConfigFlag){
            AngelServiceRecordQuery serviceRecordFinishQuery = AngelServiceRecordQuery.builder().workId(angelWork.getWorkId()).build();
            // 校验护理单是否已完成
            Boolean serviceRecordFinishFlag = angelServiceRecordApplication.checkAngelServiceRecordFinish(serviceRecordFinishQuery);
            if (!serviceRecordFinishFlag){
                throw new BusinessException(AngelPromiseBizErrorCode.ANGEL_SERVICE_RECORD_NOT_SUBMIT);
            }
        }

        // 校验样本已送检
        this.checkAngelShipDelivered(angelWork.getWorkId());

        // 更新业务状态
        List<AngelTaskExtStateBo> taskExtStateBoList = angelTaskList.stream()
                .map(task -> AngelTaskExtStateBo.builder()
                        .taskId(task.getTaskId())
                        .taskExtStatus(AngelBizExtStatusEnum.SERVICE_FINISH.getType()).build())
                .collect(Collectors.toList());
        AngelTaskExtStatusContext statusContext = AngelTaskExtStatusContext.builder()
                .workId(angelWork.getWorkId())
                .angelTaskExtStateBoList(taskExtStateBoList)
                .build();

        // 服务完成后会发送服务完成事件，结算域监听此事件触发结算
        eventCoordinator.publish(EventFactory.newDefaultEvent(angelWork, SettleEventTypeEnum.HOME_TEST_ANGEL_SETTLE_BY_SERVICE_COMPLETE, null));
        log.info("HomeTestAngelWorkFinishServiceAbility execute statusContext={}", JSON.toJSONString(statusContext));
        return statusContext;
    }

    /**
     * 校验样本已送检
     * @param workId
     */
    private void checkAngelShipDelivered(Long workId) {
        AngelShipDBQuery angelShipDBQuery = AngelShipDBQuery.builder()
                .workId(workId)
                .status(Sets.newHashSet(AngelShipStatusEnum.getValidStatus()))
                .build();
        List<AngelShip> angelShipList = angelShipRepository.findList(angelShipDBQuery);
        log.info("HomeTestAngelWorkFinishServiceAbility angelShipDBQuery={}, angelShipList={}", JSON.toJSONString(angelShipDBQuery), JSON.toJSONString(angelShipList));
        angelShipList.forEach(s->{
            // type运单类型：1=自行寄送，2=平台运力' 3=三方运力
            if (Objects.equals(1, s.getType())){
                if (!Objects.equals(AngelShipStatusEnum.ORDER_SHIP_FINISH.getShipStatus(), s.getShipStatus())){
                    throw new BusinessException(AngelPromiseBizErrorCode.ANGEL_SHIP_SAMPLE_NOT_DELIVERED);
                }
            }else if (Arrays.asList(2,3).contains(s.getType())){
                if (!Arrays.asList(AngelShipStatusEnum.DELIVERING_GOODS.getShipStatus(),AngelShipStatusEnum.ORDER_SHIP_FINISH.getShipStatus())
                        .contains(s.getShipStatus())){
                    throw new BusinessException(AngelPromiseBizErrorCode.ANGEL_SHIP_SAMPLE_NOT_DELIVERED);
                }
            }
        });
    }

    @Override
    public String getMapKey() {
        return "angelWorkFinishService"+"_"+BusinessModeEnum.ANGEL_TEST.getCode() + "_" +ServiceTypeEnum.TEST.getServiceType();

    }
}
