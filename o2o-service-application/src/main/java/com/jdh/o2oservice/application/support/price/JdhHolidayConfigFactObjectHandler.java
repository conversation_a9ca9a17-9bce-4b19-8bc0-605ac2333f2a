package com.jdh.o2oservice.application.support.price;

import cn.hutool.core.convert.Convert;
import com.googlecode.aviator.AviatorEvaluator;
import com.googlecode.aviator.Expression;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.ducc.model.HolidayConfig;
import com.jdh.o2oservice.base.enums.HolidayTypeEnum;
import com.jdh.o2oservice.base.util.SpringUtil;
import com.jdh.o2oservice.base.util.TimeFormat;
import com.jdh.o2oservice.base.util.TimeUtils;
import com.jdh.o2oservice.core.domain.support.basic.enums.PricingServiceFactObjectEnum;
import com.jdh.o2oservice.core.domain.support.price.context.PricingServiceCalculateContext;
import com.jdh.o2oservice.core.domain.support.price.handler.PricingServiceFactObjectHandler;
import com.jdh.o2oservice.export.promise.dto.PromiseDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Objects;

/**
 * @ClassName JdhHolidayFactObjectHandler
 * @Description
 * <AUTHOR>
 * @Date 2025/5/18 15:33
 **/
@Component
@Slf4j
public class JdhHolidayConfigFactObjectHandler extends AbstractFactObjectHandler {

    /**
     * 履约单数据
     */
    @Resource
    private JdhPromiseFactObjectHandler promiseFactObjectHandler;

    /**
     * 获取事实对象
     * @param context
     * @return
     */
    @Override
    public Object getData(PricingServiceCalculateContext context) {
        boolean containsKey = context.getFactObjectMap().containsKey(getMapKey());
        if (containsKey) {
            return context.getFactObjectMap().get(getMapKey());
        }
        //前置依赖履约单数据，先从上下文中获取，没有则调用接口获取，并放入上下文
        Object factObject = promiseFactObjectHandler.getFactObject(context);
        if (Objects.isNull(factObject)) {
            return null;
        }
        PromiseDto promiseDto = Convert.convert(PromiseDto.class, factObject);
        if (Objects.isNull(promiseDto.getAppointmentTime()) || Objects.isNull(promiseDto.getAppointmentTime().getAppointmentStartTime())) {
            context.getFactObjectMap().put(getMapKey(), null);
            return null;
        }
        String date = TimeUtils.dateTimeToStr(promiseDto.getAppointmentTime().getAppointmentStartTime(), TimeFormat.SHORT_PATTERN_LINE);
        DuccConfig duccConfig = SpringUtil.getBean(DuccConfig.class);
        HolidayConfig holidayConfig = duccConfig.getHolidayConfigMap().get(date);
        //return HolidayTypeEnum.HOLIDAY.getType().equals(holidayConfig.getSType());
        context.getFactObjectMap().put(getMapKey(), holidayConfig);
        return holidayConfig;
    }

    /**
     * 获取map key
     * @return
     */
    @Override
    public String getMapKey() {
        return PricingServiceFactObjectEnum.HOLIDAY_CONFIG.getCode();
    }

    /*public static void main(String[] args) {

        HashMap<String, Object> factObjectMap = new HashMap<String, Object>();

        HolidayConfig holidayConfig = new HolidayConfig();
        holidayConfig.setSType("01");
        factObjectMap.put(PricingServiceFactObjectEnum.HOLIDAY_CONFIG.getCode(), holidayConfig);
        // 定义Aviator表达式
        String expression = "'01' == holidayConfig.SType ? 10 : 0";

        // 编译表达式
        Expression compiledExp = AviatorEvaluator.compile(expression, true);

        // 计算结果
        System.out.println("Total Amount: " + compiledExp.execute(factObjectMap));
    }*/
}