package com.jdh.o2oservice.application.angelpromise.event;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jdh.o2oservice.application.medicalpromise.MedicalPromiseApplication;
import com.jdh.o2oservice.application.support.price.JdhHistorySettleAmountFactObjectHandler;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.event.Event;
import com.jdh.o2oservice.base.event.EventConsumerRegister;
import com.jdh.o2oservice.base.event.EventConsumerRetryTemplate;
import com.jdh.o2oservice.base.event.WrapperEventConsumer;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.model.DomainEnum;
import com.jdh.o2oservice.base.util.SpringUtil;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelPromiseBizErrorCode;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelWorkStatusEnum;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelWork;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelWorkRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.query.AngelWorkDBQuery;
import com.jdh.o2oservice.core.domain.settlement.bo.ServerSettleAmountBo;
import com.jdh.o2oservice.core.domain.settlement.rpc.SettleOrderInfoRpc;
import com.jdh.o2oservice.core.domain.support.basic.enums.PricingServiceFactObjectEnum;
import com.jdh.o2oservice.core.domain.support.basic.enums.PricingServiceSceneEnum;
import com.jdh.o2oservice.core.domain.support.price.context.PricingServiceCalculateContext;
import com.jdh.o2oservice.core.domain.trade.enums.TradeEventTypeEnum;
import com.jdh.o2oservice.core.domain.trade.event.OrderRefundAngelAountEventBody;
import com.jdh.o2oservice.export.medicalpromise.dto.MedicalPromiseDTO;
import com.jdh.o2oservice.export.medicalpromise.query.MedicalPromiseRequest;
import com.jdh.o2oservice.export.support.command.PricingServiceCalculateCmd;
import com.jdh.o2oservice.export.support.dto.PricingServiceCalculateResultDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @ClassName:AngelPromiseSettleEvent
 * @Description:
 * @Author: yaoqinghai
 * @Date: 2024/6/11 16:23
 * @Vserion: 1.0
 **/
@Component
@Slf4j
public class AngelPromiseSettleEvent {

    /**
     * 事件消费注册
     */
    @Resource
    private EventConsumerRegister eventConsumerRegister;

    @Resource
    private AngelWorkRepository angelWorkRepository;

    /**
     * settleOrderInfoRpc
     */
    @Resource
    private SettleOrderInfoRpc settleOrderInfoRpc;

    /**
     * 检测单数据
     */
    @Resource
    private MedicalPromiseApplication medicalPromiseApplication;

    /**
     * 获取历史结算金额事实对象
     */
    @Resource
    private JdhHistorySettleAmountFactObjectHandler historySettleAmountFactObjectHandler;

    /**
     * 注册consumer
     */
    @PostConstruct
    public void registerEventConsumer() {

        /** 工单逆向护士结算价变更事件 */
        eventConsumerRegister.register(TradeEventTypeEnum.ORDER_REFUND_ANGEL_SETTLE_AMOUNT,
                WrapperEventConsumer.newInstance(DomainEnum.ANGEL_PROMISE, "angelWorkRefundAmount", this::angelWorkRefundAmount, Boolean.TRUE, EventConsumerRetryTemplate.defaultInstance()));

    }

    /**
     * 护士退款金额变更事件
     *
     * @param event
     */
    private void angelWorkRefundAmount(Event event) {
        log.info("[AngelPromiseSettleEvent.angelWorkRefundAmount],工单发送退款,结算价发生变更!event={}", JSON.toJSONString(event));
        OrderRefundAngelAountEventBody eventBody = JSON.parseObject(event.getBody(), OrderRefundAngelAountEventBody.class);
        if(Objects.isNull(eventBody) || Objects.isNull(eventBody.getPromiseId())){
            log.error("[AngelPromiseSettleEvent.angelWorkRefundAmount],没有发送预估价变更!");
            return;
        }
        AngelWorkDBQuery angelWorkDBQuery = new AngelWorkDBQuery();
        angelWorkDBQuery.setPromiseId(eventBody.getPromiseId());
        List<AngelWork> angelWorkList = angelWorkRepository.findList(angelWorkDBQuery);
        if(CollectionUtils.isEmpty(angelWorkList)){
            log.error("[AngelPromiseSettleEvent.angelWorkRefundAmount],工单信息不存在!");
            return;
        }

        //预估收入扣减
        AngelWork angelWork = angelWorkList.get(0);

        String serviceFeeSnapshot = settleOrderInfoRpc.getOrderSettleSnapshotAmount(Long.valueOf(angelWork.getAngelId()), angelWork.getJdOrderId(), eventBody.getPromiseId());
        ServerSettleAmountBo serverSettleAmountBo = JSONObject.parseObject(serviceFeeSnapshot,ServerSettleAmountBo.class);
        Map<String, BigDecimal> feeAmountMap = serverSettleAmountBo.getFeeAmountMap();
        //结算优化新逻辑
        if(CollUtil.isNotEmpty(feeAmountMap)){
            //如果快照有feeAmountMap则说明走了结算优化新逻辑，使用新逻辑计算护士结算金额
            angelWork.setAngelCharge(this.dealNewSettleAmount(angelWork,serverSettleAmountBo));
        }else{//历史处理工单预估收入逻辑
            if(Objects.isNull(eventBody.getSettleSubtractAmount())){
                log.error("[AngelPromiseSettleEvent.angelWorkRefundAmount],没有发送预估价变更!");
                return;
            }
            if(Objects.isNull(angelWork.getAngelCharge()) || angelWork.getAngelCharge().compareTo(eventBody.getSettleSubtractAmount()) < 0){
                log.error("[AngelPromiseSettleEvent.angelWorkRefundAmount],扣减的服务费大于预估服务费,无法操作!");
                return;
            }
//        angelWork.addAngelChargeDetail(eventBody.getSettleSubtractAmount().negate(), new BigDecimal(0));
            angelWork.setAngelCharge(angelWork.getAngelCharge().subtract(eventBody.getSettleSubtractAmount()));
        }
        int save = angelWorkRepository.save(angelWork);

        if(save <= CommonConstant.ZERO) {
            throw new BusinessException(AngelPromiseBizErrorCode.DEDUCT_AMOUNT_ERROR);
        }
    }

    /**
     *
     * @param angelWork
     * @param serverSettleAmountBo
     */
    private BigDecimal dealNewSettleAmount(AngelWork angelWork, ServerSettleAmountBo serverSettleAmountBo){
        //获取有效的检测单数据列表
        List<MedicalPromiseDTO> medicalPromiseDTOList = medicalPromiseApplication.queryMedicalPromiseList(MedicalPromiseRequest.builder().promiseId(angelWork.getPromiseId()).invalid(false).freezeQuery(false).build());
        if (CollectionUtils.isEmpty(medicalPromiseDTOList)) {
            //如果没有有效的检测单数据，说明已经全退，查询历史已结算的金额返回给工单
            PricingServiceCalculateContext context = new PricingServiceCalculateContext();
            context.setAngelId(Long.valueOf(angelWork.getAngelId()));
            context.setOrderId(angelWork.getJdOrderId());
            context.setPromiseId(angelWork.getPromiseId());
            Object result = historySettleAmountFactObjectHandler.getFactObject(context);
            return Objects.isNull(result) ? BigDecimal.ZERO : Convert.toBigDecimal(result);
        }

        //计算后续服务完成的护士结算价
        Map<String,String> refundStatusRatioMapping = matchSettleRatio(AngelWorkStatusEnum.COMPLETED.getType());
        PricingServiceCalculateCmd cmd = new PricingServiceCalculateCmd();
        cmd.setVerticalCode(angelWork.getVerticalCode());
        cmd.setScene(PricingServiceSceneEnum.ANGEL_SETTLEMENT_PRICE_SNAPSHOT_REFRESH_CALCULATE.getScene());
        cmd.setOrderId(angelWork.getJdOrderId());
        cmd.setPromiseId(angelWork.getPromiseId());
        cmd.setAngelId(Long.valueOf(angelWork.getAngelId()));

        Map<String, Object> factObjectMap = new HashMap<>();
        factObjectMap.putAll(serverSettleAmountBo.getFeeAmountMap());
        factObjectMap.put(PricingServiceFactObjectEnum.SKU_SERVICE_AMOUNT_MAP.getCode(), serverSettleAmountBo.getAngelSkuServiceAmountMap());
        factObjectMap.put(PricingServiceFactObjectEnum.MATERIAL_FEE_CONFIG.getCode(), serverSettleAmountBo.getMaterialFeeConfig());
        // 需要结算的检测单列表
        factObjectMap.put(PricingServiceFactObjectEnum.MEDICAL_PROMISE_LIST.getCode(), medicalPromiseDTOList);
        // 是否是最后一个服务
        factObjectMap.put(PricingServiceFactObjectEnum.IS_LAST_SERVICE.getCode(), true);
        // 当前节点的费项结算比例 ducc配置
        factObjectMap.put(PricingServiceFactObjectEnum.ANGEL_SETTLEMENT_FEE_RATIO_CONFIG.getCode(), refundStatusRatioMapping);
        cmd.setFactObjectMap(factObjectMap);
        PricingServiceCalculateResultDto calculateResultDto = settleOrderInfoRpc.calculatePriceForDetail(cmd);
        //查询是否有已经结算的金额，金额累计返回
        BigDecimal historySettleAmount = Objects.nonNull(calculateResultDto) && Objects.nonNull(calculateResultDto.getHistorySettleAmount()) ? calculateResultDto.getHistorySettleAmount() : BigDecimal.ZERO;
        return Objects.nonNull(calculateResultDto) ? calculateResultDto.getTotalPrice().add(historySettleAmount) : historySettleAmount;
    }

    /**
     * 计算整单退款数据
     * @param workStatus
     */
    private Map<String,String> matchSettleRatio(Integer workStatus){
        // 退款比例
        Map<String, Map<String,String>> refundStatusFeeAmoutRatio = SpringUtil.getBean(DuccConfig.class).getRefundStatusFeeAmoutRatio();
        Map<String,String> refundStatusRatioMapping = JSONUtil.toBean(
                JSONUtil.toJsonStr(refundStatusFeeAmoutRatio.get(String.valueOf(workStatus))), new TypeReference<Map<String, String>>() {}, true);
        return refundStatusRatioMapping;
    }

}
