package com.jdh.o2oservice.application.trade.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.jd.i18n.order.dict.FieldKeyEnum;
import com.jd.jim.cli.Cluster;
import com.jd.medicine.base.common.util.DateUtil;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jd.purchase.domain.assist.SkuRelationInfo;
import com.jd.purchase.domain.old.bean.Cart;
import com.jd.purchase.domain.old.bean.SKU;
import com.jd.purchase.utils.serializer.helper.SerializersHelper;
import com.jdh.o2oservice.application.product.service.ProductApplication;
import com.jdh.o2oservice.application.trade.convert.JdOrderConverter;
import com.jdh.o2oservice.application.trade.service.JdOrderApplication;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.constatnt.NumConstant;
import com.jdh.o2oservice.base.enums.OrderStatusEnum;
import com.jdh.o2oservice.base.enums.OrderTypeEnum;
import com.jdh.o2oservice.base.enums.RedisKeyEnum;
import com.jdh.o2oservice.base.event.EventCoordinator;
import com.jdh.o2oservice.base.event.factory.EventFactory;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode;
import com.jdh.o2oservice.common.enums.HasAddedEnum;
import com.jdh.o2oservice.common.enums.IsAddedEnum;
import com.jdh.o2oservice.common.enums.ServiceTypeNewEnum;
import com.jdh.o2oservice.core.domain.support.patient.model.Patient;
import com.jdh.o2oservice.core.domain.support.patient.repository.PatientRepository;
import com.jdh.o2oservice.core.domain.trade.context.OrderInfoQueryContext;
import com.jdh.o2oservice.core.domain.trade.enums.OrderExtTypeEnum;
import com.jdh.o2oservice.core.domain.trade.enums.ServiceHomeTypeEnum;
import com.jdh.o2oservice.core.domain.trade.enums.TradeEventTypeEnum;
import com.jdh.o2oservice.core.domain.trade.event.OrderSplitEventBody;
import com.jdh.o2oservice.core.domain.trade.factory.JdOrderFactory;
import com.jdh.o2oservice.core.domain.trade.model.*;
import com.jdh.o2oservice.core.domain.trade.repository.db.JdOrderExtRepository;
import com.jdh.o2oservice.core.domain.trade.repository.db.JdOrderItemRepository;
import com.jdh.o2oservice.core.domain.trade.repository.db.JdOrderRepository;
import com.jdh.o2oservice.core.domain.trade.rpc.OrderInfoRpc;
import com.jdh.o2oservice.core.domain.trade.vo.JdOrderExtendVo;
import com.jdh.o2oservice.core.domain.trade.vo.JdSkuRelationInfoVo;
import com.jdh.o2oservice.export.product.dto.JdhSkuDto;
import com.jdh.o2oservice.export.product.query.JdhSkuListRequest;
import com.jdh.o2oservice.export.product.query.JdhSkuRequest;
import com.jdh.o2oservice.export.trade.dto.OrderUserActionDTO;
import com.jdh.o2oservice.export.trade.dto.SkuItemDTO;
import com.jdh.o2oservice.export.trade.query.JdOrderSaveParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * JdOrderApplicationImpl JdOrderApplication
 *
 * <AUTHOR>
 * @version 2024/4/23 23:21
 **/
@Slf4j
@Service
public class JdOrderApplicationImpl implements JdOrderApplication {

    /**
     * orderInfoRpc
     */
    @Resource
    private OrderInfoRpc orderInfoRpc;

    /**
     * jdOrderRepository
     */
    @Resource
    private JdOrderRepository jdOrderRepository;
    /**
     * eventCoordinator
     */
    @Resource
    private EventCoordinator eventCoordinator;
    /**
     *jdOrderItemRepository
     */
    @Resource
    private JdOrderItemRepository jdOrderItemRepository;
    /**
     * jdOrderExtRepository
     */
    @Resource
    private JdOrderExtRepository jdOrderExtRepository;

    /**
     *
     */
    @Resource
    private PatientRepository patientRepository;

    @Resource
    private ProductApplication productApplication;
    
    @Resource
    private Cluster jimClient;

    /**
     *
     * @param jdOrderSaveParam
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public JdOrder saveJdOrder(JdOrderSaveParam jdOrderSaveParam) {
        log.info("[JdOrderApplicationImpl->saveJdOrder], orderId={}", jdOrderSaveParam.getOrderId());
        OrderInfoQueryContext context = JdOrderConverter.INSTANCE.convertToOrderInfoQueryContext(jdOrderSaveParam);
        fillPatients(context);
        JdOrder jdOrder = orderInfoRpc.queryOrderInfo(context);
        // 初始化垂直身份
        initVerticalCode(jdOrder, jdOrderSaveParam);
        // 初始化过期时间
        initSkuExpireDate(jdOrder);
        // 初始化支付信息
        initJdOrderMoneyList(jdOrder);
        //处理商品加项逻辑
        handleAddSkuLogic4SaveOrder(jdOrder);
        // 处理赠品逻辑
        handleGiftOrder(jdOrder);
        log.info("[JdOrderApplicationImpl->saveJdOrder], jdOrder={}", JSON.toJSONString(jdOrder));
        // 订单ID已存在场景不重复新增订单
        if (Objects.nonNull(jdOrderRepository.find(jdOrder.getIdentifier()))) {
            log.error("[JdOrderApplicationImpl->saveJdOrder], 订单号已存在，jdOrder={}", JSON.toJSONString(jdOrder));
            //throw new BusinessException(BusinessErrorCode.ORDER_DUMPLICATE, "订单号"+jdOrder.getOrderId()+"已存在");
        } else {
            jdOrderRepository.save(jdOrder);
        }

        return jdOrder;
    }

    /**
     * 初始化订单支付信息
     */
    private void initJdOrderMoneyList(JdOrder jdOrder){
        // 未支付不初始化订单支付信息
        if(Objects.isNull(jdOrder) || Objects.isNull(jdOrder.getPaymentTime())){
            return;
        }
        Long skuId = null;
        if(CollectionUtils.isNotEmpty(jdOrder.getJdOrderItemList())){
            skuId = jdOrder.getJdOrderItemList().stream().findFirst().get().getSkuId();
        }
        jdOrder.setJdOrderMoneyList(JdOrderFactory.createOrderMoneyInfo(jdOrder.getOrderId(), skuId, jdOrder.getJdOrderItemList()));

    }

    /**
     * 初始化过期时间
     * @param jdOrder
     */
    private void initSkuExpireDate(JdOrder jdOrder) {
        if(Objects.isNull(jdOrder)){
            return ;
        }
        List<JdOrderItem> jdOrderItemList = jdOrder.getJdOrderItemList();
        if (CollectionUtils.isEmpty(jdOrderItemList)) {
            return ;
        }
        JdhSkuListRequest request = new JdhSkuListRequest();
        request.setSkuIdList(jdOrderItemList.stream().map(JdOrderItem::getSkuId).collect(Collectors.toSet()));
        Map<Long, JdhSkuDto> jdhSkuDtoMap = productApplication.queryJdhSkuInfoList(request);
        if (Objects.isNull(jdhSkuDtoMap)) {
            log.error("[JdOrderApplicationImpl->initSkuExpireDate],未查询到商品信息!orderId={}，request={}", jdOrder.getOrderId(), JSON.toJSONString(request));
            return ;
        }
        jdOrderItemList.forEach(s -> {
            JdhSkuDto jdhSkuDto = jdhSkuDtoMap.get(s.getSkuId());
            if(Objects.nonNull(jdhSkuDto) && Objects.nonNull(jdhSkuDto.getBuyValidPeriod())){
                s.setSkuExpireDate(DateUtil.addDays(new Date(), jdhSkuDto.getBuyValidPeriod()));
            }
        });
    }

    /**
     * 初始化垂直身份
     * @param jdOrder
     */
    private void initVerticalCode(JdOrder jdOrder, JdOrderSaveParam jdOrderSaveParam) {
        StringBuilder stringBuilder = new StringBuilder();
        Integer partnerSource = jdOrder.getPartnerSource();
        if (Objects.nonNull(partnerSource)) {
            stringBuilder.append(partnerSource);
        }
        List<JdOrderItem> jdOrderItemList = jdOrder.getJdOrderItemList();
        if (CollectionUtils.isEmpty(jdOrderItemList)) {
            throw new BusinessException(BusinessErrorCode.SKU_INFO_QUERY_FAIL);
        }
        Long skuId = jdOrderItemList.get(0).getSkuId();
        JdhSkuRequest request = new JdhSkuRequest();
        request.setSkuId(skuId);
        JdhSkuDto jdhSkuDto = productApplication.queryJdhSkuInfo(request);
        if (Objects.isNull(jdhSkuDto)) {
            log.error("[JdOrderApplicationImpl->initVerticalCode],未查询到商品信息!orderId={}，skuId={}", jdOrder.getOrderId(), skuId);
            throw new BusinessException(BusinessErrorCode.SKU_INFO_QUERY_FAIL);
        }
        if(isServiceUpgradeSelected(jdOrder, jdOrderSaveParam)){
            stringBuilder.append(ServiceTypeNewEnum.ANGEL_TEST.getType());
        } else {
            stringBuilder.append(jdhSkuDto.getServiceType());
        }
        String extend = jdOrder.getExtend();
        JdOrderExtendVo jdOrderExtendVo = new JdOrderExtendVo();
        if (StringUtils.isNotBlank(extend)) {
            jdOrderExtendVo = JSON.parseObject(extend, JdOrderExtendVo.class);
        }
        if(isServiceUpgradeSelected(jdOrder, jdOrderSaveParam)){
            jdOrderExtendVo.setSkuServiceType(ServiceTypeNewEnum.ANGEL_TEST.getType());
            jdOrderExtendVo.setServiceUpgradeSelected(true);
        } else {
            jdOrderExtendVo.setSkuServiceType(jdhSkuDto.getServiceType());
        }
        jdOrder.setExtend(JsonUtil.toJSONString(jdOrderExtendVo));

        /**
         * 非4号单场景补充immediately
         */
        if (!OrderTypeEnum.XFYL_VTP_VIRTUAL_ORDER_TYPE.getType().equals(jdOrder.getOrderType())) {
            stringBuilder.append(CommonConstant.ZERO_STR);
        }
        ServiceHomeTypeEnum serviceHomeTypeEnum = ServiceHomeTypeEnum.getServiceHomeTypeEnum(stringBuilder.toString());
        log.info("[JdOrderApplicationImpl->initVerticalCode] orderId={}，stringBuilder={}",jdOrder.getOrderId(), stringBuilder.toString());
        if (Objects.nonNull(serviceHomeTypeEnum)) {
            jdOrder.setVerticalCode(serviceHomeTypeEnum.getVerticalCode());
            jdOrder.setServiceType(serviceHomeTypeEnum.getServiceType());
            if(Objects.nonNull(jdOrder.getJdOrderItemList())){
                jdOrder.getJdOrderItemList().stream().forEach(e -> {
                    e.setVerticalCode(serviceHomeTypeEnum.getVerticalCode());
                    e.setServiceType(serviceHomeTypeEnum.getServiceType());
                });
            }
        }
    }

    private Boolean isServiceUpgradeSelected(JdOrder jdOrder, JdOrderSaveParam jdOrderSaveParam){
        // 没有选中护士升级
        if(Objects.isNull(jdOrderSaveParam.getServiceUpgradeSelected()) || !jdOrderSaveParam.getServiceUpgradeSelected()){
            return false;
        }
        // 是否包含动态调整费
        List<JdOrderExt> serviceFeeList = jdOrder.getJdOrderExtList().stream().filter(s -> OrderExtTypeEnum.SERVICE_FEE_INFO.getType().equals(s.getExtType())).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(serviceFeeList) && serviceFeeList.size() == 1){
            List<JdOrderServiceFeeInfo> jdOrderServiceFeeInfoList = JSON.parseArray(serviceFeeList.get(0).getExtContext(), JdOrderServiceFeeInfo.class);
            if(CollectionUtils.isNotEmpty(jdOrderServiceFeeInfoList)){
                List<JdOrderServiceFeeInfo> subJdOrderServiceFeeInfoList = jdOrderServiceFeeInfoList.stream().filter(s -> s.getAggregateSubType() == 39013).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(subJdOrderServiceFeeInfoList)){
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 处理加项商品逻辑
     * @param jdOrder
     */
    private void handleAddSkuLogic4SaveOrder(JdOrder jdOrder) {
        try {
            // 非200不处理
            if(!OrderTypeEnum.XFYL_ORDER_TYPE.getType().equals(jdOrder.getOrderType())){
                return;
            }
            //获取用户动作的信息
            String redisKey = RedisKeyEnum.getRedisKey(RedisKeyEnum.EXECUTE_ACTION_BUY_NOW_PIN_SKU_LIST_KEY, jdOrder.getUserPin());
            OrderUserActionDTO orderUserActionDTO = JsonUtil.parseObject(jimClient.get(redisKey), OrderUserActionDTO.class);
            if(Objects.isNull(orderUserActionDTO) || CollectionUtils.isEmpty(orderUserActionDTO.getBundleInfoDTOList())){
                return ;
            }
            Map<String,Integer> skuIsAddedMap=new HashMap<>();
            Integer hasAdded= HasAddedEnum.NOT_HAS_ADDED.getValue();
            List<SkuItemDTO> skuItemDtoList = orderUserActionDTO.getBundleInfoDTOList().get(0).getSkuItemDTOList();
            for (SkuItemDTO skuItemDTO : skuItemDtoList) {
                if(BooleanUtils.isFalse(skuItemDTO.getIsSelected())){
                    continue;
                }
                skuIsAddedMap.put(skuItemDTO.getId(),skuItemDTO.getIsAdded());
                if (IsAddedEnum.IS_ADDED.getValue().equals(skuItemDTO.getIsAdded())){
                    hasAdded =HasAddedEnum.HAS_ADDED.getValue();
                }
            }
            jdOrder.setHasAdded(hasAdded);
            for (JdOrderItem jdOrderItem : jdOrder.getJdOrderItemList()) {
                jdOrderItem.setIsAdded(skuIsAddedMap.get(String.valueOf(jdOrderItem.getSkuId())));
            }
        }catch (Exception e){
            log.error("JdOrderApplicationImpl.handleAddSkuLogic4SaveOrder has error",e);
        }

    }

    /**
     * 查询订单 + item + ext
     *
     * @param orderId
     * @return
     */
    @Override
    public JdOrder queryJdOrderAndItemExt(Long orderId) {
        JdOrder jdOrder = jdOrderRepository.find(new JdOrderIdentifier(orderId));
        if(Objects.nonNull(jdOrder)){
            List<JdOrderItem> jdOrderItems = jdOrderItemRepository.listByOrderId(orderId);
            jdOrder.setJdOrderItemList(jdOrderItems);
            List<JdOrderExt> jdOrderExtList = jdOrderExtRepository.findJdOrderExtList(orderId);
            jdOrder.setJdOrderExtList(jdOrderExtList);
        }
        return jdOrder;
    }

    /**
     * 查询订单
     *
     * @param orderId
     * @return
     */
    @Override
    public JdOrder queryJdOrderByOrderId(Long orderId) {
        JdOrder jdOrder = jdOrderRepository.find(new JdOrderIdentifier(orderId));
        return jdOrder;
    }

    /**
     *
     * @param orderIdList
     * @return
     */
    @Override
    public List<JdOrder> findOrdersByList(List<Long> orderIdList) {
        List<JdOrder> ordersByList = jdOrderRepository.findOrdersByList(orderIdList);
        return ordersByList;
    }

    /**
     * 保存订单ext信息
     *
     * @param jdOrderExt
     * @return
     */
    @Override
    public void saveJdOrderExt(JdOrderExt jdOrderExt) {
        JdOrderExt JdOrderExtTemp = jdOrderExtRepository.findJdOrderExtDetail(jdOrderExt.getOrderId(),jdOrderExt.getExtType());
        if(Objects.isNull(JdOrderExtTemp)){
            jdOrderExtRepository.save(jdOrderExt);
        }else{
            jdOrderExtRepository.updateJdOrderExt(jdOrderExt);
        }
    }

    /**
     * 查询订单扩展信息明细
     *
     * @param orderId
     * @param extType
     * @return
     */
    @Override
    public JdOrderExt findJdOrderExtDetail(Long orderId, String extType) {
        return jdOrderExtRepository.findJdOrderExtDetail(orderId,extType);
    }

    /**
     * 订单取消支付
     * @param orderId
     */
    @Override
    public void orderCancelPay(Long orderId) {
        JdOrder jdOrder = jdOrderRepository.find(new JdOrderIdentifier(orderId));
        if(Objects.nonNull(jdOrder) && OrderStatusEnum.ORDER_WAIT_PAY.getStatus().equals(jdOrder.getOrderStatus())){
            // 更新订单状态
            jdOrder.setOrderStatus(OrderStatusEnum.ORDER_CANCEL.getStatus());
            jdOrderRepository.cancalOrderByOrderId(jdOrder);
            eventCoordinator.publish(EventFactory.newDefaultEvent(jdOrder, TradeEventTypeEnum.SELF_CANCEL_ORDER, new OrderSplitEventBody(jdOrder)));
        }
    }

    @Override
    public Boolean isProcessingOrder(Long orderId) {
        List<JdOrder> jdOrderList = jdOrderRepository.findOrderListByOrderId(new JdOrderIdentifier(orderId));
        if(CollectionUtils.isEmpty(jdOrderList)){
            return Boolean.FALSE;
        }
        return jdOrderList.stream().anyMatch(s -> OrderStatusEnum.isCanReceive(s.getOrderStatus()));
    }


    private void fillPatients(OrderInfoQueryContext context){
        if(context.getPatientIds() == null || context.getPatientIds().size() == 0){
            return ;
        }
        List<Patient> patients = patientRepository.findByPin(context.getUserPin());
        if(patients == null){
            return;
        }
        context.setPatients(patients.stream().filter(patient -> context.getPatientIds().contains(patient.getPatientId())).collect(Collectors.toList()));
    }

    /**
     * 填充赠品对应的主品信息
     *
     * @param jdOrder jdOrder
     */
    private void handleGiftOrder(JdOrder jdOrder) {
        try {
            // 如果扩展字段为空直接返回
            if(jdOrder == null || jdOrder.getOriginParentOrderId() == null || jdOrder.getOriginParentOrderId() <= 0 || StringUtils.isBlank(jdOrder.getExtend())) {
                log.info("TradeApplicationImpl handleGiftOrder 父订单为空,不执行");
                return;
            }
            JdOrderExtendVo jdOrderExtendVo = JSON.parseObject(jdOrder.getExtend(), JdOrderExtendVo.class);
            if (jdOrderExtendVo == null || !NumConstant.NUM_2.equals(jdOrderExtendVo.getWareType())) {
                log.info("TradeApplicationImpl handleGiftOrder 非买赠类型,不执行");
                return;
            }
            // 订单下sku列表,用于过滤仅消费医疗业务的赠品
            List<Long> orderSkuIds = CollUtil.isEmpty(jdOrder.getJdOrderItemList()) ? null : jdOrder.getJdOrderItemList().stream().map(JdOrderItem::getSkuId).filter(Objects::nonNull).collect(Collectors.toList());
            if (CollUtil.isEmpty(orderSkuIds)) {
                log.info("TradeApplicationImpl handleGiftOrder 订单下商品列表为空,不执行");
                return;
            }
            // 1.查到订单大数据
            Map<String,Object> dataMap = orderInfoRpc.getOrderData(jdOrder.getOriginParentOrderId());
            if (CollUtil.isEmpty(dataMap)) {
                log.info("TradeApplicationImpl handleGiftOrder 通过父订单查到订单大数据为空,不执行");
                return;
            }

            String cartxml = Objects.toString(dataMap.get(FieldKeyEnum.V_CARTXML.getFieldName()));
            // 2.解压缩
            cartxml = com.jd.orderver.component.utils.ZipUtils.gunzip(cartxml);
            // 3.按照返回 serializationType 进行反序列化成对象
            String serializationType = Objects.toString(dataMap.get(FieldKeyEnum.V_CBDFLAG.getFieldName()));
            Cart serializeCart = SerializersHelper.ofString(cartxml, Cart.class, serializationType);
            log.info("TradeApplicationImpl handleGiftOrder findAllSkus={}", JSON.toJSONString(serializeCart.findAllSkus()));
            if (CollUtil.isEmpty(serializeCart.findAllSkus())) {
                log.info("TradeApplicationImpl handleGiftOrder findAllSkus为空,不执行");
                return;
            }
            // sku uuid为主键获取商品信息
            Map<String, SKU> skuUuidMap = serializeCart.findAllSkus().stream().filter(s -> StringUtils.isNotBlank(s.getUuid())).collect(Collectors.toMap(SKU::getUuid, Function.identity(), (o, n) -> o));
            if (CollUtil.isEmpty(skuUuidMap)) {
                log.info("TradeApplicationImpl handleGiftOrder skuUuidMap为空,不执行");
                return;
            }
            log.info("TradeApplicationImpl handleGiftOrder findAllSKURelationInfo={}", JSON.toJSONString(serializeCart.findAllSKURelationInfo()));
            if (CollUtil.isEmpty(serializeCart.findAllSKURelationInfo())) {
                log.info("TradeApplicationImpl handleGiftOrder findAllSKURelationInfo为空,不执行");
                return;
            }
            List<Integer> accessRelType = Stream.of(1, 2, 3, 4).collect(Collectors.toList());
            List<JdSkuRelationInfoVo> list = new ArrayList<>();
            for(SkuRelationInfo skuRelationInfo : serializeCart.findAllSKURelationInfo()) {
                if (skuRelationInfo.getRelationType() == null || !accessRelType.contains(skuRelationInfo.getRelationType())) {
                    log.info("TradeApplicationImpl handleGiftOrder relationType类型不匹配,不记录数据");
                    continue;
                }
                if (CollUtil.isEmpty(skuRelationInfo.getRelationSkuUuidList()) || CollUtil.isEmpty(skuRelationInfo.getMainSkuUuidList())) {
                    log.info("TradeApplicationImpl handleGiftOrder 主赠关系数据为空,不记录数据");
                    continue;
                }
                // 满赠存在多个父级
                List<String> parentSkuUuidList = skuRelationInfo.getMainSkuUuidList();
                if (CollUtil.isEmpty(parentSkuUuidList)) {
                    log.info("TradeApplicationImpl handleGiftOrder 主商品为空,不记录数据");
                    continue;
                }
                List<SKU> parentSkuList = new ArrayList<>();
                for (String parentSkuUuid : parentSkuUuidList) {
                    if (!skuUuidMap.containsKey(parentSkuUuid)) {
                        continue;
                    }
                    parentSkuList.add(skuUuidMap.get(parentSkuUuid));
                }
                if (CollUtil.isEmpty(parentSkuList)) {
                    log.info("TradeApplicationImpl handleGiftOrder 通过uuid反查主商品为空,不记录数据");
                    continue;
                }
                for (String relSkuUuid : skuRelationInfo.getRelationSkuUuidList()) {
                    if (!skuUuidMap.containsKey(relSkuUuid)) {
                        log.info("TradeApplicationImpl handleGiftOrder 主赠关系赠品数据为空,不记录数据");
                        continue;
                    }
                    SKU sku = skuUuidMap.get(relSkuUuid);
                    if (!orderSkuIds.contains(sku.getId())) {
                        log.info("TradeApplicationImpl handleGiftOrder 不在赠品订单商品列表,不记录数据");
                        continue;
                    }
                    JdSkuRelationInfoVo jdSkuRelationInfoVo = new JdSkuRelationInfoVo();
                    jdSkuRelationInfoVo.setRelationSkuId(sku.getId());
                    jdSkuRelationInfoVo.setRelationSkuName(sku.getName());
                    for (SKU parentSku : parentSkuList) {
                        jdSkuRelationInfoVo.setMainSkuId(parentSku.getId());
                        jdSkuRelationInfoVo.setMainSkuName(parentSku.getName());
                        list.add(jdSkuRelationInfoVo);
                    }
                }
            }
            jdOrderExtendVo.setJdSkuRelationInfoVoList(list);
            String extendStr = JSON.toJSONString(jdOrderExtendVo);
            jdOrder.setExtend(extendStr);
            log.info("TradeApplicationImpl handleGiftOrder result={}", extendStr);
        } catch (Exception e) {
            log.error("TradeApplicationImpl handleGiftOrder error ", e);
        }
    }
}
