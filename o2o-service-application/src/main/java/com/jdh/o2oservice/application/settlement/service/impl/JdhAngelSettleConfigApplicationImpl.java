package com.jdh.o2oservice.application.settlement.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jd.medicine.base.common.util.StringUtil;
import com.jdh.o2oservice.application.settlement.convert.JdServiceSettleConvert;
import com.jdh.o2oservice.application.settlement.listener.ImportAngelSettleConfigListener;
import com.jdh.o2oservice.application.settlement.service.JdhAngelSettleConfigApplication;
import com.jdh.o2oservice.application.support.service.FileManageApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.enums.ThreadPoolConfigEnum;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode;
import com.jdh.o2oservice.base.factory.ExecutorPoolFactory;
import com.jdh.o2oservice.base.factory.GenerateIdFactory;
import com.jdh.o2oservice.base.util.AssertUtils;
import com.jdh.o2oservice.base.util.SpringUtil;
import com.jdh.o2oservice.common.enums.AngelTypeEnum;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.core.domain.angel.enums.JobNatureEnum;
import com.jdh.o2oservice.core.domain.settlement.enums.AngelJobNatureEnum;
import com.jdh.o2oservice.core.domain.settlement.enums.SettleErrorCode;
import com.jdh.o2oservice.core.domain.settlement.enums.SettleFeeTypeEnum;
import com.jdh.o2oservice.core.domain.settlement.model.ImportAngelSettleConfig;
import com.jdh.o2oservice.core.domain.settlement.model.JdhSettlementAreaFeeConfig;
import com.jdh.o2oservice.core.domain.settlement.model.JdhSettlementFeeDetailConfig;
import com.jdh.o2oservice.core.domain.settlement.repository.db.JdhSettlementAreaFeeConfigRepository;
import com.jdh.o2oservice.core.domain.settlement.repository.db.JdhSettlementFeeDetailConfigRepository;
import com.jdh.o2oservice.core.domain.settlement.repository.query.JdhAngelSettleAreaFeeQuery;
import com.jdh.o2oservice.core.domain.support.file.enums.FileExportTypeEnum;
import com.jdh.o2oservice.core.domain.support.file.enums.FileOperationStatusEnum;
import com.jdh.o2oservice.core.domain.support.file.model.JdhFile;
import com.jdh.o2oservice.core.domain.support.file.model.JdhFileIdentifier;
import com.jdh.o2oservice.core.domain.support.file.model.JdhImportExportTask;
import com.jdh.o2oservice.core.domain.support.file.repository.db.JdhFileRepository;
import com.jdh.o2oservice.core.domain.support.file.repository.db.JdhFileTaskRepository;
import com.jdh.o2oservice.core.domain.support.file.service.FileManageService;
import com.jdh.o2oservice.export.settlement.cmd.AngelSettleCityConfigFileCmd;
import com.jdh.o2oservice.export.settlement.cmd.AngelSettlementConfigCmd;
import com.jdh.o2oservice.export.settlement.dto.AngelSettlementConfigDto;
import com.jdh.o2oservice.export.support.dto.FilePreSignedUrlDto;
import com.jdh.o2oservice.infrastructure.repository.db.convert.JdhAngelSettlementPoConvert;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * JdhAngelSettleConfigApplicationImpl
 * 护士结算配置
 * <AUTHOR>
 * @version 2025/4/21 12:15
 **/
@Service
@Slf4j
public class JdhAngelSettleConfigApplicationImpl implements JdhAngelSettleConfigApplication {

    /**
     * jdhSettlementAreaFeeConfigRepository
     */
    @Resource
    private JdhSettlementAreaFeeConfigRepository jdhSettlementAreaFeeConfigRepository;
    /**
     * jdhSettlementFeeDetailConfigRepository
     */
    @Resource
    private JdhSettlementFeeDetailConfigRepository jdhSettlementFeeDetailConfigRepository;
    /** */
    @Resource
    private DuccConfig duccConfig;
    /**
     * 全局id
     */
    @Resource
    private GenerateIdFactory generateIdFactory;
    /**
     * 文件服务仓
     */
    @Resource
    JdhFileRepository jdhFileRepository;
    /** */
    @Resource
    private JdhFileTaskRepository jdhFileTaskRepository;
    /**
     * 文件服务
     */
    @Resource
    private FileManageApplication fileManageApplication;
    /**
     *
     */
    @Autowired
    private ExecutorPoolFactory executorPoolFactory;
    /**
     * 服务者
     */
    @Resource
    FileManageService fileManageService;

    /**
     * 当前配置
     */
    private static String ACTIVE;
    /**
     * 初始注入maven环境
     */
    @Value("${spring.profiles.active}")
    public void setActive(String value){
        ACTIVE = value;
    }


    /**
     * 分页护士结算配置
     *
     * @param queryContext
     * @return
     */
    @Override
    public PageDto<AngelSettlementConfigDto> queryAngelSettleConfigPage(JdhAngelSettleAreaFeeQuery queryContext) {
        Page<JdhSettlementAreaFeeConfig> configPage = jdhSettlementAreaFeeConfigRepository.queryPage(queryContext);
        List<JdhSettlementAreaFeeConfig> jdhSettlementAreaFeeConfigs = configPage.getRecords();
        this.convertDetailTo(jdhSettlementAreaFeeConfigs);
        List<AngelSettlementConfigDto> configDtoList = convertToAngelSettlementConfigDto(jdhSettlementAreaFeeConfigs);
        PageDto pageDto = new PageDto();
        pageDto.setList(configDtoList);
        pageDto.setPageSize(configPage.getPages());
        pageDto.setPageNum(configPage.getCurrent());
        pageDto.setTotalPage(configPage.getPages());
        pageDto.setTotalCount(configPage.getTotal());
        return pageDto;
    }

    /**
     *
     * @param jdhSettlementAreaFeeConfigs
     * @return
     */
    private List<AngelSettlementConfigDto> convertToAngelSettlementConfigDto(List<JdhSettlementAreaFeeConfig> jdhSettlementAreaFeeConfigs){
        List<AngelSettlementConfigDto> configDtoList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(jdhSettlementAreaFeeConfigs)) {
            for (JdhSettlementAreaFeeConfig config : jdhSettlementAreaFeeConfigs) {
                AngelSettlementConfigDto dto = JdServiceSettleConvert.ins.convert2AngelSettlementConfigDto(config);
                dto.setAngelTypeDesc(AngelJobNatureEnum.getLabelByValue(config.getSettlementSubjectSubType()));
                configDtoList.add(dto);
            }
        }
        return configDtoList;
    }

    /**
     *
     * @param jdhSettlementAreaFeeConfigs
     */
    private void convertDetailTo(List<JdhSettlementAreaFeeConfig> jdhSettlementAreaFeeConfigs){
        if(CollUtil.isNotEmpty(jdhSettlementAreaFeeConfigs)){
            List<Long> feeConfigIdList = jdhSettlementAreaFeeConfigs.stream().map(JdhSettlementAreaFeeConfig::getFeeConfigId).collect(Collectors.toList());
            List<JdhSettlementFeeDetailConfig> list = jdhSettlementFeeDetailConfigRepository.queryJdhAreaFeeConfigListByFeeIdList(feeConfigIdList);
            if(CollUtil.isNotEmpty(list)){
                Map<Long,List<JdhSettlementFeeDetailConfig>> feeDetailConfigMap = list.stream().collect(Collectors.groupingBy(JdhSettlementFeeDetailConfig::getFeeConfigId));
                jdhSettlementAreaFeeConfigs.forEach(feeConfig -> {
                    List<JdhSettlementFeeDetailConfig> tempList = feeDetailConfigMap.get(feeConfig.getFeeConfigId());
                    getFeeDetail(feeConfig,tempList);
                });
            }
        }
    }

    /**
     *
     * @param feeConfig
     * @param tempList
     */
    private void getFeeDetail(JdhSettlementAreaFeeConfig feeConfig,List<JdhSettlementFeeDetailConfig> tempList){
        if(CollUtil.isNotEmpty(tempList)){
            Map<String,List<JdhSettlementFeeDetailConfig>> feeConfigMap = tempList.stream().collect(Collectors.groupingBy(JdhSettlementFeeDetailConfig::getFeeType));
            List<JdhSettlementFeeDetailConfig> configList = feeConfigMap.get(SettleFeeTypeEnum.ONSITE_FEE.getType());
            BigDecimal feeAmount = BigDecimal.ZERO;
            if(CollUtil.isNotEmpty(configList)){
                feeAmount = configList.get(0).getFeeAmount() == null ? BigDecimal.ZERO : configList.get(0).getFeeAmount();
                feeConfig.setOnSiteFee(feeAmount.toPlainString());
            }
            configList = feeConfigMap.get(SettleFeeTypeEnum.IMMEDIATELY_FF.getType());
            if(CollUtil.isNotEmpty(configList)){
                feeAmount = configList.get(0).getFeeAmount() == null ? BigDecimal.ZERO : configList.get(0).getFeeAmount();
                feeConfig.setImmediatelyFee(feeAmount.toPlainString());
            }
            configList = feeConfigMap.get(SettleFeeTypeEnum.HOLIDAY_FF.getType());
            if(CollUtil.isNotEmpty(configList)){
                feeAmount = configList.get(0).getFeeAmount() == null ? BigDecimal.ZERO : configList.get(0).getFeeAmount();
                feeConfig.setHolidayFee(feeAmount.toPlainString());
            }
            configList = feeConfigMap.get(SettleFeeTypeEnum.NIGHT_DOOR_FEE.getType());
            if(CollUtil.isNotEmpty(configList)){
                feeAmount = configList.get(0).getFeeAmount() == null ? BigDecimal.ZERO : configList.get(0).getFeeAmount();
                feeConfig.setNightDoorFee(feeAmount.toPlainString());
            }
            configList = feeConfigMap.get(SettleFeeTypeEnum.DYNAMIC_ADJUST_FEE.getType());
            if(CollUtil.isNotEmpty(configList)){
                feeAmount = configList.get(0).getFeeAmount() == null ? BigDecimal.ZERO : configList.get(0).getFeeAmount();
                feeConfig.setDynamicAdjustFee(feeAmount.toPlainString());
            }
        }
    }

    /**
     * 保存护士结算配置
     *
     * @param angelSettlementConfigCmd
     * @return
     */
    @Override
    public boolean saveAngelSettleConfig(AngelSettlementConfigCmd angelSettlementConfigCmd) {
        JdhSettlementAreaFeeConfig jdhSettlementAreaFeeConfig = getJdhSettlementAreaFeeConfig(angelSettlementConfigCmd);
        if(!angelSettlementConfigCmd.getCover()){
            JdhAngelSettleAreaFeeQuery queryContext = JdServiceSettleConvert.ins.convert2JdhAngelSettleAreaFeeQuery(angelSettlementConfigCmd);
            queryContext.setNoCountyTownQuery(true);
            List<JdhSettlementAreaFeeConfig> poList = jdhSettlementAreaFeeConfigRepository.queryJdhAreaFeeConfigList(queryContext);
            if(CollUtil.isNotEmpty(poList)){
                AssertUtils.nonNull(null,BusinessErrorCode.ANGEL_FEE_ADDR_EXSIT);
            }
            return jdhSettlementAreaFeeConfigRepository.save(jdhSettlementAreaFeeConfig) > 0 && bulidFeeDetail(jdhSettlementAreaFeeConfig);
        }else{
            JdhAngelSettleAreaFeeQuery queryContext = JdServiceSettleConvert.ins.convert2JdhAngelSettleAreaFeeQuery(angelSettlementConfigCmd);
            queryContext.setNoCountyTownQuery(true);
            List<JdhSettlementAreaFeeConfig> poList = jdhSettlementAreaFeeConfigRepository.queryJdhAreaFeeConfigList(queryContext);
            if(CollUtil.isNotEmpty(poList)){
                jdhSettlementAreaFeeConfig.setFeeConfigId(poList.get(0).getFeeConfigId());
            }
            return bulidUpdateFeeDetail(jdhSettlementAreaFeeConfig);
        }
    }

    /**
     *
     * @param angelSettlementConfigCmd
     * @return
     */
    private JdhSettlementAreaFeeConfig getJdhSettlementAreaFeeConfig(AngelSettlementConfigCmd angelSettlementConfigCmd){
        JdhSettlementAreaFeeConfig jdhSettlementAreaFeeConfig = new JdhSettlementAreaFeeConfig();
        jdhSettlementAreaFeeConfig.setFeeConfigId(generateIdFactory.getId());
        jdhSettlementAreaFeeConfig.setSettlementSubjectType(AngelTypeEnum.NURSE.getType().toString());
        jdhSettlementAreaFeeConfig.setSettlementSubjectSubType(angelSettlementConfigCmd.getAngelType());
        jdhSettlementAreaFeeConfig.setProvinceCode(angelSettlementConfigCmd.getProvinceCode());
        jdhSettlementAreaFeeConfig.setProvinceName(angelSettlementConfigCmd.getProvinceName());
        jdhSettlementAreaFeeConfig.setCityCode(angelSettlementConfigCmd.getCityCode());
        jdhSettlementAreaFeeConfig.setCityName(angelSettlementConfigCmd.getCityName());
        jdhSettlementAreaFeeConfig.setCountyCode(angelSettlementConfigCmd.getCountyCode());
        jdhSettlementAreaFeeConfig.setCountyName(angelSettlementConfigCmd.getCountyName());
        jdhSettlementAreaFeeConfig.setTownCode(angelSettlementConfigCmd.getTownCode());
        jdhSettlementAreaFeeConfig.setTownName(angelSettlementConfigCmd.getTownName());
        jdhSettlementAreaFeeConfig.setOnSiteFee(angelSettlementConfigCmd.getOnSiteFee());
        jdhSettlementAreaFeeConfig.setHolidayFee(angelSettlementConfigCmd.getHolidayFee());
        jdhSettlementAreaFeeConfig.setDynamicAdjustFee(angelSettlementConfigCmd.getDynamicAdjustFee());
        jdhSettlementAreaFeeConfig.setImmediatelyFee(angelSettlementConfigCmd.getImmediatelyFee());
        jdhSettlementAreaFeeConfig.setNightDoorFee(angelSettlementConfigCmd.getNightDoorFee());
        jdhSettlementAreaFeeConfig.setCreateUser(angelSettlementConfigCmd.getOperator());
        jdhSettlementAreaFeeConfig.setUpdateUser(angelSettlementConfigCmd.getOperator());
        jdhSettlementAreaFeeConfig.setBranch(ACTIVE);
        jdhSettlementAreaFeeConfig.setDestCode(StringUtil.isNotBlank(jdhSettlementAreaFeeConfig.getTownCode()) ? jdhSettlementAreaFeeConfig.getTownCode() : StringUtil.isNotBlank(jdhSettlementAreaFeeConfig.getCountyCode()) ? jdhSettlementAreaFeeConfig.getCountyCode() : StringUtil.isNotBlank(jdhSettlementAreaFeeConfig.getCityCode()) ? jdhSettlementAreaFeeConfig.getCityCode() : jdhSettlementAreaFeeConfig.getProvinceCode());
        return jdhSettlementAreaFeeConfig;
    }
    /**
     *
     * @param jdhSettlementAreaFeeConfig
     * @return
     */
    private Boolean bulidFeeDetail(JdhSettlementAreaFeeConfig jdhSettlementAreaFeeConfig) {
        List<JdhSettlementFeeDetailConfig> JdhSettlementFeeDetailConfigs = new ArrayList<>();
        Long feeConfigId = jdhSettlementAreaFeeConfig.getFeeConfigId();
        String fee = jdhSettlementAreaFeeConfig.getOnSiteFee();
        if(StringUtil.isNotBlank(fee)){
            JdhSettlementFeeDetailConfig feeDetailConfig = bulidSettlementFeeDetailConfig(feeConfigId, SettleFeeTypeEnum.ONSITE_FEE.getType(), new BigDecimal(fee));
            JdhSettlementFeeDetailConfigs.add(feeDetailConfig);
        }
        fee = jdhSettlementAreaFeeConfig.getImmediatelyFee();
        if(StringUtil.isNotBlank(fee)){
            JdhSettlementFeeDetailConfig feeDetailConfig = bulidSettlementFeeDetailConfig(feeConfigId, SettleFeeTypeEnum.IMMEDIATELY_FF.getType(), new BigDecimal(fee));
            JdhSettlementFeeDetailConfigs.add(feeDetailConfig);
        }
        fee = jdhSettlementAreaFeeConfig.getHolidayFee();
        if(StringUtil.isNotBlank(fee)){
            JdhSettlementFeeDetailConfig feeDetailConfig = bulidSettlementFeeDetailConfig(feeConfigId, SettleFeeTypeEnum.HOLIDAY_FF.getType(), new BigDecimal(fee));
            JdhSettlementFeeDetailConfigs.add(feeDetailConfig);
        }
        fee = jdhSettlementAreaFeeConfig.getNightDoorFee();
        if(StringUtil.isNotBlank(fee)){
            JdhSettlementFeeDetailConfig feeDetailConfig = bulidSettlementFeeDetailConfig(feeConfigId, SettleFeeTypeEnum.NIGHT_DOOR_FEE.getType(), new BigDecimal(fee));
            JdhSettlementFeeDetailConfigs.add(feeDetailConfig);
        }
        fee = jdhSettlementAreaFeeConfig.getDynamicAdjustFee();
        if(StringUtil.isNotBlank(fee)){
            JdhSettlementFeeDetailConfig feeDetailConfig = bulidSettlementFeeDetailConfig(feeConfigId, SettleFeeTypeEnum.DYNAMIC_ADJUST_FEE.getType(), new BigDecimal(fee));
            JdhSettlementFeeDetailConfigs.add(feeDetailConfig);
        }
        if(CollUtil.isNotEmpty(JdhSettlementFeeDetailConfigs)){
            return jdhSettlementFeeDetailConfigRepository.batchSaveJdhAreaFeeConfig(JdhSettlementFeeDetailConfigs) > 0;
        }

        return true;
    }

    /**
     *
     * @param feeConfigId
     * @param feeType
     * @param feeAmount
     * @return
     */
    private JdhSettlementFeeDetailConfig bulidSettlementFeeDetailConfig(Long feeConfigId, String feeType, BigDecimal feeAmount) {
        JdhSettlementFeeDetailConfig jdhSettlementFeeDetailConfig = new JdhSettlementFeeDetailConfig();
        jdhSettlementFeeDetailConfig.setFeeConfigId(feeConfigId);
        jdhSettlementFeeDetailConfig.setFeeConfigDetailId(generateIdFactory.getId());
        jdhSettlementFeeDetailConfig.setFeeAmount(feeAmount);
        jdhSettlementFeeDetailConfig.setFeeType(feeType);
        jdhSettlementFeeDetailConfig.setBranch(ACTIVE);
        return jdhSettlementFeeDetailConfig;
    }

    /**
     * 更新护士结算配置
     *
     * @param angelSettlementConfigCmd
     * @return
     */
    @Override
    public boolean updateAngelSettleConfig(AngelSettlementConfigCmd angelSettlementConfigCmd) {
        JdhSettlementAreaFeeConfig jdhSettlementAreaFeeConfig = JdServiceSettleConvert.ins.convert2JdhSettlementAreaFeeConfig(angelSettlementConfigCmd);
        jdhSettlementAreaFeeConfig.setFeeConfigId(angelSettlementConfigCmd.getFeeConfigId());
        jdhSettlementAreaFeeConfig.setUpdateUser(angelSettlementConfigCmd.getOperator());
        JdhAngelSettleAreaFeeQuery queryContext = new JdhAngelSettleAreaFeeQuery();
        List<Long> feeConfigIdList = new ArrayList<>();
        feeConfigIdList.add(angelSettlementConfigCmd.getFeeConfigId());
        queryContext.setFeeConfigIdList(feeConfigIdList);
        queryContext.setNoCountyTownQuery(false);
        List<JdhSettlementAreaFeeConfig> poList = jdhSettlementAreaFeeConfigRepository.queryJdhAreaFeeConfigList(queryContext);
        if(CollUtil.isNotEmpty(poList)){
            return bulidUpdateFeeDetail(jdhSettlementAreaFeeConfig);
        }
        return false;
    }

    /**
     * 删除护士结算配置
     *
     * @param angelSettlementConfigCmd
     * @return
     */
    @Override
    public boolean deleteAngelSettlementConfig(AngelSettlementConfigCmd angelSettlementConfigCmd) {
        JdhAngelSettleAreaFeeQuery queryContext = new JdhAngelSettleAreaFeeQuery();
        queryContext.setFeeConfigIdList(angelSettlementConfigCmd.getFeeConfigIdList());
        queryContext.setNoCountyTownQuery(false);
        List<JdhSettlementAreaFeeConfig> poList = jdhSettlementAreaFeeConfigRepository.queryJdhAreaFeeConfigList(queryContext);
        if(CollUtil.isNotEmpty(poList)){
            queryContext.setOperator(angelSettlementConfigCmd.getOperator());
            jdhSettlementAreaFeeConfigRepository.delete(queryContext);
            jdhSettlementFeeDetailConfigRepository.batchDelete(queryContext);
            return true;
        }
        return false;
    }

    /**
     *
     * @param jdhSettlementAreaFeeConfig
     * @return
     */
    private Boolean bulidUpdateFeeDetail(JdhSettlementAreaFeeConfig jdhSettlementAreaFeeConfig) {
        JdhAngelSettleAreaFeeQuery jdhSettlementAreaFeeQuery = new JdhAngelSettleAreaFeeQuery();
        jdhSettlementAreaFeeQuery.setFeeConfigId(String.valueOf(jdhSettlementAreaFeeConfig.getFeeConfigId()));
        List<JdhSettlementFeeDetailConfig> list = jdhSettlementFeeDetailConfigRepository.queryJdhAreaFeeConfigList(jdhSettlementAreaFeeQuery);
        List<JdhSettlementFeeDetailConfig> addSettlementFeeDetailConfigs = new ArrayList<>();
        List<JdhSettlementFeeDetailConfig> updateSettlementFeeDetailConfigs = new ArrayList<>();
        Map<String,List<JdhSettlementFeeDetailConfig>> map = new HashMap<>();
        if(CollUtil.isNotEmpty(list)){
            map = list.stream().collect(Collectors.groupingBy(JdhSettlementFeeDetailConfig::getFeeType));
        }

        dealSettlementFeeDetailConfig(jdhSettlementAreaFeeConfig,jdhSettlementAreaFeeConfig.getOnSiteFee(),map,
                SettleFeeTypeEnum.ONSITE_FEE,addSettlementFeeDetailConfigs,updateSettlementFeeDetailConfigs);

        dealSettlementFeeDetailConfig(jdhSettlementAreaFeeConfig,jdhSettlementAreaFeeConfig.getImmediatelyFee(),map,
                SettleFeeTypeEnum.IMMEDIATELY_FF,addSettlementFeeDetailConfigs,updateSettlementFeeDetailConfigs);

        dealSettlementFeeDetailConfig(jdhSettlementAreaFeeConfig,jdhSettlementAreaFeeConfig.getHolidayFee(),map,
                SettleFeeTypeEnum.HOLIDAY_FF,addSettlementFeeDetailConfigs,updateSettlementFeeDetailConfigs);

        dealSettlementFeeDetailConfig(jdhSettlementAreaFeeConfig,jdhSettlementAreaFeeConfig.getNightDoorFee(),map,
                SettleFeeTypeEnum.NIGHT_DOOR_FEE,addSettlementFeeDetailConfigs,updateSettlementFeeDetailConfigs);

        dealSettlementFeeDetailConfig(jdhSettlementAreaFeeConfig,jdhSettlementAreaFeeConfig.getDynamicAdjustFee(),map,
                SettleFeeTypeEnum.DYNAMIC_ADJUST_FEE,addSettlementFeeDetailConfigs,updateSettlementFeeDetailConfigs);

        jdhSettlementAreaFeeConfigRepository.update(jdhSettlementAreaFeeConfig);
        if(CollUtil.isNotEmpty(addSettlementFeeDetailConfigs)){
            jdhSettlementFeeDetailConfigRepository.batchSaveJdhAreaFeeConfig(addSettlementFeeDetailConfigs);
        }
        if(CollUtil.isNotEmpty(updateSettlementFeeDetailConfigs)){
            for (JdhSettlementFeeDetailConfig updateSettlementFeeDetailConfig : updateSettlementFeeDetailConfigs) {
                jdhSettlementFeeDetailConfigRepository.update(updateSettlementFeeDetailConfig);
            }
        }
        return true;
    }

    /**
     *
     * @param jdhSettlementAreaFeeConfig
     * @param fee
     * @param map
     * @param settleFeeTypeEnum
     * @param addSettlementFeeDetailConfigs
     * @param updateSettlementFeeDetailConfigs
     */
    private void dealSettlementFeeDetailConfig(JdhSettlementAreaFeeConfig jdhSettlementAreaFeeConfig, String fee,
                                               Map<String,List<JdhSettlementFeeDetailConfig>> map,SettleFeeTypeEnum settleFeeTypeEnum,
                                               List<JdhSettlementFeeDetailConfig> addSettlementFeeDetailConfigs,
                                               List<JdhSettlementFeeDetailConfig> updateSettlementFeeDetailConfigs){
        Long feeConfigId = jdhSettlementAreaFeeConfig.getFeeConfigId();

        if(map.containsKey(settleFeeTypeEnum.getType())){
            List<JdhSettlementFeeDetailConfig> onSiteFee = map.get(settleFeeTypeEnum.getType());
            JdhSettlementFeeDetailConfig settlementFeeDetailConfig = onSiteFee.get(0);
            if(StringUtil.isNotBlank(fee)){
                settlementFeeDetailConfig.setFeeAmount(new BigDecimal(fee));
            }else{
                settlementFeeDetailConfig.setFeeAmount(null);
            }
            updateSettlementFeeDetailConfigs.add(settlementFeeDetailConfig);
        }else{
            if(StringUtil.isNotBlank(fee)){
                JdhSettlementFeeDetailConfig feeDetailConfig = bulidSettlementFeeDetailConfig(feeConfigId,settleFeeTypeEnum.getType(), new BigDecimal(fee));
                addSettlementFeeDetailConfigs.add(feeDetailConfig);
            }
        }
    }

    /**
     * @param angelSettleCityConfigFileCmd
     * @return
     */
    @Override
    public boolean batchSaveAngelSettleConfig(AngelSettleCityConfigFileCmd angelSettleCityConfigFileCmd) {
        JdhFile jdhFile = jdhFileRepository.find(JdhFileIdentifier.builder().fileId(angelSettleCityConfigFileCmd.getFileId()).build());
        AssertUtils.nonNull(jdhFile,"上传非法文件");
        JdhImportExportTask task = bulidJdhImportExportTask(jdhFile,angelSettleCityConfigFileCmd.getApplyErp());
        jdhFileTaskRepository.add(task);
        angelSettleCityConfigFileCmd.setTaskId(task.getTaskId());

        CompletableFuture.runAsync(() -> excelAllSheetAnalysis(angelSettleCityConfigFileCmd,jdhFile),
                executorPoolFactory.get(ThreadPoolConfigEnum.DEFAULT));
        return Boolean.TRUE;
    }

    /**
     * @param queryContext
     * @return
     */
    @Override
    public Boolean exportAngelSettleConfig(JdhAngelSettleAreaFeeQuery queryContext) {
        Integer settlementCount = jdhSettlementAreaFeeConfigRepository.queryCityConfigCount(queryContext);
        if(Objects.nonNull(settlementCount) && settlementCount == CommonConstant.ZERO){
            throw new BusinessException(SettleErrorCode.EXPORT_SETTLE_DATA_ZERO);
        }
        if(Objects.nonNull(settlementCount) && settlementCount > FileExportTypeEnum.ANGEL_SETTLE_CONFIG_EXPORT.getMaxCount()){
            throw new BusinessException(SettleErrorCode.EXPORT_SETTLE_DATA_OUT);
        }
        //1、构建上下文
        Map<String, Object> ctx = BeanUtil.beanToMap(queryContext);
        ctx.put("scene", FileExportTypeEnum.ANGEL_SETTLE_CONFIG_EXPORT.getType());
        ctx.put("userPin",queryContext.getUserPin());
        ctx.put("operationType", FileExportTypeEnum.ANGEL_SETTLE_CONFIG_EXPORT.getType());
        //2、调用通用文件导入能力
        return fileManageApplication.export(ctx);
    }

    /**
     * 获取某地区护士结算配置
     *
     * @param queryContext
     * @return
     */
    @Override
    @LogAndAlarm
    public AngelSettlementConfigDto getAngelSettleAreaConfig(JdhAngelSettleAreaFeeQuery queryContext) {
        AssertUtils.nonNull(queryContext, BusinessErrorCode.ILLEGAL_ARG_ERROR);
        AssertUtils.nonNull(queryContext.getAngelType(), BusinessErrorCode.ILLEGAL_ARG_ERROR);
        if (StringUtils.isEmpty(queryContext.getProvinceCode()) && StringUtils.isEmpty(queryContext.getCityCode()) && StringUtils.isEmpty(queryContext.getDestCode()) && CollectionUtils.isEmpty(queryContext.getDestCodeList())) {
            throw new BusinessException(BusinessErrorCode.ILLEGAL_ARG_ERROR);
        }

        // 查询到城市级别
        List<JdhSettlementAreaFeeConfig> poList = jdhSettlementAreaFeeConfigRepository.queryJdhAreaFeeConfigList(queryContext);
        if(CollUtil.isNotEmpty(poList)){
            poList.stream().collect(Collectors.groupingBy(JdhSettlementAreaFeeConfig::getCountyCode, Collectors.toList()));
        }

        return null;
    }

    /**
     * 创建导入文件任务
     * @param jdhFile
     * @param userPin
     * @return
     */
    public static JdhImportExportTask bulidJdhImportExportTask(JdhFile jdhFile,String userPin){
        JdhImportExportTask task = new JdhImportExportTask();
        Long taskId = SpringUtil.getBean(GenerateIdFactory.class).getId();
        task.setTaskId(taskId);
        task.setOperatorId(userPin);
        task.setScene(FileExportTypeEnum.ANGEL_SETTLE_CONFIG_EXPORT.getType());
        task.setOperationType(FileExportTypeEnum.ANGEL_SETTLE_CONFIG_UPLOAD.getType());
        task.setStatus(FileOperationStatusEnum.PROCESSING.getStatus());
        task.setFileName(jdhFile.getFileName() + taskId);
        task.setFileUrl(jdhFile.getFilePath());
        task.setExpireTime(CommonConstant.EXPIRE_TIME);
        return task;
    }

    /**
     *
     * @param angelSettleCityConfigFileCmd
     * @param jdhFile
     */
    private void excelAllSheetAnalysis(AngelSettleCityConfigFileCmd angelSettleCityConfigFileCmd, JdhFile jdhFile) {
        JdhImportExportTask task = new JdhImportExportTask();
        task.setTaskId(angelSettleCityConfigFileCmd.getTaskId());
        try{
            InputStream inputStream = fileManageService.get(jdhFile.getFilePath());
            ImportAngelSettleConfigListener readListener = new ImportAngelSettleConfigListener(angelSettleCityConfigFileCmd.getApplyErp());
            EasyExcelFactory.read(inputStream, ImportAngelSettleConfig.class, readListener).sheet().doRead();
            //
            FilePreSignedUrlDto filePreSignedUrlDto = readListener.getFilePreSignedUrlDto();
            if (readListener.getIsFail()) {
                JdhFile jdhFailFile = jdhFileRepository.find(JdhFileIdentifier.builder().fileId(filePreSignedUrlDto.getFileId()).build());
                task.setFileUrl(jdhFailFile.getFilePath());
                task.setStatus(FileOperationStatusEnum.FAIL.getStatus());
            }else{
                List<ImportAngelSettleConfig> succList = CollUtil.newArrayList(readListener.getSuccMap().values());
                log.info("[JdServiceCityAngelSettleApplicationImpl.excelAllSheetAnalysis] succList={}",succList);
                if(CollUtil.isNotEmpty(succList)){
                    batchSaveCityConfig(succList,angelSettleCityConfigFileCmd.getApplyErp());
                }
                task.setStatus(FileOperationStatusEnum.SUCCESS.getStatus());
            }
        }catch (Exception e){
            log.error("[JdServiceCityAngelSettleApplicationImpl.excelAllSheetAnalysis] error=",e);
            String failReason = e.getMessage().substring(0,255);
            task.setFailMsg(failReason);
            task.setStatus(FileOperationStatusEnum.FAIL.getStatus());
        }
        jdhFileTaskRepository.update(task);
    }


    /**
     * 批量保存
     * @param succList
     * @param applyErp
     */
    private void batchSaveCityConfig(List<ImportAngelSettleConfig> succList,String applyErp){
        List<JdhSettlementAreaFeeConfig> addJdhSettlementConfigList = new ArrayList<>();
        List<JdhSettlementAreaFeeConfig> updateJdhSettlementConfigList = new ArrayList<>();
        for(ImportAngelSettleConfig request : succList){
            JdhSettlementAreaFeeConfig jdhAngelSettlementConfig = JdhAngelSettlementPoConvert.INSTANCE.convertToJdhAngelSettlementConfig(request);
            jdhAngelSettlementConfig.setCreateUser(applyErp);
            jdhAngelSettlementConfig.setUpdateUser(applyErp);
            jdhAngelSettlementConfig.setSettlementSubjectType(AngelTypeEnum.NURSE.getType().toString());
            jdhAngelSettlementConfig.setSettlementSubjectSubType(request.getAngelType());
            Long cityConfigId = generateIdFactory.getId();
            jdhAngelSettlementConfig.setFeeConfigId(cityConfigId);
            jdhAngelSettlementConfig.setBranch(ACTIVE);
            jdhAngelSettlementConfig.setDestCode(StringUtil.isNotBlank(jdhAngelSettlementConfig.getTownCode()) ? jdhAngelSettlementConfig.getTownCode() : StringUtil.isNotBlank(jdhAngelSettlementConfig.getCountyCode()) ? jdhAngelSettlementConfig.getCountyCode() : StringUtil.isNotBlank(jdhAngelSettlementConfig.getCityCode()) ? jdhAngelSettlementConfig.getCityCode() : jdhAngelSettlementConfig.getProvinceCode());
            JdhSettlementAreaFeeConfig entity = jdhSettlementAreaFeeConfigRepository.queryJdhAreaFeeConfig(jdhAngelSettlementConfig);
            if(Objects.nonNull(entity)){
                jdhAngelSettlementConfig.setFeeConfigId(entity.getFeeConfigId());
                updateJdhSettlementConfigList.add(jdhAngelSettlementConfig);
            }else{
                addJdhSettlementConfigList.add(jdhAngelSettlementConfig);
            }
        }
        if(CollUtil.isNotEmpty(addJdhSettlementConfigList)){
            jdhSettlementAreaFeeConfigRepository.batchSaveJdhAreaFeeConfig(addJdhSettlementConfigList);
            List<JdhSettlementFeeDetailConfig> jdhSettlementFeeDetailConfigs = generateFeeDetailList(addJdhSettlementConfigList);
            jdhSettlementFeeDetailConfigRepository.batchSaveJdhAreaFeeConfig(jdhSettlementFeeDetailConfigs);
        }
        if(CollUtil.isNotEmpty(updateJdhSettlementConfigList)){
            List<JdhSettlementFeeDetailConfig> jdhSettlementFeeDetailConfigs = generateFeeDetailList(updateJdhSettlementConfigList);
            JdhAngelSettleAreaFeeQuery queryContext = new JdhAngelSettleAreaFeeQuery();
            List<Long> feeConfigIdList = updateJdhSettlementConfigList.stream().map(JdhSettlementAreaFeeConfig::getFeeConfigId).collect(Collectors.toList());
            queryContext.setFeeConfigIdList(feeConfigIdList);
            jdhSettlementFeeDetailConfigRepository.batchDelete(queryContext);
            jdhSettlementFeeDetailConfigRepository.batchSaveJdhAreaFeeConfig(jdhSettlementFeeDetailConfigs);
        }
    }


    /**
     *
     * @param jdhSettlementAreaFeeConfigs
     * @return
     */
    private List<JdhSettlementFeeDetailConfig> generateFeeDetailList(List<JdhSettlementAreaFeeConfig> jdhSettlementAreaFeeConfigs) {
        List<JdhSettlementFeeDetailConfig> jdhSettlementFeeDetailConfigs = new ArrayList<>();
        jdhSettlementAreaFeeConfigs.forEach(jdhSettlementAreaFeeConfig ->{
            Long feeConfigId = jdhSettlementAreaFeeConfig.getFeeConfigId();
            String fee = jdhSettlementAreaFeeConfig.getOnSiteFee();
            if(StringUtil.isNotBlank(fee)){
                JdhSettlementFeeDetailConfig feeDetailConfig = bulidSettlementFeeDetailConfig(feeConfigId, SettleFeeTypeEnum.ONSITE_FEE.getType(), new BigDecimal(fee));
                jdhSettlementFeeDetailConfigs.add(feeDetailConfig);
            }
            fee = jdhSettlementAreaFeeConfig.getImmediatelyFee();
            if(StringUtil.isNotBlank(fee)){
                JdhSettlementFeeDetailConfig feeDetailConfig = bulidSettlementFeeDetailConfig(feeConfigId, SettleFeeTypeEnum.IMMEDIATELY_FF.getType(), new BigDecimal(fee));
                jdhSettlementFeeDetailConfigs.add(feeDetailConfig);
            }
            fee = jdhSettlementAreaFeeConfig.getHolidayFee();
            if(StringUtil.isNotBlank(fee)){
                JdhSettlementFeeDetailConfig feeDetailConfig = bulidSettlementFeeDetailConfig(feeConfigId, SettleFeeTypeEnum.HOLIDAY_FF.getType(), new BigDecimal(fee));
                jdhSettlementFeeDetailConfigs.add(feeDetailConfig);
            }
            fee = jdhSettlementAreaFeeConfig.getNightDoorFee();
            if(StringUtil.isNotBlank(fee)){
                JdhSettlementFeeDetailConfig feeDetailConfig = bulidSettlementFeeDetailConfig(feeConfigId, SettleFeeTypeEnum.NIGHT_DOOR_FEE.getType(), new BigDecimal(fee));
                jdhSettlementFeeDetailConfigs.add(feeDetailConfig);
            }
            fee = jdhSettlementAreaFeeConfig.getDynamicAdjustFee();
            if(StringUtil.isNotBlank(fee)){
                JdhSettlementFeeDetailConfig feeDetailConfig = bulidSettlementFeeDetailConfig(feeConfigId, SettleFeeTypeEnum.DYNAMIC_ADJUST_FEE.getType(), new BigDecimal(fee));
                jdhSettlementFeeDetailConfigs.add(feeDetailConfig);
            }
        });
        return jdhSettlementFeeDetailConfigs;
    }

}
