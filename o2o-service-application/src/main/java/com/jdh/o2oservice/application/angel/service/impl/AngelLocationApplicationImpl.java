package com.jdh.o2oservice.application.angel.service.impl;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jd.addresstranslation.api.base.BaseAddressInfo;
import com.jd.addresstranslation.api.base.JDAddressRequestInfo;
import com.jd.jim.cli.Cluster;
import com.jd.jmq.client.producer.MessageProducer;
import com.jd.jmq.client.springboot.annotation.JmqProducer;
import com.jd.jmq.common.message.Message;
import com.jdh.o2oservice.application.angel.service.AngelApplication;
import com.jdh.o2oservice.application.angel.service.AngelLocationApplication;
import com.jdh.o2oservice.base.constatnt.CacheConstant;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.enums.RedisKeyEnum;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode;
import com.jdh.o2oservice.base.factory.GenerateIdFactory;
import com.jdh.o2oservice.base.util.AssertUtils;
import com.jdh.o2oservice.base.util.TimeUtils;
import com.jdh.o2oservice.core.domain.angel.context.JdhAngelLocationFullContext;
import com.jdh.o2oservice.core.domain.angel.model.JdhAngelLocationFull;
import com.jdh.o2oservice.core.domain.angel.model.JdhAngelTimeLocation;
import com.jdh.o2oservice.core.domain.angel.repository.es.JdhAngelLocationFullEsRepository;
import com.jdh.o2oservice.core.domain.trade.rpc.OrderTrackRpcService;
import com.jdh.o2oservice.export.angel.cmd.AngelLocationMessageCmd;
import com.jdh.o2oservice.export.angel.dto.AngelLocationDto;
import com.jdh.o2oservice.export.angel.dto.JdhAngelDto;
import com.jdh.o2oservice.export.angel.query.AngelRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> limeng363
 * @Date : 2024-04-24 17:08
 * @Desc : 服务者地理位置应用
 */
@Slf4j
@Service
public class AngelLocationApplicationImpl implements AngelLocationApplication {
    /**
     * 服务者实时位置 topic
     */
    @Value("${topics.reachStoreProducer.angelApplication}")
    private String topic;

    /**
     * jimRedisService
     */
    @Resource
    private Cluster jimClient;
    @Resource
    private AngelApplication angelApplication;
    /**
     * reachStoreProducer
     */
    @JmqProducer(name = "reachStoreProducer")
    private MessageProducer reachStoreProducer;

    @Resource
    private JdhAngelLocationFullEsRepository jdhAngelLocationFullEsRepository;

    @Resource
    private OrderTrackRpcService orderTrackRpcService;

    @Resource
    private DuccConfig duccConfig;

    @Resource
    private GenerateIdFactory generateIdFactory;


    /**
     * 上报地理位置信息
     * @param angelPin 服务者pin
     * @param longitude 经度 例如:116.56277586434172
     * @param latitude 纬度 例如:39.78671417565942
     * @return
     */
    @Override
    public Boolean submitLocation(String angelPin, Double longitude, Double latitude) {
        log.info("AngelLocationApplicationImpl -> submitLocation angelPin:{} longitude:{} latitude:{}", angelPin, longitude, latitude);
        AssertUtils.nonNull(longitude, BusinessErrorCode.ILLEGAL_ARG_ERROR);
        AssertUtils.nonNull(latitude, BusinessErrorCode.ILLEGAL_ARG_ERROR);
        String redisKey = RedisKeyEnum.getRedisKey(RedisKeyEnum.ANGLE_SUBMIT_LOCATION, angelPin);
        //同一个服务者5分钟内，只能上传一次
        boolean success = jimClient.set(redisKey, "1",5L, TimeUnit.MINUTES, false);
        if(!success){
            log.info("AngelLocationApplicationImpl.submitLocation 上传地理位置过于频繁 angelPin={}", angelPin);
            return true;
        }

        //校验经度
        if(longitude < -180 || longitude > 180){
            log.error("AngelLocationApplicationImpl.submitLocation --> long经度错误 angelPin={}, longitude={}, latitude={}", angelPin, longitude, latitude);
            throw new BusinessException(BusinessErrorCode.ILLEGAL_ARG_ERROR, "经度错误");
        }
        //校验纬度
        if(latitude < -90 || latitude > 90){
            log.error("AngelLocationApplicationImpl.submitLocation --> lat纬度错误 angelPin={}, longitude={}, latitude={}", angelPin, longitude, latitude);
            throw new BusinessException(BusinessErrorCode.ILLEGAL_ARG_ERROR, "纬度错误");
        }
        //根据pin查询服务者id
        Long angelId = getAngelId(angelPin);
        if(angelId == null){
            log.info("未查询对应服务者angelPin={}", angelPin);
            return false;
        }
        //插入,缓存2小时，经度，纬度拼接，用逗号分隔，例如:116.56277586434172,39.78671417565942
        jimClient.setEx(CacheConstant.ANGEL_LOCATION_PREFIX + angelId,longitude+","+latitude, 2, TimeUnit.HOURS);
        //将服务者位置信息发送到mq里
        sendMessage(angelPin, angelId, longitude, latitude);
        //将服务者位置信息保存到es
        saveAngelLocationToEs(angelPin, angelId, longitude, latitude);
        return true;
    }



    private void sendMessage(String angelPin, Long angelId, Double longitude, Double latitude) {
        try{
            AngelLocationMessageCmd cmd = new  AngelLocationMessageCmd();
            cmd.setAngelId(angelId);
            cmd.setAngelPin(angelPin);
            cmd.setLongitude(longitude);
            cmd.setLatitude(latitude);
            cmd.setSubmitTime(new Date());
            log.info("AngelLocationApplicationImpl -> sendMessage cmd:{}", JSON.toJSONString(cmd));
            Message message = new Message(topic, JSON.toJSONString(cmd), cmd.getAngelPin());
            log.info("AngelLocationApplicationImpl-> sendMessage message={}", JSON.toJSONString(message));
            reachStoreProducer.send(message);
        }catch (Exception e){
            log.error("AngelLocationApplicationImpl.sendMessage error",e);
        }
    }

    /**
     * 根据pin查询服务者id
     * @param angelPin
     * @return
     */
    private Long getAngelId(String angelPin) {
        AngelRequest angelRequest = new AngelRequest();
        angelRequest.setAngelPin(angelPin);
        JdhAngelDto jdhAngelDto = angelApplication.queryByAngelInfo(angelRequest);
        log.info("AngelLocationApplicationImpl -> getAngelId angelPin:{} jdhAngelDto={}", angelPin, JSON.toJSONString(jdhAngelDto));
        if(jdhAngelDto == null){
            return null;
        }
        return jdhAngelDto.getAngelId();
    }

    /**
     * 查询护士经纬度
     * @param angelId 护士id
     * @return
     *
     */
    @Override
    public AngelLocationDto getLocation(Long angelId) {
        log.info("AngelLocationApplicationImpl.getLocation angelId={}", angelId);
        String longLat = jimClient.get(CacheConstant.ANGEL_LOCATION_PREFIX + angelId);
        log.info("AngelLocationApplicationImpl.getLocation angelId={} longLat={}", angelId, longLat);
        if(StringUtils.isBlank(longLat)){
            log.info("AngelLocationApplicationImpl.getLocation  longLat is null  angelId={}", angelId);
            return null;
        }
        //经度，纬度拼接，用逗号分隔，例如:116.56277586434172,39.78671417565942
        String[] longLatArray = longLat.split(",");
        if(longLatArray.length != 2){
            return null;
        }
        return new AngelLocationDto(angelId, new Double(longLatArray[0]), new Double(longLatArray[1]));
    }

    private void saveAngelLocationToEs(String angelPin, Long angelId, Double longitude, Double latitude){
        try {
            if (!checkLocationAreaFlag(longitude, latitude)){
                log.info("saveAngelLocationToEs areaFlag false");
                return;
            }

            JdhAngelLocationFullContext queryLocation = new JdhAngelLocationFullContext();
            queryLocation.setAngelId(angelId);
            queryLocation.setAngelPin(angelPin);
            // 2025-04-22 10:00:00
            String timeIntervalChar = LocalDateTime.now().truncatedTo(ChronoUnit.HOURS).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            queryLocation.setTimeIntervalChar(timeIntervalChar);
            Date timeInterval = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(timeIntervalChar);
            List<JdhAngelLocationFull> angelLocationList = jdhAngelLocationFullEsRepository.queryAngelLocationFullList(queryLocation);
            log.info("saveAngelLocationToEs queryLocation={}, angelLocationList={}", JSON.toJSONString(queryLocation), JSON.toJSONString(angelLocationList));

            JdhAngelLocationFull angelLocationFull = null;
            if (CollectionUtils.isEmpty(angelLocationList)){
                LocalTime startOfHour = LocalTime.now().withMinute(0).withSecond(0).withNano(0);
                List<String> timeRangeList = splitHourInto5MinIntervals(startOfHour);
                List<JdhAngelTimeLocation> angelTimeLocationList = new ArrayList<>();
                timeRangeList.forEach(timeRange->{
                    String[] split = timeRange.split("-");
                    JdhAngelTimeLocation angelTimeLocation = new JdhAngelTimeLocation();
                    String timeRangeStart = split[0];
                    String timeRangeEnd = split[1];
                    angelTimeLocation.setTimeRangeStart(timeRangeStart);
                    angelTimeLocation.setTimeRangeEnd(timeRangeEnd);
                    angelTimeLocation.setLongitude("");
                    angelTimeLocation.setLatitude("");
                    // 获取当前时间
                    LocalTime currentTime = LocalTime.now();
                    // 定义时间格式
                    DateTimeFormatter dtf = DateTimeFormatter.ofPattern("HH:mm");
                    // 格式化当前时间为字符串
                    String currentTimeStr = currentTime.format(dtf);
                    LocalTime timeRangeStartLocal = LocalTime.parse(timeRangeStart, dtf);
                    LocalTime timeRangeEndLocal = LocalTime.parse(timeRangeEnd, dtf);
                    LocalTime currentTimeLocal = LocalTime.parse(currentTimeStr, dtf);
                    if ((currentTimeLocal.isBefore(timeRangeEndLocal) && currentTimeLocal.isAfter(timeRangeStartLocal))
                            || (currentTimeLocal.equals(timeRangeEndLocal))){
                        angelTimeLocation.setLongitude(String.valueOf(longitude));
                        angelTimeLocation.setLatitude(String.valueOf(latitude));
                        angelTimeLocation.setTime(new Date());
                        angelTimeLocation.setTimeStr(TimeUtils.getCurrentDateTime());
                    }
                    angelTimeLocationList.add(angelTimeLocation);
                });

                angelLocationFull = new JdhAngelLocationFull();
                angelLocationFull.setId(generateIdFactory.getIdStr());
                angelLocationFull.setAngelId(angelId);
                angelLocationFull.setAngelPin(angelPin);
                angelLocationFull.setTimeInterval(timeInterval);
                angelLocationFull.setTimeIntervalChar(timeIntervalChar);
                angelLocationFull.setEsModified(new Date());
                angelLocationFull.setTimeLocation(JSON.toJSONString(angelTimeLocationList));

            }else {
                angelLocationFull = angelLocationList.get(0);
                List<JdhAngelTimeLocation> angelTimeLocationList = JSON.parseArray(angelLocationFull.getTimeLocation(), JdhAngelTimeLocation.class);
                angelTimeLocationList.forEach(angelTimeLocation->{
                    String timeRangeStart = angelTimeLocation.getTimeRangeStart();
                    String timeRangeEnd = angelTimeLocation.getTimeRangeEnd();
                    // 获取当前时间
                    LocalTime currentTime = LocalTime.now();
                    // 定义时间格式
                    DateTimeFormatter dtf = DateTimeFormatter.ofPattern("HH:mm");
                    // 格式化当前时间为字符串
                    String currentTimeStr = currentTime.format(dtf);
                    LocalTime timeRangeStartLocal = LocalTime.parse(timeRangeStart, dtf);
                    LocalTime timeRangeEndLocal = LocalTime.parse(timeRangeEnd, dtf);
                    LocalTime currentTimeLocal = LocalTime.parse(currentTimeStr, dtf);
                    if ((currentTimeLocal.isBefore(timeRangeEndLocal) && currentTimeLocal.isAfter(timeRangeStartLocal))
                            || (currentTimeLocal.equals(timeRangeEndLocal)) || (currentTimeLocal.equals(timeRangeStartLocal))){
                        angelTimeLocation.setLongitude(String.valueOf(longitude));
                        angelTimeLocation.setLatitude(String.valueOf(latitude));
                        angelTimeLocation.setTime(new Date());
                        angelTimeLocation.setTimeStr(TimeUtils.getCurrentDateTime());
                    }
                });
                angelLocationFull.setTimeLocation(JSON.toJSONString(angelTimeLocationList));
                angelLocationFull.setEsModified(new Date());
            }
            log.info("AngelLocationApplicationImpl saveAngelLocationToEs angelLocationFull={}", JSON.toJSONString(angelLocationFull));
            jdhAngelLocationFullEsRepository.save(angelLocationFull);
        } catch (Exception e) {
            log.error("AngelLocationApplicationImpl saveAngelLocationToEs error e", e);
        }
    }

    private boolean checkLocationAreaFlag(Double longitude, Double latitude) {
        JDAddressRequestInfo addressRequest = new JDAddressRequestInfo();
        addressRequest.setLng(new BigDecimal(longitude));
        addressRequest.setLat(new BigDecimal(latitude));
        BaseAddressInfo addressResult = orderTrackRpcService.getAddressByLatAndLon(addressRequest);
        log.info("AngelLocationApplicationImpl checkLocationAreaFlag addressRequest={}, addressResult={}", JSON.toJSONString(addressRequest), JSON.toJSONString(addressResult));
        if (Objects.isNull(addressResult)){
            return false;
        }
        JSONArray areaArr = JSON.parseArray(duccConfig.getAngelLocationAreaConfig());
        for (int i = 0; i < areaArr.size(); i++) {
            JSONObject obj = areaArr.getJSONObject(i);
            if (Objects.nonNull(addressResult.getProvinceCode()) && obj.getInteger("areaCode").equals(addressResult.getProvinceCode())){
                return true;
            }
            if (Objects.nonNull(addressResult.getCityCode()) && obj.getInteger("areaCode").equals(addressResult.getCityCode())){
                return true;
            }
            if (Objects.nonNull(addressResult.getDistrictCode()) && obj.getInteger("areaCode").equals(addressResult.getDistrictCode())){
                return true;
            }
            if (Objects.nonNull(addressResult.getTownCode()) && obj.getInteger("areaCode").equals(addressResult.getTownCode())){
                return true;
            }
        }
        return false;
    }

    /**
     * [15:00-15:05, 15:05-15:10, 15:10-15:15, 15:15-15:20, 15:20-15:25, 15:25-15:30, 15:30-15:35, 15:35-15:40, 15:40-15:45, 15:45-15:50, 15:50-15:55, 15:55-16:00]
     * @param startOfHour
     * @return
     */
    public static List<String> splitHourInto5MinIntervals(LocalTime startOfHour) {
        List<String> intervals = new ArrayList<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm");
        for (int i = 0; i < 12; i++) {
            LocalTime intervalStart = startOfHour.plusMinutes(i * 5);
            LocalTime intervalEnd = intervalStart.plusMinutes(5);
            // 15:00-15:05
            String formattedInterval = String.format("%s-%s",
                    intervalStart.format(formatter),
                    intervalEnd.format(formatter));

            intervals.add(formattedInterval);
        }
        return intervals;
    }
}
