package com.jdh.o2oservice.application.promise.convert;

import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.*;
import com.jdh.o2oservice.application.product.service.ProductApplication;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.enums.YnStatusEnum;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.util.SpringUtil;
import com.jdh.o2oservice.base.util.TimeUtils;
import com.jdh.o2oservice.common.enums.MedicalPromiseStatusEnum;
import com.jdh.o2oservice.common.result.request.AbstractCmd;
import com.jdh.o2oservice.core.domain.promise.bo.PromiseFreezeStatusBO;
import com.jdh.o2oservice.core.domain.promise.bo.PromiseFreezeUser;
import com.jdh.o2oservice.core.domain.promise.bo.PromiseInvalidUser;
import com.jdh.o2oservice.core.domain.promise.context.*;
import com.jdh.o2oservice.core.domain.promise.enums.PromiseErrorCode;
import com.jdh.o2oservice.core.domain.promise.enums.PromiseExtendKeyEnum;
import com.jdh.o2oservice.core.domain.promise.ext.dto.ExtAutoSubmitDraft;
import com.jdh.o2oservice.core.domain.promise.ext.dto.ExtPromiseUser;
import com.jdh.o2oservice.core.domain.promise.model.*;
import com.jdh.o2oservice.core.domain.promise.repository.query.PromiseIndexPageQuery;
import com.jdh.o2oservice.core.domain.promise.rpc.MedicalPromiseRpc;
import com.jdh.o2oservice.core.domain.promise.rpc.bo.MedicalPromiseBO;
import com.jdh.o2oservice.core.domain.provider.model.ProviderPromise;
import com.jdh.o2oservice.core.domain.support.basic.enums.JdhFreezeEnum;
import com.jdh.o2oservice.core.domain.support.basic.enums.JdhProcessDataTypeEnum;
import com.jdh.o2oservice.core.domain.support.basic.model.*;
import com.jdh.o2oservice.export.dispatch.cmd.*;
import com.jdh.o2oservice.export.medicalpromise.cmd.*;
import com.jdh.o2oservice.export.product.dto.JdhSkuDto;
import com.jdh.o2oservice.export.product.dto.ServiceItemDto;
import com.jdh.o2oservice.export.product.query.JdhSkuListRequest;
import com.jdh.o2oservice.export.promise.cmd.*;
import com.jdh.o2oservice.export.promise.dto.*;
import com.jdh.o2oservice.export.promise.query.StoreProgramPromiseQuery;
import org.apache.commons.collections.CollectionUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;
import org.springframework.beans.BeanUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * PromiseApplicationConvert
 * @author: yangxiyu
 * @date: 2024/4/24 5:48 下午
 * @version: 1.0
 */
@Mapper
public interface PromiseApplicationConverter {

    /**
     *
     */
    PromiseApplicationConverter INS = Mappers.getMapper(PromiseApplicationConverter.class);


    /**
     * 转化预约时间
     *
     * @param appointmentTime
     * @return
     */
    PromiseTime cmd2AppointmentTime(AppointmentTime appointmentTime);

    /**
     * 转化意向护士
     *
     * @param intendedNurse
     * @return
     */
    PromiseIntendedNurse cmd2PromiseIntendedNurse(IntendedNurse intendedNurse);

    /**
     * 履约服务转换为实体
     * @param serviceItemList
     * @return
     */
    List<PromiseService> cmd2PromiseService(List<PromiseServiceItem> serviceItemList);

    @Mapping(source = "credentialType", target = "credentialNum.credentialType")
    @Mapping(source = "credentialNo", target = "credentialNum.credentialNo")
    @Mapping(source = "birthday", target = "birthday.birth")
    @Mapping(source = "phone", target = "phoneNumber.phone")
    @Mapping(source = "name", target = "userName.name")
    @Mapping(source = "gender", target = "gender")
    User cmd2PromiseUser(SubmitUser submitUsers);


    /**
     * @param cmd
     * @return
     */
    @Mapping(source = "user.credentialType", target = "user.credentialNum.credentialType")
    @Mapping(source = "user.credentialNo", target = "user.credentialNum.credentialNo")
    @Mapping(source = "user.birthday", target = "user.birthday.birth")
    @Mapping(source = "user.phone", target = "user.phoneNumber.phone")
    @Mapping(source = "user.name", target = "user.userName.name")
    @Mapping(source = "userPin", target = "user.userPin")
    SubmitAppointmentDraftContext cmd2SubmitAppointmentDraftContext(SubmitAppointmentDraftCmd cmd);

    /**
     * 修改预约命令转上下文
     * @param cmd
     * @return
     */
    PromiseModifySubmitAbilityContext cmd2ModifyContext(ModifyAppointCmd cmd);

    /**
     * 取消预约命令转上下文
     * @param cmd
     * @return
     */
    PromiseCancelSubmitAbilityContext cmd2CancelContext(CancelAppointCmd cmd);

    /**
     * 回调上下文
     *
     * @param cmd
     * @return
     */
    PromiseCallbackAbilityContext cmd2CallbackCtx(PromiseCallbackCmd cmd);


    /**
     * 核销命令转上下文
     * @param cmd
     * @return
     */
    PromiseWriteOffContext cmd2WriteOffCtx(PromiseUserWriteOffCmd cmd);

    /**
     *
     * @param cmd
     * @return
     */
    PromiseSubmitAbilityContext initSubmitContext(AbstractCmd cmd);
    /**
     * cmd转context
     *
     * @param cmd
     * @return
     */
    default PromiseSubmitAbilityContext cmd2SubmitContext(SubmitPromiseCmd cmd) {

        PromiseSubmitAbilityContext context = initSubmitContext(cmd);
        PromiseStation station = new PromiseStation();
        station.setStoreId(cmd.getStoreId());
        context.setStation(station);
        context.setSmsCode(cmd.getSmsCode());
        context.setUserPin(cmd.getUserPin());
        context.setRemark(cmd.getRemark());

        PromiseTime promiseTime = cmd2AppointmentTime(cmd.getAppointmentTime());
        context.setAppointmentTime(promiseTime);
        context.setServices(cmd2PromiseService(cmd.getServices()));

        context.setOperator(cmd.getOperator());
        context.setOperatorRoleType(cmd.getOperatorRoleType());

        context.setAppointmentPhone(cmd.getAppointmentPhone());
        context.setAppointmentUserName(cmd.getAppointmentUserName());
        // 到家业务可能有多个user
        if (CollectionUtils.isNotEmpty(cmd.getUsers())) {
            List<User> users = Lists.newArrayList();
            for (SubmitUser user : cmd.getUsers()) {
                User u1 = cmd2PromiseUser(user);
                u1.setUserPin(cmd.getUserPin());
                users.add(u1);
            }
            context.setUsers(users);
            // 历史逻辑，到店业务只有一个User
        } else if (Objects.nonNull(cmd.getUser())) {
            List<User> users = Lists.newArrayList();
            User u1 = cmd2PromiseUser(cmd.getUser());
            u1.setUserPin(cmd.getUserPin());
            users.add(u1);
            context.setUsers(users);
        }
        PromiseIntendedNurse intendedNurse = cmd2PromiseIntendedNurse(cmd.getIntendedNurse());
        context.setIntendedNurse(intendedNurse);
        return context;
    }

    /**
     * 草稿数据转提交预约context
     *
     * @param draft
     * @return
     */
    default PromiseSubmitAbilityContext draft2SubmitContext(PromiseAppointmentDraft draft) {
        if (draft == null) {
            return new PromiseSubmitAbilityContext();
        }

        PromiseSubmitAbilityContext promiseSubmitAbilityContext = new PromiseSubmitAbilityContext();
        PromiseStation station = new PromiseStation();
        station.setStoreId(draft.getStoreId());
        promiseSubmitAbilityContext.setStation(station);
        List<PromiseService> list = draft.getServices();
        if (list != null) {
            promiseSubmitAbilityContext.setServices(new ArrayList<>(list));
        }
        promiseSubmitAbilityContext.setAppointmentTime(draft.getAppointmentTime());


        List<User> users = Lists.newArrayList(draft.getUser());
        promiseSubmitAbilityContext.setUsers(users);
        return promiseSubmitAbilityContext;
    }


    /**
     * entity2SubmitDispatchCmd
     *
     * @param promise promise
     * @return {@link SubmitDispatchCmd}
     */
    default SubmitDispatchCmd promise2SubmitDispatchCmd(JdhPromise promise){
        SubmitDispatchCmd cmd = new SubmitDispatchCmd();
        cmd.setVerticalCode(promise.getVerticalCode());
        cmd.setServiceType(promise.getServiceType());
        cmd.setPromiseId(promise.getPromiseId());
        cmd.setVoucherId(promise.getVoucherId());
        cmd.setSourceVoucherId(promise.getSourceVoucherId());
        cmd.setUserPin(promise.getUserPin());

        DispatchServiceLocation station = new DispatchServiceLocation();
        station.setServiceLocationDetail(promise.getStore().getStoreAddr());
        cmd.setServiceLocation(station);

        DispatchAppointmentTime appointmentTime = new DispatchAppointmentTime();
        appointmentTime.setAppointmentStartTime(promise.getAppointmentTime().formatAppointmentStartTime());
        appointmentTime.setAppointmentEndTime(promise.getAppointmentTime().formatAppointmentEndTime());
        appointmentTime.setDateType(promise.getAppointmentTime().getDateType());
        appointmentTime.setIsImmediately(promise.getAppointmentTime().getIsImmediately());
        cmd.setAppointmentTime(appointmentTime);

        List<MedicalPromiseBO> medPromiseList = SpringUtil.getBean(MedicalPromiseRpc.class).findListByPromiseId(promise.getPromiseId());
        Map<Long, List<MedicalPromiseBO>> promisePatientMedPromiseList = medPromiseList.stream().collect(Collectors.groupingBy(MedicalPromiseBO::getPromisePatientId));
        List<DispatchAppointmentPatient> patients = new ArrayList<>();
        for (JdhPromisePatient patient : promise.getPatients()) {
            List<MedicalPromiseBO> medicalPromiseList = promisePatientMedPromiseList.get(patient.getPromisePatientId());
            //过滤不是作废，不是冻结的
            List<MedicalPromiseBO> validMedPromiseList = medicalPromiseList.stream().filter(ele -> !JdhFreezeEnum.FREEZE.getStatus().equals(ele.getFreeze())
                    && !MedicalPromiseStatusEnum.INVALID.getStatus().equals(ele.getStatus())).collect(Collectors.toList());
            if(CollUtil.isEmpty(validMedPromiseList)){
                continue;
            }
            List<DispatchServiceItem> serviceItemList = new ArrayList<>();
            for (MedicalPromiseBO medicalPromiseBO : validMedPromiseList) {
                DispatchServiceItem ele = new DispatchServiceItem();
                ele.setServiceId(medicalPromiseBO.getServiceId());
                ele.setItemId(Long.parseLong(medicalPromiseBO.getServiceItemId()));
                ele.setItemName(medicalPromiseBO.getServiceItemName());
                serviceItemList.add(ele);
            }
            DispatchAppointmentPatient ele = new DispatchAppointmentPatient();
            ele.setPromisePatientId(patient.getPromisePatientId());
            ele.setPatientId(patient.getPatientId());
            ele.setName(patient.getUserName().getName());
            ele.setUserPhone(patient.getPhoneNumber().getPhone());
            ele.setPatientGender(patient.getGender());
            ele.setPatientAge(patient.getCredentialNum().parseAge());
            ele.setServiceItems(serviceItemList);
            patients.add(ele);
        }
        cmd.setPatients(patients);

        List<JdhPromiseExtend> promiseExtends = promise.getPromiseExtends();
        if (CollectionUtils.isNotEmpty(promiseExtends)){
            for (JdhPromiseExtend promiseExtend : promiseExtends) {
                if(promiseExtend.getAttribute().equals(PromiseExtendKeyEnum.ORDER_REMARK.getFiledKey())){
                    cmd.setRemark(promiseExtend.getOrderRemark());
                }
            }
        }
        return cmd;
    }

    /**
     * 扩展点dto转为提交预约的context
     * @param draft
     * @return
     */
    default PromiseSubmitAbilityContext extDto2SubmitContext(ExtAutoSubmitDraft draft, JdhPromise snapshot) {

        PromiseSubmitAbilityContext context = new PromiseSubmitAbilityContext();
        if (CollectionUtils.isNotEmpty(draft.getUsers())){
            List<User> users = Lists.newArrayList();
            for (ExtPromiseUser extUser : draft.getUsers()) {
                User u = new User();
                u.setUserPin(snapshot.getUserPin());
                u.setPatientId(extUser.getPatientId());
                u.setBirthday(new Birthday(extUser.getBirthday()));
                u.setGender(extUser.getGender());
                u.setMarriage(extUser.getMarriage());
                u.setUserName(new UserName(extUser.getName()));
                u.setPhoneNumber(new PhoneNumber(extUser.getPhone()));
                u.setCredentialNum(new CredentialNumber(extUser.getCredentialType(), extUser.getCredentialNo()));
                u.setRelativesType(extUser.getRelativesType());
                users.add(u);
            }
            context.setUsers(users);
        }


        if (Objects.nonNull(draft.getAppointmentTime())){
            PromiseTime appointmentTime = new PromiseTime();
            appointmentTime.setAppointmentStartTime(draft.getAppointmentTime().getAppointmentStartTime());
            appointmentTime.setAppointmentEndTime(draft.getAppointmentTime().getAppointmentEndTime());
            appointmentTime.setDateType(draft.getAppointmentTime().getDateType());
            appointmentTime.setIsImmediately(draft.getAppointmentTime().getIsImmediately());
            context.setAppointmentTime(appointmentTime);
        }

        if (Objects.nonNull(draft.getStation())){
            PromiseStation station = new PromiseStation();
            station.setStoreAddr(draft.getStation().getAddressDetail());
            station.setStoreId(Objects.toString(draft.getStation().getAddressId(), null));
            context.setStation(station);
        }

        context.setRemark(draft.getRemark());

        return context;
    }

    /**
     *
     * @param patients
     * @return
     */
    List<PromisePatientDto> convert2Patient(List<JdhPromisePatient> patients);

    /**
     * 履约单冻结，转换上下文
     * 1、如果是整单冻结，所有人和服务都需要冻结
     * 2、如果是按服务冻结，那么所有患者的这个服务都需要冻结
     * @return
     */
    default PromiseFreezeContext convert2FreezeContext(PromiseFreezeCmd cmd, JdhPromise snapshot){
        if (Objects.isNull(cmd)){
            return null;
        }
        PromiseFreezeContext context = new PromiseFreezeContext();
        context.setReason(cmd.getReason());
        context.setFreezeType(cmd.getFreezeType());
        context.setAllowPromiseStatus(cmd.getAllowPromiseStatus());
        context.setAllowWorkStatus(cmd.getAllowWorkStatus());
        context.setVerticalCode(snapshot.getVerticalCode());
        context.setServiceType(snapshot.getServiceType());
        // 按人维度
        List<PromiseFreezeUser> freezeUser = Lists.newArrayList();
        if (Objects.equals(cmd.getFreezeType(), JdhProcessDataTypeEnum.PROCESS_USER.getType())){

            for (PromisePatientDto promisePatientDto : cmd.getFreeUser()) {
                PromiseFreezeUser user = new PromiseFreezeUser();
                user.setPromiseId(snapshot.getPromiseId());
                user.setPromisePatientId(promisePatientDto.getPromisePatientId());

                List<PromiseService> serviceDetails = Lists.newArrayList();
                for (PromiseServiceDetailDto serviceDetail : promisePatientDto.getServiceDetails()) {
                    PromiseService service = new PromiseService();
                    service.setServiceId(serviceDetail.getServiceId());
                    serviceDetails.add(service);
                }
                user.setServiceDetails(serviceDetails);
                freezeUser.add(user);

            }
        }else if(Objects.equals(cmd.getFreezeType(), JdhProcessDataTypeEnum.PROCESS_SERVICE.getType())){
            for (JdhPromisePatient patient : snapshot.getPatients()) {
                PromiseFreezeUser user = new PromiseFreezeUser();
                user.setPromiseId(patient.getPromiseId());
                user.setPromisePatientId(patient.getPromisePatientId());
                List<PromiseService> serviceDetails = Lists.newArrayList();

                for (PromiseServiceDetailDto promiseServiceDetailDto : cmd.getFreezeService()) {
                    PromiseService service = new PromiseService();
                    service.setServiceId(promiseServiceDetailDto.getServiceId());
                    serviceDetails.add(service);
                }
                user.setServiceDetails(serviceDetails);
                freezeUser.add(user);
            }
        }else if(Objects.equals(cmd.getFreezeType(), JdhProcessDataTypeEnum.PROCESS_PROMISE.getType())){
            for (JdhPromisePatient patient : snapshot.getPatients()) {
                PromiseFreezeUser user = new PromiseFreezeUser();
                user.setPromiseId(patient.getPromiseId());
                user.setPromisePatientId(patient.getPromisePatientId());
                user.setServiceDetails(snapshot.getServices());
                freezeUser.add(user);
            }
        }else {
            throw new BusinessException(PromiseErrorCode.PROMISE_FREEZE_TYPE_NOT_SUPPORT);

        }

        context.setFreezeUser(freezeUser);
        return context;
    }

    /**
     * 作废上下文
     * @param cmd
     * @return
     */
    default PromiseInvalidContext convert2InvalidContext(PromiseInvalidCmd cmd, JdhPromise snapshot){
        PromiseInvalidContext context = new PromiseInvalidContext();
        context.setReason(cmd.getReason());
        context.setInvalidType(cmd.getInvalidType());
        // 按人维度,把当前人的服务全部作废
        List<PromiseInvalidUser> invalidUsers = Lists.newArrayList();
        if (Objects.equals(cmd.getInvalidType(), JdhProcessDataTypeEnum.PROCESS_USER.getType())){

            for (PromisePatientDto promisePatientDto : cmd.getPromisePatient()) {
                PromiseInvalidUser user = new PromiseInvalidUser();
                user.setPromiseId(snapshot.getPromiseId());
                user.setPromisePatientId(promisePatientDto.getPromisePatientId());

                List<PromiseService> serviceDetails = Lists.newArrayList();
                for (PromiseServiceDetailDto serviceDetail : promisePatientDto.getServiceDetails()) {
                    PromiseService service = new PromiseService();
                    service.setServiceId(serviceDetail.getServiceId());
                    serviceDetails.add(service);
                }
                user.setServiceDetails(serviceDetails);
                invalidUsers.add(user);

            }
            // 把当前服务全部作废，每个人的当前服务都作废
        }else if(Objects.equals(cmd.getInvalidType(), JdhProcessDataTypeEnum.PROCESS_SERVICE.getType())){
            for (JdhPromisePatient patient : snapshot.getPatients()) {
                PromiseInvalidUser user = new PromiseInvalidUser();
                user.setPromiseId(patient.getPromiseId());
                user.setPromisePatientId(patient.getPromisePatientId());
                List<PromiseService> serviceDetails = Lists.newArrayList();

                for (PromiseServiceDetailDto promiseServiceDetailDto : cmd.getInvalidService()) {
                    PromiseService service = new PromiseService();
                    service.setServiceId(promiseServiceDetailDto.getServiceId());
                    serviceDetails.add(service);
                }
                user.setServiceDetails(serviceDetails);
                invalidUsers.add(user);
            }
            //全部作废
        }else if(Objects.equals(cmd.getInvalidType(), JdhProcessDataTypeEnum.PROCESS_PROMISE.getType())){
            for (JdhPromisePatient patient : snapshot.getPatients()) {
                PromiseInvalidUser user = new PromiseInvalidUser();
                user.setPromiseId(patient.getPromiseId());
                user.setPromisePatientId(patient.getPromisePatientId());
                user.setServiceDetails(snapshot.getServices());
                invalidUsers.add(user);
            }
        }else {
            throw new BusinessException(PromiseErrorCode.PROMISE_FREEZE_TYPE_NOT_SUPPORT);

        }

        context.setInvalidUsers(invalidUsers);
        return context;
    }

    /**
     * promise实体 转 创建实验室检测单命令
     *
     * @param jdhPromise jdhPromise
     * @return {@link MedicalPromiseCreateCmd}
     */
    default MedicalPromiseCreateCmd promise2CreateMedPromiseCmd(JdhPromise jdhPromise){
        MedicalPromiseCreateCmd cmd = new MedicalPromiseCreateCmd();
        cmd.setVerticalCode(jdhPromise.getVerticalCode());
        cmd.setServiceType(jdhPromise.getServiceType());
        cmd.setUserPin(jdhPromise.getUserPin());
        cmd.setPromiseId(jdhPromise.getPromiseId());
        cmd.setVoucherId(jdhPromise.getVoucherId());

        MedPromiseCmdAppointmentTime appointmentTime = new MedPromiseCmdAppointmentTime();
        appointmentTime.setAppointmentEndTime(jdhPromise.getAppointmentTime().formatAppointmentStartTime());
        appointmentTime.setAppointmentEndTime(jdhPromise.getAppointmentTime().formatAppointmentEndTime());
        appointmentTime.setDateType(jdhPromise.getAppointmentTime().getDateType());
        appointmentTime.setIsImmediately(jdhPromise.getAppointmentTime().getIsImmediately());
        cmd.setAppointmentTime(appointmentTime);

        MedPromiseCmdStation station = new MedPromiseCmdStation();
        station.setStationAddress(jdhPromise.getStore().getStoreAddr());
        cmd.setStation(station);

        List<PromiseService> services = jdhPromise.getServices();
        ProductApplication productApplication = SpringUtil.getBean(ProductApplication.class);
        Map<Long, JdhSkuDto> jdhSkuDtoMap = productApplication.queryJdhSkuInfoList(JdhSkuListRequest.builder()
                .skuIdList(services.stream().map(PromiseService::getServiceId).collect(Collectors.toSet()))
                .querySkuCoreData(Boolean.TRUE)
                .queryServiceItem(Boolean.TRUE)
                .build());
        List<MedPromiseCmdServiceItem> serviceItemList = new ArrayList<>();
        for (PromiseService service : services) {
            JdhSkuDto jdhSkuDto = jdhSkuDtoMap.get(service.getServiceId());
            List<ServiceItemDto> itemList = jdhSkuDto.getServiceItemList();
            if(CollUtil.isEmpty(itemList)){
                MedPromiseCmdServiceItem ele = new MedPromiseCmdServiceItem();
                ele.setServiceId(service.getServiceId());
                serviceItemList.add(ele);
            }else{
                for (ServiceItemDto serviceItemDto : itemList) {
                    MedPromiseCmdServiceItem ele = new MedPromiseCmdServiceItem();
                    ele.setServiceId(service.getServiceId());
                    ele.setItemId(serviceItemDto.getItemId());
                    ele.setItemName(serviceItemDto.getItemName());
                    serviceItemList.add(ele);
                }
            }

        }

        List<MedPromiseCmdAppointmentPatient> patients = new ArrayList<>();
        for (JdhPromisePatient patient : jdhPromise.getPatients()) {
            MedPromiseCmdAppointmentPatient ele = new MedPromiseCmdAppointmentPatient();
            ele.setPromisePatientId(patient.getPromisePatientId());
            ele.setServiceItems(serviceItemList);
            patients.add(ele);
        }
        cmd.setPatientList(patients);
        return cmd;
    }

    /**
     * 实体2 dto
     *
     * @param jdhPromise jdhPromise
     * @return {@link PromiseDto}
     */
    @Mappings({
            @Mapping(target = "expireDate",expression = "java(com.jdh.o2oservice.base.util.TimeUtils.localDateTimeToDate(jdhPromise.getExpireDate()))"),
            @Mapping(target = "appointmentTime",expression = "java(convert2PromiseAppointmentTimeDto(jdhPromise.getAppointmentTime()))"),
    })
    PromiseDto entity2Dto(JdhPromise jdhPromise);


    /**
     * convert2PromiseAppointmentTimeDto
     *
     * @param appointmentTime 预约时间
     * @return {@link PromiseAppointmentTimeDto}
     */
    @Named("convert2PromiseAppointmentTimeDto")
    default PromiseAppointmentTimeDto convert2PromiseAppointmentTimeDto(PromiseAppointmentTime appointmentTime){
        if(Objects.isNull(appointmentTime)){
            return null;
        }
        PromiseAppointmentTimeDto dto = new PromiseAppointmentTimeDto();
        dto.setDateType(appointmentTime.getDateType());
        dto.setAppointmentStartTime(TimeUtils.localDateTimeToDate(appointmentTime.getAppointmentStartTime()));
        dto.setAppointmentEndTime(TimeUtils.localDateTimeToDate(appointmentTime.getAppointmentEndTime()));
        dto.setIsImmediately(appointmentTime.getIsImmediately());
        return dto;
    }

    /**
     * 根据履约单和检测单聚合每个人拥有哪些检测服务
     * @param jdhPromise
     * @return
     */
    default PromiseDto entity2Dto(JdhPromise jdhPromise, List<MedicalPromiseBO> medicalPromises){
        PromiseDto dto = entity2Dto(jdhPromise);
        if (Objects.isNull(dto)){
            return dto;
        }

        // 根据患者、service，单据状态进行分组
        Table<Long, Long, Set<Integer>> medicalStatusMap = HashBasedTable.create();
        // 根据患者、service，冻结状态进行分组
        Table<Long, Long, Set<Integer>> freezeMap = HashBasedTable.create();
        // 构建service维度的table
        Map<Long, Set<PromiseService>> serviceMap = Maps.newHashMap();
        // 当前履约单存在的所有服务信息分组
        Map<Long, PromiseService> promiseServiceMap =  jdhPromise.getServices().stream().collect(Collectors.toMap(PromiseService::getServiceId, Function.identity(), (o,n)->o));
        for (MedicalPromiseBO medicalPromiseBO : medicalPromises) {
            // 根据患者、service、单据状态进行分组
            Set<Integer> status = medicalStatusMap.get(medicalPromiseBO.getPromisePatientId(), medicalPromiseBO.getServiceId());
            if (Objects.isNull(status)){
                status = Sets.newHashSet();
            }
            status.add(medicalPromiseBO.getStatus());
            medicalStatusMap.put(medicalPromiseBO.getPromisePatientId(), medicalPromiseBO.getServiceId(), status);

            // 根据患者、service、单据是否冻结进行分组，最终用来设置一个服务是否被冻结
            Set<Integer> freezeStatus = freezeMap.get(medicalPromiseBO.getPromisePatientId(), medicalPromiseBO.getServiceId());
            if (Objects.isNull(freezeStatus)){
                freezeStatus = Sets.newHashSet();
            }
            freezeStatus.add(medicalPromiseBO.getFreeze());
            freezeMap.put(medicalPromiseBO.getPromisePatientId(), medicalPromiseBO.getServiceId(), freezeStatus);

            PromiseService service = promiseServiceMap.get(medicalPromiseBO.getServiceId());
            Set<PromiseService> services = serviceMap.computeIfAbsent(medicalPromiseBO.getPromisePatientId(), k-> new HashSet());
            services.add(service);

        }


        for (PromisePatientDto patient : dto.getPatients()) {
            Set<PromiseService> services = serviceMap.get(patient.getPromisePatientId());
            List<PromiseServiceDetailDto> serviceDetails = Lists.newArrayList();

            /**
             * 因为一个服务（service）可能有多个检测项目，最终service的状态由项目聚合出来
             */
            for (PromiseService service : services) {
                Set<Integer> status = medicalStatusMap.get(patient.getPromisePatientId(), service.getServiceId());
                Set<Integer> freezeStatus = freezeMap.get(patient.getPromisePatientId(), service.getServiceId());
                PromiseServiceDetailDto serviceDetailDto = new PromiseServiceDetailDto();
                BeanUtils.copyProperties(service, serviceDetailDto);
                // 聚合SKU状态，
                if (Objects.nonNull(status) && status.size() == 1){
                    if (status.contains(MedicalPromiseStatusEnum.COMPLETED.getStatus())){
                        serviceDetailDto.setSettleStatus(PromiseServiceDetailDto.COMPLETE_STATUS);
                    }else if(status.contains(MedicalPromiseStatusEnum.INVALID.getStatus())){
                        serviceDetailDto.setSettleStatus(PromiseServiceDetailDto.INVALID_STATUS);
                    }else{
                        serviceDetailDto.setSettleStatus(PromiseServiceDetailDto.OTHER_STATUS);
                    }
                }else{
                    serviceDetailDto.setSettleStatus(PromiseServiceDetailDto.OTHER_STATUS);
                }
                // 聚合冻结状态，因为一个sku可以有多个检测项目，所以SKU的状态有检测项目聚合处理
                if (Objects.nonNull(freezeStatus) && freezeStatus.size() == 1){
                    if (freezeStatus.contains(YnStatusEnum.YES.getCode())){
                        serviceDetailDto.setFreeze(YnStatusEnum.YES.getCode());
                    }else{
                        serviceDetailDto.setFreeze(YnStatusEnum.NO.getCode());
                    }
                }else{
                    serviceDetailDto.setFreeze(YnStatusEnum.NO.getCode());
                }
                serviceDetails.add(serviceDetailDto);
            }
            patient.setServiceDetails(serviceDetails);
        }

        return dto;

    }

    /**
     * 实体2 dto列表
     *
     * @param promiseList promiseList
     * @return {@link List}<{@link PromiseDto}>
     */
    List<PromiseDto> entity2DtoList(List<JdhPromise> promiseList);

    /**
     *
     * @param statusBO
     * @return
     */
    default FreezeStateDto freezeBo2StatusDto(PromiseFreezeStatusBO statusBO){
        FreezeStateDto dto = new FreezeStateDto();
        dto.setWorkId(statusBO.getWorkId());
        dto.setFreezeWorkStatus(statusBO.getWorkStatus());
        dto.setHasAvailableService(statusBO.getHasAvailableService());
        return dto;
    }

    default JdhO2oPromiseIndexCmd convertToJdhO2oPromiseIndexCmd(JdhPromise jdhPromise, ProviderPromise providerPromise){
        JdhO2oPromiseIndexCmd cmd = new JdhO2oPromiseIndexCmd();
        cmd.setVerticalCode(jdhPromise.getVerticalCode());
        cmd.setServiceType(jdhPromise.getServiceType());
        cmd.setPromiseId(jdhPromise.getPromiseId());
        cmd.setPromiseStatus(jdhPromise.getPromiseStatus());
        cmd.setCreateTime(jdhPromise.getCreateTime());
        cmd.setUpdateTime(jdhPromise.getUpdateTime());
        cmd.setUserPin(jdhPromise.getUserPin());
        cmd.setChannelNo(Objects.nonNull(jdhPromise.getChannelNo()) ? String.valueOf(jdhPromise.getChannelNo()) : null);
        cmd.setDateType(Objects.nonNull(jdhPromise.getAppointmentTime()) ? jdhPromise.getAppointmentTime().getDateType() : null);

        List<JdhPromisePatient> patients = jdhPromise.getPatients();
        if(CollectionUtils.isNotEmpty(patients)){
            JdhPromisePatient jdhPromisePatient = patients.get(0);
            cmd.setBirthday(Objects.nonNull(jdhPromisePatient.getBirthday()) ? jdhPromisePatient.getBirthday().getBirth() : null);
            cmd.setUserAge(Objects.nonNull(jdhPromisePatient.getBirthday()) ? jdhPromisePatient.getBirthday().getAge() : null);
            cmd.setUserGender(jdhPromisePatient.getGender());
            cmd.setMarriage(jdhPromisePatient.getMarriage());
            cmd.setUserCredentialNo(Objects.nonNull(jdhPromisePatient.getCredentialNum()) ? jdhPromisePatient.getCredentialNum().getCredentialNo() : null);
            cmd.setUserCredentialType(Objects.nonNull(jdhPromisePatient.getCredentialNum()) ? jdhPromisePatient.getCredentialNum().getCredentialType() : null);
            cmd.setUserPhone(Objects.nonNull(jdhPromisePatient.getPhoneNumber()) ? jdhPromisePatient.getPhoneNumber().getPhone() : null);
            cmd.setUserName(Objects.nonNull(jdhPromisePatient.getUserName()) ? jdhPromisePatient.getUserName().getName() : null);
        }

        if(Objects.nonNull(providerPromise)){
            cmd.setProcessStatus(providerPromise.getProcessStatus());
        }else {
            cmd.setProcessStatus(CommonConstant.ZERO);
        }

        PromiseStation store = jdhPromise.getStore();
        if(Objects.nonNull(store)) {
            cmd.setStoreId(store.getStoreId());
            cmd.setStoreName(store.getStoreName());
        }

        PromiseAppointmentTime appointmentTime = jdhPromise.getAppointmentTime();
        if(Objects.nonNull(appointmentTime)){
            cmd.setAppointmentStartTime(TimeUtils.localDateTimeToDate(appointmentTime.getAppointmentStartTime()));
            cmd.setAppointmentEndTime(TimeUtils.localDateTimeToDate(appointmentTime.getAppointmentEndTime()));
            cmd.setExpireDate(TimeUtils.localDateTimeToDate(jdhPromise.getExpireDate()));
        }
        return cmd;
    }

    O2oPromiseIndex convertToO2oPromiseIndexCmd(JdhO2oPromiseIndexCmd jdhO2oPromiseIndexCmd);

    PromiseIndexPageQuery convertToPromiseIndexPageQuery(StoreProgramPromiseQuery storeProgramPromiseQuery);

    O2oPromiseIndexDto convertToO2oPromiseIndexDto(O2oPromiseIndex o2oPromiseIndex);

    List<O2oPromiseIndexDto> convertToO2oPromiseIndexDtoList(List<O2oPromiseIndex> o2oPromiseIndexList);

}
