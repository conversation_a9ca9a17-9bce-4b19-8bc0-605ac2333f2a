package com.jdh.o2oservice.application.settlement.handler.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.jdh.o2oservice.application.settlement.context.SettlementEbsContext;
import com.jdh.o2oservice.application.settlement.handler.SettlementEbsHandler;
import com.jdh.o2oservice.application.settlement.handler.SettlementEbsHandlerManager;
import com.jdh.o2oservice.application.settlement.handler.SettlementEbsUtil;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.errorcode.SystemErrorCode;
import com.jdh.o2oservice.common.enums.BusinessModeEnum;
import com.jdh.o2oservice.common.enums.EbsSettleMainBodyTypeEnum;
import com.jdh.o2oservice.common.enums.EbsSettleSplitTypeEnum;
import com.jdh.o2oservice.common.enums.EbsSettleTypeEnum;
import com.jdh.o2oservice.core.domain.settlement.bo.JdOrderDetailBo;
import com.jdh.o2oservice.core.domain.settlement.model.AngelSettlementAndEbsDetail;
import com.jdh.o2oservice.core.domain.settlement.model.JdhSettlementEbs;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 护士上门（护士检测+护士服务）--费项收入
 * @date 2024-05-31 09:50
 * <AUTHOR>
 */
@Slf4j
@Component
public class HomeAngelPeriodJdIncomeEbsHandler implements SettlementEbsHandler {
    /**
     * 结算前置计算构建对象
     *
     * @param settlementEbsContext context
     * @return ebs
     */
    @Override
    public JdhSettlementEbs prepareEbs(SettlementEbsContext settlementEbsContext) {
        log.info("HomeAngelPeriodJdIncomeEbsHandler#prepareEbs settlementEbsContext={}", JSON.toJSONString(settlementEbsContext));
        if (settlementEbsContext == null || settlementEbsContext.getExtBusinessModel() == null) {
            throw new BusinessException(SystemErrorCode.PARAM_NULL_ERROR.formatDescription("extBusinessModel为空"));
        }
        JdhSettlementEbs jdhSettlementEbs = settlementEbsContext.getJdhSettlementEbs();
        if (jdhSettlementEbs == null) {
            jdhSettlementEbs = new JdhSettlementEbs();
        }
        if (settlementEbsContext.getExtBusinessModel() instanceof AngelSettlementAndEbsDetail) {
            AngelSettlementAndEbsDetail andEbsDetail = (AngelSettlementAndEbsDetail) settlementEbsContext.getExtBusinessModel();
            JdOrderDetailBo jdOrderDetailBo = andEbsDetail.getJdOrderDetailBo();
            String preId = SettlementEbsUtil.appendSplitMultiKey(CommonConstant.CHARACTER_UNDERLINE, andEbsDetail.getSettlementBusinessId(), settlementEbsContext.getEbsSettleSplitTypeEnum().getType(), settlementEbsContext.getEbsSettleTypeEnum().getType().toString());
            String preDocNum = SettlementEbsUtil.appendSplitMultiKey(CommonConstant.CHARACTER_UNDERLINE, andEbsDetail.getSettlementBusinessId(), settlementEbsContext.getEbsSettleSplitTypeEnum().getType(), settlementEbsContext.getEbsSettleTypeEnum().getType().toString());
            jdhSettlementEbs.setPreId(preId);
            jdhSettlementEbs.setPreDocNum(preDocNum);
            jdhSettlementEbs.setDocCreateTime(jdOrderDetailBo.getPaymentTime());
            jdhSettlementEbs.setAppliedDate(andEbsDetail.getSettleTime());
            jdhSettlementEbs.setTotalAmount(andEbsDetail.getSettleAmout());
            jdhSettlementEbs.setOrderId(jdOrderDetailBo.getOrderId());
            jdhSettlementEbs.setParentOrderId(jdOrderDetailBo.getParentId());
        } else {
            throw new BusinessException(SystemErrorCode.PARAM_NULL_ERROR.formatDescription("extBusinessModel不合法"));
        }
        return jdhSettlementEbs;
    }

    /**
     * 结算前置计算构建对象
     *
     * @param settlementEbsContext context
     */
    @Override
    public void afterEbsSend(SettlementEbsContext settlementEbsContext) {
        log.info("HomeAngelPeriodJdIncomeEbsHandler#afterEbsSend settlementEbsContext={}", JSON.toJSONString(settlementEbsContext));
    }

    /**
     * 结算前置计算构建对象
     *
     * @return ebs
     */
    @Override
    public List<String> settlementSceneCode() {
        String angelTest = SettlementEbsUtil.getSettlementSceneCode(BusinessModeEnum.ANGEL_TEST, EbsSettleTypeEnum.INCOME, EbsSettleSplitTypeEnum.HOME_PERIOD_FEE, EbsSettleMainBodyTypeEnum.JD);
        String angelCare = SettlementEbsUtil.getSettlementSceneCode(BusinessModeEnum.ANGEL_CARE, EbsSettleTypeEnum.INCOME, EbsSettleSplitTypeEnum.HOME_PERIOD_FEE, EbsSettleMainBodyTypeEnum.JD);
        String angelTestNoLaboratory = SettlementEbsUtil.getSettlementSceneCode(BusinessModeEnum.ANGEL_TEST_NO_LABORATORY, EbsSettleTypeEnum.INCOME, EbsSettleSplitTypeEnum.HOME_PERIOD_FEE, EbsSettleMainBodyTypeEnum.JD);
        return Stream.of(angelTest, angelCare, angelTestNoLaboratory).collect(Collectors.toList());
    }

    /**
     */
    @Override
    public void afterPropertiesSet() {
        if (CollUtil.isEmpty(this.settlementSceneCode())) {
            return;
        }
        for (String sceneCode : this.settlementSceneCode()) {
            SettlementEbsHandlerManager.register(sceneCode, this);
        }
    }
}
