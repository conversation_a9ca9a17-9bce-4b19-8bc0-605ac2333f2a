package com.jdh.o2oservice.application.promise.event;

import com.alibaba.fastjson.JSON;
import com.jdh.o2oservice.application.promise.service.PromiseApplication;
import com.jdh.o2oservice.base.event.Event;
import com.jdh.o2oservice.base.event.EventConsumerRegister;
import com.jdh.o2oservice.base.event.EventConsumerRetryTemplate;
import com.jdh.o2oservice.base.event.WrapperEventConsumer;
import com.jdh.o2oservice.base.model.DomainEnum;
import com.jdh.o2oservice.common.enums.AppointCallBackEnum;
import com.jdh.o2oservice.common.enums.BusinessModeEnum;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelWorkAggregateEnum;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelWorkEventTypeEnum;
import com.jdh.o2oservice.core.domain.angelpromise.event.eventbody.AngelWorkEventBody;
import com.jdh.o2oservice.core.domain.dispatch.enums.DispatchEventTypeEnum;
import com.jdh.o2oservice.core.domain.dispatch.event.DispatchCallbackEventBody;
import com.jdh.o2oservice.core.domain.medpromise.enums.MedPromiseAggregateEnum;
import com.jdh.o2oservice.core.domain.medpromise.enums.MedPromiseEventTypeEnum;
import com.jdh.o2oservice.core.domain.medpromise.event.MedicalPromiseAggregateEventBody;
import com.jdh.o2oservice.core.domain.medpromise.event.MedicalPromiseEventBody;
import com.jdh.o2oservice.core.domain.medpromise.repository.db.MedicalPromiseRepository;
import com.jdh.o2oservice.core.domain.promise.enums.PromiseEventTypeEnum;
import com.jdh.o2oservice.core.domain.promise.event.PromiseModifyEventBody;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromise;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromiseIdentifier;
import com.jdh.o2oservice.core.domain.promise.repository.db.PromiseRepository;
import com.jdh.o2oservice.core.domain.support.basic.enums.JdhPromiseStatusEnum;
import com.jdh.o2oservice.core.domain.support.vertical.model.JdhVerticalBusiness;
import com.jdh.o2oservice.core.domain.support.vertical.repository.VerticalBusinessRepository;
import com.jdh.o2oservice.export.promise.cmd.PromiseCallbackCmd;
import com.jdh.o2oservice.export.promise.cmd.PromiseCreateMedPromiseCmd;
import com.jdh.o2oservice.export.promise.cmd.PromiseDelayCmd;
import com.jdh.o2oservice.export.promise.cmd.PromiseDispatchCmd;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Objects;

/**
 * 履约单豫，事件订阅处理
 *
 * @author: yangxiyu
 * @date: 2023/12/18 3:34 下午
 * @version: 1.0
 */
@Slf4j
@Component
public class PromiseEventSubscriber {

    /**
     * 事件消费注册
     */
    @Resource
    private EventConsumerRegister eventConsumerRegister;

    /**
     * promiseApplication
     */
    @Resource
    private PromiseApplication promiseApplication;

    /**
     * verticalBusinessRepository
     */
    @Autowired
    private VerticalBusinessRepository verticalBusinessRepository;

    /**
     * promiseRepository
     */
    @Resource
    private PromiseRepository promiseRepository;

    /**
     * promiseRepository
     */
    @Resource
    private MedicalPromiseRepository medicalPromiseRepository;
    /**
     * 注册事件使用者
     */
    @PostConstruct
    public void registerEventConsumer() {


        // 履约单延期
        eventConsumerRegister.register(PromiseEventTypeEnum.VOUCHER_DELAY, WrapperEventConsumer.newInstance(DomainEnum.PROMISE,
                "delayPromise", this::delayPromise, Boolean.FALSE, Boolean.FALSE));

        /**
         * 下面的consumerCode和事件要调整，看看其他领域的事件如何定义，原则
         * 多个event，可以公用一个consumerCode。
         * 多个consumerCode，消费同一个event
         * 但是一个event + 一个 consumerCode 不能多次注册
         */
        //=======>>>>>> 履约单 自动预约后 派单
        eventConsumerRegister.register(Arrays.asList(PromiseEventTypeEnum.PROMISE_AUTO_SUBMIT, PromiseEventTypeEnum.PROMISE_SUBMIT), WrapperEventConsumer.newInstance(DomainEnum.PROMISE,
                "promiseDispatch", this::promiseDispatch, Boolean.TRUE, Boolean.TRUE, EventConsumerRetryTemplate.fixRetryInstance(3,60000)));

        //=======>>>>>> 履约单 修改预约后 派单
        eventConsumerRegister.register(PromiseEventTypeEnum.PROMISE_USER_SUBMIT_MODIFY, WrapperEventConsumer.newInstance(DomainEnum.PROMISE,
                "promiseDispatch", this::promiseModifyDispatch, Boolean.TRUE, Boolean.TRUE));

        //=======>>>>>> 履约单 监听派单成功消息 -> 变更为预约成功
        eventConsumerRegister.register(DispatchEventTypeEnum.DISPATCH_RECEIVED, WrapperEventConsumer.newInstance(DomainEnum.PROMISE,
                "promiseDispatchSuccess", this::promiseDispatchSuccess, Boolean.TRUE, Boolean.TRUE));

        //=======>>>>>> 履约单 监听派单成功消息 -> 变更为修改预约成功
        eventConsumerRegister.register(DispatchEventTypeEnum.MODIFY_DATE_DISPATCH_SUCCESS, WrapperEventConsumer.newInstance(DomainEnum.PROMISE,
                "promiseDispatchModifySuccess", this::promiseDispatchModifySuccess, Boolean.TRUE, Boolean.TRUE));

        //=======>>>>>> 履约单 监听派单失败消息 -> 变更为修改预约失败
        eventConsumerRegister.register(DispatchEventTypeEnum.MODIFY_DATE_DISPATCH_FAIL, WrapperEventConsumer.newInstance(DomainEnum.PROMISE,
                "promiseDispatchModifyFail", this::promiseDispatchModifyFail, Boolean.TRUE, Boolean.TRUE));

        //=======>>>>>> 履约单 监听派单成功消息 -> 变更为预约成功
        eventConsumerRegister.register(DispatchEventTypeEnum.DISPATCH_SUCCESS_AFTER_MODIFY_DATE_SUCCESS, WrapperEventConsumer.newInstance(DomainEnum.PROMISE,
                "promiseDispatchSuccess", this::promiseDispatchSuccess, Boolean.TRUE, Boolean.TRUE));

        eventConsumerRegister.register(DispatchEventTypeEnum.DISPATCH_TARGET_DISPATCH, WrapperEventConsumer.newInstance(DomainEnum.PROMISE,
                "promiseDispatchSuccess", this::promiseDispatchSuccess, Boolean.TRUE, Boolean.TRUE));

        eventConsumerRegister.register(DispatchEventTypeEnum.DISPATCH_ASSIGN_SUCCESS_RECEIVED, WrapperEventConsumer.newInstance(DomainEnum.PROMISE,
                "promiseDispatchSuccess", this::promiseDispatchSuccess, Boolean.TRUE, Boolean.TRUE));

        //=======>>>>>> 履约单 创建实验室检测单 事件同步消费的方式进行同步创建。保证在一个事物里
        eventConsumerRegister.register(PromiseEventTypeEnum.PROMISE_CREATED, WrapperEventConsumer.newInstance(DomainEnum.PROMISE,
                "createMedPromise", this::createMedPromise, Boolean.FALSE, Boolean.TRUE));

        //=======>>>>>> 履约单 监听服务者履约域 护士开始服务，护士消费码验证成功，骑手待取货 -> 状态变更为 待服务
        eventConsumerRegister.register(AngelWorkEventTypeEnum.ANGEL_WORK_EVENT_WAITING_SERVED, WrapperEventConsumer.newInstance(DomainEnum.PROMISE,
                "promiseWaitService", this::promiseWaitService, Boolean.TRUE, Boolean.TRUE));

        //=======>>>>>> 履约单 监听 护士消费码验证完成，骑手到店 -> 状态变更为 服务中
        eventConsumerRegister.register(AngelWorkEventTypeEnum.ANGEL_WORK_EVENT_IN_SERVED, WrapperEventConsumer.newInstance(DomainEnum.PROMISE,
                "promiseServicing", this::promiseServicing, Boolean.TRUE, Boolean.TRUE));

        //=======>>>>>> 履约单 监听 护士服务完成，骑手取货完成，-> 状态变更为 服务完成
        eventConsumerRegister.register(Arrays.asList(AngelWorkEventTypeEnum.ANGEL_WORK_EVENT_DONE_SERVED,
                MedPromiseEventTypeEnum.MED_PROMISE_BIND_SPECIMEN_CODE), WrapperEventConsumer.newInstance(DomainEnum.PROMISE,
                "promiseServiceComplete", this::promiseServiceComplete, Boolean.TRUE, Boolean.TRUE));

        //=======>>>>>> 履约单 监听检测单报告已出，且所有报告已出，状态变更为 履约完成
        eventConsumerRegister.register(MedPromiseEventTypeEnum.MED_PROMISE_ALL_COMPLETE, WrapperEventConsumer.newInstance(DomainEnum.PROMISE,
                "promiseMedPromiseAllComplete", this::promiseMedPromiseAllComplete, Boolean.TRUE, Boolean.TRUE));

        // mock异步回调操作，到家场景的取消操作是同步返回结果，但是为了适配整体流程，通过监听事件修改状态为取消成功，预留取消中的状态的处理逻辑。
        eventConsumerRegister.register(PromiseEventTypeEnum.PROMISE_USER_SUBMIT_CANCEL, WrapperEventConsumer.newInstance(DomainEnum.PROMISE,
                "mockCallBack", this::mockCallBack, Boolean.TRUE, Boolean.TRUE));

    }

    /**
     * mock预约回调成功，到家服务模式下，提交取消默认就是成功的，为了更平滑一致，提交取消会变成取消中，mock回调修改为取消成功
     * @param event
     */
    private void mockCallBack(Event event) {
        Long promiseId;
        if (StringUtils.equals(event.getEventCode(), PromiseEventTypeEnum.PROMISE_USER_SUBMIT_CANCEL.getCode())) {
            promiseId = Long.valueOf(event.getAggregateId());
            JdhPromise promise = promiseRepository.find(new JdhPromiseIdentifier(promiseId));
            JdhVerticalBusiness jdhVerticalBusiness = verticalBusinessRepository.find(promise.getVerticalCode());
            // 只有到家业务会取消回调mock
            if (StringUtils.equals(jdhVerticalBusiness.getBusinessModeCode(), BusinessModeEnum.SELF_TEST.getCode())
                    || StringUtils.equals(jdhVerticalBusiness.getBusinessModeCode(), BusinessModeEnum.ANGEL_TEST.getCode())
                    || StringUtils.equals(jdhVerticalBusiness.getBusinessModeCode(), BusinessModeEnum.ANGEL_CARE.getCode())){
                PromiseCallbackCmd cmd = PromiseCallbackCmd.builder()
                        .callbackCode(AppointCallBackEnum.CANCEL_SUCC.getCode())
                        .promiseId(promiseId)
                        .build();
                promiseApplication.callBack(cmd);
            }
        }
    }

    /**
     * 创建实验室履约单
     *
     * @param event 事件
     */
    private void createMedPromise(Event event) {
        log.info("PromiseEventSubscriber -> promiseDispatchSuccess event:{}", JSON.toJSONString(event));
        //DispatchCallbackEventBody eventBody = JSON.parseObject(event.getBody(), DispatchCallbackEventBody.class);
        promiseApplication.createMedPromise(PromiseCreateMedPromiseCmd.builder().promiseId(Long.parseLong(event.getAggregateId())).build());
    }

    /**
     * 履约单下的服务单全部完成
     * *     收当前履约单下的最后一个已出报告事件
     * *     AppointCallBackEnum.PROMISE_COMPLETE("60000", "履约完成")
     *
     * @param event 事件
     */
    private void promiseMedPromiseAllComplete(Event event) {
        log.info("PromiseEventSubscriber -> promiseMedPromiseAllComplete event:{}", JSON.toJSONString(event));
        MedicalPromiseAggregateEventBody medicalPromiseAggregateEventBody = JSON.parseObject(event.getBody(), MedicalPromiseAggregateEventBody.class);
        PromiseCallbackCmd cmd = PromiseCallbackCmd.builder()
                .callbackCode(AppointCallBackEnum.PROMISE_COMPLETE.getCode())
                .promiseId(medicalPromiseAggregateEventBody.getPromiseId())
                .build();
        promiseApplication.callBack(cmd);
    }

    /**
     * 履约 服务完成
     * *     收 护士服务完成，或者骑手流程下的服务完成事件，最后转 内部服务完成
     * *     AppointCallBackEnum.SERVICE_COMPLETE("50003", "服务完成"),
     *
     * @param event 事件
     */
    private void promiseServiceComplete(Event event) {
        log.info("PromiseEventSubscriber -> promiseServiceComplete event:{}", JSON.toJSONString(event));
        Long promiseId = null;
        //如果是work事件
        if (AngelWorkAggregateEnum.WORK.getCode().equals(event.getAggregateCode())
                && AngelWorkEventTypeEnum.ANGEL_WORK_EVENT_DONE_SERVED.getCode().equals(event.getEventCode())) {
            AngelWorkEventBody angelWorkEventBody = JSON.parseObject(event.getBody(), AngelWorkEventBody.class);
            promiseId = angelWorkEventBody.getPromiseId();

        //如果是检测单事件
        }else if(MedPromiseAggregateEnum.MED_PROMISE.getCode().equals(event.getAggregateCode())){
            MedicalPromiseEventBody medicalPromiseEventBody = JSON.parseObject(event.getBody(), MedicalPromiseEventBody.class);
            promiseId = medicalPromiseEventBody.getPromiseId();
            JdhPromise promise = promiseRepository.find(new JdhPromiseIdentifier(promiseId));
            JdhVerticalBusiness jdhVerticalBusiness = verticalBusinessRepository.find(promise.getVerticalCode());
            //如果是骑手上门，则绑码、服务完成事件（骑手取货）都处理，否则只处理服务完成事件
            if (!StringUtils.equals(jdhVerticalBusiness.getBusinessModeCode(), BusinessModeEnum.SELF_TEST.getCode())) {
                log.info("PromiseEventSubscriber -> promiseServiceComplete 回调服务完成 非骑手业务，不处理绑码事件");
                return;
            }
        }

        if(Objects.isNull(promiseId)){
            log.info("PromiseEventSubscriber -> promiseServiceComplete 回调服务完成 promiseId 不存在");
            return;
        }

        PromiseCallbackCmd cmd = PromiseCallbackCmd.builder()
                .callbackCode(AppointCallBackEnum.SERVICE_COMPLETE.getCode())
                .promiseId(promiseId)
                .build();
        promiseApplication.callBack(cmd);

//
//        Long promiseId = null;
//        if (AngelWorkAggregateEnum.WORK.getCode().equals(event.getAggregateCode())
//                && AngelWorkEventTypeEnum.ANGEL_WORK_EVENT_DONE_SERVED.getCode().equals(event.getEventCode())) {
//            AngelWorkEventBody angelWorkEventBody = JSON.parseObject(event.getBody(), AngelWorkEventBody.class);
//            promiseId = angelWorkEventBody.getPromiseId();
//            JdhPromise promise = promiseRepository.find(new JdhPromiseIdentifier(promiseId));
//            JdhVerticalBusiness jdhVerticalBusiness = verticalBusinessRepository.find(promise.getVerticalCode());
//            if (StringUtils.equals(jdhVerticalBusiness.getBusinessModeCode(), BusinessModeEnum.ANGEL_TEST.getCode())
//                    || StringUtils.equals(jdhVerticalBusiness.getBusinessModeCode(), BusinessModeEnum.ANGEL_CARE.getCode())) {
//                PromiseCallbackCmd cmd = PromiseCallbackCmd.builder()
//                        .callbackCode(AppointCallBackEnum.SERVICE_COMPLETE.getCode())
//                        .promiseId(promiseId)
//                        .build();
//                promiseApplication.callBack(cmd);
//            }
//        } else if (MedPromiseAggregateEnum.MED_PROMISE.getCode().equals(event.getAggregateCode())) {
//            MedicalPromiseEventBody medicalPromiseEventBody = JSON.parseObject(event.getBody(), MedicalPromiseEventBody.class);
//            //只有骑手上门自检测才处理这个事件
//            String verticalCode = medicalPromiseEventBody.getVerticalCode();
//            JdhVerticalBusiness jdhVerticalBusiness = verticalBusinessRepository.find(verticalCode);
//            if (!BusinessModeEnum.SELF_TEST.getCode().equals(jdhVerticalBusiness.getBusinessModeCode())) {
//                return;
//            }
//
//            promiseId = medicalPromiseEventBody.getPromiseId();
//            JdhPromise promise = promiseRepository.find(new JdhPromiseIdentifier(promiseId));
//            // promise不是服务完成状态时才消费
//            if (!Objects.equals(promise.getPromiseStatus(), JdhPromiseStatusEnum.SERVICE_COMPLETE.getStatus())){
//                PromiseCallbackCmd cmd = PromiseCallbackCmd.builder()
//                        .callbackCode(AppointCallBackEnum.SERVICE_COMPLETE.getCode())
//                        .promiseId(promiseId)
//                        .build();
//                promiseApplication.callBack(cmd);
//            }
//        } else {
//            log.info("放弃处理该事件，未匹配到对应的业务模式");
//        }
    }

    /**
     * 履约服务开始
     * * 收 验证码验证事件，或者骑手流程 骑手已上门事件
     * *     AppointCallBackEnum.SERVICING("50002", "服务中"),
     *
     * @param event 事件
     */
    private void promiseServicing(Event event) {
        log.info("PromiseEventSubscriber -> promiseServicing event:{}", JSON.toJSONString(event));
        AngelWorkEventBody angelWorkEventBody = JSON.parseObject(event.getBody(), AngelWorkEventBody.class);
        PromiseCallbackCmd cmd = PromiseCallbackCmd.builder()
                .callbackCode(AppointCallBackEnum.SERVICING.getCode())
                .promiseId(angelWorkEventBody.getPromiseId())
                .build();
        promiseApplication.callBack(cmd);
    }


    /**
     * 履约 待服务
     * * 收 护士已出门 或者骑手流程相关
     * *     AppointCallBackEnum.SERVICE_READY("50001", "待服务"),
     *
     * @param event 事件
     */
    private void promiseWaitService(Event event) {
        log.info("PromiseEventSubscriber -> promiseWaitService event:{}", JSON.toJSONString(event));
        String body = event.getBody();
        AngelWorkEventBody angelWorkEventBody = JSON.parseObject(body, AngelWorkEventBody.class);
        PromiseCallbackCmd cmd = PromiseCallbackCmd.builder()
                .callbackCode(AppointCallBackEnum.SERVICE_READY.getCode())
                .promiseId(angelWorkEventBody.getPromiseId())
                .build();
        promiseApplication.callBack(cmd);
    }

    /**
     * 派单成功
     * * 收 派单成功 结果
     * *     AppointCallBackEnum.MODIFY_SUCC("20000", "修改成功"),
     *
     * @param event 事件
     */
    private void promiseDispatchModifySuccess(Event event) {
        log.info("PromiseEventSubscriber -> promiseDispatchModifySuccess event:{}", JSON.toJSONString(event));
        DispatchCallbackEventBody eventBody = JSON.parseObject(event.getBody(), DispatchCallbackEventBody.class);
        JdhPromise promise = promiseRepository.find(new JdhPromiseIdentifier(eventBody.getPromiseId()));
        JdhVerticalBusiness jdhVerticalBusiness = verticalBusinessRepository.find(promise.getVerticalCode());

        if (Objects.equals(promise.getPromiseStatus(), JdhPromiseStatusEnum.MODIFY_ING.getStatus())) {
            PromiseCallbackCmd cmd = PromiseCallbackCmd.builder()
                    .callbackCode(AppointCallBackEnum.MODIFY_SUCC.getCode())
                    .promiseId(eventBody.getPromiseId())
                    .build();
            promiseApplication.callBack(cmd);
        }

    }

    /**
     * 派单成功
     * * 收 派单成功 结果
     * *     AppointCallBackEnum.MODIFY_SUCC("20000", "修改成功"),
     *
     * @param event 事件
     */
    private void promiseDispatchModifyFail(Event event) {
        log.info("PromiseEventSubscriber -> promiseDispatchModifyFail event:{}", JSON.toJSONString(event));
        DispatchCallbackEventBody eventBody = JSON.parseObject(event.getBody(), DispatchCallbackEventBody.class);
        JdhPromise promise = promiseRepository.find(new JdhPromiseIdentifier(eventBody.getPromiseId()));
        JdhVerticalBusiness jdhVerticalBusiness = verticalBusinessRepository.find(promise.getVerticalCode());

        if (Objects.equals(promise.getPromiseStatus(), JdhPromiseStatusEnum.MODIFY_ING.getStatus())) {
            PromiseCallbackCmd cmd = PromiseCallbackCmd.builder()
                    .callbackCode(AppointCallBackEnum.MODIFY_FAIL.getCode())
                    .promiseId(eventBody.getPromiseId())
                    .build();
            promiseApplication.callBack(cmd);
        }

    }

    /**
     * 派单成功
     * * 收 派单成功 结果
     * *     AppointCallBackEnum.DISPATCH_SUCCESS("50000", "派单成功"),
     *
     * @param event 事件
     */
    private void promiseDispatchSuccess(Event event) {
        log.info("PromiseEventSubscriber -> promiseDispatchSuccess event:{}", JSON.toJSONString(event));
        DispatchCallbackEventBody eventBody = JSON.parseObject(event.getBody(), DispatchCallbackEventBody.class);
        JdhPromise promise = promiseRepository.find(new JdhPromiseIdentifier(eventBody.getPromiseId()));
        JdhVerticalBusiness jdhVerticalBusiness = verticalBusinessRepository.find(promise.getVerticalCode());

        /**
         * 非自检测 且状态不是预约成功才处理
         * (1)自检测不处理派单成功事件，因为已经听了骑手接单事件（状态已经变成了15）。骑手接单事件比派单成功更先发出，并且状态已经变成了待服务，派单成功事件处理不会成功
         * (2)如果当前预约成功则不再处理，因为派单会取消后重排，多次消费会失败。后续完整流程应该需要监听派单的取消事件，当前没有这个业务流程
         */
        if (!StringUtils.equals(jdhVerticalBusiness.getBusinessModeCode(), BusinessModeEnum.SELF_TEST.getCode())
                && (Objects.equals(promise.getPromiseStatus(), JdhPromiseStatusEnum.APPOINTMENT_ING.getStatus())
                || Objects.equals(promise.getPromiseStatus(), JdhPromiseStatusEnum.WAIT_PROMISE.getStatus())
                || Objects.equals(promise.getPromiseStatus(), JdhPromiseStatusEnum.MODIFY_ING.getStatus()))) {
            PromiseCallbackCmd cmd = PromiseCallbackCmd.builder()
                    .callbackCode(AppointCallBackEnum.DISPATCH_SUCCESS.getCode())
                    .promiseId(eventBody.getPromiseId())
                    .build();
            promiseApplication.callBack(cmd);
        }

    }

    /**
     * 创建履约单触发派单逻辑,仅仅快递模式支持
     */
//    private void createPromiseDispatch(Event event) {
//        log.info("PromiseEventSubscriber -> promiseDispatch event:{}", JSON.toJSONString(event));
//        Long promiseId = Long.parseLong(event.getAggregateId());
//        JdhPromise promise = promiseRepository.find(new JdhPromiseIdentifier(promiseId));
//        JdhVerticalBusiness verticalBusiness = verticalBusinessRepository.find(promise.getVerticalCode());
//        if (!StringUtils.equals(verticalBusiness.getBusinessModeCode(), BusinessModeEnum.SELF_TEST_TRANSPORT.getCode())){
//            log.info("PromiseEventSubscriber -> promiseDispatch return not match");
//
//            return;
//        }
//        PromiseDispatchCmd cmd = PromiseDispatchCmd.builder().promiseId(Long.parseLong(event.getAggregateId())).build();
//        promiseApplication.dispatch(cmd);
//    }

    /**
     * 履约单派单
     * 业务逻辑
     * * 收 预约中事件，判断身份进行派单逻辑触发派单
     * * 在businessMode 为 homeTest  homeCare流程下触发。
     *
     * @param event 事件
     */
    private void promiseDispatch(Event event) {
        log.info("PromiseEventSubscriber -> promiseDispatch event:{}", JSON.toJSONString(event));
        PromiseDispatchCmd cmd = PromiseDispatchCmd.builder().promiseId(Long.parseLong(event.getAggregateId())).build();
        promiseApplication.dispatch(cmd);
    }

    /**
     * 履约单修改派单
     * 业务逻辑
     * * 收 预约中事件，判断身份进行派单逻辑触发派单
     * * 在businessMode 为 homeTest  homeCare流程下触发。
     *
     * @param event 事件
     */
    private void promiseModifyDispatch(Event event) {
        log.info("PromiseEventSubscriber -> promiseModifyDispatch event:{}", JSON.toJSONString(event));
        PromiseDispatchCmd cmd = PromiseDispatchCmd.builder().promiseId(Long.parseLong(event.getAggregateId())).build();
        PromiseModifyEventBody promiseModifyEventBody = JSON.parseObject(event.getBody(), PromiseModifyEventBody.class);
        if (promiseModifyEventBody != null) {
            cmd.setOperator(promiseModifyEventBody.getOperator());
            cmd.setOperatorRoleType(promiseModifyEventBody.getOperatorRoleType());
        }
        promiseApplication.modifyServiceDateDispatch(cmd);
    }

    /**
     * 履约单延期
     *
     * @param event 事件
     */
    private void delayPromise(Event event) {
        PromiseDelayCmd cmd = new PromiseDelayCmd();
        cmd.setVoucherId(event.getAggregateId());
        promiseApplication.delay(cmd);
    }

}
