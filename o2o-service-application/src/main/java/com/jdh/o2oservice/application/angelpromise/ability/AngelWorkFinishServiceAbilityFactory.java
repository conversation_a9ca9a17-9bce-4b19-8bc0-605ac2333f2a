package com.jdh.o2oservice.application.angelpromise.ability;
import com.jdh.o2oservice.base.annotation.MapAutowired;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import java.util.Map;
@Component
@Slf4j
public class AngelWorkFinishServiceAbilityFactory {

    /**
     * 注入serviceMap
     */
    @MapAutowired
    private Map<String, AngelWorkFinishServiceAbility> abilityMap;

    /**
     *
     * @param businessModeCode
     * @param serviceType
     * @return
     */
    public AngelWorkFinishServiceAbility createAngelWorkFinishServiceAbility(String businessModeCode, String serviceType) {
        log.info("AngelWorkFinishServiceAbilityFactory -> createAngelWorkFinishServiceAbility, businessModeCode={}, serviceType={}", businessModeCode, serviceType);
        if (StringUtils.isBlank(businessModeCode) || StringUtils.isBlank(serviceType)) {
            throw new BusinessException(BusinessErrorCode.UNKNOWN_ERROR);
        }
        String routeKey = "angelWorkFinishService" + "_"+ businessModeCode + "_" + serviceType;
        return abilityMap.get(routeKey);
    }
}
