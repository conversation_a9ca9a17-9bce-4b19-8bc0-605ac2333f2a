package com.jdh.o2oservice.application.product.service;

import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.export.product.cmd.BindCareFormCmd;
import com.jdh.o2oservice.export.product.cmd.QuestionRemoveCmd;
import com.jdh.o2oservice.export.product.cmd.QuestionSaveCmd;
import com.jdh.o2oservice.export.product.dto.CareFormDTO;
import com.jdh.o2oservice.export.product.dto.JdhQuestionDto;
import com.jdh.o2oservice.export.product.dto.QuestionDTO;
import com.jdh.o2oservice.export.product.query.CareFormDetailQuery;
import com.jdh.o2oservice.export.product.query.QuestionDetailQuery;
import com.jdh.o2oservice.export.product.query.QuestionPageQuery;

/**
 * <AUTHOR>
 * @date 2025/5/25
 * @description 题库
 */
public interface QuestionServiceApplication {
    /**
     * 查询题库详情
     * @param questionDetailQuery
     * @return
     */
    JdhQuestionDto findDetail(QuestionDetailQuery questionDetailQuery);

    /**
     * 删除接口
     * @param questionRemoveCmd
     * @return
     */
    Boolean remove(QuestionRemoveCmd questionRemoveCmd);

    /**
     * 题库新增/修改
     * @param questionSaveCmd
     * @return
     */
    Boolean saveOrUpdate(QuestionSaveCmd questionSaveCmd);

    /**
     * 分页查询题库
     * @param questionPageQuery
     * @return
     */
    PageDto<QuestionDTO> findPage(QuestionPageQuery questionPageQuery);

    /**
     * 保存护理单配置
     * @param bindCareFormCmd
     * @return
     */
    Boolean bindCareForm(BindCareFormCmd bindCareFormCmd);

    /**
     * 查询护理单配置
     * @param careFormDetailQuery
     * @return
     */
    CareFormDTO careFormDetail(CareFormDetailQuery careFormDetailQuery);
}
