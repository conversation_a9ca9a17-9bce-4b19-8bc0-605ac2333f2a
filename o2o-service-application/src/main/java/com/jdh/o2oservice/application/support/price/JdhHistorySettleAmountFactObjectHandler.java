package com.jdh.o2oservice.application.support.price;

import com.jdh.o2oservice.core.domain.settlement.context.AngelSettlementQueryContext;
import com.jdh.o2oservice.core.domain.settlement.enums.SettleStatusEnum;
import com.jdh.o2oservice.core.domain.settlement.enums.SettleTypeEnum;
import com.jdh.o2oservice.core.domain.settlement.model.AngelSettlement;
import com.jdh.o2oservice.core.domain.settlement.model.AngelSettlementDetail;
import com.jdh.o2oservice.core.domain.settlement.repository.AngelSettlementRepository;
import com.jdh.o2oservice.core.domain.support.basic.enums.PricingServiceFactObjectEnum;
import com.jdh.o2oservice.core.domain.support.price.context.PricingServiceCalculateContext;
import com.jdh.o2oservice.infrastructure.repository.db.po.AngelSettlementPo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @ClassName JdhHistorySettleAmountFactObjectHandler
 * @Description
 * <AUTHOR>
 * @Date 2025/5/21 21:19
 **/
@Component
@Slf4j
public class JdhHistorySettleAmountFactObjectHandler extends AbstractFactObjectHandler{

    /**
     * angelSettlementRepository
     */
    @Resource
    private AngelSettlementRepository angelSettlementRepository;

    /**
     * 获取事实对象
     * @param context
     * @return
     */
    @Override
    public Object getData(PricingServiceCalculateContext context) {
        boolean containsKey = context.getFactObjectMap().containsKey(getMapKey());
        if (containsKey) {
            return context.getFactObjectMap().get(getMapKey());
        }
        AngelSettlementQueryContext incomeQuery = new AngelSettlementQueryContext();
        incomeQuery.setAngelId(context.getAngelId());
        incomeQuery.setOrderId(context.getOrderId());
        incomeQuery.setPromiseId(context.getPromiseId());
        incomeQuery.setSettlementType(SettleTypeEnum.INCOME.getType());
        //incomeQuery.setSettleStatusList(Arrays.asList(SettleStatusEnum.FREEZE.getType(),SettleStatusEnum.SETTLED.getType()));
        List<AngelSettlement> angelSettlements = angelSettlementRepository.querySettlementList(incomeQuery);

        //没有历史结算数据，默认返回0
        if (CollectionUtils.isEmpty(angelSettlements)) {
            context.getFactObjectMap().put(getMapKey(), BigDecimal.ZERO);
            return BigDecimal.ZERO;
        }
        //查询结算明细数据，计算明细金额总和，即历史计算金额
        incomeQuery.setSettleIdList(angelSettlements.stream().map(AngelSettlement::getSettleId).collect(Collectors.toList()));
        List<AngelSettlementDetail> angelSettlementDetails = angelSettlementRepository.querySettlementDetailList(incomeQuery);
        if (CollectionUtils.isEmpty(angelSettlementDetails)) {
            context.getFactObjectMap().put(getMapKey(), BigDecimal.ZERO);
            return BigDecimal.ZERO;
        }
        BigDecimal tot = angelSettlementDetails.stream()
                .map(AngelSettlementDetail::getSettleAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        context.getFactObjectMap().put(getMapKey(), tot);
        return tot;
    }

    /**
     * 获取map key
     * @return
     */
    @Override
    public String getMapKey() {
        return PricingServiceFactObjectEnum.HISTORY_SETTLE_AMOUNT.getCode();
    }
}