package com.jdh.o2oservice.application.settlement.listener;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.fastjson.JSON;
import com.jd.medicine.base.common.util.NumberUtil;
import com.jd.medicine.base.common.util.StringUtil;
import com.jdh.o2oservice.application.settlement.service.JdServiceCityAngelSettleApplication;
import com.jdh.o2oservice.application.support.service.FileManageApplication;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.model.DomainEnum;
import com.jdh.o2oservice.base.util.SpringUtil;
import com.jdh.o2oservice.core.domain.settlement.enums.AngelJobNatureEnum;
import com.jdh.o2oservice.core.domain.settlement.enums.SettleErrorCode;
import com.jdh.o2oservice.core.domain.settlement.enums.SettlementFileBizTypeEnum;
import com.jdh.o2oservice.core.domain.settlement.model.ImportAngelSettleConfig;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.BaseAddressBo;
import com.jdh.o2oservice.export.support.command.GeneratePutUrlCommand;
import com.jdh.o2oservice.export.support.dto.FilePreSignedUrlDto;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;

/**
 * 护士结算城市配置导入
 *
 * <AUTHOR>
 * @date 2025-04-21 11:46
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Slf4j
public class ImportAngelSettleConfigListener extends AnalysisEventListener<ImportAngelSettleConfig> {
    /**
     * 导入数据总量
     */
    private int total = 0;
    /**
     * 导入数据成功总量
     */
    private int successTotal = 0;
    /**
     * 导入数据百分比
     */
    private Integer percentage = 0;
    /**
     * 失败文件地址
     */
    private String failUrl;
    /**
     * 解析是否有失败
     */
    private Boolean isFail = false;
    /**
     * 失败列表
     */
    private List<ImportAngelSettleConfig> failList = new ArrayList<>();
    /**
     * 成功列表
     */
    private List<ImportAngelSettleConfig> succList = new ArrayList<>();
    /**
     * 成功列表
     */
    private Map<String,ImportAngelSettleConfig> succMap = new HashMap<>();

    /**
     * userPin
     */
    private String userPin;
    /**
     * filePreSignedUrlDto
     */
    private FilePreSignedUrlDto filePreSignedUrlDto;
    /**
     * cityAreaMap
     */
    private Map<String, BaseAddressBo> cityAreaMap = new HashMap<>();

    /**
     * 构造函数
     */
    public ImportAngelSettleConfigListener(String userPin) {
        this.userPin = userPin;
    }

    /**
     * @param importAngelSettleConfig
     * @param analysisContext         analysisContext
     */
    @Override
    public void invoke(ImportAngelSettleConfig importAngelSettleConfig, AnalysisContext analysisContext) {
        String failReason = "";
        try {
            Integer rowNumber = analysisContext.readSheetHolder().getApproximateTotalRowNumber();
            log.info("ImportAngelSettleConfigListener#invoke ImportAngelSettleConfig={} rowNumber={}", JSON.toJSONString(importAngelSettleConfig), rowNumber);
            if (rowNumber > 99) {
                throw new BusinessException(SettleErrorCode.ANGEL_ASJUST_DATA_OUT);
            }
            Integer provinceCode = null;
            StringBuilder stringBuilder = new StringBuilder();
            String provinceName = importAngelSettleConfig.getProvinceName();
            if (StringUtil.isEmpty(provinceName)) {
                stringBuilder.append("省名称为空,");
            }else{
                BaseAddressBo baseAddress = getBaseAddressBo(provinceName);
                if(Objects.isNull(baseAddress)){
                    stringBuilder.append("省名称查不到，请确认,");
                }else{
                    provinceCode = baseAddress.getProvinceCode();
                    importAngelSettleConfig.setProvinceCode(String.valueOf(provinceCode));
                    String costName = provinceName;
                    String cityName = importAngelSettleConfig.getCityName();
                    if (StringUtil.isEmpty(cityName)) {
                        if(Objects.nonNull(provinceCode) && provinceCode > CommonConstant.FOUR){
                            stringBuilder.append("市名称为空,");
                        }
                    }else{
                        costName += cityName;
                        if(Objects.nonNull(provinceCode) && provinceCode > CommonConstant.FOUR){
                            baseAddress = getBaseAddressBo(cityName);
                        }else{
                            baseAddress = getBaseAddressBo(costName);
                        }
                        if(Objects.isNull(baseAddress)){
                            stringBuilder.append("市名称查不到，请确认,");
                        }else{
                            Integer cityCode = baseAddress.getCityCode();
                            if(!provinceCode.equals(baseAddress.getProvinceCode())){
                                stringBuilder.append("省市不匹配，请确认,");
                            }else{
                                if(!cityName.contains(baseAddress.getCityName()) && !baseAddress.getCityName().contains(cityName)){
                                    stringBuilder.append("省市不匹配，请确认,");
                                }else{
                                    importAngelSettleConfig.setCityCode(String.valueOf(cityCode));
                                    String countyName = importAngelSettleConfig.getCountyName();
                                    if (StringUtil.isNotBlank(countyName)) {
                                        costName += countyName;
                                        baseAddress = getBaseAddressBo(costName);
                                        if(Objects.isNull(baseAddress)){
                                            stringBuilder.append("区名称查不到，请确认,");
                                        }else{
                                            if(!countyName.contains(baseAddress.getDistrictName()) && !baseAddress.getDistrictName().contains(countyName)){
                                                stringBuilder.append("区县名称错误，请确认,");
                                            }else{
                                                Integer countyCode = baseAddress.getDistrictCode();
                                                importAngelSettleConfig.setCountyCode(String.valueOf(countyCode));
                                                String townName = importAngelSettleConfig.getTownName();
                                                if (StringUtil.isNotBlank(townName)) {
                                                    costName += townName;
                                                    baseAddress = getBaseAddressBo(costName);
                                                    if(Objects.isNull(baseAddress)){
                                                        stringBuilder.append("街道名称查不到，请确认");
                                                    }else{
                                                        if(!townName.contains(baseAddress.getTownName()) && !baseAddress.getTownName().contains(townName)){
                                                            stringBuilder.append("乡镇/街道名称错误，请确认,");
                                                        }else{
                                                            Integer townCode = baseAddress.getTownCode();
                                                            importAngelSettleConfig.setTownCode(String.valueOf(townCode));
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }

            if (StringUtil.isEmpty(importAngelSettleConfig.getAngelTypeDesc())) {
                stringBuilder.append("护士类型为空,");
            } else {
                Boolean validJobNature = AngelJobNatureEnum.validJobNature(importAngelSettleConfig.getAngelTypeDesc());
                if (!validJobNature) {
                    stringBuilder.append("护士类型不符合,");
                }else{
                    importAngelSettleConfig.setAngelType(AngelJobNatureEnum.getLabelByLabel(importAngelSettleConfig.getAngelTypeDesc()));
                }
            }
            stringBuilder.append(checkAmount(importAngelSettleConfig));
            if (StringUtil.isNotBlank(stringBuilder.toString())) {
                importAngelSettleConfig.setFailReason(stringBuilder.toString());
                isFail = true;
            }
            failList.add(importAngelSettleConfig);
        } catch (Exception e) {
            log.error("ImportAngelSettleConfigListener exception", e);
            failReason = e.getMessage();
            importAngelSettleConfig.setFailReason(failReason);
            isFail = true;
            failList.add(importAngelSettleConfig);
        }
        String costCode = importAngelSettleConfig.getProvinceCode() + importAngelSettleConfig.getCityCode() +
                importAngelSettleConfig.getCountyCode() + importAngelSettleConfig.getTownCode() + importAngelSettleConfig.getAngelTypeDesc();
        succMap.put(costCode,importAngelSettleConfig);
    }

    private StringBuilder checkAmount(ImportAngelSettleConfig importAngelSettleConfig) {
        StringBuilder stringBuilder = new StringBuilder();
        String onSiteFee = importAngelSettleConfig.getOnSiteFee();
        String immediatelyFee = importAngelSettleConfig.getImmediatelyFee();
        String holidayFee = importAngelSettleConfig.getHolidayFee();
        String nightDoorFee = importAngelSettleConfig.getNightDoorFee();
        String dynamicAdjustFee = importAngelSettleConfig.getDynamicAdjustFee();
        if(StringUtil.isBlank(onSiteFee)){
            importAngelSettleConfig.setOnSiteFee("0");
        }
        if(StringUtil.isBlank(onSiteFee)){
            importAngelSettleConfig.setOnSiteFee("0");
        }
        if(StringUtil.isBlank(immediatelyFee)){
            importAngelSettleConfig.setImmediatelyFee("0");
        }
        if(StringUtil.isBlank(holidayFee)){
            importAngelSettleConfig.setHolidayFee("0");
        }
        if(StringUtil.isBlank(nightDoorFee)){
            importAngelSettleConfig.setNightDoorFee("0");
        }

        String checkResult1 = checkFee(onSiteFee, "上门费");
        stringBuilder.append(checkResult1);
        String checkResult2 = checkFee(immediatelyFee, "即时加价");
        stringBuilder.append(checkResult2);
        String checkResult3 = checkFee(holidayFee, "节假日加价");
        stringBuilder.append(checkResult3);
        String checkResult4 = checkFee(nightDoorFee, "夜间加价");
        stringBuilder.append(checkResult4);
        String checkResult5 = checkFee(dynamicAdjustFee, "动态调整费");
        stringBuilder.append(checkResult5);
        return stringBuilder;
    }
    /**
     *
     * @param fee
     * @param paramName
     * @return
     */
    private String checkFee(String fee,String paramName){
        if(StringUtil.isEmpty(fee)){
            return "";
        }
        if(!NumberUtil.isNumber(fee)){
            return paramName + "金额非法,";
        }
        String pattern = "^-?(([1-9]{1}\\d*)|(0{1}))(\\.\\d{1,2})?$";
        if(!fee.matches(pattern)){
            return paramName + "金额小数点后最多两位,";
        }else{
            BigDecimal amount = new BigDecimal(fee);
            if(amount.compareTo(new BigDecimal("9999.99")) > 0 || amount.compareTo(BigDecimal.ZERO) <= 0 ){
                return paramName + "金额需要在0至9999.99之间,";
            }
        }
        return "";
    }


    /**
     *
     * @param cityName
     * @return
     */
    private BaseAddressBo getBaseAddressBo(String cityName){
        try{
            BaseAddressBo baseAddressBo = cityAreaMap.get(cityName);
            if(Objects.nonNull(baseAddressBo)){
                return baseAddressBo;
            }else{
                JdServiceCityAngelSettleApplication angelApplication = SpringUtil.getBean(JdServiceCityAngelSettleApplication.class);
                baseAddressBo = angelApplication.getProvinceName(cityName);
                if(Objects.nonNull(baseAddressBo)){
                    cityAreaMap.put(cityName,baseAddressBo);
                }
            }
            return baseAddressBo;
        }catch (Throwable e){
            return null;
        }
    }

    /**
     *
     * @param analysisContext analysisContext
     */
    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        if (isFail) {
            log.info("ImportAngelSettleConfigListener doAfterAllAnalysed failList={}", JSON.toJSONString(failList));
            FileManageApplication fileManageApplication = SpringUtil.getBean(FileManageApplication.class);
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            EasyExcelFactory.write(outputStream, ImportAngelSettleConfig.class).sheet("失败记录").doWrite(failList);
            String fileName = "错误文件.xlsx";
            GeneratePutUrlCommand data = new GeneratePutUrlCommand();
            data.setUserPin(userPin);
            data.setDomainCode(DomainEnum.SETTLE_MENT.getCode());
            data.setFileBizType(SettlementFileBizTypeEnum.ANGEL_SETTLE_ADJUST.getBizType());
            filePreSignedUrlDto = fileManageApplication.upload(fileName, new ByteArrayInputStream(outputStream.toByteArray()), LocalDateTime.now().plusHours(24), data);
        }
    }
}
