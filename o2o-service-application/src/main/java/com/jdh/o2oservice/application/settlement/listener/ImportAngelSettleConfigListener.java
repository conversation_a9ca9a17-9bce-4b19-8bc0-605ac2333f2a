package com.jdh.o2oservice.application.settlement.listener;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.fastjson.JSON;
import com.jd.medicine.base.common.util.NumberUtil;
import com.jd.medicine.base.common.util.StringUtil;
import com.jdh.o2oservice.application.settlement.service.JdServiceCityAngelSettleApplication;
import com.jdh.o2oservice.application.support.service.FileManageApplication;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.model.DomainEnum;
import com.jdh.o2oservice.base.util.SpringUtil;
import com.jdh.o2oservice.core.domain.settlement.enums.AngelJobNatureEnum;
import com.jdh.o2oservice.core.domain.settlement.enums.SettleErrorCode;
import com.jdh.o2oservice.core.domain.settlement.enums.SettlementFileBizTypeEnum;
import com.jdh.o2oservice.core.domain.settlement.model.ImportAngelSettleConfig;
import com.jdh.o2oservice.core.domain.support.basic.rpc.AddressRpc;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.BaseAddressBo;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.JDDistrictBo;
import com.jdh.o2oservice.export.support.command.GeneratePutUrlCommand;
import com.jdh.o2oservice.export.support.dto.FilePreSignedUrlDto;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 护士结算城市配置导入
 *
 * <AUTHOR>
 * @date 2025-04-21 11:46
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Slf4j
public class ImportAngelSettleConfigListener extends AnalysisEventListener<ImportAngelSettleConfig> {
    /**
     * 导入数据总量
     */
    private int total = 0;
    /**
     * 导入数据成功总量
     */
    private int successTotal = 0;
    /**
     * 导入数据百分比
     */
    private Integer percentage = 0;
    /**
     * 失败文件地址
     */
    private String failUrl;
    /**
     * 解析是否有失败
     */
    private Boolean isFail = false;
    /**
     * 失败列表
     */
    private List<ImportAngelSettleConfig> failList = new ArrayList<>();
    /**
     * 成功列表
     */
    private List<ImportAngelSettleConfig> succList = new ArrayList<>();
    /**
     * 成功列表
     */
    private Map<String,ImportAngelSettleConfig> succMap = new HashMap<>();

    /**
     * userPin
     */
    private String userPin;
    /**
     * filePreSignedUrlDto
     */
    private FilePreSignedUrlDto filePreSignedUrlDto;
    /**
     * cityAreaMap
     */
    private Map<String, JDDistrictBo> cityAreaMap = new HashMap<>();

    /**
     * 构造函数
     */
    public ImportAngelSettleConfigListener(String userPin) {
        this.userPin = userPin;
    }

    /**
     * @param importAngelSettleConfig
     * @param analysisContext         analysisContext
     */
    @Override
    public void invoke(ImportAngelSettleConfig importAngelSettleConfig, AnalysisContext analysisContext) {
        String failReason = "";
        try {
            Integer rowNumber = analysisContext.readSheetHolder().getApproximateTotalRowNumber();
            log.info("ImportAngelSettleConfigListener#invoke ImportAngelSettleConfig={} rowNumber={}", JSON.toJSONString(importAngelSettleConfig), rowNumber);
            if (rowNumber > 1000) {
                throw new BusinessException(SettleErrorCode.ANGEL_ASJUST_DATA_OUT);
            }
            //查出所有省信息
            List<JDDistrictBo> provinces = SpringUtil.getBean(AddressRpc.class).getProvinces();
            Map<Integer, JDDistrictBo> id2Province = provinces.stream().collect(Collectors.toMap(JDDistrictBo::getDistrictCode, Function.identity(), (k1, k2) -> k1));
            StringBuilder stringBuilder = new StringBuilder();
            validateAndAssignAddress(importAngelSettleConfig, id2Province, stringBuilder);
            validateAngelType(importAngelSettleConfig, stringBuilder);

            stringBuilder.append(checkAmount(importAngelSettleConfig));
            if (StringUtil.isNotBlank(stringBuilder.toString())) {
                importAngelSettleConfig.setFailReason(stringBuilder.toString());
                isFail = true;
            }
            failList.add(importAngelSettleConfig);
        } catch (Exception e) {
            log.error("ImportAngelSettleConfigListener exception", e);
            failReason = e.getMessage();
            importAngelSettleConfig.setFailReason(failReason);
            isFail = true;
            failList.add(importAngelSettleConfig);
        }
        String costCode = importAngelSettleConfig.getProvinceCode() + importAngelSettleConfig.getCityCode() +
                importAngelSettleConfig.getCountyCode() + importAngelSettleConfig.getTownCode() + importAngelSettleConfig.getAngelTypeDesc();
        succMap.put(costCode, importAngelSettleConfig);
    }

    private void validateAndAssignAddress(ImportAngelSettleConfig config, Map<Integer, JDDistrictBo> id2Province, StringBuilder errors) {
        if (StringUtil.isEmpty(config.getProvinceCode())) {
            errors.append("京标省ID为空,");
            return;
        }

        JDDistrictBo province = id2Province.get(Integer.valueOf(config.getProvinceCode()));
        if (province == null) {
            errors.append("省信息查不到，请确认,");
            return;
        }
        config.setProvinceName(province.getDistrictName());

        validateCity(config, errors);
    }

    private void validateCity(ImportAngelSettleConfig config, StringBuilder errors) {
        if (StringUtil.isEmpty(config.getCityCode())) {
            if (Integer.parseInt(config.getProvinceCode()) > CommonConstant.FOUR) {
                errors.append("市名称为空,");
            }
            config.setCityName("");
        } else {
            JDDistrictBo city = getJDDistrictBo(config.getCityCode());
            if (city == null) {
                errors.append("市信息查不到，请确认,");
                return;
            }
            config.setCityName(city.getDistrictName());
            if (!StringUtils.equals(city.getParentCode().toString(), config.getProvinceCode())) {
                errors.append("省市不匹配，请确认,");
                return;
            }
            validateCounty(config, errors);
        }
    }

    private void validateCounty(ImportAngelSettleConfig config, StringBuilder errors) {
        if (StringUtil.isNotBlank(config.getCountyCode())) {
            JDDistrictBo county = getJDDistrictBo(config.getCountyCode());
            if (county == null) {
                errors.append("区信息查不到，请确认,");
                return;
            }
            config.setCountyName(county.getDistrictName());
            if (!StringUtils.equals(county.getParentCode().toString(), config.getCityCode())) {
                errors.append("市区不匹配，请确认,");
                return;
            }
            validateTown(config, errors);
        }
    }

    private void validateTown(ImportAngelSettleConfig config, StringBuilder errors) {
        if (StringUtil.isNotBlank(config.getTownCode())) {
            JDDistrictBo town = getJDDistrictBo(config.getTownCode());
            if (town == null) {
                errors.append("街道信息查不到，请确认");
                return;
            }
            config.setTownName(town.getDistrictName());
            if (!StringUtils.equals(town.getParentCode().toString(), config.getCountyCode())) {
                errors.append("区和街道不匹配，请确认,");
            }
        }
    }

    /**
     * 校验护士类型
     * @param config
     * @param errors
     */
    private void validateAngelType(ImportAngelSettleConfig config, StringBuilder errors) {
        if (StringUtil.isEmpty(config.getAngelTypeDesc())) {
            errors.append("护士类型为空,");
        } else {
            Boolean validJobNature = AngelJobNatureEnum.validJobNature(config.getAngelTypeDesc());
            if (!validJobNature) {
                errors.append("护士类型不符合,");
            } else {
                config.setAngelType(AngelJobNatureEnum.getLabelByLabel(config.getAngelTypeDesc()));
            }
        }
    }

    private StringBuilder checkAmount(ImportAngelSettleConfig importAngelSettleConfig) {
        StringBuilder stringBuilder = new StringBuilder();
        String onSiteFee = importAngelSettleConfig.getOnSiteFee();
        String immediatelyFee = importAngelSettleConfig.getImmediatelyFee();
        String holidayFee = importAngelSettleConfig.getHolidayFee();
        String nightDoorFee = importAngelSettleConfig.getNightDoorFee();
        String dynamicAdjustFee = importAngelSettleConfig.getDynamicAdjustFee();
        if(StringUtil.isBlank(onSiteFee)){
            importAngelSettleConfig.setOnSiteFee("0");
        }
        if(StringUtil.isBlank(onSiteFee)){
            importAngelSettleConfig.setOnSiteFee("0");
        }
        if(StringUtil.isBlank(immediatelyFee)){
            importAngelSettleConfig.setImmediatelyFee("0");
        }
        if(StringUtil.isBlank(holidayFee)){
            importAngelSettleConfig.setHolidayFee("0");
        }
        if(StringUtil.isBlank(nightDoorFee)){
            importAngelSettleConfig.setNightDoorFee("0");
        }

        String checkResult1 = checkFee(onSiteFee, "上门费");
        stringBuilder.append(checkResult1);
        String checkResult2 = checkFee(immediatelyFee, "即时加价");
        stringBuilder.append(checkResult2);
        String checkResult3 = checkFee(holidayFee, "节假日加价");
        stringBuilder.append(checkResult3);
        String checkResult4 = checkFee(nightDoorFee, "夜间加价");
        stringBuilder.append(checkResult4);
        String checkResult5 = checkFee(dynamicAdjustFee, "动态调整费");
        stringBuilder.append(checkResult5);
        return stringBuilder;
    }
    /**
     *
     * @param fee
     * @param paramName
     * @return
     */
    private String checkFee(String fee,String paramName){
        if(StringUtil.isEmpty(fee)){
            return "";
        }
        if(!NumberUtil.isNumber(fee)){
            return paramName + "金额非法,";
        }
        String pattern = "^-?(([1-9]{1}\\d*)|(0{1}))(\\.\\d{1,2})?$";
        if(!fee.matches(pattern)){
            return paramName + "金额小数点后最多两位,";
        }else{
            BigDecimal amount = new BigDecimal(fee);
            if(amount.compareTo(new BigDecimal("9999.99")) > 0 || amount.compareTo(BigDecimal.ZERO) < 0 ){
                return paramName + "金额需要在0至9999.99之间,";
            }
        }
        return "";
    }


    /**
     *
     * @param addressCode
     * @return
     */
    private JDDistrictBo getJDDistrictBo(String addressCode){
        try{
            JDDistrictBo baseAddressBo = cityAreaMap.get(addressCode);
            if(Objects.nonNull(baseAddressBo)){
                return baseAddressBo;
            }else{
                baseAddressBo = SpringUtil.getBean(AddressRpc.class).getDistrictInfo(Integer.valueOf(addressCode));
                //如果查出地址信息，顺便把下级地址列表查出来
                if (Objects.nonNull(baseAddressBo)) {
                    List<JDDistrictBo> children = SpringUtil.getBean(AddressRpc.class).getChildren(Integer.valueOf(addressCode));
                    if(CollectionUtils.isNotEmpty(children)){
                        children.forEach(child -> cityAreaMap.put(child.getDistrictCode().toString(),child));
                    }
                }

            }
            return baseAddressBo;
        }catch (Throwable e){
            return null;
        }
    }

    /**
     *
     * @param analysisContext analysisContext
     */
    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        if (isFail) {
            log.info("ImportAngelSettleConfigListener doAfterAllAnalysed failList={}", JSON.toJSONString(failList));
            FileManageApplication fileManageApplication = SpringUtil.getBean(FileManageApplication.class);
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            EasyExcelFactory.write(outputStream, ImportAngelSettleConfig.class).sheet("失败记录").doWrite(failList);
            String fileName = "错误文件.xlsx";
            GeneratePutUrlCommand data = new GeneratePutUrlCommand();
            data.setUserPin(userPin);
            data.setDomainCode(DomainEnum.SETTLE_MENT.getCode());
            data.setFileBizType(SettlementFileBizTypeEnum.ANGEL_SETTLE_ADJUST.getBizType());
            filePreSignedUrlDto = fileManageApplication.upload(fileName, new ByteArrayInputStream(outputStream.toByteArray()), LocalDateTime.now().plusHours(24), data);
        }
    }
}
