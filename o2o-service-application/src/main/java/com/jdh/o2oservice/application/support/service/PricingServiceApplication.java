package com.jdh.o2oservice.application.support.service;

import com.jdh.o2oservice.export.support.command.PricingServiceCalculateCmd;
import com.jdh.o2oservice.export.support.dto.PricingServiceCalculateResultDto;

import java.math.BigDecimal;
import java.util.Map;

/**
 * @ClassName PricingServiceApplication
 * @Description
 * <AUTHOR>
 * @Date 2025/4/29 16:34
 **/
public interface PricingServiceApplication {

    /**
     * 计算价格
     * @param cmd
     * @return
     */
     BigDecimal calculatePrice(PricingServiceCalculateCmd cmd);

    /**
     * 计算价格，返回详细信息
     * @param cmd
     * @return
     */
    PricingServiceCalculateResultDto calculatePriceForDetail(PricingServiceCalculateCmd cmd);

    /**
     * 根据护士全兼职标签批量计算价格，返回详细信息
     * @param cmd
     * @return
     */
    Map<Integer, PricingServiceCalculateResultDto> batchCalculatePriceForDetailByAngelJobNature(PricingServiceCalculateCmd cmd);
}