package com.jdh.o2oservice.base.enums;


/**
 * @ClassName SkuBusinessProcessTypeEnum
 * @Description 商品业务流程类型
 * <AUTHOR>
 * @Date 2025/7/2 21:50
 **/
public enum SkuBusinessProcessTypeEnum {

    /**
     *
     */
    STANDARD_HOME_TEST(1, "采样+送样+检测报告"),

    ONLY_SAMPLING(2, "仅采样+送样"),

    ONLY_TEST_REPORT(3, "仅检测报告"),
    ;

    /**
     * @param type
     * @param desc
     */
    SkuBusinessProcessTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    private Integer type;

    private String desc;

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }


    /**
     * 根据类型获取枚举对象
     *
     * @param type
     * @return
     */
    public static SkuBusinessProcessTypeEnum getByType(Integer type) {
        for (SkuBusinessProcessTypeEnum businessProcessTypeEnum : SkuBusinessProcessTypeEnum.values()) {
            if (businessProcessTypeEnum.getType().equals(type)) {
                return businessProcessTypeEnum;
            }
        }
        return null;
    }
}