package com.jdh.o2oservice.base.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName BusinessModeParam
 * @Description
 * <AUTHOR>
 * @Date 2025/7/7 21:08
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BusinessModeParam {

    /**
     * <pre>
     * 服务类型
     * @see com.jdh.o2oservice.common.enums.ServiceTypeNewEnum
     * </pre>
     */
    private Integer skuServiceType;

    /**
     * 业务流程类型 1-采样+送样+检测报告 2-仅采样+送样 3-仅检测报告
     */
    private Integer businessProcessType;
}