package com.jdh.o2oservice.base.ducc.model.price;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName PricingServiceFeeCalculateRule
 * @Description
 * <AUTHOR>
 * @Date 2025/4/29 13:44
 **/
@Data
public class PricingServiceFeeCalculateRule implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 场景名称
     */
    private String sceneName;

    /**
     * 匹配本场景的表达式
     */
    private String sceneExpression;

    /**
     * 优先级
     */
    private Integer salience;

    /**
     * 计算表达式
     */
    private String calculateExpression;

    /**
     * 计算方法
     */
    protected String calculateMethod;
}