package com.jdh.o2oservice.base.aspect;

import com.jd.jim.cli.Cluster;
import com.jdh.o2oservice.base.annotation.UserOperationLimit;
import com.jdh.o2oservice.base.annotation.UserPinCheck;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode;
import com.jdh.o2oservice.base.exception.errorcode.SystemErrorCode;
import com.jdh.o2oservice.base.util.GwMapUtil;
import com.jdh.o2oservice.base.util.ResponseUtil;
import com.jdh.o2oservice.base.util.SpringUtil;
import com.jdh.o2oservice.base.util.UserPinContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * 用户校验切面
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/7/9 19:38
 */
@Slf4j
@Aspect
@Component
public class UserPinCheckAspect{

    /**
     * jimClient
     */
    @Autowired
    private Cluster jimClient;

    /**
     * 拦截的注解
     */
    @Pointcut("@annotation(com.jdh.o2oservice.base.annotation.UserPinCheck)")
    public void pointCut() {
    }


    /**
     * 用户操作校验
     *
     * @param proceedingJoinPoint 切点
     * @return Object
     * @throws Throwable 异常
     */
    @Around("pointCut()")
    public Object aroundOperation(ProceedingJoinPoint proceedingJoinPoint) throws Throwable {
        Object result = null;
        try {
            // 获取参数
            Object[] args = proceedingJoinPoint.getArgs();
            Signature signature = proceedingJoinPoint.getSignature();
            Class<?> cls = proceedingJoinPoint.getTarget().getClass();
            String methodName = signature.getName();
            Method method;
            if (signature instanceof MethodSignature) {
                method = ((MethodSignature) signature).getMethod();
            } else {
                return null;
            }

            // 实际调用方法,避免spring事务AOP无法获取实际方法
            Method realMethod = cls.getDeclaredMethod(methodName, method.getParameterTypes());

            // 校验userPin,如果userPin存在会存入ThreadLocal中
            Object userPinCheck = userPinCheck(cls, realMethod, args);
            //不为null说明校验pin没通过
            if (userPinCheck != null) {
                return userPinCheck;
            }

            Boolean userOperationLimitSwitch = SpringUtil.getBean(DuccConfig.class).getUserOperationLimitSwitch();
            userOperationLimitSwitch = Optional.ofNullable(userOperationLimitSwitch).orElse(true);
            if (userOperationLimitSwitch) {
                // 用户操作频率限制
                Object userOperationLimit = userOperationLimit(cls, realMethod);
                if (userOperationLimit != null) {
                    return userOperationLimit;
                }
            }
            result = proceedingJoinPoint.proceed();
        } finally {
            // 执行结束,清除ThreadLocal中userPin
            UserPinContext.remove();
        }
        return result;
    }

    /**
     * userPin校验
     *
     * @param cls    类
     * @param method 方法
     * @param args   参数数组
     * @return Object
     */
    private Object userPinCheck(Class<?> cls, Method method, Object[] args) {
        try {
            String userPin = null;
            // rpc接口统一使用的Map作为接收参数,校验参数个数、参数类型
            if (ArrayUtils.isNotEmpty(args) && args.length == 1 && args[0] instanceof Map) {
                userPin = GwMapUtil.getPin((Map<?, ?>) args[0]);
            }
            if (StringUtils.isBlank(userPin)) {
                String className = cls.getSimpleName();
                String methodName = method.getName();
                log.error("UserPinCheckAspect -> 校验userPin, {}->{} , userPin = {}", className, methodName, userPin);
                UserPinCheck userPinCheck = method.getAnnotation(UserPinCheck.class);
                boolean allowNoPin = userPinCheck.allowNoPin();
                if(!allowNoPin){
                    return ResponseUtil.buildErrResponse(BusinessErrorCode.UNKNOWN_USER_LOGIN);
                }
            }
            // 将userPin放入ThreadLocal
            UserPinContext.put(userPin);
            return null;
        } catch (Exception e) {
            log.error("UserPinCheckAspect -> userPinCheck, error",e);
            return ResponseUtil.buildErrResponse(SystemErrorCode.UNKNOWN_ERROR);
        }
    }

    /**
     * 用户操作锁,针对只能串行进行的操作动作,防并发
     *
     * @param cls    类
     * @param method 方法
     * @return Object
     */
    private Object userOperationLimit(Class<?> cls, Method method) {
        try {
            UserOperationLimit userOperationLimit = method.getAnnotation(UserOperationLimit.class);
            if (userOperationLimit != null) {
                String userPin = UserPinContext.get();
                String className = cls.getSimpleName();
                String methodName = method.getName();
                String key = generateUserOperationLimitKey(userPin, className, methodName);
                Long count = jimClient.incr(key);
                Long ttl = jimClient.ttl(key);
                // 为key设置失效时间,必须
                if (ttl == null || ttl <= 0L) {
                    long timeInterval = userOperationLimit.timeInterval();
                    jimClient.expire(key, timeInterval, TimeUnit.SECONDS);
                }
                int maxLimitTimes = userOperationLimit.maxLimitTimes();
                if (count != null && (count.intValue() > maxLimitTimes)) {
                    return ResponseUtil.buildErrResponse(userOperationLimit.errorCode());
                }
            }
            return null;
        } catch (Exception e) {
            log.error("UserOperationCheckAspect->userOperationLimit , error = {}", e.getMessage());
            return null;
        }
    }

    /**
     * 生产用户操作频率限制key
     *
     * @param userPin    用户pin
     * @param className  类名
     * @param methodName 方法名
     * @return String
     */
    private String generateUserOperationLimitKey(String userPin, String className, String methodName) {
        return CommonConstant.USER_OPERATION_LIMIT + "_" + userPin + "_" + className + "_" + methodName;
    }
}