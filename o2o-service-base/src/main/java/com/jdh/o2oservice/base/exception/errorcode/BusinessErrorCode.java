package com.jdh.o2oservice.base.exception.errorcode;

import lombok.ToString;

/**
 * 业务错误码
 *
 * <AUTHOR>
 * @date 2023/12/11
 */
@ToString
public enum BusinessErrorCode implements AbstractErrorCode{

    /**
     * 成功
     */
    SUCCESS("0000", "OK"),

    RPC_JSF_ERROR("8888", "RPC接口异常"),

    /**
     * 未知异常
     */
    UNKNOWN_ERROR("9999", "系统忙请稍后重试"),

    /**
     * 用户登录信息失败
     */
    UNKNOWN_USER_LOGIN("11111", "抱歉，您的登录信息已过期，请重新登录"),
    CUSTOM_ERROR_CODE("222222", " 自定义错误:{}"),

    /**
     * 订单中缺失商品信息
     */
    ORDER_LACK_SKU_INFO("10001", "订单中缺失商品信息"),

    /**
     * 未查询到商品信息
     */
    SKU_INFO_QUERY_FAIL("10002", "未查询到商品信息"),

    /**
     * 未查询到商品服务类型
     */
    SKU_SERVICE_TYPE_NOT_EXIST("10003", "未查询到商品服务类型"),

    /**
     * 业务身份不存在
     */
    VERTICAL_CODE_NOT_EXIST("10004", "业务身份不存在"),
    ORDER_CART_IS_EMPTY("10005", "订单购物车信息为空"),
    ORDER_CART_SKU_IS_EMPTY("10006", "订单购物车中商品信息为空"),

    ORDER_ITEM_NOT_EXIST("10007", "订单明细信息不存在"),
    ORDER_NOT_EXIST("10008", "订单信息不存在"),

    LOC_CODE_ILLEGAL_STATUS("10009", "无效核销码状态"),
    JDH_PROMISE_NOT_EXIST("10010", "履约单信息不存在"),
    JDH_VOUCHER_NOT_EXIST("10011", "服务单信息不存在"),
    SAVE_OPTION_DOING("10012", "正在创建，请稍后"),
    JDH_USER_WITE_OFF_FAIL("10013", "核销失败，请稍后重试"),

    ILLEGAL_ARG_ERROR("100012", "请求参数错误"),
    PIN_IS_NULL("100013", "用户未登录"),

    APPOINTMENT_TIME_EXPIRE("100014", "请重新选择上门时间"),
    SUBMIT_ORDER_PARTNER_SOURCE_FAIL("100015", "该检验单已存在订单，请勿重复下单"),

    SUBMIT_ORDER_PARTNER_SOURCE_STATUS_FAIL("100016", "检验单已无效，请联系医生进行沟通"),

    /**
     * 查询用户收货地址 收货地址转换失败
     */
    USER_ADDRESS_CONVERT_IS_ERROR("31014", "收货地址转换失败"),
    /**
     * 获取机器IP失败
     */
    USER_ADDRESS_IP_IS_ERROR("31015", "获取机器IP失败"),

    CREDENTIAL_FORMAT_ERROR("12012","证件号码格式有误,请修改"),
    CREDENTIAL_HZFORMAT_ERROR("12020","护照格式有误,请修改"),


    /**
     * 19000-19999 消费医疗申请接口异常定义
     */
    XFYL_USER_NAME_NULL_ERROR("19000", "姓名不可为空"),
    XFYL_USER_ID_CARD_TYPE_NULL_ERROR("19001", "证件类型不可为空"),
    XFYL_USER_ID_CARD_NULL_ERROR("19002", "证件号不可为空"),

    GOODS_NOT_EXISTS("11211", "商品不存在"),
    /**
     * 获取商品信息失败
     */
    QUERY_SKU_INFO_IS_ERROR("31017", "获取商品信息失败"),

    /**
     * 订单中缺失商品信息
     */
    TRADE_LACK_SKU_INFO("31018", "无可结算商品信息"),

    /**
     * 订单中缺失商品信息
     */
    TRADE_LASGEST_SKU_INFO("31018", "至少选择一个检查项"),

    ES_PREPARE_ACTION_ERROR("31020", "ES行为异常"),
    /**
     * 订单中商品服务类型不统一
     */
    TRADE_SKUS_HAS_DIFF_SERVICE_TYPES("31019", "请选择相同服务类型商品"),

    RETRY_ERROR("31020", "重试失败"),

    ORDER_DUMPLICATE("31020", "订单号已存在"),

    SHIP_STATUS_TRANSFER_ERROR("31021", "三方运单状态转换异常!"),

    EXT_EXECUTE_ERROR("31022", "扩展点执行错误!"),

    CITY_NOT_OPEN("31023", "该城市未开通运力店铺"),

    NUMBER_FORMAT_ERROR("31024", "数字转换异常"),

    CANCEL_SHIP_ERROR("31025", "取消运单异常"),

    CREATE_SHIP_ERROR("31026", "创建运单异常"),

    GROOVY_INIT_ERROR("31027", "创建groovy实例异常"),

    CALL_RIDER_ERROR("31028", "呼叫运力失败"),
    /**
     * 收货地址文本解析失败
     */
    USER_ADDRESS_TEXT_PARSE_ERROR("31029", "抱歉，没有识别成功噢"),

    CALL_ANGEL_ERROR("31030", "仅支持护士服务完成前联系护士"),

    ANGEL_NOT_EXIST("31031", "护士信息不存在"),

    VIRTUAL_NUMBER_FULL_ERROR("31032", "系统繁忙，请稍后再拨"),

    VIRTUAL_NUMBER_A_B_EQUALS_ERROR("31033", "A/B号码不可相同"),

    ANGEL_FEE_ADDR_EXSIT("31034", "已存在该地址数据，是否覆盖"),

    OPERATOR_REPEAT("31034", "操作过于频繁，请稍后再试"),

    UAV_SHIP_TIME_ERROR("31035", "查询无人机航班异常"),

    UAV_SHIP_CREATE_ERROR("31036", "无人机航班创建飞行需求ID异常"),

    UAV_SHIP_CREATE_LOCK_ERROR("31037", "无人机航班创建飞行需求ID异常"),

    ;

    BusinessErrorCode(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     *
     */
    private String code;
    /**
     *
     */
    private String desc;

    /**
     *
     */
    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getDescription() {
        return desc;
    }
}
