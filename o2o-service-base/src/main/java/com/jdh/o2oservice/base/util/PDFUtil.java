package com.jdh.o2oservice.base.util;

import com.jd.medicine.base.common.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.PDPageContentStream;
import org.apache.pdfbox.pdmodel.graphics.image.PDImageXObject;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;

@Slf4j
public class PDFUtil {

    /**
     * 知情同意书PDF和签名合成
     *
     * @param pdfUrl PDF地址
     * @param imageUrl 签名图片地址
     * @param saveUrl 合成后保存地址
     * @param page PDF定位页码
     * @param x PDF定位X坐标
     * @param y PDF定位Y坐标
     * @return
     */
    public static Boolean addImageToPDF(String pdfUrl,String imageUrl,String saveUrl,int page,int x,int y) {
        try (InputStream inputStream = new URL(pdfUrl).openStream();
             PDDocument document = PDDocument.load(inputStream)) {
            PDPage pdPage = document.getPage(page); // 获取第一页
            try (PDPageContentStream contentStream = new PDPageContentStream(document, pdPage, PDPageContentStream.AppendMode.APPEND, true, true)) {
                // 加载图片文件
                BufferedImage image = ImageIO.read(new URL(imageUrl));
                // 创建一个ByteArrayOutputStream来存储字节流
                ByteArrayOutputStream baos = new ByteArrayOutputStream();
                String fileExtension = getFileExtension(imageUrl);
                if(StringUtil.isBlank(fileExtension)){
                    return false;
                }
                // 使用ImageIO将BufferedImage写入到ByteArrayOutputStream中
                ImageIO.write(image, fileExtension, baos); // 注意：这里指定了图片格式，例如"jpg"或"png"
                PDImageXObject pdImage = PDImageXObject.createFromByteArray(document, baos.toByteArray(), "image");
                // 在页面上指定位置添加图片（例如，x=100, y=700）
                contentStream.drawImage(pdImage, x, y); // 注意y坐标是从页面底部开始计算的，所以700大致在页面顶部附近
            }
            document.save(saveUrl); // 保存修改后的文档
            return true;
        } catch (IOException e) {
            log.error("PDFUtil.addImageToPDF:",e);
            return false;
        }
    }

    private static String getFileExtension(String fileName){
        int lastIndex = fileName.lastIndexOf(".");
        if (lastIndex != -1 && lastIndex != fileName.length() - 1) {
            return fileName.substring(lastIndex + 1);
        }
        return null;
    }
}
