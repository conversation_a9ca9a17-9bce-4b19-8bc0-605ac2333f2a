package com.jdh.o2oservice.base.ducc.model;

import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/3
 * @description 无人机配置
 */
@Data
public class UavConfig {

    private String baseUrl;//无人机域名

    private String key;//秘钥

    private String privateKeyStr;//数据解密密钥

    private List<UavFlightInfo> uavFlightInfos;//无人机信息

    private String businessHours="07:00-22:30";//营业时间

    private Long intervalMinute=15L;//间隔时间 15分钟

    private Integer specimenCodeMaxSize=30;//一个飞行批次 最多30个样本


    @Data
    public static class UavFlightInfo{

        private String startFlightId;//起点机场id

        private String endFlightId;//终点机场id

        private String receiverName;//收货人姓名

        private String receiverPhone;//收货人电话
    }


    public UavFlightInfo getUavFlightInfoByFlightId(String startFlightId,String endFlightId){
        if(CollectionUtils.isEmpty(uavFlightInfos)){
            return null;
        }
        return uavFlightInfos.stream().filter(t->t.getStartFlightId().equals(startFlightId)&&t.getEndFlightId().equals(endFlightId)).findFirst().orElse(null);
    }

}
