package com.jdh.o2oservice.base.ducc.model.price;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName SettlementFormulaConfig
 * @Description
 * <AUTHOR>
 * @Date 2025/4/28 16:27
 **/
@Data
public class PricingServiceFormula implements Serializable {

    /**
     * 公式表达式
     */
    private String expression;

    /**
     * 公式描述
     */
    private String expressionDesc;

    /**
     * 公式名称
     */
    private String formulaName;

    /**
     * 减分的费项编码
     */
    private List<String> subtractFeeCodeList;
}