package com.jdh.o2oservice.base.enums;

import com.jd.medicine.base.common.util.StringUtil;
import lombok.Getter;

/**
 * 城市级别枚举
 * @author: lwm
 * @date: 2025/4/21 10:35 上午
 * @version: 1.0
 */
@Getter
public enum CityLevelEnum {

    /**
     * 通用
     */
    FIRST_LEVEL("1", "一级"),
    /**
     * 未婚
     */
    SECOND_LEVEL("2", "二级"),
    /**
     * 已婚
     */
    THIRD_LEVEL("3", "三级"),
    /**
     * 丧偶
     */
    FOURTH_LEVEL("4", "四级"),
    ;


    CityLevelEnum(String cityLevel, String desc){
        this.cityLevel = cityLevel;
        this.desc = desc;
    }

    /**
     * 城市级别
     */
    private String cityLevel;
    /**
     * 描述
     */
    private String desc;

    /**
     * cityLevel
     *
     * @param cityLevel
     */
    public void setCityLevel(String cityLevel) {
        this.cityLevel = cityLevel;
    }

    /**
     *
     * @param desc 说明
     */
    public void setDesc(String desc) {
        this.desc = desc;
    }

    /**
     *
     * @param cityLevel
     * @return
     */
    public static String getDescOfCityLevel(String cityLevel){
        if (StringUtil.isBlank(cityLevel)){
            return "";
        }
        for (CityLevelEnum typeEnum: CityLevelEnum.values()){
            if(typeEnum.getCityLevel().equals(cityLevel)){
                return typeEnum.getDesc();
            }
        }
        return "";
    }

    /**
     *
     * @param desc
     * @return
     */
    public static String getLevelByDesc(String desc){
        if (StringUtil.isBlank(desc)){
            return "";
        }
        for (CityLevelEnum typeEnum: CityLevelEnum.values()){
            if(typeEnum.getDesc().equals(desc)){
                return typeEnum.getCityLevel();
            }
        }
        return "";
    }

    /**
     * 验证是否合理
     *
     * @param desc
     * @return
     */
    public static Boolean validateCityLevel(String desc) {
        if (StringUtil.isBlank(desc)) {
            return Boolean.FALSE;
        }
        for (CityLevelEnum typeEnum: CityLevelEnum.values()){
            if(typeEnum.getDesc().equals(desc)){
                return Boolean.TRUE;
            }
        }
        return Boolean.FALSE;
    }
}
