package com.jdh.o2oservice.base.queue;


import com.google.common.base.Joiner;

import java.util.LinkedList;
import java.util.Queue;

/**
 * <AUTHOR>
 * @date 2025/6/6
 * @description 队列
 */
public class FixedSizeQueue<E> {

    private final Queue<E> queue;
    private final int maxSize;

    /**
     * 构造函数
     * @param size 队列最大容量（必须>0）
     */
    public FixedSizeQueue(int size) {
        if (size <= 0) throw new IllegalArgumentException("队列长度必须大于0");
        this.maxSize = size;
        this.queue = new LinkedList<>();
    }

    /**
     * 添加元素到队列尾部
     * @param element 要添加的元素（null值将被忽略）
     */
    public void add(E element) {
        if (element == null) return; // 忽略null值

        // 当队列已满时，自动移除队首元素（最旧元素）
        if (queue.size() >= maxSize) {
            queue.poll();
        }
        queue.offer(element);
    }

    /**
     * 获取队列当前内容（保持插入顺序）
     * @return 队列内容的字符串表示
     */
    @Override
    public String toString() {
        return queue.toString();
    }

    /**
     * 清空队列
     */
    public void clear() {
        queue.clear();
    }

    /**
     * 获取队列当前元素数量
     * @return 队列大小
     */
    public int size() {
        return queue.size();
    }

    public Queue getQueue(){
        return this.queue;
    }

    public static void main(String[] args) {
        FixedSizeQueue<String> queue = new FixedSizeQueue<>(4);
        queue.add("1");
        queue.add("2");
        queue.add("3");
        queue.add("4");
        queue.add("5");
        System.out.println(queue);
        System.out.println(Joiner.on("").join(queue.queue));
    }
}
