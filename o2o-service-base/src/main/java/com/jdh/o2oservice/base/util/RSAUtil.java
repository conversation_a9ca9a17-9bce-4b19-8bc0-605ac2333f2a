package com.jdh.o2oservice.base.util;

import javax.crypto.Cipher;
import java.io.ByteArrayOutputStream;
import java.security.*;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.Base64;


public class RSAUtil {

    public static final String KEY_ALGORITHM = "RSA";
    /**
     * RSA最大加密明文大小
     */
    private static final int MAX_DECRYPT_BLOCK = 128;


    /**
     * 获取私钥
     */
    public static PrivateKey getPrivateKey(String privateKeyString) throws Exception {
        byte[] privateKeyByte = Base64.getDecoder().decode(privateKeyString);
        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(privateKeyByte);
        KeyFactory keyFactory = KeyFactory.getInstance(KEY_ALGORITHM);
        return keyFactory.generatePrivate(keySpec);
    }


    /**
     * 分段解密
     */
    public static String decrypt(String encryptTextHex, String privateKeyStr) throws Exception {
        byte[] encryptText = Base64.getDecoder().decode(encryptTextHex);
        PrivateKey privateKey = getPrivateKey(privateKeyStr);
        Cipher cipher = Cipher.getInstance(KEY_ALGORITHM);
        cipher.init(Cipher.DECRYPT_MODE, privateKey);
        int inputLen = encryptText.length;

        int offSet = 0;
        byte[] cache;
        int i = 0;
        // 对数据分段解密
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        while (inputLen - offSet > 0) {
            if (inputLen - offSet > MAX_DECRYPT_BLOCK) {
                cache = cipher.doFinal(encryptText, offSet, MAX_DECRYPT_BLOCK);
            } else {
                cache = cipher.doFinal(encryptText, offSet, inputLen - offSet);
            }
            out.write(cache, 0, cache.length);
            i++;
            offSet = i * MAX_DECRYPT_BLOCK;
        }
        byte[] plainText = out.toByteArray();
        out.close();
        return new String(plainText);
    }


    public static void main(String[] args) throws Exception {

        try {
            String privateKey = "MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBALO+C8A2svTJTBqpuTjkzChOHY4RqyJw1grNu6EmNfKKQlFc+QyPG3kYtuvd8ZLxLodhRGetTL1E5NCbkTIOobmtb6aruXSW8HLdYTy7qQY8ii89RVvZAh3/8viV+4O+kUIYG3tjIyGTIng5FbDD0r5LXk7nKsWp4JoYDl1bTaevAgMBAAECgYEAl9u2/dy48Yuo2tYOgXz86Aine8J9vglrLZGINqyb46DgSvGsEOpPoc45ranEUgum7gZFzvph3X75ey4UTCCEjXrTgB1n1uqCGLLdwmTurROzJ5QF+B6DD3X5uvlYLDWbplIpkzgT3IqQ+uJFv9mWEON7v/VaflSsNreflop5yDECQQDdBdwjuHqsSokH/WmiLAF1BJkVNO1XZ5xfGobzb0416sME8fEG3n0zCK66kjG7gaTdjOSA5UlRb6nujBrRkI0LAkEA0C/RbN1KgGftDAly8CGfVt2vWUv0BRH+ppBVEPy30GzjfNYNnx48pq81xPjUUc+5VmIrP1cHTWEemcrWhAEObQJAZp8a6Gb2ZlqxJ0mNK2QdRRTecw9BB+0umKW7dPoAKV6YMAqZ66OQJArq/et1NedrAgcx7XsIQMyE7SKWLSldowJAFnSh9UklSRZspji6shYeVsNQr6QWrRlUy2iUFGH9/bhRDV0VWQ1s41nQxe9FD5IFXsD2Az4C5qDMKTCM1O48PQJAOMET3cbLUOE4MAfCQHAtpN0AvYdAWS7oSbWZbnXgt+9XIEiZbntCK96tDpESKiw7lvHNOJhYO7EgH0Sf9fs9jA==";
            String cipherText;
            cipherText = "Yp6Ta9gvpet6Sf2QYeN0uo5RCQocb7lBeHtOgkDiszR14k6ma4+0sGOoJT8s6e8T3AmsnRpWru8HHUJ050PXe3tnYwpcqvGz+xMfDRwLuImZ3KhEMfh+MYZW3lPFBp9Gz3wpFJpTTykgTYyBP3FwvJ2M/QL//MoUZuyIwfZJGRM=";
            String plainText = decrypt(cipherText, privateKey);
            // 迅蚁科技
            System.out.println("解密后明文===== " + plainText);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}