package com.jdh.o2oservice.base.annotation;


import com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 用户操作限制
 *
 * <AUTHOR>
 * @date 2020/7/1 17:00
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface UserOperationLimit {
    // 用户时间间隔内最大操作次数限制,单位次,3次
    int maxLimitTimes() default 3;

    // 用户操作时间间隔,单位秒,默认值 1S
    long timeInterval() default 1;

    // 默认提示操作过于频繁
    BusinessErrorCode errorCode() default BusinessErrorCode.OPERATOR_REPEAT;
}
