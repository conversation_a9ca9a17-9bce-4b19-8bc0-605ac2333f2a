package com.jdh.o2oservice.base.util;


import com.alibaba.fastjson.JSON;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;

import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class TimeIntervalIntersection {


    private static final Long DEFAULT_INTERVAL = 60L;

    @Data
    public static class TimeInterval {  
        private LocalTime start;
        private LocalTime end;

        private Long intervalMins;
  
        public TimeInterval(LocalTime start, LocalTime end) {
            this.start = start;  
            this.end = end;  
        }

        public TimeInterval(LocalTime start, LocalTime end, Long intervalMins) {
            this.start = start;
            this.end = end;
            this.intervalMins = intervalMins;
        }

  
        // 计算两个时间段的交集  
        public Optional<TimeInterval> intersection(TimeInterval other) {
            if (this.end.isBefore(other.start) || this.start.isAfter(other.end)) {
                return Optional.empty(); // 无交集
            }  
            LocalTime start = this.start.isAfter(other.start) ? this.start : other.start;
            LocalTime end = this.end.isBefore(other.end) ? this.end : other.end;
            return Objects.equals(start,end) ? Optional.empty() : Optional.of(new TimeInterval(start, end));
        }
        //计算两个时间的并集
        public TimeInterval mergeWith(TimeInterval other) {
            if (this.end.isBefore(other.start) || this.start.isAfter(other.end)) {
                return new TimeInterval[]{this, other}[0];
            }

            LocalTime newStart = this.start.isBefore(other.start) ? this.start : other.start;
            LocalTime newEnd = this.end.isAfter(other.end) ? this.end : other.end;
            return new TimeInterval(newStart, newEnd);
        }

        public List<TimeInterval> split() {
            List<TimeInterval> timeIntervalList = Lists.newArrayList();
            if(Objects.isNull(intervalMins)){
                intervalMins = DEFAULT_INTERVAL;
            }
            while (start.isBefore(end)) {
                LocalTime splitEnd = start.plusMinutes(intervalMins);
                if (splitEnd.isAfter(end)) {
                    timeIntervalList.add(new TimeInterval(start, end));
                    break;
                }
                if (splitEnd.isBefore(start) || splitEnd.equals(start)) {
                    timeIntervalList.add(new TimeInterval(start, LocalTime.of(23, 59, 59)));
                    break;
                }
                timeIntervalList.add(new TimeInterval(start, splitEnd));
                start = splitEnd;
            }
            return timeIntervalList;
        }

        public List<TimeInterval> splitTwo() {
            List<TimeInterval> timeIntervalList = Lists.newArrayList();
            if(Objects.isNull(intervalMins)){
                intervalMins = DEFAULT_INTERVAL;
            }
            while (start.isBefore(end)) {
                LocalTime splitEnd = start.plusMinutes(intervalMins);
                if (splitEnd.isAfter(end)) {
                    timeIntervalList.add(new TimeInterval(start, end));
                    break;
                }
                if (splitEnd.isBefore(start) || splitEnd.equals(start)) {
                    timeIntervalList.add(new TimeInterval(start, LocalTime.of(23, 59)));
                    break;
                }
                timeIntervalList.add(new TimeInterval(start, splitEnd));
                start = splitEnd;
            }
            return timeIntervalList;
        }

        /**
         * 格式化时间
         * @return
         */
        public String localTimeFormat() {
            return MessageFormat.format("{0}-{1}", start.format(DateTimeFormatter.ofPattern("HH:mm")), end.format(DateTimeFormatter.ofPattern("HH:mm")));
        }
    }

    @Data
    public static class DateTimeInterval {

        private LocalDateTime startDateTime;

        private LocalDateTime endDateTime;

        private Long intervalMins;

        public DateTimeInterval(LocalDateTime startDateTime, LocalDateTime endDateTime, Long intervalMins) {
            this.startDateTime = startDateTime;
            this.endDateTime = endDateTime;
            this.intervalMins = intervalMins;
        }

        public DateTimeInterval(LocalDateTime startDateTime, LocalDateTime endDateTime) {
            this.startDateTime = startDateTime;
            this.endDateTime = endDateTime;
        }

        public List<DateTimeInterval> split() {
            List<DateTimeInterval> timeIntervalList = Lists.newArrayList();
            if(Objects.isNull(intervalMins)){
                intervalMins = DEFAULT_INTERVAL;
            }
            while (startDateTime.isBefore(endDateTime)) {
                LocalDateTime splitEnd = startDateTime.plusMinutes(intervalMins);
                log.info("[TimeIntervalIntersection -> split],startDateTime={}, endDateTime={}", JSON.toJSONString(startDateTime), JSON.toJSONString(splitEnd));
                if (splitEnd.isAfter(endDateTime)) {
                    timeIntervalList.add(new DateTimeInterval(startDateTime, endDateTime));
                    break;
                }
                if(splitEnd.getHour() == 0 && splitEnd.getMinute() == 0 && splitEnd.getSecond() == 0){
                    timeIntervalList.add(new DateTimeInterval(startDateTime, LocalDateTime.of(splitEnd.toLocalDate(), LocalTime.of(23, 59, 59))));
                    startDateTime = splitEnd;
                    continue;
                }
                timeIntervalList.add(new DateTimeInterval(startDateTime, splitEnd));
                startDateTime = splitEnd;
            }
            return timeIntervalList;
        }


        // 计算两个时间段的交集
        public Optional<DateTimeInterval> intersection(DateTimeInterval other) {
            if (this.endDateTime.isBefore(other.startDateTime) || this.startDateTime.isAfter(other.endDateTime)) {
                return Optional.empty(); // 无交集
            }
            LocalDateTime start = this.startDateTime.isAfter(other.startDateTime) ? this.startDateTime : other.startDateTime;
            LocalDateTime end = this.endDateTime.isBefore(other.endDateTime) ? this.endDateTime : other.endDateTime;
            return Optional.of(new DateTimeInterval(start, end));
        }

        public boolean checkIntersection(DateTimeInterval dateTimeInterval) {
            if(startDateTime.isEqual(endDateTime) || startDateTime.isAfter(endDateTime)) {
                return true;
            }
            if(endDateTime.isAfter(dateTimeInterval.startDateTime)){
                return true;
            }
            return false;
        }
    }

    /**
     * 按时间段分割时间
     *
     * @param timeInterval
     * @return
     */
    public static List<TimeInterval> splitTimeIntervals(TimeInterval timeInterval) {
        if(Objects.isNull(timeInterval)) {
            return Lists.newArrayList();
        }
        log.info("[TimeIntervalIntersection -> splitTimeIntervals],localtime split, timeInterval={}", JSON.toJSONString(timeInterval));
        return timeInterval.split();
    }

    /**
     * 按时间段分割时间-包含跨夜场景
     *
     * @param timeInterval
     * @return
     */
    public static List<TimeInterval> splitTimeIntervalsContainsAcrossNight(TimeInterval timeInterval) {
        if(Objects.isNull(timeInterval)) {
            return Lists.newArrayList();
        }
        log.info("[TimeIntervalIntersection -> splitTimeIntervalsContainsAcrossNight],localtime split, timeInterval={}", JSON.toJSONString(timeInterval));
        //跨夜场景:营业开始时间22:00；营业结束时间07:00
        if (timeInterval.getEnd().isBefore(timeInterval.getStart())) {
            //分成两个时间段再进行切分
            return com.google.common.collect.Lists.newArrayList(
                    new TimeIntervalIntersection.TimeInterval(timeInterval.getStart(), LocalTime.of(23, 59), timeInterval.getIntervalMins()),
                    new TimeIntervalIntersection.TimeInterval(LocalTime.of(0, 0), timeInterval.getEnd(), timeInterval.getIntervalMins())
            ).stream().flatMap(interval -> interval.split().stream()).collect(Collectors.toList());
        }
        return timeInterval.split();
    }

    // 计算多个时间段的交集
    public static Optional<TimeInterval> calculateIntersection(List<TimeInterval> intervals) {  
        if (intervals == null || intervals.isEmpty()) {  
            return Optional.empty(); // 没有时间段，无法计算交集  
        }  
  
        TimeInterval result = intervals.get(0);  
        for (int i = 1; i < intervals.size(); i++) {  
            Optional<TimeInterval> intersection = result.intersection(intervals.get(i));  
            if (!intersection.isPresent()) {  
                return Optional.empty(); // 发现没有交集的情况，直接返回empty  
            }  
            result = intersection.get(); // 更新交集结果为当前交集  
        }  
  
        return Optional.of(result); // 返回最终的交集结果  
    }

    // 计算多个时间段的交集
    public static Optional<List<TimeInterval>> groupCalculateIntersection(TimeInterval interval, List<TimeInterval> otherIntervals) {
        if (interval == null || otherIntervals == null || otherIntervals.isEmpty()) {
            return Optional.empty(); // 没有时间段或独立时间段为空，无法计算交集
        }
        try {
            List<TimeInterval> intersections = new ArrayList<>();
            for (TimeInterval otherInterval : otherIntervals) {
                Optional<TimeInterval> intersection = interval.intersection(otherInterval);
                intersection.ifPresent(intersections::add);
            }

            if (intersections.isEmpty()) {
                return Optional.empty(); // 没有找到任何交集
            }

            return Optional.of(intersections); // 返回交集结果列表
        } catch (Exception e) {
            log.error("TimeIntervalIntersection -> groupCalculateIntersection error, interval={}, otherIntervals={}", JSON.toJSONString(interval), JSON.toJSONString(otherIntervals), e);
        }
        return Optional.empty();
    }

    //计算多个时间的并集
    public static List<TimeInterval> mergeIntervals(List<TimeInterval> intervals) {
        if (intervals == null || intervals.isEmpty()) {
            return Collections.emptyList();
        }

        intervals.sort(Comparator.comparing(TimeInterval::getStart));

        List<TimeInterval> merged = new ArrayList<>();
        TimeInterval current = intervals.get(0);

        for (int i = 1; i < intervals.size(); i++) {
            TimeInterval next = intervals.get(i);
            if (current.end.isAfter(next.start)||current.end.equals(next.start)) {
                current = current.mergeWith(next);
            } else {
                merged.add(current);
                current = next;
            }
        }
        merged.add(current);
        return merged;
    }

    /**
     * 按时间段分割时间
     *
     * @param dateTimeInterval
     * @return
     */
    public static List<DateTimeInterval> splitIntervals(DateTimeInterval dateTimeInterval) {
        if(Objects.isNull(dateTimeInterval)) {
            return Lists.newArrayList();
        }
        return dateTimeInterval.split();
    }

    /**
     * 命中有交集的时间段
     *
     * @param timeInterval
     * @return
     */
    public static List<TimeInterval> hitTimeIntervals(TimeInterval timeInterval) {
        if(Objects.isNull(timeInterval)) {
            return Lists.newArrayList();
        }
        TimeInterval timeInterval24 = new TimeInterval(LocalTime.of(0,0),LocalTime.of(23,59));
        List<TimeInterval> allTimeInterval = timeInterval24.splitTwo();

        List<TimeInterval> result =  allTimeInterval.stream().filter(t->{
            if(timeInterval.getStart().toString().equals(t.getEnd().toString())){
                return false;
            }
            if(timeInterval.getEnd().toString().equals(t.getStart().toString())){
                return false;
            }
            //计算两个时段的交集
            if(!timeInterval.intersection(t).isPresent()){
                return false;
            }
            return true;
        }).collect(Collectors.toList());
        return result;
    }

    /**
     * 检查时间是否存在交集
     *
     * @param dateTimeIntervalList
     * @param title
     * @return
     */
    public static String checkIntersection(List<DateTimeInterval> dateTimeIntervalList, String errMsg, String title) {
        StringBuffer sb = new StringBuffer(errMsg);
        if(CollectionUtils.isEmpty(dateTimeIntervalList) || dateTimeIntervalList.size() == CommonConstant.ONE) {
            return sb.toString();
        }
        log.info("[TimeIntervalIntersection -> checkIntersection],dateTimeIntervalList={}", JSON.toJSONString(dateTimeIntervalList));
        List<DateTimeInterval> sortList = dateTimeIntervalList.stream().sorted(Comparator.comparing(DateTimeInterval::getStartDateTime)).collect(Collectors.toList());
        boolean appendTitle = true;
        for (int i = 0; i < sortList.size()-1; i++) {
            DateTimeInterval firstTime = sortList.get(i);
            DateTimeInterval secondTime = sortList.get(i+1);
            log.info("[TimeIntervalIntersection -> checkIntersection],compare time.curr={}, other={}", JSON.toJSONString(firstTime), JSON.toJSONString(secondTime));
            if(firstTime.checkIntersection(secondTime)) {
                if(appendTitle) {
                    sb.append("\n"+title+"\n");
                    appendTitle = false;
                }
                sb.append(MessageFormat.format("{0}-{1}服务资源数量重复，请检查.", TimeUtils.localDateTimeToStr(firstTime.getStartDateTime()), TimeUtils.localDateTimeToStr(firstTime.getEndDateTime())));
            }
        }
        return sb.toString();
    }


    /**
     * 取时间交集
     *
     * @param dateTimeIntervals
     * @return
     */
    public static Optional<DateTimeInterval> dateTimeIntersection(List<DateTimeInterval> dateTimeIntervals) {
        if(CollectionUtils.isEmpty(dateTimeIntervals)) {
            return Optional.empty();
        }

        DateTimeInterval result = dateTimeIntervals.get(0);
        for (int i = 1; i < dateTimeIntervals.size(); i++) {
            Optional<DateTimeInterval> intersection = result.intersection(dateTimeIntervals.get(i));
            if (!intersection.isPresent()) {
                return Optional.empty(); // 发现没有交集的情况，直接返回empty
            }
            result = intersection.get(); // 更新交集结果为当前交集
        }

        return Optional.of(result); // 返回最终的交集结果
    }

    /**
     * 计算交集时长
     * @param startDate
     * @param endDate
     * @param planStartDate
     * @param planEndDate
     * @return
     */
    public static long calculateIntersectionMinutes(Date startDate, Date endDate, Date planStartDate, Date planEndDate) {
        if (Objects.isNull(startDate) || Objects.isNull(endDate) || Objects.isNull(planStartDate) || Objects.isNull(planEndDate)) {
            return 0L;
        }
        // 确定交集的开始时间
        Date intersectionStart = startDate.after(planStartDate) ? startDate : planStartDate;

        // 确定交集的结束时间
        Date intersectionEnd = endDate.before(planEndDate) ? endDate : planEndDate;

        // 计算交集时间，以分钟为单位
        long intersectionMillis = intersectionEnd.getTime() - intersectionStart.getTime();
        //为负数说明无交集，默认取0
        return Math.max(intersectionMillis / (60 * 1000), 0);
    }


    public static void main(String[] args) {  
//        List<TimeInterval> intervals = new ArrayList<>();
//        // 添加时间段到列表...
//        TimeInterval timeInterval1 = new TimeInterval(LocalTime.parse("08:00", DateTimeFormatter.ofPattern("HH:mm")),LocalTime.parse("09:00", DateTimeFormatter.ofPattern("HH:mm")));
//        TimeInterval timeInterval2 = new TimeInterval(LocalTime.parse("08:00", DateTimeFormatter.ofPattern("HH:mm")),LocalTime.parse("20:00", DateTimeFormatter.ofPattern("HH:mm")));
//        TimeInterval timeInterval3 = new TimeInterval(LocalTime.parse("20:00", DateTimeFormatter.ofPattern("HH:mm")),LocalTime.parse("21:00", DateTimeFormatter.ofPattern("HH:mm")));
//        System.out.println(LocalTime.parse("08:00", DateTimeFormatter.ofPattern("HH:mm")).toString());
//        intervals.add(timeInterval1);
//        intervals.add(timeInterval2);
//        intervals.add(timeInterval3);
//        Optional<TimeInterval> intersection = calculateIntersection(intervals);
//        System.out.println(JSON.toJSONString(intersection.isPresent()));
//
//        List<TimeInterval> timeIntervals = mergeIntervals(intervals);
//        System.out.println(JSON.toJSONString(timeIntervals));

//        LocalDateTime start = LocalDate.now().atTime(12,0,0);
//        LocalDateTime end = LocalDate.now().atTime(15,0,0);
//        TimeIntervalIntersection.DateTimeInterval dateTimeInterval = new DateTimeInterval(start, end, 30L);
//
//        List<DateTimeInterval> dateTimeIntervals = splitIntervals(dateTimeInterval);
//        System.out.println(JSON.toJSONString(dateTimeIntervals));

        LocalTime start = LocalTime.of(7,0,0);
        LocalTime end = LocalTime.of(22,30,0);
        TimeIntervalIntersection.TimeInterval timeInterval = new TimeInterval(start, end, 15L);

        List<TimeInterval> dateTimeIntervals = splitTimeIntervals(timeInterval);
        System.out.println(JSON.toJSONString(dateTimeIntervals));


        LocalTime start3 = LocalTime.of(9,0,0);
        LocalTime end3 = LocalTime.of(10,0,0);

        TimeIntervalIntersection.TimeInterval timeInterva2 = new TimeInterval(start, end);
        TimeIntervalIntersection.TimeInterval timeInterva3 = new TimeInterval(start3, end3);

        Optional<TimeInterval> optionalTimeInterval = timeInterva2.intersection(timeInterva3);
        System.out.println(optionalTimeInterval.get());



        LocalTime start1 = LocalTime.of(22,34);
        LocalTime end1 = LocalTime.of(23,54);

        TimeInterval timeIntervalOne = new TimeInterval(start1,end1);


        TimeInterval timeInterval24 = new TimeInterval(LocalTime.of(0,0),LocalTime.of(23,59));
        List<TimeInterval> timeIntervalList = timeInterval24.splitTwo();
        System.out.println(JSON.toJSONString(timeIntervalList));

        List<TimeInterval> result =  timeIntervalList.stream().filter(t->{
            if(start1.toString().equals(t.getEnd().toString())){
                return false;
            }
            if(end1.toString().equals(t.getStart().toString())){
                return false;
            }
            //计算两个时段的交集
            if(!timeIntervalOne.intersection(t).isPresent()){
                return false;
            }
            return true;
        }).collect(Collectors.toList());
        System.out.println("ha="+JSON.toJSONString(result));
    }
}