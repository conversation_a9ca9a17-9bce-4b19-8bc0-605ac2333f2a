package com.jdh.o2oservice.base.enums;

import lombok.Getter;

@Getter
public enum JdhStoreTransferStationTypeEnum {

    SELF_RECEIVE_SAMPLE(1, "自收样"),
    UAV_RECEIVE_SAMPLE(2, "无人机收样")
    ;

    JdhStoreTransferStationTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    /**
     *
     */
    private Integer type;

    /**
     *
     */
    private String desc;
}
