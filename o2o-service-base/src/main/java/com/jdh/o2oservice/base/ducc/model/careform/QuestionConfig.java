package com.jdh.o2oservice.base.ducc.model.careform;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/25
 * @description 题库
 */
@Data
public class QuestionConfig {

    private String name;//题目名称

    private String quesCode;//题编码

    private Integer sort;//排序

    private List<Long> mappingQuesId;

    private Integer tag;//1和null: input  2: textarea

    private String signKey;//签字关键词

    private Integer appendQuesId;//拼接题目id

}
