package com.jdh.o2oservice.base.util;

import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Field;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/10/18 15:35
 */
@SuppressWarnings("ALL")
public class TextUtil {



    /**
     * 按照指定长度size，截取文本；char为英文字母或者符号时占一位长度，即在区间 [0,127]占一位，其他占两位
     * 在DynamicParse 配置中有引用
     * @return
     */
    public static String splitCN(String text, String sizeStr){
        if (StringUtils.isBlank(text) || StringUtils.isBlank(sizeStr)){
            return text;
        }
        Integer size = Integer.valueOf(sizeStr);
        char[] array = text.toCharArray();
        StringBuilder builder = new StringBuilder();
        int count = 0;
        int i = 0;
        for (; i < array.length && count < size; i++) {
            char cur = array[i];
            builder.append(cur);
            if (cur <= 127){
                count += 1;
            }else{
                count += 2;
            }
        }
        // 截取的文本小于原始文本，后面追加...
        if (count >= size && i < text.length()){
            builder.append("...");
        }
        return builder.toString();
    }

    /**
     * 替换空格和制表符
     *
     * @param content
     * @return
     */
    public static String replaceSpace(String content) {
        if(StringUtils.isBlank(content)) {
            return null;
        }
        content = content.replaceAll("\\\\n", "");
        content = content.replaceAll("\\\\r", "");
        content = content.replaceAll("\\\\t", "");
        content = content.replaceAll("\\s+", "");
        return content;
    }

    /**
     * 使用给定的值替换模板中的占位符。
     * @param template 包含占位符的模板字符串。
     * @param values 用于替换占位符的键值对映射。
     * @return 替换后的字符串。
     */
    public static String replacePlaceholders(String template, Map<String, String> values) {
        // 正则表达式匹配占位符，例如 {med}，{link}
        Pattern pattern = Pattern.compile("\\{(\\w+)}");
        Matcher matcher = pattern.matcher(template);

        StringBuffer result = new StringBuffer();
        while (matcher.find()) {
            // 获取占位符中的键
            String key = matcher.group(1);
            // 获取键对应的值
            String replacement = values.getOrDefault(key, matcher.group(0));
            // 替换并追加到结果中
            matcher.appendReplacement(result, replacement);
        }
        // 追加剩余部分
        matcher.appendTail(result);

        return result.toString();
    }

    /**
     * 遍历获取对象集合的属性值字符串
     * @param list
     * @param propertyName
     * @param delimiter
     * @return
     * @param <T>
     */
    public static <T> String joinProperty(List<T> list, String propertyName, String delimiter) {
        return list.stream()
                .map(item -> {
                    try {
                        String name = item.getClass().getName();
                        if (item instanceof Map) {
                            // 如果是Map，直接获取键对应的值
                            Map convert = Convert.convert(Map.class, item);
                            return convert.getOrDefault(propertyName, "").toString().trim();
                        } else if (name.contains("JSONObject")) {
                            // 如果是JSONObject，统一转换为fastjson JSONObject获取键对应的值
                            JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(item));
                            return jsonObject.getOrDefault(propertyName, "").toString().trim();
                        }
                        // 使用反射获取属性值
                        Field declaredField = item.getClass().getDeclaredField(propertyName);
                        declaredField.setAccessible(true);
                        Object o = declaredField.get(item);
                        return Objects.isNull(o) ? "" : o.toString().trim();
                    } catch (NoSuchFieldException | IllegalAccessException e) {
                        e.printStackTrace();
                        return "";
                    }
                })
                .collect(Collectors.joining(delimiter));
    }

    /**
     * 遍历获取对象集合的属性值字符串，支持限制返回的最大条数和附加后缀。
     *
     * @param list         对象集合
     * @param propertyName 属性名
     * @param delimiter    分隔符
     * @param maxCount     取前n条数据
     * @param suffix       如数据多余n条，返回字符串后缀
     * @return 拼接后的字符串
     * @param <T>          泛型类型
     */
    public static <T> String joinPropertyLimit(List<T> list, String propertyName, String delimiter, Integer maxCount, String suffix) {
        if (0 >= maxCount || CollectionUtils.isEmpty(list)) {
            return "";
        }
        maxCount = Math.min(maxCount, list.size());

        String result = list.stream()
                .limit(maxCount)
                .map(item -> {
                    try {
                        String name = item.getClass().getName();
                        if (item instanceof Map) {
                            // 如果是Map，直接获取键对应的值
                            Map convert = Convert.convert(Map.class, item);
                            return convert.getOrDefault(propertyName, "").toString().trim();
                        } else if (name.contains("JSONObject")) {
                            // 如果是JSONObject，统一转换为fastjson JSONObject获取键对应的值
                            JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(item));
                            return jsonObject.getOrDefault(propertyName, "").toString().trim();
                        }
                        // 使用反射获取属性值
                        Field declaredField = item.getClass().getDeclaredField(propertyName);
                        declaredField.setAccessible(true);
                        Object o = declaredField.get(item);
                        return Objects.isNull(o) ? "" : o.toString().trim();
                    } catch (NoSuchFieldException | IllegalAccessException e) {
                        e.printStackTrace();
                        return "";
                    }
                })
                .collect(Collectors.joining(delimiter));

        // 如果原始数据多于 maxCount 条，则在结果后面加上 suffix
        if (list.size() > maxCount) {
            result += suffix;
        }

        return result;
    }

    public static void main(String[] args) {
        System.out.println(TextUtil.splitCN("京东消费医疗 护士到家服务", "20"));
    }
}
