package com.jdh.o2oservice.base.util;

import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.jd.ump.profiler.util.StringUtil;
import com.jdh.o2oservice.base.constatnt.GwConstants;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode;
import com.jdh.o2oservice.base.gw.ColorGwRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 网关map转换工具类
 *
 * <AUTHOR>
 * @date 2019-11-13 14:19
 */
@Slf4j
public class GwMapUtil {

    /**
     * 参数转换
     *
     * @param gwMap
     * @param clazz
     * @param <T>
     * @return
     */
    public static <T extends Object> T convertToParam(Map<?, ?> gwMap, Class<T> clazz) {
        ColorGwRequest colorGwRequest = new ColorGwRequest(gwMap);
        String body = colorGwRequest.getBody();
        T domain = null;
        if (StringUtil.isBlank(body)) {
            log.info("GwMapUtil -> convertToParam param error body is null, gwMap={}, clazz={}", JSON.toJSONString(gwMap), clazz);
            domain = ReflectUtil.newInstance(clazz);
        }else{
            domain = colorGwRequest.toDomain(clazz);
        }
        String pin = GwMapUtil.getPin(gwMap);
        String clientIp = GwMapUtil.getIP(gwMap);
        String uuid = GwMapUtil.getUuid(gwMap);
        if(StrUtil.isNotEmpty(pin) && Objects.nonNull(domain)){
            putUserPinField(domain,pin);
        }
        if(StrUtil.isNotEmpty(clientIp) && Objects.nonNull(domain)){
            putClientIpField(domain,clientIp);
        }
        if(StrUtil.isNotEmpty(uuid) && Objects.nonNull(domain)){
            putUuidField(domain,uuid);
        }
        return domain;
    }

    /**
     * 参数转换
     *
     * @return
     */
    public static Map<String, Object> parseBody(Map<?, ?> gwMap) {
        ColorGwRequest colorGwRequest = new ColorGwRequest(gwMap);
        String body = colorGwRequest.getBody();
        Map<String, Object> params;
        if (StringUtils.isNoneBlank(body)) {
            params = JSON.parseObject(body);
            log.info("GwMapUtil -> parseBody");
        }else{
            params = new HashMap<>();
        }
        String pin = GwMapUtil.getPin(gwMap);
        if(StrUtil.isNotEmpty(pin)){
            params.put("userPin", pin);
        }
        return params;
    }

    /**
     * 默认塞入userPin，字段名必须为userPin
     *
     * @param domain domain
     * @param pin    pin
     */
    private static <T> void putUserPinField(T domain,String pin){
        try {
            ReflectUtil.invoke(domain,"setUserPin",pin);
        }catch (Throwable throwable){
            //log.error("GwMapUtil putUserPinField error",throwable);
        }
    }

    private static <T> void putClientIpField(T domain, String clientIp){
        try {
            ReflectUtil.invoke(domain,"setClientIp",clientIp);
        }catch (Throwable throwable){
            //log.error("GwMapUtil putClientIpField error",throwable);
        }
    }

    private static <T> void putUuidField(T domain, String uuid){
        try {
            ReflectUtil.invoke(domain,"setUuid",uuid);
        }catch (Throwable throwable){
            //log.error("GwMapUtil putClientIpField error",throwable);
        }
    }
    /**
     * @param request
     * @return
     */
    public static String getPin(Map<?, ?> request) {
        if (null == request) {
            return null;
        }
        Object val = request.get(GwConstants.PIN);
        if (val == null) {
            return null;
        } else {
            return String.valueOf(val);
        }
    }

    /**
     * 校验pin是否登录
     *
     * @param map
     * @return
     */
    public static void checkGwPin(Map<?, ?> map) {
        //获取pin
        String pin = getPin(map);
        //校验pin
        checkPin(pin);
    }

    /**
     * 校验pin是否登录
     *
     * @param userPin
     * @return
     */
    public static void checkPin(String userPin) {
        if (StringUtil.isBlank(userPin)) {
            log.info("GwMapUtil -> checkPin 用户未登录，请先登录, userPin={}", userPin);
            throw new BusinessException(BusinessErrorCode.UNKNOWN_USER_LOGIN);
        }
    }


    /**
     * @param request
     * @return
     */
    public static String getIP(Map<?, ?> request) {
        Object val = request.get(GwConstants.IP);
        if (val == null) {
            return null;
        } else {
            return String.valueOf(val);
        }
    }

    /**
     * @param request
     * @return
     */
    public static String getUuid(Map<?, ?> request) {
        Object val = request.get(GwConstants.Uuid);
        if (val == null) {
            return null;
        } else {
            return String.valueOf(val);
        }
    }

}
