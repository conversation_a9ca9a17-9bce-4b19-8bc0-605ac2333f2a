package com.jdh.o2oservice.base.ducc.model.fee;

import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName CityLevelSettlementCoefficientConfig
 * @Description
 * <AUTHOR>
 * @Date 2025/5/16 23:52
 **/
@Data
public class CityLevelSettlementCoefficientConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自营服务者结算系数
     */
    protected String selfServiceCoefficient;

    /**
     * 兼职服务者结算系数
     */
    protected String sidelineServiceCoefficient;

    /**
     * 平台使用费结算系数
     */
    protected String platformServiceCoefficient;

    /**
     * 平台补贴结算系数
     */
    protected String platformSubsidyCoefficient;
}