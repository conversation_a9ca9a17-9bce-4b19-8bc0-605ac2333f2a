package com.jdh.o2oservice.base.ducc.model;

import lombok.Data;


/**
 * @ClassName:RefundFreezeMapping
 * @Description:
 * @Author: liwenming
 * @Date: 2024/5/1 10:40
 * @Vserion: 1.0
 **/
@Data
public class RefundStatusRatioMapping {

    ///////////////////////////////////退款金额比例////////////////////////////////////
    /**
     * 服务费退款比例
     */
    private String serviceAmountRefundRatio;
    /**
     * 其他费项退款比例（最后一笔）
     */
    private String feeAmountRefundRatio;

    ///////////////////////////////////结算金额比例////////////////////////////////////
    /**
     * sku服务费-结算比例
     */
    private String settleServiceRatio;
    /**
     * 费项（时段费+距离费+动态调整费）结算比例
     */
    private String settleFeeRatio;
    /**
     * 派单加价-结算比例
     */
    private String dispatchAddFee;
    /**
     * 记收入:服务费比例
     */
    private String serviceAmountIncomeRatio;
    /**
     * 记收入:其他费项比例（最后一笔）
     */
    private String feeAmountIncomeRatio;
}
