package com.jdh.o2oservice.base.ducc.model.price;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName PricingServiceFeeConfig
 * @Description
 * <AUTHOR>
 * @Date 2025/4/29 13:40
 **/
@Data
public class PricingServiceFeeConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 费项配置ID（实例ID）
     */
    protected String feeConfigId;

    /**
     * 费项编码
     */
    protected String feeCode;

    /**
     * 依赖的事实对象编码
     */
    protected List<String> dependFactObjectCodes;

    /**
     * 依赖的费项编码
     */
    protected List<String> dependFeeConfigIds;

    /**
     * 计算规则列表
     */
    protected List<PricingServiceFeeCalculateRule> calculateRuleList;
}