########################################################################################################################
##############################################  provider config     #######################################################
########################################################################################################################
jsf.registry.index=test.i.jsf.jd.local
common.provider.alias=o2o-service-dev
common.provider.token=1024888


########################################################################################################################
##############################################  consumer config     ####################################################
########################################################################################################################

# popLoc ???????
com.jd.pop.order.loc.assembledflow.soa.service.LocOrderCodeSoaService.alias=jsfLocSoaAlias
## \u5546\u5BB6\u7AEF\u63A5\u53E3\u9ED8\u8BA4\u522B\u540D\u3001token
xfyl.merchant.export.alias=test
xfyl.merchant.export.token=123456

## \u5065\u5EB7\u6863\u6848-\u60A3\u8005\u4FE1\u606F
com.jd.pop.patient.client.service.PatientInfoManageService.alias=jdhyf
com.jd.pop.patient.client.service.PatientInfoManageService.token=123456


# \u95E8\u4E2D\u63A5\u53E3
com.jd.pop.wxo2o.spi.store.StoreService.alias=pop-wxo2o-provider
com.jd.pop.wxo2o.spi.store.StoreService.authCode=physical_1021790
com.jd.pop.wxo2o.spi.store.StoreService.accessToken=0c93d826f98156994313518485eb53ef

#\u5206\u5E03\u5F0Fid\u63A5\u53E3
com.global.service.id.GlobalServiceID.alias=test
com.global.service.id.GlobalServiceID.token=123456
com.global.service.id.GlobalServiceID.failBack.alias=test
com.global.service.id.GlobalServiceID.failBack.token=123456

# \u5E97\u94FA\u57FA\u672C\u4FE1\u606F\u63A5\u53E3
com.jd.pop.vender.center.i18n.api.shop.VenderShopFacade.alias=settled-stable
com.jd.pop.vender.center.i18n.api.shop.VenderShopFacade.token=fddd4409-7fbc-4c63-8ed0-249ee65b2add
com.jd.pop.vender.center.i18n.api.shop.VenderShopFacade.clientName=J-dos-physicalexamination

#\u5546\u54C1\u8BFB
#test:   yf:component_crs_jd     online:HT_PUBLIC_LEVEL0_0
com.jd.gms.crs.rpc.ProductRpc.alias=component_crs_jd
#test:   yf:6c076cf7-1ef6-46d5-b0f3-26ad4b33a148      online:6c076cf7-1ef6-46d5-b0f3-26ad4b33a148
com.jd.gms.crs.rpc.ProductRpc.authToken=6c076cf7-1ef6-46d5-b0f3-26ad4b33a148
#test:   yf:1021675      online:1021675
com.jd.gms.crs.rpc.ProductRpc.clientName=1021675
#test:   yf:component_crs_jd      online:HT_PUBLIC_LEVEL0_0
com.jd.gms.crs.rpc.ProductRpc.otherAlias=component_crs_jd

# \u865A\u62DF\u53F7
com.jd.jdcc.privacy.number.alias=pre
## \u77ED\u94FE\u670D\u52A1
com.jd.shorturl.api.jsf.ShortUrlService.alias=shortUrlService-test

# \u9A7E\u8F66\u8DEF\u5F84\u89C4\u5212
com.jd.lbs.jdlbsapi.search.DirectionService.alias=lbs-search:v0.1
com.jd.lbs.jdlbsapi.search.DirectionService.token=gj78kq8m
# \u667A\u80FD\u6587\u672C\u89E3\u6790
com.jd.lbs.jdlbsapi.c2c.TextParsingService.alias=lbsapi-c2c-onlinecc
com.jd.lbs.jdlbsapi.c2c.TextParsingService.token=fdds235b
# \u4E92\u8054\u7F51\u533B\u9662\u8BA2\u5355\u63A5\u53E3
com.jd.newnethp.trade.center.export.core.service.submit.SubmitDiagExport.alias=jdhyf
com.jd.newnethp.trade.center.export.core.service.submit.SubmitDiagExport.token=123456
# \u4E92\u8054\u7F51\u533B\u9662\u6D3E\u5355\u63A5\u53E3
com.jd.newnethp.diag.export.triage.service.TriageAssignExport.alias=jdhyf_nurse
com.jd.newnethp.diag.export.triage.service.TriageAssignExport.token=123456
# \u4E92\u8054\u7F51\u533B\u9662\u6D3E\u5355\u63A5\u5355\u63A5\u53E3
com.jd.newnethp.trade.center.export.core.service.basediag.TradeDiagStatusChangeExport.alias=jdhyf_nurse
com.jd.newnethp.trade.center.export.core.service.basediag.TradeDiagStatusChangeExport.token=123456
# \u4E92\u8054\u7F51\u533B\u9662\u6D3E\u5355\u5F00\u5173\u63A5\u53E3
com.jd.newnethp.diag.export.triage.service.TriageInfoExport.alias=jdhyf_nurse
com.jd.newnethp.diag.export.triage.service.TriageInfoExport.token=123456
# \u4E92\u8054\u7F51\u533B\u9662\u6D3E\u5355\u8F6C\u8BCA\u63A5\u53E3
com.jd.newnethp.diag.export.triage.service.OrderTransferExport.alias=jdhyf_nurse
com.jd.newnethp.diag.export.triage.service.OrderTransferExport.token=123456
#\u8BA2\u5355\u91D1\u989D\u8BA1\u7B97\u670D\u52A1\uFF08ocs\uFF09\u67E5\u8BE2\u63A5\u53E3
## test:Jsf-OrderCalculationQueryServiceJsf    yf:     online:queryocs
com.jd.ofc.ocs.webservice.jsf.OrderCalculationQueryServiceJsf.alias=queryocs

com.jd.mobilePhoneMsg.sender.client.service.SmsMessageTemplateRpcService.alias=test
com.jd.mobilePhoneMsg.sender.client.service.SmsMessageTemplateRpcService.token=63544240ZVV4

# \u8D26\u53F7\u67E5\u8BE2\u670D\u52A1
com.jd.seller.baccount.cache.api.belong.AccountBelongFacade.alias=baccount-cache-i18n-ht


com.jd.trade2.core.export.service.SubmitOrderExportService.alias=ztcs-vertical
com.jd.trade2.core.export.service.SubmitOrderExportService.token=a68123b7-7fd1-40fd-a114-78d98ba9bf81
com.jd.trade2.core.export.service.SubmitOrderExportService.clientName=J-dos-physicalexamination

com.jd.trade2.core.export.service.UserActionExportService.alias=ztcs-vertical
com.jd.trade2.core.export.service.UserActionExportService.token=a68123b7-7fd1-40fd-a114-78d98ba9bf81
com.jd.trade2.core.export.service.UserActionExportService.clientName=J-dos-physicalexamination

com.jd.pop.vender.center.service.shop.ShopSafService.alias=popvendercenter-trade
com.jd.pop.vender.center.service.shop.ShopSafService.token=b20c51d34c88681fd894a2fc67cdfcd6

#\u5546\u54C1\u5927\u5B57\u6BB5\uFF08\u5546\u54C1\u8BE6\u60C5\u3001\u89C6\u9891\u3001\u4E2D\u53F0\u89C4\u683C\u53C2\u6570\uFF09
com.jd.gms.crs.rpc.AssemblyRpc.alias=test

com.jd.limitbuy.service.StrategyMetadataService.alias=WebFlat-LF-Test
com.jd.limitbuy.service.StrategyMetadataService.token=245734ed-5c15-43f8-82e9-b063a51bf05a


com.jd.gms.component.labrador.api.service.read.ProductReadService.alias=jdos_yfb_jd
com.jd.gms.component.labrador.api.service.read.ProductReadService.authToken=5e43ae00-defe-45b5-92fa-3a3f2cb5d86d
com.jd.gms.component.labrador.api.service.read.ProductReadService.clientName=J-dos-examinationman

examination.man.export.alias=jdhyf
examination.man.export.token=xfylhme100

com.jd.user.address4.AddressReadExportService.alias=paas-test
com.jd.user.address4.AddressReadExportService.token=d06a330d-b50d-441c-bfd8-8ca76294ac1e
com.jd.user.address4.AddressReadExportService.clientName=(J-dos)physicalexamination

com.jd.boundaryless.lbs.center.spi.store.StoreLbsProvider.alias=lbs
com.jd.boundaryless.lbs.center.spi.store.StoreLbsProvider.authCode=physical_1021790
com.jd.boundaryless.lbs.center.spi.store.StoreLbsProvider.accessToken=0c93d826f98156994313518485eb53ef

com.jd.store.soa.StoreTagRpcService.alias=storecenter
com.jd.store.soa.StoreScoreRpcService.alias=storecenter

jdh.market.export.jsf.alias=jdhyf
jdh.market.export.jsf.token=123456


com.jdh.trade.settle.biz.sdk.service.JdhPayJsfService.alias=jdhyf
com.jdh.trade.settle.biz.sdk.service.JdhPayJsfService.token=np4ktnZ9

app.cashier.appKey=7395909e76dd9bffbabff0b063dcd461

com.jd.paytrade.front.export.PaymentAccResource.alias=paymentAccResourceJsf
com.jd.pay.platform.api.provider.channel.PlatPayChannelService.alias.prefix=platPay_vertical_lf
com.jd.pay.platform.api.provider.channel.PlatPayChannelService.authToken=68c3526e-5cfe-48dd-867a-b4e6d197d9bb
com.jd.pay.platform.api.provider.channel.PlatPayChannelService.clientName=J-dos-physicalexamination

com.jd.trade.guide.sdk.service.GuideService.alias=trade-guide-pre
com.jd.trade.guide.sdk.service.GuideService.authToken=ee7f0293-9e60-43f7-a4bf-e322e692c81b
com.jd.trade.guide.sdk.service.GuideService.clientName=J-dos-physicalexamination
com.jd.trade.guide.sdk.service.GuideService.otherAlias=trade-guide-pre

com.jd.addresstranslation.api.address.GBAddressToJDAddressService.alias=addresstranslation:v1
com.jd.addresstranslation.api.address.GBAddressToJDAddressService.token=g24aj7mp

com.jd.pap.priceinfo.sdk.service.PriceInfoService.alias=LF_main
com.jd.pap.priceinfo.sdk.service.PriceInfoService.authToken=27f45de5-03c6-4f67-8d31-cac85d34ccfa

com.jd.pop.vender.center.i18n.api.business.BusinessQueryByVenderIdAndKeysFacade.alias=settled-stable
com.jd.pop.vender.center.i18n.api.business.BusinessQueryByVenderIdAndKeysFacade.token=8893cf3f-28f6-40c8-9a65-68f46e0aacbc

server.room.name=
check.miniprogram.cashier.returnUrl=check.miniprogram.cashier.returnUrl

#\u6D4B\u8BD5\u73AF\u5883   yf:lbs_addressMapService_lf  online:addresstranslation:v1
com.jd.addresstranslation.api.address.JDAddressDistrictService.alias=addresstranslation:v1
#\u6D4B\u8BD5\u73AF\u5883   yf:lbs_addressMapService_lf  online:jd83lelv
com.jd.addresstranslation.api.address.JDAddressDistrictService.token=jd83lelv

#\u5168\u5730\u5740\u8F6C\u6362\u7ECF\u7EAC\u5EA6
com.jd.lbs.geocode.api.GeocodingService.alias=lbs-geocoding:v1
com.jd.lbs.geocode.api.GeocodingService.token=7hj89w6v

com.jd.cmp.soa.ugc.service.content.ContentExportService.alias=jdhyf-bpass
com.jd.cmp.soa.ugc.service.content.ContentExportService.authToken=be6533c2-3e95-4e62-b5c1-39cec24ee53f
com.jd.cmp.soa.ugc.service.content.ContentExportService.clientName=J-dos-jdh-o2o-service

dada.base.url=test
dada.sourceId=test
dada.appKey=test
dada.appSecret=test

com.jd.newnethp.diag.message.center.export.MsgChannelExport.alias=jdhyf
com.jd.newnethp.diag.message.center.export.MsgChannelExport.token=123456

com.jdh.b2c.export.api.address.AddressInfoExport.alias=jdh_b2c_lf
com.jdh.b2c.export.api.address.AddressInfoExport.token=b2c_online

#\u88C5\u5427 \u83B7\u53D6\u5546\u54C1\u79FB\u52A8\u7AEF\u6837\u5F0F
com.sdd.mkt.sdk.component.ZbWareDetailService.alias=WARE-DETAIL
com.sdd.mkt.sdk.component.ZbWareDetailService.token=c42e229e-e405-4907-be92-628f795c52a0
com.sdd.mkt.sdk.component.ZbWareDetailService.clientName=(J-dos)physicalexamination

com.jd.ugc.comment.api.CommentService.alias=cn_sdk_ht_other2

#3ad33565-2021-4275-8321-0f2b6bc316f7
com.jd.order.sdk.export.QueryOrderInfoService.token=
#xsrh_pre
com.jd.order.sdk.export.QueryOrderInfoService.alias=ztcs

##test:fce-orb-state-test      yf:fce-orb-state-yfb     online:\u5ECA\u574A\uFF1Afce-orb-state  \u6C47\u5929\uFF1Afce-orb-state-ht
com.jd.fce.orb.service.OrbOomCancelOrderService.alias=fce-orb-state-test

com.jd.lbs.geofencing.api.customfence.CustomPresortService.alias=test

com.jd.newnethp.cps.center.export.service.inspection.InspectionQueryExport.alias=test
com.jd.newnethp.cps.center.export.service.inspection.InspectionQueryExport.token=test
#\u9884\u53D1\u522B\u540D\uFF1Ajdhyf\uFF0C\u9884\u53D1token\uFF1A123456
#\u7EBF\u4E0A\u5ECA\u574A\u522B\u540D\uFF1Ajdh_medical_famdoc_open_api_prod\uFF0C  \u7EBF\u4E0A\u6C47\u5929\u522B\u540D\uFF1Ajdh_medical_famdoc_open_api_prod_ht\uFF0C\u7EBF\u4E0Atoken=w3hrSwec
com.jdh.medical.famdoc.open.api.service.im.FdImSessionService.alias=jdhyf
com.jdh.medical.famdoc.open.api.service.im.FdImSessionService.token=123456

# POP\u95E8\u5E97\u7BA1\u7406\u63A5\u53E3-\u6709\u6548\u6570\u636E
com.jd.boundaryless.store.center.spi.store.StoresServiceProvider.alias=storecenter_medicine
com.jd.boundaryless.store.center.spi.store.StoresServiceProvider.authCode=1111
com.jd.boundaryless.store.center.spi.store.StoresServiceProvider.accessToken=1111

# \u5929\u7F51\u654F\u611F\u8BCD\u63A5\u53E3
com.jd.sensitiveword.service.SensitiveWordJsfService.alias=sensitive_liantiao

com.jd.vd.api.service.VODService.alias=test
com.jd.vd.api.service.VODService.appId=test
com.jd.vd.api.service.VODService.secretKey=test

com.jdd.baoxian.b.insurance.core.trade.export.resource.NurseVisitInsuranceResource.alias=test
########################################################################################################################
##############################################  \u9664\u4E86 consumer\uFF0Cprovider config start     ################################
########################################################################################################################
gis.appKey=3FBA518E74114637880B213D17395E75
h5SettleUrl=https://laputa-yf.jd.com/serviceHome/nurse/examineSettlement?addressId=%s&partnerSource=%s&partnerSourceOrderId=%s
miniSettleUrl=https://laputa-yf.jd.com/serviceHome/nurse/examineSettlement?addressId=%s&partnerSource=%s&partnerSourceOrderId=%s
orderDetailUrl=https://healthcare-center-beta.jd.com/jmiOrderList/orderDetail?orderId=%s
########################################################################################################################
##############################################  \u9664\u4E86 consumer\uFF0Cprovider config end     ##################################
########################################################################################################################

#csrf
csrf.url=jd.com
# uim \u83DC\u5355
com.jd.uim2.facade.jsf.alias=prod_lf


#HR\u670D\u52A1
com.jd.enterprise.api.client.hr.alias=ASIA_HR_USER_SERVICE
com.jd.enterprise.api.client.hr.appCode=30000
com.jd.enterprise.api.client.hr.tenantCode=CN.JD.GROUP
com.jd.enterprise.api.client.hr.safetyKey=U24301RTV8H7PLLCLS92

com.jd.health.xfyl.merchant.export.service.XfylMerchantAppointApiExportService.alias=test
com.jd.health.xfyl.merchant.export.service.XfylMerchantAppointApiExportService.token=123456


#\u670D\u52A1\u8005\u4E0A\u4F20\u5730\u7406\u4FE1\u606F
topics.reachStoreProducer.angelApplication=o2o_service_angel_location
topics.angelSettleProducer.settleApplication=xfyl_server_settle_pre
com.jd.fce.orb.service.OrbVirtualOrderCancelService.alias=orbvorefund-yfb
com.jd.fce.orb.service.OrbVirtualOrderCancelService.token=FCE_ORB_STATE
com.jd.fce.orb.service.OrbOrderCancelReasonService.alias=fce-orb-state-rc-ht
#??
producer.healthcare.finish.settle.result.topic=healthcare_finish_settle_topic_pre

#\u00E4\u00BA\u0092\u00E5\u008C\u00BB\u00E6\u008A\u00A4\u00E5\u00A3\u00AB\u00E4\u00BF\u00A1\u00E6\u0081\u00AF
com.jd.nethp.doctor.side.front.export.doctorworkstatus.service.jsf.DoctorWorkStatusExportService.alias=jdhyf_nurse
com.jd.nethp.doctor.side.front.export.doctorworkstatus.service.jsf.DoctorWorkStatusExportService.token=123456

com.jd.nethp.base.manage.export.sku.command.DoctorServiceExportCmdService.alias=jdhyf_nurse
com.jd.nethp.base.manage.export.sku.command.DoctorServiceExportCmdService.token=123456

com.jdh.nethp.doctor.entry.audit.client.DoctorInfoEntryExportClient.alias=jdhyf_nurse
com.jdh.nethp.doctor.entry.audit.client.DoctorInfoEntryExportClient.token=123456

#??
jdh.settle.SyncOrderClient.alias=jdhyftest_100
jdh.settle.SyncOrderClient.token=net921

#\u5EF6\u65F6\u961F\u5217
com.jd.health.delayqueue.export.service.DelayApiExportService.alias=test
com.jd.health.delayqueue.export.service.DelayApiExportService.token=test

## ????
com.jd.pop.patient.client.service.patient.PatientInfoCommandExportService.alias=jdhyf
com.jd.pop.patient.client.service.patient.PatientInfoCommandExportService.token=123456


com.jdh.nethp.doctor.base.client.service.doctor.alias=jdhyf
com.jdh.nethp.doctor.base.client.service.doctor.token=123456


com.jd.medicine.settlement.center.export.service.app.BankCardEasyExportService.alias=jdhyf_tx
com.jd.medicine.settlement.center.export.service.app.BankCardEasyExportService.token=123456


com.jd.medicine.settlement.center.export.service.app.DoctorWithdrawExportService.alias=jdhyf_tx
com.jd.medicine.settlement.center.export.service.app.DoctorWithdrawExportService.token=123456

# \u5230\u5E97\u4EAC\u9EA6\u6D88\u606F\u53D1\u9001\u670D\u52A1
com.jd.pop.order.loc.assembledflow.soa.service.LocMessageSoaService.alias=loc-center-pre-yf03

com.jd.order.ioms.component.export.IomsModifyCBDExport.alias=modify-i18n-yfb
com.jd.order.ioms.component.export.IomsModifyCBDExport.token=0e5516ba-c99b-40b6-82a3-2d26a09e944c

#\u67E5\u8BE2\u7528\u6237\u4FE1\u606F\u522B\u540D\u914D\u7F6E
com.jd.o2o.arrive.store.api.UserApiService.alias=arrive-store-pre01

#\u95E8\u5E97\u4E2D\u53F0\u670D\u52A1
com.jd.o2o.arrive.store.api.StoreApiService.alias=arrive-store-pre01

#\u8D26\u53F7\u4FE1\u606F\u670D\u52A1
com.jd.user.sdk.export.UserInfoExportService.alias=userSdk-yfb-mid
com.jd.user.sdk.export.UserInfoExportService.source=xfyl-shop



## \u4EAC\u9EA6\u53D1\u54C1\u63A5\u53E3\u9ED8\u8BA4\u522B\u540D\u3001token
xfyl.merchant.jm.export.alias=jdhyf
xfyl.merchant.jm.token=f7c4d3d85d9952068a91b5013e6fbfe5

## pop\u53D1\u54C1\u63A5\u53E3\u9ED8\u8BA4\u522B\u540D\u3001token
xfyl.merchant.pop.export.alias=jdhyf
xfyl.merchant.pop.token=f7c4d3d85d9952068a91b5013e6fbfe5

## \u5546\u54C1\u4E2D\u53F0\u5199\u670D\u52A1
com.jd.gms.component.labrador.api.service.write.ProductWriteService.alias=jdos_yfb_jd
com.jd.gms.component.labrador.api.service.write.ProductWriteService.authToken=6fdc967f-9d5d-48a1-a597-d5c3db6f3d48
com.jd.gms.component.labrador.api.service.write.ProductWriteService.clientName=J-dos-examinationman

com.jd.vtp.client.outer.api.VtpOrderService.alias=vtp_online_pre
com.jd.vtp.client.outer.api.VtpRefundService.alias=vtp_online_pre

com.jd.health.medical.examination.export.service.SelfAppointRecordExportService.alias=test
com.jd.health.medical.examination.export.service.SelfAppointRecordExportService.token=123456

com.jd.jdorders.component.export.OrderMiddlewareCBDExport.alias=test
com.jd.jdorders.component.export.OrderMiddlewareCBDExport.token=123456

## \u62A5\u544A\u4E2D\u5FC3
com.jdh.health.record.data.jsf.service.ReportRepositoryService.alias=jdhyf_nutrition
com.jdh.health.record.data.jsf.service.ReportRepositoryService.token=123456

#\u4E0A\u4F20\u6587\u4EF6\u7528\u4E8E\u8D39\u9879\u914D\u7F6E

com.jd.coupon.soa.business.client.service.CouponGuideQueryService.alias=test
com.jd.coupon.soa.business.client.service.CouponGuideQueryService.authToken=123456
com.jd.coupon.soa.business.client.service.GuideComponentService.alias=test
com.jd.coupon.soa.business.client.service.GuideComponentService.authToken=123456
com.jd.coupon.client.FreeCouponSoaService.alias=test
com.jd.coupon.client.FreeCouponSoaService.token=123456

two_level_cache_config=false

jdh.upload.JssUploadJsfService.alias=test

com.jdh.o2o.promisego.export.service.PromisegoQueryExport.alias=jdhyf
com.jdh.o2o.promisego.export.service.PromisegoQueryExport.token=1024888

jsf.ugc.qa.alias=TEST_ANSWER_AUTH
jsf.ugc.qa.token=f12e19deadb482841c68d152e100f47c


com.jd.medicine.oplog.center.export.service.LogRecordExportService.alias=jdhyf
com.jd.medicine.oplog.center.export.service.LogRecordExportService.token=123456

com.jd.medicine.oplog.center.export.service.LogReadExportService.alias=jdhyf
com.jd.medicine.oplog.center.export.service.LogReadExportService.token=123456

com.jd.union.api.goods.GoodsService.alias=test
com.jd.union.api.goods.GoodsService.token=123456
com.jd.union.api.promotion.PromotionService.alias=test
com.jd.union.api.promotion.PromotionService.token=123456
com.jdh.o2o.promisego.export.service.UserIdentityExport.alias=test
com.jdh.o2o.promisego.export.service.UserIdentityExport.token=123456
com.jd.pop.order.sensitive.service.ISecurityNumberService.alias=test
com.jd.pop.order.sensitive.service.ISecurityNumberService.token=123456
com.jd.pop.order.sensitive.service.ISecurityNumberLifeCycleService.alias=test
com.jd.pop.order.sensitive.service.ISecurityNumberSearchService.alias=test
com.jd.medicine.b2c.app.export.quickcheck.QuickCheckExport.alias=test
com.jd.medicine.b2c.app.export.quickcheck.QuickCheckExport.token=123456

com.jd.medicine.b2c.trade.center.export.rxnewprescriptive.RxNewPrescriptiveExport.alias=test
com.jd.medicine.b2c.trade.center.export.rxnewprescriptive.RxNewPrescriptiveExport.token=123456
com.jd.nethp.rx.doctor.export.service.query.JdhRxQueryServiceExport.alias=test
com.jd.nethp.rx.doctor.export.service.query.JdhRxQueryServiceExport.token=123456


com.jdh.health.record.data.jsf.service.ReportExtendService.alias=test
com.jdh.health.record.data.jsf.service.ReportExtendService.token=123456
cn.jdl.jecap.api.order.create.CommonCreateOrderApi.alias=test
cn.jdl.jecap.api.order.create.CommonCreateOrderApi.token=test
cn.jdl.jecap.api.order.fulfillment.CommonModifyCancelOrderApi.alias=test
cn.jdl.jecap.api.order.fulfillment.CommonModifyCancelOrderApi.token=test


com.jd.gms.crs.rpc.MapRpc.alias=test
com.jd.gms.crs.rpc.MapRpc.token=123456

com.jd.risk.riskservice.v2.RiskQueryService.alias=test
com.jd.risk.riskservice.v2.RiskQueryService.useType=test

com.jd.health.xfyl.open.export.service.outer.QuickCheckThirdExportService.alias=test
com.jd.health.xfyl.open.export.service.outer.QuickCheckThirdExportService.token=123456

com.jdh.message.center.delay.sdk.ws.MessageCenterSocketService.alias=abtest
com.jdh.message.center.delay.sdk.ws.MessageCenterSocketService.token=abtest

product.label.lua.sha=123456

com.jd.newnethp.in.diag.export.im.DiagAuthCheckExport.alias=test
com.jd.newnethp.in.diag.export.im.DiagAuthCheckExport.token=test


#\u67E5\u8BE2\u75C5\u5047\u5355
com.jdh.afterdiag.medicalrecord.export.sickLeave.SickLeaveExport.alias=jdhyf_sick
com.jdh.afterdiag.medicalrecord.export.sickLeave.SickLeaveExport.token=123456
com.jd.health.ares.open.platform.export.service.HomeInspectionExportService.alias=jdhyf
com.jd.health.ares.open.platform.export.service.HomeInspectionExportService.token=123456
