<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:jsf="http://jsf.jd.com/schema/jsf"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
                            http://jsf.jd.com/schema/jsf http://jsf.jd.com/schema/jsf/jsf.xsd"
       default-autowire="byName">

    <jsf:registry id="jsfRegistry" protocol="jsfRegistry" index="${jsf.registry.index}"/>

    <!--全局唯一id-->
    <jsf:consumer id="globalServiceID" interface="com.global.service.id.GlobalServiceID" retries="2" protocol="jsf"
                  alias="${com.global.service.id.GlobalServiceID.alias}" timeout="500">
        <jsf:parameter key="token" value="${com.global.service.id.GlobalServiceID.token}" hide="true"/>
    </jsf:consumer>

    <!--全局唯一id-->
    <jsf:consumer id="globalServiceIDFailBack" interface="com.global.service.id.GlobalServiceID" retries="2" protocol="jsf"
                  alias="${com.global.service.id.GlobalServiceID.failBack.alias}" timeout="500">
        <jsf:parameter key="token" value="${com.global.service.id.GlobalServiceID.failBack.token}" hide="true"/>
    </jsf:consumer>

    <!--Loc查询码数据接口-->
    <jsf:consumer id="locOrderCodeSoaService"
                  interface="com.jd.pop.order.loc.assembledflow.soa.service.LocOrderCodeSoaService"
                  alias="${com.jd.pop.order.loc.assembledflow.soa.service.LocOrderCodeSoaService.alias}" protocol="jsf" timeout="5000">
    </jsf:consumer>

    <!--健康档案患者信息-->
    <jsf:consumer id="patientInfoManageService" interface="com.jd.pop.patient.client.service.PatientInfoManageService"
                  protocol="jsf" alias="${com.jd.pop.patient.client.service.PatientInfoManageService.alias}" timeout="3000">
        <jsf:parameter key="token" value="${com.jd.pop.patient.client.service.PatientInfoManageService.token}" hide="true" />
    </jsf:consumer>

    <!--健康档案患者信息-->
    <jsf:consumer id="patientInfoCommandExportService" interface="com.jd.pop.patient.client.service.patient.PatientInfoCommandExportService"
                  protocol="jsf" alias="${com.jd.pop.patient.client.service.patient.PatientInfoCommandExportService.alias}" timeout="3000">
        <jsf:parameter key="token" value="${com.jd.pop.patient.client.service.patient.PatientInfoCommandExportService.token}" hide="true" />
    </jsf:consumer>

    <!-- loc查询附近门店接口(根据门店组ID、经纬度查询最近门店数据) -->
    <jsf:consumer id="locStoreService" interface="com.jd.pop.wxo2o.spi.store.StoreService"
                  protocol="jsf"
                  alias="${com.jd.pop.wxo2o.spi.store.StoreService.alias}" timeout="2000" retries="2">
        <jsf:parameter key="authCode" value="${com.jd.pop.wxo2o.spi.store.StoreService.authCode}"
                       hide="true"/>
        <jsf:parameter key="accessToken"
                       value="${com.jd.pop.wxo2o.spi.store.StoreService.accessToken}" hide="true"/>
    </jsf:consumer>
    <!--发短信-->
    <jsf:consumer id="smsMessageTemplateRpcService" interface="com.jd.mobilePhoneMsg.sender.client.service.SmsMessageTemplateRpcService"
                  protocol="jsf" alias="${com.jd.mobilePhoneMsg.sender.client.service.SmsMessageTemplateRpcService.alias}" timeout="5000">
        <jsf:parameter key="token" value="${com.jd.mobilePhoneMsg.sender.client.service.SmsMessageTemplateRpcService.token}" hide="true" />
    </jsf:consumer>

    <!--获取短链接接口-->
    <jsf:consumer id="shortUrlService"
                  interface="com.jd.shorturl.api.jsf.ShortUrlService"
                  alias="${com.jd.shorturl.api.jsf.ShortUrlService.alias}"  protocol="jsf" timeout="2000" retries="2" check="false" >
    </jsf:consumer>

    <jsf:consumer id="directionService"
                  interface="com.jd.lbs.jdlbsapi.search.DirectionService"
                  protocol="jsf" alias="${com.jd.lbs.jdlbsapi.search.DirectionService.alias}" timeout="2000" retries="0">
        <jsf:parameter key="token" value="${com.jd.lbs.jdlbsapi.search.DirectionService.token}" hide="true" />
    </jsf:consumer>

    <!-- 互医订单接口（派单使用） -->
    <jsf:consumer id="submitDiagExport"
                  interface="com.jd.newnethp.trade.center.export.core.service.submit.SubmitDiagExport"
                  protocol="jsf" alias="${com.jd.newnethp.trade.center.export.core.service.submit.SubmitDiagExport.alias}" timeout="3000" retries="0">
        <jsf:parameter key="token" value="${com.jd.newnethp.trade.center.export.core.service.submit.SubmitDiagExport.token}" hide="true" />
    </jsf:consumer>

    <!-- 互医派单接口（派单使用） -->
    <jsf:consumer id="triageAssignExport"
                  interface="com.jd.newnethp.diag.export.triage.service.TriageAssignExport"
                  protocol="jsf" alias="${com.jd.newnethp.diag.export.triage.service.TriageAssignExport.alias}" timeout="3000" retries="0">
        <jsf:parameter key="token" value="${com.jd.newnethp.diag.export.triage.service.TriageAssignExport.token}" hide="true" />
    </jsf:consumer>

    <!-- 互医派单接单 -->
    <jsf:consumer id="tradeDiagStatusChangeExport"
                  interface="com.jd.newnethp.trade.center.export.core.service.basediag.TradeDiagStatusChangeExport"
                  protocol="jsf" alias="${com.jd.newnethp.trade.center.export.core.service.basediag.TradeDiagStatusChangeExport.alias}" timeout="3000" retries="0">
        <jsf:parameter key="token" value="${com.jd.newnethp.trade.center.export.core.service.basediag.TradeDiagStatusChangeExport.token}" hide="true" />
    </jsf:consumer>

    <!-- 互医派单开关接口（派单使用） -->
    <jsf:consumer id="triageInfoExport"
                  interface="com.jd.newnethp.diag.export.triage.service.TriageInfoExport"
                  protocol="jsf" alias="${com.jd.newnethp.diag.export.triage.service.TriageInfoExport.alias}" timeout="3000" retries="0">
        <jsf:parameter key="token" value="${com.jd.newnethp.diag.export.triage.service.TriageInfoExport.token}" hide="true" />
    </jsf:consumer>

    <!-- 互医派单转诊接口（重派） -->
    <jsf:consumer id="orderTransferExport"
                  interface="com.jd.newnethp.diag.export.triage.service.OrderTransferExport"
                  protocol="jsf" alias="${com.jd.newnethp.diag.export.triage.service.OrderTransferExport.alias}" timeout="3000" retries="0">
        <jsf:parameter key="token" value="${com.jd.newnethp.diag.export.triage.service.OrderTransferExport.token}" hide="true" />
    </jsf:consumer>


    <!-- 店铺基本信息接口 -->
    <jsf:consumer id="venderShopFacade" interface="com.jd.pop.vender.center.i18n.api.shop.VenderShopFacade"
                  protocol="jsf" alias="${com.jd.pop.vender.center.i18n.api.shop.VenderShopFacade.alias}" timeout="5000">
        <!-- 藏经阁生成token -->
        <jsf:parameter key="authToken" value="${com.jd.pop.vender.center.i18n.api.shop.VenderShopFacade.token}" hide="true" />
        <!-- 客户端应用英文名（需与申请中的一致） -->
        <jsf:parameter key="clientName" value="${com.jd.pop.vender.center.i18n.api.shop.VenderShopFacade.clientName}" hide="true" />
        <!--JSF分组名 -->
        <jsf:parameter key="alias" value="${com.jd.pop.vender.center.i18n.api.shop.VenderShopFacade.alias}" hide="true" />
    </jsf:consumer>

    <!--POV预约服务-->
    <jsf:consumer id="merchantAppointApiExportService"
                  interface="com.jd.health.xfyl.merchant.export.service.XfylMerchantAppointApiExportService"
                  alias="${xfyl.merchant.export.alias}"
                  protocol="jsf" timeout="8000" retries="0">
        <jsf:parameter key="token" hide="true" value="${xfyl.merchant.export.token}"/>
    </jsf:consumer>

    <!--POV预约服务-->
    <jsf:consumer id="xfylMerchantScheduleExportService"
                  interface="com.jd.health.xfyl.merchant.export.service.XfylMerchantScheduleExportService"
                  alias="${xfyl.merchant.export.alias}"
                  protocol="jsf" timeout="8000" retries="0">
        <jsf:parameter key="token" hide="true" value="${xfyl.merchant.export.token}"/>
    </jsf:consumer>

    <!--POP商家信息-->
    <jsf:consumer id="xfylProviderExportService"
                  interface="com.jd.health.xfyl.merchant.export.service.XfylProviderExportService"
                  alias="${xfyl.merchant.export.alias}"
                  protocol="jsf"  timeout="2000" retries="0">
        <jsf:parameter key="token" hide="true" value="${xfyl.merchant.export.token}"/>
    </jsf:consumer>

    <!--消费医疗商家门店信息信息-->
    <jsf:consumer id="xfylProviderStoreExportService"
                  interface="com.jd.health.xfyl.merchant.export.service.supplier.SupplierMerchantStoreExportService"
                  alias="${xfyl.merchant.export.alias}"
                  protocol="jsf"  timeout="2000" retries="0">
        <jsf:parameter key="token" hide="true" value="${xfyl.merchant.export.token}"/>
    </jsf:consumer>

    <!--商品组件化-->
    <jsf:consumer id="productRpc"
                  interface="com.jd.gms.crs.rpc.ProductRpc"
                  alias="${com.jd.gms.crs.rpc.ProductRpc.alias}"  protocol="jsf"  timeout="2000" retries="0"  >
        <!-- 藏经阁生成token -->
        <jsf:parameter key="authToken" value="${com.jd.gms.crs.rpc.ProductRpc.authToken}" hide="true"/>
        <!-- 客户端应用英文名（需与申请中的一致） -->
        <jsf:parameter key="clientName" value="${com.jd.gms.crs.rpc.ProductRpc.clientName}" hide="true"/>
        <!--JSF分组名 -->
        <jsf:parameter key="alias" value="${com.jd.gms.crs.rpc.ProductRpc.otherAlias}" hide="true"/>
    </jsf:consumer>

    <!--虚拟号授权-->
    <jsf:consumer id="authorizationServiceFacade"
                  interface="com.jd.jdcc.privacy.number.gateway.facade.authorization.AuthorizationServiceFacade"
                  alias="${com.jd.jdcc.privacy.number.alias}"  protocol="jsf"  timeout="5000" retries="0"  >
    </jsf:consumer>

    <!--虚拟号绑定接口-->
    <jsf:consumer id="bindNumberServiceFacade"
                  interface="com.jd.jdcc.privacy.number.gateway.facade.business.BindNumberServiceFacade"
                  alias="${com.jd.jdcc.privacy.number.alias}"  protocol="jsf"  timeout="5000" retries="0"  >
    </jsf:consumer>

    <!--获取订单金额明细-->
    <jsf:consumer id="queryocs"
                  alias="${com.jd.ofc.ocs.webservice.jsf.OrderCalculationQueryServiceJsf.alias}"
                  interface="com.jd.ofc.ocs.webservice.jsf.OrderCalculationQueryServiceJsf" protocol="jsf" timeout="3000">
    </jsf:consumer>

    <!-- vender账号查询服务 -->
    <jsf:consumer id="accountBelongFacade"
                  alias="${com.jd.seller.baccount.cache.api.belong.AccountBelongFacade.alias}"
                  interface="com.jd.seller.baccount.cache.api.belong.AccountBelongFacade" protocol="jsf" timeout="3000">
    </jsf:consumer>

    <!-- 用户行为 -->
    <jsf:consumer id="userActionExportService" interface="com.jd.trade2.core.export.service.UserActionExportService"
                  protocol="jsf" alias="${com.jd.trade2.core.export.service.UserActionExportService.alias}" timeout="5000" serialization="hessian">
        <!-- 藏经阁生成token -->
        <jsf:parameter key="authToken" value="${com.jd.trade2.core.export.service.UserActionExportService.token}" hide="true" />
        <!-- 客户端应用英文名（需与申请中的一致） -->
        <jsf:parameter key="clientName" value="${com.jd.trade2.core.export.service.UserActionExportService.clientName}" hide="true" />
        <!--JSF分组名 -->
        <jsf:parameter key="alias" value="${com.jd.trade2.core.export.service.UserActionExportService.alias}" hide="true" />
    </jsf:consumer>

    <!-- 提交订单 -->
    <jsf:consumer id="submitOrderExportService" interface="com.jd.trade2.core.export.service.SubmitOrderExportService"
                  protocol="jsf" alias="${com.jd.trade2.core.export.service.SubmitOrderExportService.alias}" timeout="5000" serialization="hessian">
        <!-- 藏经阁生成token -->
        <jsf:parameter key="authToken" value="${com.jd.trade2.core.export.service.SubmitOrderExportService.token}" hide="true" />
        <!-- 客户端应用英文名（需与申请中的一致） -->
        <jsf:parameter key="clientName" value="${com.jd.trade2.core.export.service.SubmitOrderExportService.clientName}" hide="true" />
        <!--JSF分组名 -->
        <jsf:parameter key="alias" value="${com.jd.trade2.core.export.service.SubmitOrderExportService.alias}" hide="true" />
    </jsf:consumer>

    <!-- 商家店铺 https://cf.jd.com/pages/viewpage.action?pageId=43686480 -->
    <jsf:consumer id="shopSafService"
                  interface="com.jd.pop.vender.center.service.shop.ShopSafService"
                  alias="${com.jd.pop.vender.center.service.shop.ShopSafService.alias}"
                  protocol="jsf" timeout="1000">
        <jsf:parameter key="signToken" value="${com.jd.pop.vender.center.service.shop.ShopSafService.token}" hide="true"/>
    </jsf:consumer>


    <jsf:consumer id="productReadService" interface="com.jd.gms.component.labrador.api.service.read.ProductReadService"
                  alias="${com.jd.gms.component.labrador.api.service.read.ProductReadService.alias}"
                  protocol="jsf">
        <jsf:parameter key="authToken"
                       value="${com.jd.gms.component.labrador.api.service.read.ProductReadService.authToken}"
                       hide="true"/>
        <jsf:parameter key="clientName"
                       value="${com.jd.gms.component.labrador.api.service.read.ProductReadService.clientName}"
                       hide="true"/>
    </jsf:consumer>

    <jsf:consumer id="skuInfoExportService" interface="com.jd.health.medical.examination.export.service.SkuInfoExportService"
                  alias="${examination.man.export.alias}"
                  protocol="jsf" timeout="10000" serialization="hessian">
        <jsf:parameter key="token" value="${examination.man.export.token}" hide="true"/>
    </jsf:consumer>

    <!-- 查询用户收货地址接口 -->
    <jsf:consumer id="addressReadExportServiceJSF" interface="com.jd.address4.core.export.service.AddressReadExportService"
                  protocol="jsf" alias="${com.jd.user.address4.AddressReadExportService.alias}"
                  timeout="3000" serialization="hessian">
        <!-- 藏经阁生成token -->
        <jsf:parameter key="authToken" value="${com.jd.user.address4.AddressReadExportService.token}" hide="true" />
        <!-- 客户端应用英文名（需与申请中的一致） -->
        <jsf:parameter key="clientName" value="${com.jd.user.address4.AddressReadExportService.clientName}" hide="true" />
        <!--JSF分组名 -->
        <jsf:parameter key="alias" value="${com.jd.user.address4.AddressReadExportService.alias}" hide="true" />
    </jsf:consumer>

    <!-- loc查询附近门店接口(根据门店组ID、经纬度查询最近门店数据) -->
    <jsf:consumer id="storeLbsProvider" interface="com.jd.boundaryless.lbs.center.spi.store.StoreLbsProvider"
                  protocol="jsf"
                  alias="${com.jd.boundaryless.lbs.center.spi.store.StoreLbsProvider.alias}" timeout="200" retries="3">
        <jsf:parameter key="authCode" value="${com.jd.boundaryless.lbs.center.spi.store.StoreLbsProvider.authCode}"
                       hide="true"/>
        <jsf:parameter key="accessToken"
                       value="${com.jd.boundaryless.lbs.center.spi.store.StoreLbsProvider.accessToken}" hide="true"/>
    </jsf:consumer>

    <jsf:consumer id="storeScoreRpcService" interface="com.jd.store.soa.StoreScoreRpcService"
                  protocol="jsf"  serialization="hessian"
                  alias="${com.jd.store.soa.StoreScoreRpcService.alias}" timeout="5000" retries="2">
    </jsf:consumer>

    <jsf:consumer id="storeTagRpcService" interface="com.jd.store.soa.StoreTagRpcService"
                  protocol="jsf"  serialization="hessian"
                  alias="${com.jd.store.soa.StoreTagRpcService.alias}" timeout="5000" retries="2">
    </jsf:consumer>

    <jsf:consumer id="guideService" interface="com.jd.trade.guide.sdk.service.GuideService"
                  protocol="jsf" alias="${com.jd.trade.guide.sdk.service.GuideService.alias}" timeout="5000">
        <!-- 藏经阁生成token -->
        <jsf:parameter key="authToken" value="${com.jd.trade.guide.sdk.service.GuideService.authToken}" hide="true" />
        <!-- 客户端应用英文名（需与申请中的一致） -->
        <jsf:parameter key="clientName" value="${com.jd.trade.guide.sdk.service.GuideService.clientName}" hide="true" />
        <!--JSF分组名 -->
        <jsf:parameter key="alias" value="${com.jd.trade.guide.sdk.service.GuideService.otherAlias}" hide="true" />
    </jsf:consumer>

    <!--经纬度查询地址-->
    <jsf:consumer id="gbAddressToJDAddressService" interface="com.jd.addresstranslation.api.address.GBAddressToJDAddressService"
                  protocol="jsf" alias="${com.jd.addresstranslation.api.address.GBAddressToJDAddressService.alias}"
                  timeout="2000" retries="1">
        <jsf:parameter key="token" value="${com.jd.addresstranslation.api.address.GBAddressToJDAddressService.token}" hide="true" />
    </jsf:consumer>

    <!--级联地址-->
    <jsf:consumer id="jDAddressDistrictService" interface="com.jd.addresstranslation.api.address.JDAddressDistrictService"
                  protocol="jsf" alias="${com.jd.addresstranslation.api.address.JDAddressDistrictService.alias}"
                  timeout="2000" retries="1">
        <jsf:parameter key="token" value="${com.jd.addresstranslation.api.address.JDAddressDistrictService.token}" hide="true" />
    </jsf:consumer>

    <!--全地址转换经纬度-->
    <jsf:consumer id="geocodingService" interface="com.jd.lbs.geocode.api.GeocodingService"
                  protocol="jsf" alias="${com.jd.lbs.geocode.api.GeocodingService.alias}"
                  timeout="2000" retries="1">
        <jsf:parameter key="token" value="${com.jd.lbs.geocode.api.GeocodingService.token}" hide="true" />
    </jsf:consumer>

    <!--领取兑换码-->
    <jsf:consumer id="couponClientService" interface="com.jdh.market.export.promo.CouponClientService"
                  alias="${jdh.market.export.jsf.alias}" protocol="jsf" timeout="2000" retries="0">
        <jsf:parameter key="token" value="${jdh.market.export.jsf.token}" hide="true"/>
    </jsf:consumer>

    <!-- sku商家门店信息 -->
    <jsf:consumer id="skuStoreExportService" interface="com.jd.health.medical.examination.export.service.SkuStoreExportService"
                  alias="${examination.man.export.alias}"
                  protocol="jsf" >
        <jsf:parameter key="token" value="${examination.man.export.token}" hide="true"/>
    </jsf:consumer>


    <!--商品价格接口-->
    <jsf:consumer id="priceInfoService" interface="com.jd.pap.priceinfo.sdk.service.PriceInfoService"
                  protocol="jsf" alias="${com.jd.pap.priceinfo.sdk.service.PriceInfoService.alias}" timeout="3000">
        <!-- 藏经阁生成token -->
        <jsf:parameter key="authToken" value="${com.jd.pap.priceinfo.sdk.service.PriceInfoService.authToken}" hide="true" />
        <jsf:parameter key="clientName" value="1021790" hide="true" />
        <!--JSF分组名 -->
        <jsf:parameter key="alias" value="${com.jd.pap.priceinfo.sdk.service.PriceInfoService.alias}" hide="true" />
    </jsf:consumer>

    <jsf:consumer id="businessQueryByVenderIdAndKeysFacade" interface="com.jd.pop.vender.center.i18n.api.business.BusinessQueryByVenderIdAndKeysFacade"
                  protocol="jsf" alias="${com.jd.pop.vender.center.i18n.api.business.BusinessQueryByVenderIdAndKeysFacade.alias}" timeout="5000">
        <!-- 藏经阁生成token -->
        <jsf:parameter key="authToken" value="${com.jd.pop.vender.center.i18n.api.business.BusinessQueryByVenderIdAndKeysFacade.token}" hide="true" />
        <!-- 客户端应用英文名（需与申请中的一致） -->
        <jsf:parameter key="clientName" value="J-dos-jdh-o2o-service" hide="true" />
        <!--JSF分组名 -->
        <jsf:parameter key="alias" value="${com.jd.pop.vender.center.i18n.api.business.BusinessQueryByVenderIdAndKeysFacade.alias}" hide="true" />
    </jsf:consumer>

    <!--京东健康收银台-->
    <jsf:consumer id="jdhPayJsfService" interface="com.jdh.trade.settle.biz.sdk.service.JdhPayJsfService"
                  alias="${com.jdh.trade.settle.biz.sdk.service.JdhPayJsfService.alias}"
                  protocol="jsf" >
        <jsf:parameter key="token" value="${com.jdh.trade.settle.biz.sdk.service.JdhPayJsfService.token}" hide="true"/>
    </jsf:consumer>

    <!--数科收银台-->
    <jsf:consumer id="paymentAccResource"  interface="com.jd.paytrade.front.export.PaymentAccResource"
                  alias="${com.jd.paytrade.front.export.PaymentAccResource.alias}" protocol="jsf"
                  timeout="3000" retries="1"  check="false" >
    </jsf:consumer>

    <!-- 收银台JSF接口 -->
    <jsf:consumer id="platPayChannelService" interface="com.jd.pay.platform.api.provider.channel.PlatPayChannelService"
                  protocol="jsf" alias="${com.jd.pay.platform.api.provider.channel.PlatPayChannelService.alias.prefix}${server.room.name}" serialization="hessian" retries="1" timeout="250">
        <!-- 藏经阁生成token -->
        <jsf:parameter key="authToken" value="${com.jd.pay.platform.api.provider.channel.PlatPayChannelService.authToken}" hide="true" />
        <!-- 客户端应用英文名（需与申请中的一致） -->
        <jsf:parameter key="clientName" value="${com.jd.pay.platform.api.provider.channel.PlatPayChannelService.clientName}" hide="true" />
        <!--JSF分组名 -->
        <jsf:parameter key="alias" value="${com.jd.pay.platform.api.provider.channel.PlatPayChannelService.alias.prefix}${server.room.name}" hide="true" />
    </jsf:consumer>


    <!-- 健康内容接口 -->
    <jsf:consumer id="contentExportService" interface="com.jd.cmp.soa.ugc.service.content.ContentExportService"
                  protocol="jsf" alias="${com.jd.cmp.soa.ugc.service.content.ContentExportService.alias}" serialization="hessian" retries="1" timeout="250">
        <!-- 藏经阁生成token -->
        <jsf:parameter key="authToken" value="${com.jd.cmp.soa.ugc.service.content.ContentExportService.authToken}" hide="true" />
        <!-- 客户端应用英文名（需与申请中的一致） -->
        <jsf:parameter key="clientName" value="${com.jd.cmp.soa.ugc.service.content.ContentExportService.clientName}" hide="true" />
        <!--JSF分组名 -->
        <jsf:parameter key="alias" value="${com.jd.cmp.soa.ugc.service.content.ContentExportService.alias}" hide="true" />
    </jsf:consumer>

    <!-- 互医消息通知：https://joyspace.jd.com/pages/PCpRaPz7OXeZwa7r6Vzf -->
    <jsf:consumer id="msgChannelExport"
                  interface="com.jd.newnethp.diag.message.center.export.MsgChannelExport"
                  alias="${com.jd.newnethp.diag.message.center.export.MsgChannelExport.alias}"
                  protocol="jsf" timeout="3000">
        <jsf:parameter key="token" value="${com.jd.newnethp.diag.message.center.export.MsgChannelExport.token}" hide="true"/>
    </jsf:consumer>

    <!-- 互医医生端消息配置：https://joyspace.jd.com/pages/PCpRaPz7OXeZwa7r6Vzf -->
    <jsf:consumer id="msgSendRuleExport"
                  interface="com.jd.newnethp.diag.message.center.export.MsgSendRuleExport"
                  alias="${com.jd.newnethp.diag.message.center.export.MsgSendRuleExport.alias}"
                  protocol="jsf" timeout="3000">
        <jsf:parameter key="token" value="${com.jd.newnethp.diag.message.center.export.MsgSendRuleExport.token}" hide="true"/>
    </jsf:consumer>


    <!--商品大字段（商品详情、视频、中台规格参数）-->
    <jsf:consumer id="assemblyRpc" interface="com.jd.gms.crs.rpc.AssemblyRpc"
                  protocol="jsf" alias="${com.jd.gms.crs.rpc.AssemblyRpc.alias}" timeout="5000">
        <!-- 藏经阁生成token -->
        <jsf:parameter key="authToken" value="aef90275-b098-4f43-911f-7be8b8155df6" hide="true" />
        <!-- 客户端应用英文名（需与申请中的一致） -->
        <jsf:parameter key="clientName" value="J-dos-physicalexamination" hide="true" />
        <!--JSF分组名 -->
        <!-- 区分机房调用,配置在JDOS common.properties中 -->
        <jsf:parameter key="alias" value="${com.jd.gms.crs.rpc.AssemblyRpc.alias}" hide="true" />
    </jsf:consumer>

    <jsf:consumer id="strategyMetadataService" interface="com.jd.limitbuy.service.StrategyMetadataService"
                  alias="${com.jd.limitbuy.service.StrategyMetadataService.alias}">
        <jsf:parameter key="token" value="${com.jd.limitbuy.service.StrategyMetadataService.token}" hide="false"/>
    </jsf:consumer>


    <jsf:consumer id="addressInfoExport" interface="com.jdh.b2c.export.api.address.AddressInfoExport"
                  alias="${com.jdh.b2c.export.api.address.AddressInfoExport.alias}"
                  protocol="jsf" >
        <jsf:parameter key="token" value="${com.jdh.b2c.export.api.address.AddressInfoExport.token}" hide="true"/>
    </jsf:consumer>


    <!-- 装吧 获取商品移动端样式接口 -->
    <jsf:consumer id="zbWareDetailService" interface="com.jd.sdd.mkt.sdk.component.ZbWareDetailService"
                  protocol="jsf" alias="${com.sdd.mkt.sdk.component.ZbWareDetailService.alias}" timeout="3000">
        <!-- 藏经阁生成token -->
        <jsf:parameter key="authToken" value="${com.sdd.mkt.sdk.component.ZbWareDetailService.token}" hide="true" />
        <!-- 客户端应用英文名（需与申请中的一致） -->
        <jsf:parameter key="clientName" value="${com.sdd.mkt.sdk.component.ZbWareDetailService.clientName}" hide="true" />
        <!--JSF分组名 -->
        <jsf:parameter key="alias" value="${com.sdd.mkt.sdk.component.ZbWareDetailService.alias}" hide="true" />
    </jsf:consumer>

    <!--评价SDK国际化-->
    <jsf:consumer id="commentService" interface="com.jd.ugc.comment.api.CommentService"
                  protocol="jsf" alias="${com.jd.ugc.comment.api.CommentService.alias}" timeout="5000">
        <!-- 藏经阁生成token -->
        <jsf:parameter key="authToken" value="c296f204-2795-42c9-9267-5fd68fe78e78" hide="true" />
        <!-- 客户端应用英文名（需与申请中的一致） -->
        <jsf:parameter key="clientName" value="1021790" hide="true" />
        <!--JSF分组名 -->
        <jsf:parameter key="alias" value="${com.jd.ugc.comment.api.CommentService.alias}" hide="true" />
    </jsf:consumer>

    <jsf:consumer id="customPresortService" interface="com.jd.lbs.geofencing.api.customfence.CustomPresortService"
                  alias="${com.jd.lbs.geofencing.api.customfence.CustomPresortService.alias}"
                  protocol="jsf" >
        <!--        <jsf:parameter key="token" value="${jdh.settle.SyncOrderClient.token}" hide="true"/>-->
    </jsf:consumer>

    <jsf:consumer id="queryOrderInfoService" interface="com.jd.order.sdk.export.QueryOrderInfoService"
                  protocol="jsf" alias="${com.jd.order.sdk.export.QueryOrderInfoService.alias}" timeout="5000">
        <!-- 藏经阁生成token -->
        <jsf:parameter key="authToken" value="${com.jd.order.sdk.export.QueryOrderInfoService.token}" hide="true" />
        <!-- 客户端应用英文名（需与申请中的一致） -->
        <jsf:parameter key="clientName" value="J-dos-jdh-o2o-service" hide="true" />
        <!--JSF分组名 -->
        <jsf:parameter key="alias" value="${com.jd.order.sdk.export.QueryOrderInfoService.alias}" hide="true" />
    </jsf:consumer>

    <!--  uim  -->
    <jsf:consumer id="uim2MenuFacade" interface="com.jd.uim2.facade.jsf.Uim2MenuFacade"
                  protocol="jsf" alias="${com.jd.uim2.facade.jsf.alias}" timeout="5000">
    </jsf:consumer>

    <!--  uim  -->
    <jsf:consumer id="uim2DimPermissionFacade" interface="com.jd.uim2.facade.jsf.Uim2DimPermissionFacade"
                  protocol="jsf" alias="${com.jd.uim2.facade.jsf.alias}" timeout="5000">
    </jsf:consumer>

    <!--  uim  -->
    <jsf:consumer id="uim2RoleFacade" interface="com.jd.uim2.facade.jsf.Uim2RoleFacade"
                  protocol="jsf" alias="${com.jd.uim2.facade.jsf.alias}" timeout="5000">
    </jsf:consumer>

    <jsf:consumer id="hrUserService" interface="com.jd.enterprise.api.client.HrUserService" protocol="jsf"
                  alias="${com.jd.enterprise.api.client.hr.alias}" timeout="2000" retries="0">
    </jsf:consumer>

    <!--取消预约支付接口-->
    <jsf:consumer id="orbService"
                  interface="com.jd.fce.orb.service.OrbOomCancelOrderService"
                  alias="${com.jd.fce.orb.service.OrbOomCancelOrderService.alias}" protocol="jsf" serialization="hessian" >
        <jsf:parameter key="token" hide="true" value="FCE_ORB_STATE"/>
    </jsf:consumer>

<!--    &lt;!&ndash;商家端预约接口&ndash;&gt;-->
<!--    <jsf:consumer id="xfylMerchantAppointApiExportService"-->
<!--                  interface="com.jd.health.xfyl.merchant.export.service.XfylMerchantAppointApiExportService"-->
<!--                  alias="${com.jd.health.xfyl.merchant.export.service.XfylMerchantAppointApiExportService.alias}" protocol="jsf" serialization="hessian"-->
<!--                  timeout="8000"-->
<!--    >-->
<!--        <jsf:parameter key="token" hide="true" value="${com.jd.health.xfyl.merchant.export.service.XfylMerchantAppointApiExportService.token}"/>-->
<!--    </jsf:consumer>-->

    <!-- 查询检验单详情接口   -->
    <jsf:consumer id="inspectionQueryExportConsumer"
                  interface="com.jd.newnethp.cps.center.export.service.inspection.InspectionQueryExport"
                  alias="${com.jd.newnethp.cps.center.export.service.inspection.InspectionQueryExport.alias}" protocol="jsf" serialization="hessian" >
        <jsf:parameter key="token" hide="true" value="${com.jd.newnethp.cps.center.export.service.inspection.InspectionQueryExport.token}"/>
    </jsf:consumer>

    <!-- 虚拟订单取消接口 -->
    <jsf:consumer id="orbVirtualOrderCancelService" interface="com.jd.fce.orb.service.OrbVirtualOrderCancelService"
                  alias="${com.jd.fce.orb.service.OrbVirtualOrderCancelService.alias}" protocol="jsf" serialization="hessian" >
        <jsf:parameter key="token" hide="true" value="${com.jd.fce.orb.service.OrbVirtualOrderCancelService.token}"/>
    </jsf:consumer>

    <!--取消预约原因接口-->
    <jsf:consumer id="orbOrderCancelReasonService"
                  interface="com.jd.fce.orb.service.OrbOrderCancelReasonService"
                  alias="${com.jd.fce.orb.service.OrbOrderCancelReasonService.alias}" protocol="jsf" serialization="hessian" >
    </jsf:consumer>

    <!--天算  -->
    <jsf:consumer id="syncOrderClient" interface="com.jd.jdh.net.domain.client.service.order.SyncOrderClient"
                  alias="${jdh.settle.SyncOrderClient.alias}"
                  protocol="jsf" >
        <jsf:parameter key="token" value="${jdh.settle.SyncOrderClient.token}" hide="true"/>
    </jsf:consumer>

    <!--更新互医护士主数据-->
    <jsf:consumer id="doctorInfoEntryExportClient"
                  interface="com.jdh.nethp.doctor.entry.audit.client.DoctorInfoEntryExportClient"
                  alias="${com.jdh.nethp.doctor.entry.audit.client.DoctorInfoEntryExportClient.alias}" protocol="jsf" serialization="hessian" >
        <jsf:parameter key="token" hide="true" value="${com.jdh.nethp.doctor.entry.audit.client.DoctorInfoEntryExportClient.token}"/>
    </jsf:consumer>

    <!--更新互医护士技能-->
    <jsf:consumer id="doctorServiceExportCmdService"
                  interface="com.jd.nethp.base.manage.export.sku.command.DoctorServiceExportCmdService"
                  alias="${com.jd.nethp.base.manage.export.sku.command.DoctorServiceExportCmdService.alias}" protocol="jsf" serialization="hessian" >
        <jsf:parameter key="token" hide="true" value="${com.jd.nethp.base.manage.export.sku.command.DoctorServiceExportCmdService.token}"/>
    </jsf:consumer>

    <!--查询互医护士技能-->
    <jsf:consumer id="doctorInfoSearchExportClient"
                  interface="com.jdh.nethp.doctor.base.client.service.doctor.DoctorInfoSearchExportClient"
                  alias="${com.jdh.nethp.doctor.base.client.service.doctor.alias}" protocol="jsf" serialization="hessian" >
        <jsf:parameter key="token" hide="true" value="${com.jdh.nethp.doctor.base.client.service.doctor.token}"/>
    </jsf:consumer>

    <!--更新互医护士开关诊-->
    <jsf:consumer id="doctorWorkStatusExportService"
                  interface="com.jd.nethp.doctor.side.front.export.doctorworkstatus.service.jsf.DoctorWorkStatusExportService"
                  alias="${com.jd.nethp.doctor.side.front.export.doctorworkstatus.service.jsf.DoctorWorkStatusExportService.alias}" protocol="jsf" serialization="hessian" >
        <jsf:parameter key="token" hide="true" value="${com.jd.nethp.doctor.side.front.export.doctorworkstatus.service.jsf.DoctorWorkStatusExportService.token}"/>
    </jsf:consumer>

    <!-- 延时队列 -->
    <jsf:consumer id="delayApiExportService" interface="com.jd.health.delayqueue.export.service.DelayApiExportService"
                  alias="${com.jd.health.delayqueue.export.service.DelayApiExportService.alias}"
                  protocol="jsf" >
        <jsf:parameter key="token" value="${com.jd.health.delayqueue.export.service.DelayApiExportService.token}" hide="true"/>
    </jsf:consumer>

    <!-- 互医结算银行卡 -->
    <jsf:consumer id="bankCardEasyExportService" interface="com.jd.medicine.settlement.center.export.service.app.BankCardEasyExportService"
                  alias="${com.jd.medicine.settlement.center.export.service.app.BankCardEasyExportService.alias}"
                  protocol="jsf" >
        <jsf:parameter key="token" value="${com.jd.medicine.settlement.center.export.service.app.BankCardEasyExportService.token}" hide="true"/>
    </jsf:consumer>

    <!-- 互医结算提现 -->
    <jsf:consumer id="doctorWithdrawExportService" interface="com.jd.medicine.settlement.center.export.service.app.DoctorWithdrawExportService"
                  alias="${com.jd.medicine.settlement.center.export.service.app.DoctorWithdrawExportService.alias}"
                  protocol="jsf" >
        <jsf:parameter key="token" value="${com.jd.medicine.settlement.center.export.service.app.DoctorWithdrawExportService.token}" hide="true"/>
    </jsf:consumer>

    <!--改写机构号-->
    <jsf:consumer id="iomsModifyCBDExport" interface="com.jd.order.ioms.component.export.IomsModifyCBDExport"
                  protocol="jsf" alias="${com.jd.order.ioms.component.export.IomsModifyCBDExport.alias}" timeout="3000">
        <!-- 藏经阁生成token -->
        <jsf:parameter key="authToken" value="${com.jd.order.ioms.component.export.IomsModifyCBDExport.token}" hide="true" />
        <!-- 客户端应用英文名（需与申请中的一致） -->
        <jsf:parameter key="clientName" value="J-dos-jdh-o2o-service" hide="true" />
        <!--JSF分组名 -->
        <jsf:parameter key="alias" value="${com.jd.order.ioms.component.export.IomsModifyCBDExport.alias}" hide="true" />
    </jsf:consumer>

    <!-- 到店京麦消息发送服务 -->
    <jsf:consumer id="locMessageSoaService" protocol="jsf"
                  interface="com.jd.pop.order.loc.assembledflow.soa.service.LocMessageSoaService"
                  alias="${com.jd.pop.order.loc.assembledflow.soa.service.LocMessageSoaService.alias}" timeout="5000">
    </jsf:consumer>


    <!-- 视频中台服务 -->
    <jsf:consumer id="vODService" protocol="jsf" serialization="hessian"
                  interface="com.jd.vd.api.service.VODService"
                  alias="${com.jd.vd.api.service.VODService.alias}" timeout="5000">
        <!-- 视频中台appId -->
        <jsf:parameter key="appId" value="${com.jd.vd.api.service.VODService.appId}" />
        <!-- 视频中台secretKey -->
        <jsf:parameter key="secretKey" value="${com.jd.vd.api.service.VODService.secretKey}" />
    </jsf:consumer>


    <!--  POP门店管理接口-包含审核中、有效的门店数据-->
    <jsf:consumer id="storesServiceProvider" protocol="jsf"
                  interface="com.jd.boundaryless.store.center.spi.store.StoresServiceProvider"
                  alias="${com.jd.boundaryless.store.center.spi.store.StoresServiceProvider.alias}" timeout="2000">
        <jsf:parameter key="authCode" value="${com.jd.boundaryless.store.center.spi.store.StoresServiceProvider.authCode}"
                       hide="true"/>
        <jsf:parameter key="accessToken"
                       value="${com.jd.boundaryless.store.center.spi.store.StoresServiceProvider.accessToken}" hide="true"/>
    </jsf:consumer>

    <!-- 获取用户信息 -->
    <jsf:consumer id="userApiServiceJsf" protocol="jsf"
                  interface="com.jd.o2o.arrive.store.api.UserApiService"
                  alias="${com.jd.o2o.arrive.store.api.UserApiService.alias}" timeout="2000">
    </jsf:consumer>

    <!-- 获取用户信息 -->
    <jsf:consumer id="storeApiServiceJsf" protocol="jsf"
                  interface="com.jd.o2o.arrive.store.api.StoreApiService"
                  alias="${com.jd.o2o.arrive.store.api.StoreApiService.alias}" timeout="5000">
    </jsf:consumer>

    <!-- 获取用户信息 -->
    <jsf:consumer id="userInfoExportServiceJsf" protocol="jsf"
                  interface="com.jd.user.sdk.export.UserInfoExportService"
                  alias="${com.jd.user.sdk.export.UserInfoExportService.alias}" timeout="2000">
        <jsf:parameter key="source" value="${com.jd.user.sdk.export.UserInfoExportService.source}" hide="true" />
    </jsf:consumer>


    <jsf:consumer id="xfylManApprovalRecordExportService" interface="com.jd.health.medical.examination.export.service.supplier.XfylManApprovalRecordExportService"
                  alias="${examination.man.export.alias}"
                  protocol="jsf" timeout="10000" serialization="hessian">
        <jsf:parameter key="token" value="${examination.man.export.token}" hide="true"/>
    </jsf:consumer>

    <!--护士保险-->
    <jsf:consumer id="nurseVisitInsuranceResource"
                  interface="com.jdd.baoxian.b.insurance.core.trade.export.resource.NurseVisitInsuranceResource"
                  protocol="jsf" alias="${com.jdd.baoxian.b.insurance.core.trade.export.resource.NurseVisitInsuranceResource.alias}" retries="2"/>

    <!-- 天网敏感词接口 -->
    <jsf:consumer id="sensitiveJsfService" interface="com.jd.sensitiveword.service.SensitiveWordJsfService"
                  alias="${com.jd.sensitiveword.service.SensitiveWordJsfService.alias}${server.room.name}"
                  protocol="jsf" timeout="1000"/>

    <!-- 商品中台商品写服务 -->
    <jsf:consumer id="productWriteService"
                  interface="com.jd.gms.component.labrador.api.service.write.ProductWriteService"
                  alias="${com.jd.gms.component.labrador.api.service.write.ProductWriteService.alias}"
                  protocol="jsf">
        <jsf:parameter key="authToken"
                       value="${com.jd.gms.component.labrador.api.service.write.ProductWriteService.authToken}"
                       hide="true"/>
        <jsf:parameter key="clientName"
                       value="${com.jd.gms.component.labrador.api.service.write.ProductWriteService.clientName}"
                       hide="true"/>
    </jsf:consumer>
        <!--上传文件-->
    <jsf:consumer id="jssUploadJsfService"
                  interface="com.jdh.laputa.base.client.jsf.JssUploadJsfService" protocol="jsf"
                  alias="${jdh.upload.JssUploadJsfService.alias}" retries="0" timeout="3000">
    </jsf:consumer>


    <!-- VTP -->
    <jsf:consumer id="vtpOrderService" interface="com.jd.vtp.client.outer.api.VtpOrderService"
                  alias="${com.jd.vtp.client.outer.api.VtpOrderService.alias}"
                  protocol="jsf" timeout="3000"/>
    <jsf:consumer id="vtpRefundService" interface="com.jd.vtp.client.outer.api.VtpRefundService"
                  alias="${com.jd.vtp.client.outer.api.VtpRefundService.alias}"
                  protocol="jsf" timeout="3000"/>

    <!-- 体检预约信息 -->
    <jsf:consumer id="selfAppointRecordExportService" interface="com.jd.health.medical.examination.export.service.SelfAppointRecordExportService"
                  alias="${com.jd.health.medical.examination.export.service.SelfAppointRecordExportService.alias}"
                  protocol="jsf" >
        <jsf:parameter key="token" value="${com.jd.health.medical.examination.export.service.SelfAppointRecordExportService.token}" hide="true"/>
    </jsf:consumer>

    <jsf:consumer id="orderMiddlewareCBDExport" interface="com.jd.jdorders.component.export.OrderMiddlewareCBDExport"
                  protocol="jsf" alias="${com.jd.jdorders.component.export.OrderMiddlewareCBDExport.alias}" timeout="2000">
        <!-- 藏经阁生成token -->
        <jsf:parameter key="authToken" value="${com.jd.jdorders.component.export.OrderMiddlewareCBDExport.token}" hide="true" />
        <!-- 客户端应用英文名（需与申请中的一致） -->
        <jsf:parameter key="clientName" value="J-dos-physicalexamination" hide="true" />
        <!--JSF分组名 -->
        <jsf:parameter key="alias" value="${com.jd.jdorders.component.export.OrderMiddlewareCBDExport.alias}" hide="true" />
    </jsf:consumer>

    <!--报告中心-->
    <jsf:consumer id="reportRepositoryService" interface="com.jdh.health.record.data.jsf.service.ReportRepositoryService"
                  alias="${com.jdh.health.record.data.jsf.service.ReportRepositoryService.alias}"
                  protocol="jsf" timeout="3000" >
        <jsf:parameter key="token" value="${com.jdh.health.record.data.jsf.service.ReportRepositoryService.token}" hide="true"/>
    </jsf:consumer>


    <!--券购-可领券查询-->
    <jsf:consumer id="couponGuideQueryService" interface="com.jd.coupon.soa.business.client.service.CouponGuideQueryService"
                  protocol="jsf" alias="${com.jd.coupon.soa.business.client.service.CouponGuideQueryService.alias}" timeout="5000">
        <!-- 藏经阁生成token -->
        <jsf:parameter key="authToken" value="${com.jd.coupon.soa.business.client.service.CouponGuideQueryService.authToken}" hide="true" />
        <!-- 客户端应用英文名（需与申请中的一致） -->
        <jsf:parameter key="clientName" value="J-dos-jdh-o2o-service" hide="true" />
        <!--JSF分组名 -->
        <jsf:parameter key="alias" value="${com.jd.coupon.soa.business.client.service.CouponGuideQueryService.alias}" hide="true" />
    </jsf:consumer>

    <!--券购-可用券查询-->
    <jsf:consumer id="guideComponentService" interface="com.jd.coupon.soa.business.client.service.GuideComponentService"
                  protocol="jsf" alias="${com.jd.coupon.soa.business.client.service.GuideComponentService.alias}" timeout="5000">
        <!-- 藏经阁生成token -->
        <jsf:parameter key="authToken" value="${com.jd.coupon.soa.business.client.service.GuideComponentService.authToken}" hide="true" />
        <!-- 客户端应用英文名（需与申请中的一致） -->
        <jsf:parameter key="clientName" value="J-dos-jdh-o2o-service" hide="true" />
        <!--JSF分组名 -->
        <jsf:parameter key="alias" value="${com.jd.coupon.soa.business.client.service.GuideComponentService.alias}" hide="true" />
    </jsf:consumer>

    <!--优惠券-免费领券-->
    <jsf:consumer id="freeCouponSoaService" interface="com.jd.coupon.client.FreeCouponSoaService"
                  alias="${com.jd.coupon.client.FreeCouponSoaService.alias}"
                  protocol="jsf" >
        <jsf:parameter key="token" value="${com.jd.coupon.client.FreeCouponSoaService.token}" hide="true"/>
    </jsf:consumer>

    <jsf:consumer id="goodsService" interface="com.jd.union.api.goods.GoodsService"
                  alias="${com.jd.union.api.goods.GoodsService.alias}"
                  protocol="jsf" >
        <jsf:parameter key="token" value="${com.jd.union.api.goods.GoodsService.token}" hide="true"/>
    </jsf:consumer>

    <jsf:consumer id="promotionService" interface="com.jd.union.api.promotion.PromotionService"
                  alias="${com.jd.union.api.promotion.PromotionService.alias}"
                  protocol="jsf" >
        <jsf:parameter key="token" value="${com.jd.union.api.promotion.PromotionService.token}" hide="true"/>
    </jsf:consumer>


    <!-- promisego -->
    <jsf:consumer id="promisegoQueryExport" interface="com.jdh.o2o.promisego.export.service.PromisegoQueryExport"
                  alias="${com.jdh.o2o.promisego.export.service.PromisegoQueryExport.alias}"
                  timeout="500" retries="2"
                  protocol="jsf" >
        <jsf:parameter key="token" value="${com.jdh.o2o.promisego.export.service.PromisegoQueryExport.token}" hide="true"/>
    </jsf:consumer>

    <!-- 问答组件国际化接口 -->
    <jsf:consumer id="qAOpenService" interface="com.jd.ugc.qa.soa.QAOpenService" alias="${jsf.ugc.qa.alias}" protocol="jsf" timeout="2000">
        <jsf:parameter key="token" value="${jsf.ugc.qa.token}" hide="true" />
    </jsf:consumer>
    <!-- 快检一键购药 -->
    <jsf:consumer id="quickCheckExport" interface="com.jd.medicine.b2c.app.outexport.QuickCheckExport"
                  alias="${com.jd.medicine.b2c.app.export.quickcheck.QuickCheckExport.alias}"
                  protocol="jsf" >
        <jsf:parameter key="token" value="${com.jd.medicine.b2c.app.export.quickcheck.QuickCheckExport.token}" hide="true"/>
    </jsf:consumer>

    <!-- 大药房处方单加车 -->
    <jsf:consumer id="rxNewPrescriptiveExport" interface="com.jd.medicine.b2c.trade.center.export.rxnewprescriptive.RxNewPrescriptiveExport"
                  alias="${com.jd.medicine.b2c.trade.center.export.rxnewprescriptive.RxNewPrescriptiveExport.alias}"
                  protocol="jsf" >
        <jsf:parameter key="token" value="${com.jd.medicine.b2c.trade.center.export.rxnewprescriptive.RxNewPrescriptiveExport.token}" hide="true"/>
    </jsf:consumer>

    <!-- 处方单查询 -->
    <jsf:consumer id="jdhRxQueryServiceExport" interface="com.jd.nethp.rx.doctor.export.service.query.JdhRxQueryServiceExport"
                  alias="${com.jd.nethp.rx.doctor.export.service.query.JdhRxQueryServiceExport.alias}"
                  protocol="jsf" >
        <jsf:parameter key="token" value="${com.jd.nethp.rx.doctor.export.service.query.JdhRxQueryServiceExport.token}" hide="true"/>
    </jsf:consumer>


    <!--智能文本解析接口 https://lbsapi.jd.com/iframe.html?childURL=docid=2-75&childNav=1-17-->
    <jsf:consumer id="textParsingService" interface="com.jd.lbs.jdlbsapi.c2c.TextParsingService"
                  protocol="jsf" alias="${com.jd.lbs.jdlbsapi.c2c.TextParsingService.alias}"
                  timeout="2000" retries="1">
        <jsf:parameter key="token" value="${com.jd.lbs.jdlbsapi.c2c.TextParsingService.token}" hide="true" />
    </jsf:consumer>
    <!-- UserIdentityExport -->
    <jsf:consumer id="userIdentityExport" interface="com.jd.newnethp.diag.review.center.export.service.UserIdentityExport"
                  alias="${com.jdh.o2o.promisego.export.service.UserIdentityExport.alias}"
                  protocol="jsf" >
        <jsf:parameter key="token" value="${com.jdh.o2o.promisego.export.service.UserIdentityExport.token}" hide="true"/>
    </jsf:consumer>

    <!--通用操作日志查询-->
    <jsf:consumer id="opLogInfoExportService"
                  interface="com.jd.medicine.oplog.center.export.service.LogReadExportService"
                  alias="${com.jd.medicine.oplog.center.export.service.LogReadExportService.alias}"
                  protocol="jsf" timeout="10000" retries="0">
        <jsf:parameter key="token" value="${com.jd.medicine.oplog.center.export.service.LogReadExportService.token}"
                       hide="true"/>
    </jsf:consumer>

    <!--通用操作日志写入-->
    <jsf:consumer id="logRecordExportService" interface="com.jd.medicine.oplog.center.export.service.LogRecordExportService"
                  alias="${com.jd.medicine.oplog.center.export.service.LogRecordExportService.alias}"
                  protocol="jsf" timeout="10000" retries="0">
        <jsf:parameter key="token" value="${com.jd.medicine.oplog.center.export.service.LogRecordExportService.token}" hide="true"/>
    </jsf:consumer>

    <!--虚拟号-绑定-->
    <jsf:consumer id="iSecurityNumberService" interface="com.jd.pop.order.sensitive.service.ISecurityNumberService"
                  protocol="jsf" alias="${com.jd.pop.order.sensitive.service.ISecurityNumberService.alias}" timeout="5000">
        <!-- 藏经阁生成token -->
        <jsf:parameter key="authToken" value="${com.jd.pop.order.sensitive.service.ISecurityNumberService.token}" hide="true" />
        <!-- 客户端应用英文名（需与申请中的一致） -->
        <jsf:parameter key="clientName" value="J-dos-jdh-o2o-service" hide="true" />
        <!--JSF分组名 -->
        <jsf:parameter key="alias" value="${com.jd.pop.order.sensitive.service.ISecurityNumberService.alias}" hide="true" />
    </jsf:consumer>

    <!--虚拟号-解绑-->
    <jsf:consumer id="iSecurityNumberLifeCycleService" interface="com.jd.pop.order.sensitive.service.ISecurityNumberLifeCycleService"
                  alias="${com.jd.pop.order.sensitive.service.ISecurityNumberLifeCycleService.alias}"
                  protocol="jsf" >
    </jsf:consumer>

    <!--虚拟号-查询当前在绑虚拟号-->
    <jsf:consumer id="iSecurityNumberSearchService" interface="com.jd.pop.order.sensitive.service.ISecurityNumberSearchService"
                  alias="${com.jd.pop.order.sensitive.service.ISecurityNumberSearchService.alias}"
                  protocol="jsf" >
    </jsf:consumer>

    <!--报告中心-->
    <jsf:consumer id="reportExtendService" interface="com.jdh.health.record.data.jsf.service.ReportExtendService"
                  alias="${com.jdh.health.record.data.jsf.service.ReportExtendService.alias}"
                  protocol="jsf" timeout="3000" >
        <jsf:parameter key="token" value="${com.jdh.health.record.data.jsf.service.ReportExtendService.token}" hide="true"/>
    </jsf:consumer>

    <jsf:consumer id="quickCheckThirdExportService" interface="com.jd.health.xfyl.open.export.service.outer.QuickCheckThirdExportService"
                  alias="${com.jd.health.xfyl.open.export.service.outer.QuickCheckThirdExportService.alias}"
                  protocol="jsf" timeout="3000" >
        <jsf:parameter key="token" value="${com.jd.health.xfyl.open.export.service.outer.QuickCheckThirdExportService.token}" hide="true"/>
    </jsf:consumer>

    <!-- 长连接 -->
    <jsf:consumer id="messageCenterSocketService"
                  interface="com.jdh.message.center.delay.sdk.ws.MessageCenterSocketService"
                  protocol="jsf" alias="${com.jdh.message.center.delay.sdk.ws.MessageCenterSocketService.alias}" timeout="5000" retries="0">
        <jsf:parameter key="token" value="${com.jdh.message.center.delay.sdk.ws.MessageCenterSocketService.token}" hide="true" />
    </jsf:consumer>

    <!-- 实验室操作 -->
    <jsf:consumer id="inStoreMerchantStoreExportService" interface="com.jd.health.xfyl.open.export.service.inner.InStoreMerchantStoreExportService"
                  alias="${xfyl.merchant.export.alias}"
                  timeout="3000"
                  protocol="jsf" >
        <jsf:parameter key="token" value="${xfyl.merchant.export.token}" hide="true"/>
    </jsf:consumer>

    <!-- 实验室操作 -->
    <jsf:consumer id="inStoreThirdDataExportService" interface="com.jd.health.xfyl.open.export.service.outer.InStoreThirdDataExportService"
                  alias="${xfyl.merchant.export.outer.alias}"
                  timeout="3000"
                  protocol="jsf" >
        <jsf:parameter key="token" value="${xfyl.merchant.export.outer.token}" hide="true"/>
    </jsf:consumer>

    <jsf:consumer id="operationLogGwExport" interface="com.jdh.o2oservice.b2b.export.operationLog.OperationLogGwExport"
                  alias="${common.b2b.alias}"
                  protocol="jsf" timeout="3000" serialization="hessian">
        <jsf:parameter key="token" value="${common.b2b.token}" hide="true"/>
    </jsf:consumer>

    <jsf:consumer id="b2bEnterpriseAccountGwExport" interface="com.jdh.o2oservice.b2b.export.enterpriseaccount.B2bEnterpriseAccountGwExport"
                  alias="${common.b2b.alias}"
                  protocol="jsf" timeout="4000" serialization="hessian">
        <jsf:parameter key="token" value="${common.b2b.token}" hide="true"/>
    </jsf:consumer>

    <jsf:consumer id="b2bEnterpriseGwExport" interface="com.jdh.o2oservice.b2b.export.enterprise.B2bEnterpriseGwExport"
                  alias="${common.b2b.alias}"
                  protocol="jsf" timeout="4000" serialization="hessian">
        <jsf:parameter key="token" value="${common.b2b.token}" hide="true"/>
    </jsf:consumer>
    <!-- 医患关系 -->
    <jsf:consumer id="diagAuthCheckExport" interface="com.jd.newnethp.in.diag.export.im.DiagAuthCheckExport"
                  alias="${com.jd.newnethp.in.diag.export.im.DiagAuthCheckExport.alias}"
                  timeout="3000"
                  protocol="jsf" >
        <jsf:parameter key="token" value="${com.jd.newnethp.in.diag.export.im.DiagAuthCheckExport.token}" hide="true"/>
    </jsf:consumer>

    <!--京东物流-前置校验标准服务-->
    <jsf:consumer id="commonCreateOrderApi" interface="cn.jdl.jecap.api.order.create.CommonCreateOrderApi" alias="${cn.jdl.jecap.api.order.create.CommonCreateOrderApi.alias}" protocol="jsf" serialization="hessian">
        <jsf:parameter key="token" hide="true" value="${cn.jdl.jecap.api.order.create.CommonCreateOrderApi.token}"/>
    </jsf:consumer>

    <!--京东物流-修改物流单-->
    <jsf:consumer id="commonModifyCancelOrderApi" interface="cn.jdl.jecap.api.order.fulfillment.CommonModifyCancelOrderApi" alias="${cn.jdl.jecap.api.order.fulfillment.CommonModifyCancelOrderApi.alias}" protocol="jsf" serialization="hessian">
        <jsf:parameter key="token" hide="true" value="${cn.jdl.jecap.api.order.fulfillment.CommonModifyCancelOrderApi.token}"/>
    </jsf:consumer>

    <!-- 商品读服务 -->
    <jsf:consumer id="mapRpc" interface="com.jd.gms.crs.rpc.MapRpc"
                  protocol="jsf"  alias="${com.jd.gms.crs.rpc.MapRpc.alias}" timeout="3000">
        <!-- 藏经阁生成token -->
        <jsf:parameter key="authToken" value="${com.jd.gms.crs.rpc.MapRpc.token}" hide="true" />
        <!-- 客户端应用英文名（需与申请中的一致） -->
        <jsf:parameter key="clientName" value="J-dos-jdh-o2o-service" hide="true" />
        <!--JSF分组名 -->
        <jsf:parameter key="alias" value="${com.jd.gms.crs.rpc.MapRpc.alias}" hide="true" />
    </jsf:consumer>

    <!-- 风险识别服务 -->
    <jsf:consumer id="riskQueryService" interface="com.jd.risk.riskservice.v2.RiskQueryService"
                  protocol="jsf" alias="${com.jd.risk.riskservice.v2.RiskQueryService.alias}" timeout="500" serialization="hessian" retries="0">

    </jsf:consumer>

    <!--查询病假单-->
    <jsf:consumer id="sickLeaveExport" interface="com.jdh.afterdiag.medicalrecord.export.sickLeave.SickLeaveExport"
                  alias="${com.jdh.afterdiag.medicalrecord.export.sickLeave.SickLeaveExport.alias}"
                  protocol="jsf" >
        <jsf:parameter key="token" value="${com.jdh.afterdiag.medicalrecord.export.sickLeave.SickLeaveExport.token}" hide="true"/>
    </jsf:consumer>

    <jsf:consumer id="autoBotsService" interface="com.jd.llm.client.service.AutoBotsService" alias="${jsf.autobots.alias}" timeout="10000">
        <jsf:parameter key="token" value="${jsf.autobots.token}" hide="true"/>
    </jsf:consumer>


    <!--语音记录-->
    <jsf:consumer id="soundRecordingExport" interface="com.jd.health.xfyl.file.export.soundrecording.sound.SoundRecordingExport"
                  alias="${com.jd.health.xfyl.file.export.soundrecording.sound.SoundRecordingExport.alias}"
                  protocol="jsf" >
        <jsf:parameter key="token" value="${com.jd.health.xfyl.file.export.soundrecording.sound.SoundRecordingExport.token}" hide="true"/>
    </jsf:consumer>
</beans>