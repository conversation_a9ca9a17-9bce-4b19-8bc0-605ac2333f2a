package com.jdh.o2oservice.web.controller.admin.support;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.api.R;
import com.jd.llm.client.domain.autobots.AutoBotsResult;
import com.jdh.o2oservice.application.support.service.AutoBotsApplication;
import com.jdh.o2oservice.core.domain.support.autobos.bo.AutoBotsResultBO;
import com.jdh.o2oservice.infrastructure.rpc.autobots.AutoBotsConvert;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * 智能体controller
 * <AUTHOR>
 * @description
 * @date 2025/5/13
 */

@Slf4j
@RestController
@RequestMapping("/autobots")
public class AutoBotsController {

    /**
     * 自动化机器人应用程序实例，用于处理工作流结果。
     */
    @Autowired
    private AutoBotsApplication autoBotsApplication;

    @RequestMapping(value = "/callbackResult", method = { RequestMethod.POST })
    public R<String> callbackResult(@RequestBody AutoBotsResult result) {
        log.info("工作流结果：{}", JSONObject.toJSONString(result));
        AutoBotsResultBO resultBO = AutoBotsConvert.INSTANCE.convert(result);
        autoBotsApplication.callback(resultBO);

        return null;
    }


}
