package com.jdh.o2oservice.web.controller.admin.product;

import com.jd.common.web.LoginContext;
import com.jdh.o2oservice.annotation.OperationLog;
import com.jdh.o2oservice.application.product.QuestionExtApplication;
import com.jdh.o2oservice.application.product.service.QuestionServiceApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.util.ResponseUtil;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.enums.OpTypeEnum;
import com.jdh.o2oservice.export.product.cmd.BindCareFormCmd;
import com.jdh.o2oservice.export.product.cmd.QuestionRemoveCmd;
import com.jdh.o2oservice.export.product.cmd.QuestionSaveCmd;
import com.jdh.o2oservice.export.product.dto.CareFormDTO;
import com.jdh.o2oservice.export.product.dto.JdhQuestionDto;
import com.jdh.o2oservice.export.product.dto.QuestionDTO;
import com.jdh.o2oservice.export.product.dto.QuestionGroupDto;
import com.jdh.o2oservice.export.product.query.CareFormDetailQuery;
import com.jdh.o2oservice.export.product.query.QueryQuesByGroupCode;
import com.jdh.o2oservice.export.product.query.QuestionDetailQuery;
import com.jdh.o2oservice.export.product.query.QuestionPageQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/25
 * @description 题库
 */
@Slf4j
@RestController
@RequestMapping("/product/question")
public class QuestionController {

    @Autowired
    private QuestionServiceApplication questionServiceApplication;

    @Autowired
    private QuestionExtApplication questionExtApplication;


    /**
     * 查询题详情
     *
     * @param questionDetailQuery
     * @return
     */
    @ResponseBody
    @RequestMapping(method = RequestMethod.POST, value = "/findDetail")
    @LogAndAlarm
    @OperationLog(operationType = OpTypeEnum.QUERY, operationDesc = "查询题库详情")
    public Response<JdhQuestionDto> findDetail(@RequestBody QuestionDetailQuery questionDetailQuery) {
        questionDetailQuery.setUserName(LoginContext.getLoginContext().getPin());
        return ResponseUtil.buildSuccResponse(questionServiceApplication.findDetail(questionDetailQuery));
    }

    /**
     * 新增/修改题库
     *
     * @param questionSaveCmd
     * @return
     */
    @ResponseBody
    @RequestMapping(method = RequestMethod.POST, value = "/saveOrUpdate")
    @LogAndAlarm
    @OperationLog(operationType = OpTypeEnum.UPDATE, operationDesc = "新增/修改题库")
    public Response<Boolean> saveOrUpdate(@RequestBody QuestionSaveCmd questionSaveCmd) {
        questionSaveCmd.setUserName(LoginContext.getLoginContext().getPin());
        return ResponseUtil.buildSuccResponse(questionServiceApplication.saveOrUpdate(questionSaveCmd));
    }

    /**
     * 新增/修改题库
     *
     * @param questionPageQuery
     * @return
     */
    @ResponseBody
    @RequestMapping(method = RequestMethod.POST, value = "/findPage")
    @LogAndAlarm
    @OperationLog(operationType = OpTypeEnum.QUERY, operationDesc = "分页查询题库")
    public Response<PageDto<QuestionDTO>> findPage(@RequestBody QuestionPageQuery questionPageQuery) {
        questionPageQuery.setUserName(LoginContext.getLoginContext().getPin());
        return ResponseUtil.buildSuccResponse(questionServiceApplication.findPage(questionPageQuery));
    }

    /**
     * 删除
     * @param questionRemoveCmd
     * @return
     */
    @ResponseBody
    @RequestMapping(method = RequestMethod.POST, value = "/remove")
    @LogAndAlarm
    @OperationLog(operationType = OpTypeEnum.QUERY, operationDesc = "删除题库")
    public Response<Boolean> remove(@RequestBody QuestionRemoveCmd questionRemoveCmd) {
        questionRemoveCmd.setUserName(LoginContext.getLoginContext().getPin());
        return ResponseUtil.buildSuccResponse(questionServiceApplication.remove(questionRemoveCmd));
    }


    /**
     * 配置护理单
     * @param bindCareFormCmd
     * @return
     */
    @ResponseBody
    @RequestMapping(method = RequestMethod.POST, value = "/bindCareForm")
    @LogAndAlarm
    @OperationLog(operationType = OpTypeEnum.ADD, operationDesc = "保存护理单配置")
    public Response<Boolean> bindCareForm(@RequestBody BindCareFormCmd bindCareFormCmd) {
        bindCareFormCmd.setUserName(LoginContext.getLoginContext().getPin());
        return ResponseUtil.buildSuccResponse(questionServiceApplication.bindCareForm(bindCareFormCmd));
    }

    /**
     * 查询护理单
     * @param careFormDetailQuery
     * @return
     */
    @ResponseBody
    @RequestMapping(method = RequestMethod.POST, value = "/careFormDetail")
    @LogAndAlarm
    @OperationLog(operationType = OpTypeEnum.ADD, operationDesc = "查询护理单配置")
    public Response<CareFormDTO> careFormDetail(@RequestBody CareFormDetailQuery careFormDetailQuery) {
        careFormDetailQuery.setUserName(LoginContext.getLoginContext().getPin());
        return ResponseUtil.buildSuccResponse(questionServiceApplication.careFormDetail(careFormDetailQuery));
    }


    /**
     * 查询护理单中的题
     * @param queryQuesByGroupCode
     * @return
     */
    @ResponseBody
    @RequestMapping(method = RequestMethod.POST, value = "/queryList")
    @LogAndAlarm
    @OperationLog(operationType = OpTypeEnum.ADD, operationDesc = "查询护理单中的题")
    public Response<List<QuestionGroupDto>> queryList(@RequestBody QueryQuesByGroupCode queryQuesByGroupCode) {
        return ResponseUtil.buildSuccResponse(questionExtApplication.queryQuesByGroupCode(queryQuesByGroupCode));
    }


}
