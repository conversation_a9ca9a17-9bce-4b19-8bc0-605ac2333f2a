package com.jdh.o2oservice.web.controller.admin.settlement;

import com.jd.common.web.LoginContext;
import com.jdh.o2oservice.annotation.OperationLog;
import com.jdh.o2oservice.application.settlement.service.JdhAngelSettleConfigApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode;
import com.jdh.o2oservice.base.util.AssertUtils;
import com.jdh.o2oservice.base.util.ResponseUtil;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.core.domain.settlement.repository.query.JdhAngelSettleAreaFeeQuery;
import com.jdh.o2oservice.enums.OpTypeEnum;
import com.jdh.o2oservice.export.settlement.cmd.AngelSettleCityConfigFileCmd;
import com.jdh.o2oservice.export.settlement.cmd.AngelSettlementConfigCmd;
import com.jdh.o2oservice.export.settlement.dto.AngelSettlementConfigDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 护士结算调账
 *
 * <AUTHOR>
 * @date 2024/8/06
 */
@Slf4j
@RestController
@RequestMapping("/angelSettle/config")
public class AngelSettlementConfigController {
    
    /**
     * jdhAngelSettleConfigApplication
     */
    @Resource
    JdhAngelSettleConfigApplication jdhAngelSettleConfigApplication;

    /**
     * 分页查询护士结算配置
     *
     * @param jdhAngelSettleAreaFeeQuery
     * @return response
     */
    @PostMapping(value = "/queryAngelSettlementConfigPage")
    @LogAndAlarm(jKey = "AngelSettlementConfigController.queryAngelSettlementConfigPage")
    @OperationLog(operationType = OpTypeEnum.QUERY, operationDesc = "护士结算配置分页信息")
    public Response<PageDto<AngelSettlementConfigDto>> queryAngelSettlementConfigPage(@RequestBody JdhAngelSettleAreaFeeQuery jdhAngelSettleAreaFeeQuery) {
        AssertUtils.nonNull(jdhAngelSettleAreaFeeQuery, BusinessErrorCode.ILLEGAL_ARG_ERROR);
        PageDto<AngelSettlementConfigDto> result = jdhAngelSettleConfigApplication.queryAngelSettleConfigPage(jdhAngelSettleAreaFeeQuery);
        return ResponseUtil.buildSuccResponse(result);
    }

    /**
     * 新建护士结算配置
     * @param angelSettlementConfigCmd
     * @return response
     */
    @PostMapping(value = "/saveAngelSettlementConfig")
    @LogAndAlarm(jKey = "AngelSettlementConfigController.saveAngelSettlementConfig")
    @OperationLog(operationType = OpTypeEnum.ADD, operationDesc = "新建护士结算配置")
    public Response<Boolean> saveAngelSettlementConfig(@RequestBody AngelSettlementConfigCmd angelSettlementConfigCmd) {
        this.checkAngelSettleConfigParam(angelSettlementConfigCmd);
        String pin = LoginContext.getLoginContext().getPin();
        angelSettlementConfigCmd.setOperator(pin);
        Boolean result = jdhAngelSettleConfigApplication.saveAngelSettleConfig(angelSettlementConfigCmd);
        return ResponseUtil.buildSuccResponse(result);
    }

    /**
     * 更新护士结算配置
     *
     * @param angelSettlementConfigCmd
     * @return response
     */
    @PostMapping(value = "/updateAngelSettlementConfig")
    @LogAndAlarm(jKey = "AngelSettlementConfigController.updateAngelSettlementConfig")
    @OperationLog(operationType = OpTypeEnum.QUERY, operationDesc = "更新护士结算配置")
    public Response<Boolean> updateAngelSettlementConfig(@RequestBody AngelSettlementConfigCmd angelSettlementConfigCmd) {
        AssertUtils.nonNull(angelSettlementConfigCmd, BusinessErrorCode.ILLEGAL_ARG_ERROR);
        AssertUtils.nonNull(angelSettlementConfigCmd.getFeeConfigId(), BusinessErrorCode.ILLEGAL_ARG_ERROR);
        String pin = LoginContext.getLoginContext().getPin();
        angelSettlementConfigCmd.setOperator(pin);
        Boolean result = jdhAngelSettleConfigApplication.updateAngelSettleConfig(angelSettlementConfigCmd);
        return ResponseUtil.buildSuccResponse(result);
    }

    /**
     *
     * @param angelSettlementConfigCmd
     */
    private void checkAngelSettleConfigParam(AngelSettlementConfigCmd angelSettlementConfigCmd) {
        AssertUtils.nonNull(angelSettlementConfigCmd, BusinessErrorCode.ILLEGAL_ARG_ERROR);
        AssertUtils.hasText(angelSettlementConfigCmd.getProvinceCode(),"省不能为空");
        AssertUtils.hasText(angelSettlementConfigCmd.getAngelType(),"护士类型不能为空");
        AssertUtils.hasText(angelSettlementConfigCmd.getOnSiteFee(), "上门费不能为空");
    }

    /**
     * 批量新建护士结算配置
     *
     * @param angelSettleCityConfigFileCmd
     * @return
     */
    @PostMapping(value = "/batchSubmitCityConfig")
    @LogAndAlarm(jKey = "AngelSettlementConfigController.batchSubmitCityConfig")
    @OperationLog(operationType = OpTypeEnum.ADD, operationDesc = "批量新建护士结算配置")
    public Response<Boolean> batchSubmitCityConfig(@RequestBody AngelSettleCityConfigFileCmd angelSettleCityConfigFileCmd) {
        AssertUtils.nonNull(angelSettleCityConfigFileCmd, BusinessErrorCode.ILLEGAL_ARG_ERROR);
        AssertUtils.nonNull(angelSettleCityConfigFileCmd.getFileId(),"请上传文件后再提交");
        String pin = LoginContext.getLoginContext().getPin();
        angelSettleCityConfigFileCmd.setApplyErp(pin);
        Boolean result = jdhAngelSettleConfigApplication.batchSaveAngelSettleConfig(angelSettleCityConfigFileCmd);
        return ResponseUtil.buildSuccResponse(result);
    }

    /**
     * 导出护士结算配置
     *
     * @param request request
     * @return response
     */
    @PostMapping(value = "/exportAngelSettlementConfig")
    @LogAndAlarm(jKey = "AngelSettlementConfigController.exportAngelSettlementConfig")
    @OperationLog(operationType = OpTypeEnum.QUERY, operationDesc = "导出护士结算配置")
    public Response<Boolean> exportAngelSettlementConfig(@RequestBody JdhAngelSettleAreaFeeQuery request) {
        AssertUtils.nonNull(request, BusinessErrorCode.ILLEGAL_ARG_ERROR);
        String pin = LoginContext.getLoginContext().getPin();
        request.setUserPin(pin);
        Boolean result = jdhAngelSettleConfigApplication.exportAngelSettleConfig(request);
        return ResponseUtil.buildSuccResponse(result);
    }

    /**
     * 删除护士结算配置
     * @param cmd
     * @return response
     */
    @PostMapping(value = "/deleteAngelSettlementConfig")
    @LogAndAlarm(jKey = "AngelSettlementConfigController.deleteAngelSettlementConfig")
    @OperationLog(operationType = OpTypeEnum.ADD, operationDesc = "删除护士结算配置")
    public Response<Boolean> deleteAngelSettlementConfig(@RequestBody AngelSettlementConfigCmd cmd) {
        AssertUtils.nonNull(cmd, BusinessErrorCode.ILLEGAL_ARG_ERROR);
        AssertUtils.isNotEmpty(cmd.getFeeConfigIdList(),"删除配置不能为空");
        String pin = LoginContext.getLoginContext().getPin();
        AngelSettlementConfigCmd angelSettlementConfigCmd = new AngelSettlementConfigCmd();
        angelSettlementConfigCmd.setOperator(pin);
        angelSettlementConfigCmd.setFeeConfigIdList(cmd.getFeeConfigIdList());
        Boolean result = jdhAngelSettleConfigApplication.deleteAngelSettlementConfig(angelSettlementConfigCmd);
        return ResponseUtil.buildSuccResponse(result);
    }

}
