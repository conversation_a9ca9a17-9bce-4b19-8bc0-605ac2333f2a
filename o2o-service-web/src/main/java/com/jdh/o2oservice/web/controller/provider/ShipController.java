package com.jdh.o2oservice.web.controller.provider;

import com.alibaba.fastjson.JSON;
import com.jdh.o2oservice.annotation.OperationLog;
import com.jdh.o2oservice.common.enums.DeliveryTypeEnum;
import com.jdh.o2oservice.core.domain.support.operationlog.context.OperationLogContext;
import com.jdh.o2oservice.enums.OpTypeEnum;
import com.jdh.o2oservice.export.angelpromise.cmd.*;
import com.jdh.o2oservice.ext.ship.reponse.UavResponse;
import com.jdh.o2oservice.vertical.enums.ShunFengShipStatusEnum;
import com.jdh.o2oservice.application.angelpromise.service.AngelWorkApplication;
import com.jdh.o2oservice.application.support.service.TemplateParseApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode;
import com.jdh.o2oservice.base.util.ShunFengUtils;
import com.jdh.o2oservice.base.util.TimeUtils;
import com.jdh.o2oservice.common.enums.AngelDetailTypeEnum;
import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.common.result.response.ShipResponse;
import com.jdh.o2oservice.core.domain.angelpromise.context.AngelShipCallBackContext;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelPromiseBizErrorCode;
import com.jdh.o2oservice.ext.ship.reponse.ShunFengResponse;
import com.jdh.o2oservice.vertical.enums.UavShipStatusEnum;
import org.apache.commons.lang3.StringEscapeUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Map;
import java.util.Objects;
import com.jdh.o2oservice.base.util.RSAUtil;


/**
 *  ShipController：运单状态回传
 * <AUTHOR>
 * @date 2024-05-17 17:46
 */
@RequestMapping("/provider/ship")
@Controller
public class ShipController {
    /**
     * log
     */
    private static final Logger log = LoggerFactory.getLogger(ShipController.class);

    @Resource
    private AngelWorkApplication angelWorkApplication;

    @Resource
    TemplateParseApplication templateParseApplication;

    @Autowired
    private ShunFengUtils shunFengUtils;

    @Autowired
    private DuccConfig duccConfig;

    @RequestMapping(value="/dada/callback")
    @ResponseBody
    @OperationLog(operationType = OpTypeEnum.UPDATE, operationDesc = "达达回调", recordParamBizIdExpress = {"args[0].orderId"})
    public Response<Boolean> shipStatusCallback(@RequestBody ShipInfoForCallBackRequest callBackRequest){
        log.info("DadaShipCallbackController -> shipStatusCallback start, dadaCallbackRequest = {}", JSON.toJSONString(callBackRequest));
        try {
            if(Objects.isNull(callBackRequest)){
                return Response.buildErrorResult("400", "请求参数为空");
            }

            if(callBackRequest.getOrderStatus() == CommonConstant.FIVE && StringUtils.isBlank(callBackRequest.getCancelReason())) {
                callBackRequest.setCancelReason("因运力紧张");
            }
            return Response.buildSuccessResult(angelWorkApplication.shipStatusCallback(callBackRequest));
        }catch (BusinessException be){
            log.error("ShipController -> shipStatusCallback exception, dadaCallbackRequest = {}", JSON.toJSONString(callBackRequest), be);
            throw new BusinessException(AngelPromiseBizErrorCode.DADA_CALL_BACK_HANDLE_ERROR);
        }catch (Exception e){
            log.error("ShipController -> shipStatusCallback exception, dadaCallbackRequest = {}", JSON.toJSONString(callBackRequest), e);
            throw new BusinessException(AngelPromiseBizErrorCode.DADA_CALL_BACK_HANDLE_ERROR);
        }
    }

    /**
     * 闪送回调
     * @param ssCallBackRequest
     * @return
     */
    @RequestMapping(value="/ss/callback")
    @ResponseBody
    @OperationLog(operationType = OpTypeEnum.UPDATE, operationDesc = "闪送回调", recordParamBizIdExpress = {"args[0].orderNo"}, resultJudgeExpress = {"ret.status"}, resultSuccessCode = {"200"})
    public ShipResponse<Boolean> ssShipStatusCallback(@RequestBody ShipInfoForSsCallBackRequest ssCallBackRequest){
        log.info("ShipController -> ssShipStatusCallback start, ssCallBackRequest = {}", JSON.toJSONString(ssCallBackRequest));
        try {
            if(Objects.isNull(ssCallBackRequest)){
                return ShipResponse.buildErrorResult(400, "请求参数为空");
            }
            Map<String, Object> map = JSON.parseObject(JSON.toJSONString(ssCallBackRequest), Map.class);
            AngelShipCallBackContext angelShipCallBackContext = templateParseApplication.parseToShipCallbackContext(map, ssCallBackRequest.getOrderNo(), AngelDetailTypeEnum.SHANSONG_SUPPLIER);
            return ShipResponse.buildSuccessResult(angelWorkApplication.shipStatusCallback(angelShipCallBackContext));
        }catch (BusinessException be){
            log.error("ShipController -> ssShipStatusCallback exception, ssCallBackRequest = {}", JSON.toJSONString(ssCallBackRequest), be);
            throw new BusinessException(AngelPromiseBizErrorCode.DADA_CALL_BACK_HANDLE_ERROR);
        }catch (Exception e){
            log.error("ShipController -> ssShipStatusCallback exception, ssCallBackRequest = {}", JSON.toJSONString(ssCallBackRequest), e);
            throw new BusinessException(AngelPromiseBizErrorCode.DADA_CALL_BACK_HANDLE_ERROR);
        }
    }

    /**
     * 顺丰回调
     * @param body
     * @return
     */
    @RequestMapping(value="/shunfeng/callback")
    @ResponseBody
    @LogAndAlarm
    @OperationLog(operationType = OpTypeEnum.UPDATE, operationDesc = "顺丰回调", resultJudgeExpress = {"ret.errorCode"}, resultSuccessCode = {"0"})
    public ShunFengResponse shunFengShipStatusCallback(@RequestBody String body, @RequestParam("sign") String sign){
        log.info("ShipController -> shunFengShipStatusCallback start, body = {}",body);
        try {
            log.info("theirSign={} body={}",sign,body);
            //校验sign
            String mySign = shunFengUtils.generateOpenSign(body,Long.parseLong(duccConfig.getShunFengConfig().getDevId()),duccConfig.getShunFengConfig().getDevKey());
            if(!mySign.equals(sign)){
                return ShunFengResponse.buildErrorResult(-1,"加密参数校验失败!!!");
            }
            //状态同步
            ShunFengCallBackRequest shunFengCancelCallBackRequest = JSON.parseObject(body, ShunFengCallBackRequest.class);
            if(!duccConfig.getShunFengUrlIndex().contains(shunFengCancelCallBackRequest.getUrl_index())){
                log.info("ShipController -> shipStatusCallback url_index前缀不匹配,逻辑终止!!!");
                return ShunFengResponse.buildSuccessResult(null);
            }
            OperationLogContext operationLogContext = new OperationLogContext();
            operationLogContext.setBizId(shunFengCancelCallBackRequest.getShop_order_id());
            OperationLogContext.put(operationLogContext);
            AngelShipCallBackContext angelShipCallBackContext = toAngelShipCallBackContext(shunFengCancelCallBackRequest);
            angelWorkApplication.shipStatusCallback(angelShipCallBackContext);
            return ShunFengResponse.buildSuccessResult(null);
        }catch (BusinessException be){
            log.error("ShipController -> shipStatusCallback exception, shunFengShipStatusCallback = {}", body, be);
            return ShunFengResponse.buildErrorResult(Integer.parseInt(be.getErrorCode().getCode()),be.getErrorCode().getDescription());
        }catch (Exception e){
            log.error("ShipController -> shipStatusCallback exception, shunFengShipStatusCallback = {}", body, e);
            return ShunFengResponse.buildErrorResult(Integer.parseInt(BusinessErrorCode.UNKNOWN_ERROR.getCode()),BusinessErrorCode.UNKNOWN_ERROR.getDescription());
        } finally {
            OperationLogContext.remove();
        }
    }

    /**
     * 顺丰入参对象转成内部对象
     * @param shunFengCancelCallBackRequest
     * @return
     */
    private AngelShipCallBackContext toAngelShipCallBackContext(ShunFengCallBackRequest shunFengCancelCallBackRequest){
        AngelShipCallBackContext angelShipCallBackContext = AngelShipCallBackContext.builder().build();
        if(StringUtils.isNotEmpty(shunFengCancelCallBackRequest.getRider_lat())){
            angelShipCallBackContext.setLatitude(Double.parseDouble(shunFengCancelCallBackRequest.getRider_lat()));
        }
        if(StringUtils.isNotEmpty(shunFengCancelCallBackRequest.getRider_lng())){
            angelShipCallBackContext.setLongitude(Double.parseDouble(shunFengCancelCallBackRequest.getRider_lng()));
        }


        angelShipCallBackContext.setOrderStatus(ShunFengShipStatusEnum.transferToStandShipStatus(shunFengCancelCallBackRequest.getOrder_status()));
        angelShipCallBackContext.setOrderId(Long.parseLong(shunFengCancelCallBackRequest.getShop_order_id()));
        angelShipCallBackContext.setDmId("0");
        angelShipCallBackContext.setDmName(shunFengCancelCallBackRequest.getOperator_name());
        angelShipCallBackContext.setDmMobile(shunFengCancelCallBackRequest.getOperator_phone());
        angelShipCallBackContext.setTime(TimeUtils.dateTimeToStr(new Date(shunFengCancelCallBackRequest.getPush_time()*1000)));
        return angelShipCallBackContext;
    }

    private AngelShipCallBackContext toAngelShipCallBackContext(UavCallBackStatusRequest uavCallBackStatusRequest) {
        AngelShipCallBackContext angelShipCallBackContext = AngelShipCallBackContext.builder().build();

        angelShipCallBackContext.setOrderStatus(UavShipStatusEnum.transferToStandShipStatus(uavCallBackStatusRequest.getFlight_work_state()));
        angelShipCallBackContext.setClientId(uavCallBackStatusRequest.getFlight_work_id());
        angelShipCallBackContext.setDmId("0");
        angelShipCallBackContext.setDmName("无人机");
        angelShipCallBackContext.setDmMobile("暂无");
        angelShipCallBackContext.setTime(TimeUtils.dateTimeToStr(new Date(uavCallBackStatusRequest.getTimestamp()*1000)));

        String note = StringEscapeUtils.unescapeJava(uavCallBackStatusRequest.getMsg());
        if(StringUtils.isNotEmpty(note)&&StringUtils.contains(note,"——")){
            //异常情况,不发送事件,因为之前正常走流程,已发送过对应事件
            angelShipCallBackContext.setErrorMsg(note);
            angelShipCallBackContext.setSendEvent(false);
        }
        angelShipCallBackContext.setDeliveryType(DeliveryTypeEnum.UAV.getType());

        return angelShipCallBackContext;
    }

    /**
     * 无人机状态回到
     * @param uavCallBackRequest
     * @return
     */
    @RequestMapping(value="/uav/callbackStatus")
    @ResponseBody
    @LogAndAlarm
    @OperationLog(operationType = OpTypeEnum.UPDATE, operationDesc = "无人机状态回调")
    public UavResponse uavStatusCallback(@RequestBody UavCallBackRequest uavCallBackRequest){
        log.info("ShipController -> uavStatusCallback start, body = {}",JSON.toJSONString(uavCallBackRequest));
        try {
            String content = RSAUtil.decrypt(uavCallBackRequest.getContent(),duccConfig.getUavConfig().getPrivateKeyStr());
            log.info("ShipController -> uavStatusCallback start, content = {}",content);
            UavCallBackStatusRequest  uavCallBackStatusRequest = JSON.parseObject(content,UavCallBackStatusRequest.class);
            AngelShipCallBackContext angelShipCallBackContext = toAngelShipCallBackContext(uavCallBackStatusRequest);
            angelWorkApplication.shipStatusCallback(angelShipCallBackContext);
            return UavResponse.buildSuccessResult(null);
        }catch (BusinessException be){
            log.error("ShipController -> navStatusCallback exception, shunFengShipStatusCallback = {}", JSON.toJSONString(uavCallBackRequest), be);
            return UavResponse.buildErrorResult(Integer.parseInt(be.getErrorCode().getCode()),be.getErrorCode().getDescription());
        }catch (Exception e){
            log.error("ShipController -> navStatusCallback exception, shunFengShipStatusCallback = {}", JSON.toJSONString(uavCallBackRequest), e);
            return UavResponse.buildErrorResult(Integer.parseInt(BusinessErrorCode.UNKNOWN_ERROR.getCode()),BusinessErrorCode.UNKNOWN_ERROR.getDescription());
        } finally {
            OperationLogContext.remove();
        }
    }



    /**
     * 无人机实时位置
     * @param uavCallBackRequest
     * @return
     */
    @RequestMapping(value="/uav/callbackPosition")
    @ResponseBody
    @LogAndAlarm
    @OperationLog(operationType = OpTypeEnum.UPDATE, operationDesc = "无人机实时位置回调")
    public UavResponse uavPositionCallback(@RequestBody UavCallBackRequest uavCallBackRequest){
        log.info("ShipController -> uavPositionCallback start, body = {}",JSON.toJSONString(uavCallBackRequest));
        try {
            String content = RSAUtil.decrypt(uavCallBackRequest.getContent(),duccConfig.getUavConfig().getPrivateKeyStr());
            log.info("ShipController -> uavPositionCallback start, content = {}",content);
            UavCallBackPositionRequest uavCallBackPositionRequest = JSON.parseObject(content,UavCallBackPositionRequest.class);
            Boolean b = angelWorkApplication.shipPositionCallback(uavCallBackPositionRequest);
            return UavResponse.buildSuccessResult(b);
        }catch (BusinessException be){
            log.error("ShipController -> uavPositionCallback exception, shunFengShipStatusCallback = {}", JSON.toJSONString(uavCallBackRequest), be);
            return UavResponse.buildErrorResult(Integer.parseInt(be.getErrorCode().getCode()),be.getErrorCode().getDescription());
        }catch (Exception e){
            log.error("ShipController -> uavPositionCallback exception, shunFengShipStatusCallback = {}", JSON.toJSONString(uavCallBackRequest), e);
            return UavResponse.buildErrorResult(Integer.parseInt(BusinessErrorCode.UNKNOWN_ERROR.getCode()),BusinessErrorCode.UNKNOWN_ERROR.getDescription());
        } finally {
            OperationLogContext.remove();
        }
    }

    public static void main(String[] args) throws Exception {
        String content = "KhxoCDVPPIm9LoSJGBkf+90goyLZxe/Iy6BABnvxD95DD9aXfKp3bXr3fIyNG+vzD1vEzLFsNOlVg58Z2BcMQfcVNaC09Fg6LbSPvmPRClz8Nt+ig2vrizhfnjhifEPIAnVzX7QbDDRbZ8c9gzQRQ5jNtIMrx9CEIL23YDlRHqA=";
        content = RSAUtil.decrypt(content,"MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBALO+C8A2svTJTBqpuTjkzChOHY4RqyJw1grNu6EmNfKKQlFc+QyPG3kYtuvd8ZLxLodhRGetTL1E5NCbkTIOobmtb6aruXSW8HLdYTy7qQY8ii89RVvZAh3/8viV+4O+kUIYG3tjIyGTIng5FbDD0r5LXk7nKsWp4JoYDl1bTaevAgMBAAECgYEAl9u2/dy48Yuo2tYOgXz86Aine8J9vglrLZGINqyb46DgSvGsEOpPoc45ranEUgum7gZFzvph3X75ey4UTCCEjXrTgB1n1uqCGLLdwmTurROzJ5QF+B6DD3X5uvlYLDWbplIpkzgT3IqQ+uJFv9mWEON7v/VaflSsNreflop5yDECQQDdBdwjuHqsSokH/WmiLAF1BJkVNO1XZ5xfGobzb0416sME8fEG3n0zCK66kjG7gaTdjOSA5UlRb6nujBrRkI0LAkEA0C/RbN1KgGftDAly8CGfVt2vWUv0BRH+ppBVEPy30GzjfNYNnx48pq81xPjUUc+5VmIrP1cHTWEemcrWhAEObQJAZp8a6Gb2ZlqxJ0mNK2QdRRTecw9BB+0umKW7dPoAKV6YMAqZ66OQJArq/et1NedrAgcx7XsIQMyE7SKWLSldowJAFnSh9UklSRZspji6shYeVsNQr6QWrRlUy2iUFGH9/bhRDV0VWQ1s41nQxe9FD5IFXsD2Az4C5qDMKTCM1O48PQJAOMET3cbLUOE4MAfCQHAtpN0AvYdAWS7oSbWZbnXgt+9XIEiZbntCK96tDpESKiw7lvHNOJhYO7EgH0Sf9fs9jA==");
        System.out.println(content);

        String s = "GZ5\\u2014\\u2014\\u65e0\\u4eba\\u673a\\u6545\\u969c\\uff0c\\u5168\\u5929\\u65e0\\u6cd5\\u8fd0\\u8f93";
        System.out.println(s);
    }
}
