package com.jdh.o2oservice.web.controller.admin.settlement;

import com.jd.common.web.LoginContext;
import com.jdh.o2oservice.annotation.OperationLog;
import com.jdh.o2oservice.application.settlement.service.JdServiceCityAngelSettleApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.ducc.model.fee.CityLevelSettlementCoefficientConfig;
import com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode;
import com.jdh.o2oservice.base.util.AssertUtils;
import com.jdh.o2oservice.base.util.ResponseUtil;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.core.domain.settlement.repository.query.CityAngelSettlementPageQuery;
import com.jdh.o2oservice.enums.OpTypeEnum;
import com.jdh.o2oservice.export.settlement.cmd.AngelSettleCityConfigFileCmd;
import com.jdh.o2oservice.export.settlement.cmd.CityAngelSettlementConfigCmd;
import com.jdh.o2oservice.export.settlement.dto.CityAngelSettlementConfigDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Map;

/**
 * 护士结算调账
 *
 * <AUTHOR>
 * @date 2024/8/06
 */
@Slf4j
@RestController
@RequestMapping("/settle/config")
public class CityAngelSettlementController {
    
    /**
     * 商品接口信息
     */
    @Resource
    JdServiceCityAngelSettleApplication jdServiceCityAngelSettleApplication;

    /**
     * 分页查询城市等级护士结算配置
     *
     * @param cityAngelSettlementPageQuery
     * @return response
     */
    @PostMapping(value = "/queryCityLevelConfigPage")
    @LogAndAlarm(jKey = "CityAngelSettlementController.queryCityLevelConfigPage")
    @OperationLog(operationType = OpTypeEnum.QUERY, operationDesc = "护士结算调账分页信息")
    public Response<PageDto<CityAngelSettlementConfigDto>> queryCityLevelConfigPage(@RequestBody CityAngelSettlementPageQuery cityAngelSettlementPageQuery) {
        AssertUtils.nonNull(cityAngelSettlementPageQuery, BusinessErrorCode.ILLEGAL_ARG_ERROR);
        PageDto<CityAngelSettlementConfigDto> result = jdServiceCityAngelSettleApplication.queryCityAngelSettlementPage(cityAngelSettlementPageQuery);
        return ResponseUtil.buildSuccResponse(result);
    }

    /**
     * 新建城市等级护士结算配置
     * @param cityAngelSettlementConfigCmd
     * @return response
     */
    @PostMapping(value = "/saveCityLevelConfig")
    @LogAndAlarm(jKey = "CityAngelSettlementController.saveCityLevelConfig")
    @OperationLog(operationType = OpTypeEnum.ADD, operationDesc = "新建城市等级护士结算配置")
    public Response<Boolean> saveCityLevelConfig(@RequestBody CityAngelSettlementConfigCmd cityAngelSettlementConfigCmd) {
        this.checkCityConfigParam(cityAngelSettlementConfigCmd);
        String pin = LoginContext.getLoginContext().getPin();
        cityAngelSettlementConfigCmd.setOperator(pin);
        Boolean result = jdServiceCityAngelSettleApplication.saveCityAngelSettlement(cityAngelSettlementConfigCmd);
        return ResponseUtil.buildSuccResponse(result);
    }

    /**
     * 更新城市等级护士结算配置
     *
     * @param cityAngelSettlementConfigCmd
     * @return response
     */
    @PostMapping(value = "/updateCityLevelConfig")
    @LogAndAlarm(jKey = "CityAngelSettlementController.updateCityLevelConfig")
    @OperationLog(operationType = OpTypeEnum.QUERY, operationDesc = "更新城市等级护士结算配置")
    public Response<Boolean> updateCityAngelSettlement(@RequestBody CityAngelSettlementConfigCmd cityAngelSettlementConfigCmd) {
        this.checkCityConfigParam(cityAngelSettlementConfigCmd);
        String pin = LoginContext.getLoginContext().getPin();
        cityAngelSettlementConfigCmd.setOperator(pin);
        Boolean result = jdServiceCityAngelSettleApplication.updateCityAngelSettlement(cityAngelSettlementConfigCmd);
        return ResponseUtil.buildSuccResponse(result);
    }

    /**
     *
     * @param cityAngelSettlementConfigCmd
     */
    private void checkCityConfigParam(CityAngelSettlementConfigCmd cityAngelSettlementConfigCmd) {
        AssertUtils.nonNull(cityAngelSettlementConfigCmd, BusinessErrorCode.ILLEGAL_ARG_ERROR);
        AssertUtils.hasText(cityAngelSettlementConfigCmd.getProvinceCode(),"省不能为空");
        if(Integer.parseInt(cityAngelSettlementConfigCmd.getProvinceCode()) > 4){
            AssertUtils.hasText(cityAngelSettlementConfigCmd.getCityCode(),"市不能为空");
        }
        AssertUtils.hasText(cityAngelSettlementConfigCmd.getLevel(), "城市等级不能为空");
    }

    /**
     * 批量新建城市等级护士结算配置
     *
     * @param angelSettleCityConfigFileCmd
     * @return
     */
    @PostMapping(value = "/batchSubmitCityConfig")
    @LogAndAlarm(jKey = "CityAngelSettlementController.batchSubmitCityConfig")
    @OperationLog(operationType = OpTypeEnum.ADD, operationDesc = "批量新建城市等级护士结算配置")
    public Response<Boolean> batchSubmitCityConfig(@RequestBody AngelSettleCityConfigFileCmd angelSettleCityConfigFileCmd) {
        AssertUtils.nonNull(angelSettleCityConfigFileCmd, BusinessErrorCode.ILLEGAL_ARG_ERROR);
        AssertUtils.nonNull(angelSettleCityConfigFileCmd.getFileId(),"请上传文件后再提交");
        String pin = LoginContext.getLoginContext().getPin();
        angelSettleCityConfigFileCmd.setApplyErp(pin);
        Boolean result = jdServiceCityAngelSettleApplication.batchSubmitCityConfig(angelSettleCityConfigFileCmd);
        return ResponseUtil.buildSuccResponse(result);
    }

    /**
     * 导出城市等级护士结算配置
     *
     * @param request request
     * @return response
     */
    @PostMapping(value = "/exportCityLevelConfig")
    @LogAndAlarm(jKey = "CityAngelSettlementController.exportCityLevelConfig")
    @OperationLog(operationType = OpTypeEnum.QUERY, operationDesc = "导出城市等级护士结算配置")
    public Response<Boolean> exportCityLevelConfig(@RequestBody CityAngelSettlementPageQuery request) {
        AssertUtils.nonNull(request, BusinessErrorCode.ILLEGAL_ARG_ERROR);
        String pin = LoginContext.getLoginContext().getPin();
        request.setUserPin(pin);
        Boolean result = jdServiceCityAngelSettleApplication.exportCityLevelConfig(request);
        return ResponseUtil.buildSuccResponse(result);
    }

    /**
     * 查询城市等级护士结算系数
     *
     * @param request
     * @return response
     */
    @PostMapping(value = "/getCityLevelConfig")
    @LogAndAlarm(jKey = "CityAngelSettlementController.getCityLevelConfig")
    @OperationLog(operationType = OpTypeEnum.QUERY, operationDesc = "查询城市等级护士结算系数")
    public Response<CityLevelSettlementCoefficientConfig> getCityLevelConfig(@RequestBody CityAngelSettlementPageQuery request) {
        AssertUtils.nonNull(request, BusinessErrorCode.ILLEGAL_ARG_ERROR);
        AssertUtils.hasText(request.getCityLevel(), BusinessErrorCode.ILLEGAL_ARG_ERROR);
        CityLevelSettlementCoefficientConfig result = jdServiceCityAngelSettleApplication.getCityLevelSettlementCoefficientConfig(request.getCityLevel());
        return ResponseUtil.buildSuccResponse(result);
    }

}
