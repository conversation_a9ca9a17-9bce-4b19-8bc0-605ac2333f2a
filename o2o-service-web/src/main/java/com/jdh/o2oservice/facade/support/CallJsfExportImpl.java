package com.jdh.o2oservice.facade.support;
import com.jdh.o2oservice.application.support.service.CallRecordApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.util.ResponseUtil;
import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.export.support.CallJsfExport;
import com.jdh.o2oservice.export.support.dto.CallRecordDto;
import com.jdh.o2oservice.export.support.query.QueryCallRecordRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.List;

/**
 * @Description 外呼服务
 * @Date 2025/2/27 下午4:47
 * <AUTHOR>
 **/
@Slf4j
@Service
public class CallJsfExportImpl implements CallJsfExport {

    @Resource
    private CallRecordApplication callRecordApplication;


    /**
     * 外呼记录列表
     * @param request
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.support.CallJsfExportImpl.queryCallRecordList")
    public Response<List<CallRecordDto>> queryCallRecordList(QueryCallRecordRequest request) {
        return ResponseUtil.buildSuccResponse(callRecordApplication.queryCallRecordList(request));
    }

    /**
     * 查询外呼url
     * @param request
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.support.CallJsfExportImpl.queryCallRecordUrl")
    public Response<String> queryCallRecordUrl(QueryCallRecordRequest request) {
        return ResponseUtil.buildSuccResponse(callRecordApplication.queryCallRecordUrl(request));
    }
}
