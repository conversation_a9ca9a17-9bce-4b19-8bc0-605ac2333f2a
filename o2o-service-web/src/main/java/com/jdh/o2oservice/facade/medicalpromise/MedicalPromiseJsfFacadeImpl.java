package com.jdh.o2oservice.facade.medicalpromise;

import com.jd.ump.profiler.util.StringUtil;
import com.jdh.o2oservice.application.medicalpromise.MedicalPromiseApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.core.domain.medpromise.enums.MedPromiseErrorCode;
import com.jdh.o2oservice.export.medicalpromise.MedicalPromiseJsfExport;
import com.jdh.o2oservice.export.medicalpromise.cmd.MedicalPromiseCallbackCmd;
import com.jdh.o2oservice.export.medicalpromise.cmd.MedicalPromiseHandCmd;
import com.jdh.o2oservice.export.medicalpromise.cmd.MedicalPromiseReportCmd;
import com.jdh.o2oservice.export.medicalpromise.cmd.UpdateMedicalPromiseEtaCmd;
import com.jdh.o2oservice.export.medicalpromise.dto.MedicalPromiseCallbackResultDTO;
import com.jdh.o2oservice.export.medicalpromise.dto.MedicalPromiseDTO;
import com.jdh.o2oservice.export.medicalpromise.dto.MedicalPromiseFullDTO;
import com.jdh.o2oservice.export.medicalpromise.dto.MedicalPromisePushReportDTO;
import com.jdh.o2oservice.export.medicalpromise.query.LabQueryMedPromisePageRequest;
import com.jdh.o2oservice.export.medicalpromise.query.MedicalPromiseListRequest;
import com.jdh.o2oservice.export.medicalpromise.query.MedicalPromiseReportVerifyRequest;
import com.jdh.o2oservice.export.medicalpromise.query.MedicalPromiseRequest;
import com.jdh.o2oservice.export.provider.dto.ExportDataDTO;
import com.jdh.o2oservice.export.report.dto.QuickStructReportVerifyResultDTO;
import com.jdh.o2oservice.export.report.dto.StructQuickReportResultDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * @Description: 检测单JsfFacade实现类
 * @Author: wangpengfei144
 * @Date: 2024/4/16
 */
@Service("medicalPromiseJsfExport")
@Slf4j
public class MedicalPromiseJsfFacadeImpl implements MedicalPromiseJsfExport {

    /**
     * 检测单应用层
     */
    @Autowired
    private MedicalPromiseApplication medicalPromiseApplication;

    /**
     * 根据条件分页查询检测单
     *
     * @param medicalPromiseListRequest
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.medicalpromise.MedicalPromiseJsfFacadeImpl.queryMedicalPromisePage")
    public Response<List<MedicalPromiseDTO>> queryMedicalPromisePage(MedicalPromiseListRequest medicalPromiseListRequest) {
        PageDto<MedicalPromiseDTO> medicalPromiseDTOPageDto = medicalPromiseApplication.queryMedicalPromisePage(medicalPromiseListRequest);
        return Response.buildSuccessResult(medicalPromiseDTOPageDto.getList());
    }

    /**
     * 检测单状态回传
     *
     * @param medicalPromiseCallbackCmd
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.medicalpromise.MedicalPromiseJsfFacadeImpl.medicalPromiseCallBack")
    public Response<MedicalPromiseCallbackResultDTO> medicalPromiseCallBack(MedicalPromiseCallbackCmd medicalPromiseCallbackCmd) {
        MedicalPromiseCallbackResultDTO medicalPromiseCallbackResultDTO = medicalPromiseApplication.medicalPromiseCallBack(medicalPromiseCallbackCmd);
        return Response.buildSuccessResult(medicalPromiseCallbackResultDTO);
    }

    /**
     * 推送检测单结构化报告
     *
     * @param medicalPromiseReportCmd
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.medicalpromise.MedicalPromiseJsfFacadeImpl.pushMedicalPromiseReport")
    public Response<MedicalPromisePushReportDTO> pushMedicalPromiseReport(MedicalPromiseReportCmd medicalPromiseReportCmd) {
        MedicalPromisePushReportDTO medicalPromisePushReportDTO = medicalPromiseApplication.pushMedicalPromiseReport(medicalPromiseReportCmd);
        return Response.buildSuccessResult(medicalPromisePushReportDTO);
    }

    /**
     * 创建检测单
     *
     * @param medicalPromiseHandCmd
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.medicalpromise.MedicalPromiseJsfFacadeImpl.handCreateMedicalPromise")
    public Response<Boolean> handCreateMedicalPromise(MedicalPromiseHandCmd medicalPromiseHandCmd) {
        Boolean res = medicalPromiseApplication.handCreateMedicalPromise(medicalPromiseHandCmd);
        return Response.buildSuccessResult(res);
    }

    /**
     * 根据条件分页查询检测单
     *
     * @param medicalPromiseRequest
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.medicalpromise.MedicalPromiseJsfFacadeImpl.queryMedicalPromise")
    public Response<MedicalPromiseDTO> queryMedicalPromise(MedicalPromiseRequest medicalPromiseRequest) {
        //校验能确定唯一检测单的参数
        if (Objects.isNull(medicalPromiseRequest.getMedicalPromiseId()) && StringUtil.isBlank(medicalPromiseRequest.getSpecimenCode())){
            throw new BusinessException(MedPromiseErrorCode.PARAM_NULL);
        }
        MedicalPromiseDTO medicalPromiseDTO = medicalPromiseApplication.queryMedicalPromise(medicalPromiseRequest);
        return Response.buildSuccessResult(medicalPromiseDTO);
    }

    /**
     * 根据条件查询检测单
     *
     * @param medicalPromiseListRequest
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.medicalpromise.MedicalPromiseJsfFacadeImpl.queryMedicalPromiseList")
    public Response<List<MedicalPromiseDTO>> queryMedicalPromiseList(MedicalPromiseListRequest medicalPromiseListRequest) {
        List<MedicalPromiseDTO> medicalPromiseDTOS = medicalPromiseApplication.queryMedicalPromiseList(medicalPromiseListRequest);
        return Response.buildSuccessResult(medicalPromiseDTOS);
    }

    /**
     * 分页查询检测单综合数据
     *
     * @param labQueryMedPromisePageRequest
     * @return
     */
    @Override
    @LogAndAlarm
    public Response<PageDto<MedicalPromiseFullDTO>> labQueryMedicalPromisePage(LabQueryMedPromisePageRequest labQueryMedPromisePageRequest) {
        PageDto<MedicalPromiseFullDTO> medicalPromiseDTOPageDto = medicalPromiseApplication.labQueryMedicalPromisePage(labQueryMedPromisePageRequest);
        return Response.buildSuccessResult(medicalPromiseDTOPageDto);
    }

    /**
     * 导出门店下检测单
     *
     * @param labQueryMedPromisePageRequest
     * @return
     */
    @Override
    @LogAndAlarm
    public Response<ExportDataDTO> exportMedicalPromiseList(LabQueryMedPromisePageRequest labQueryMedPromisePageRequest) {
        labQueryMedPromisePageRequest.setPageNum(1);
        labQueryMedPromisePageRequest.setPageSize(9000);
        ExportDataDTO res = medicalPromiseApplication.exportMedicalPromiseList(labQueryMedPromisePageRequest);
        return Response.buildSuccessResult(res);
    }

    /**
     * 校验结构化报告
     *
     * @param reportResult reportResult
     * @return dto dto
     */
    @Override
    @LogAndAlarm
    public Response<QuickStructReportVerifyResultDTO> verifyStructQuickReport(List<MedicalPromiseReportVerifyRequest> reportResult) {
        return Response.buildSuccessResult(medicalPromiseApplication.verifyStructQuickReport(reportResult));
    }

    /**
     * 更新检测单eta信息
     *
     * @param medicalPromiseEtaCmd UpdateMedicalPromiseEtaCmd
     * @return {@link Response }<{@link Boolean }>
     */
    @Override
    @LogAndAlarm
    public Response<Boolean> updateMedicalPromiseEta(UpdateMedicalPromiseEtaCmd medicalPromiseEtaCmd) {
        return Response.buildSuccessResult(medicalPromiseApplication.updateMedicalPromiseEta(medicalPromiseEtaCmd));
    }

    /**
     * 分页查询检测单综合数据
     *
     * @param labQueryMedPromisePageRequest
     * @return
     */
    @Override
    @LogAndAlarm
    public Response<MedicalPromiseFullDTO> labQueryMedicalPromiseDetail(LabQueryMedPromisePageRequest labQueryMedPromisePageRequest) {
        MedicalPromiseFullDTO medicalPromiseDTOPageDto = medicalPromiseApplication.labQueryMedicalPromiseDetail(labQueryMedPromisePageRequest);
        return Response.buildSuccessResult(medicalPromiseDTOPageDto);
    }


}
