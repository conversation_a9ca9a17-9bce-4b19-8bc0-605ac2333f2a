package com.jdh.o2oservice.facade.support;

import com.jdh.o2oservice.application.support.service.SoundExtApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.util.GwMapUtil;
import com.jdh.o2oservice.base.util.ResponseUtil;
import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.export.support.SoundExtGwExport;
import com.jdh.o2oservice.export.support.query.SoundExtRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2025/5/28
 */
@Service
@Slf4j
public class SoundExtGwExportImpl implements SoundExtGwExport {

    /**
     * 自动注入的 SoundExtApplication 实例，用于调用其提供的方法。
     */
    @Autowired
    private SoundExtApplication soundExtApplication;


    /**
     * 查询声音扩展参数。
     *
     * @param request 声音扩展参数请求对象。
     * @return 包含声音扩展参数的响应对象。
     */
    @Override
    @LogAndAlarm
    public Response<Object> querySoundExtParam(Map<String, String> paramst) {
        SoundExtRequest request = GwMapUtil.convertToParam(paramst, SoundExtRequest.class);
        Object result = soundExtApplication.querySoundExtParam(request);
        return ResponseUtil.buildSuccResponse(result);
    }


}
