
package com.jdh.o2oservice.facade.support;

import com.jdh.o2oservice.application.support.service.DictApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.util.GwMapUtil;
import com.jdh.o2oservice.base.util.ResponseUtil;
import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.export.support.DictGwExport;
import com.jdh.o2oservice.export.support.DictJsfExport;
import com.jdh.o2oservice.export.support.dto.DictInfoDto;
import com.jdh.o2oservice.export.support.query.DictRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 词典对外JSF接口
 *
 * <AUTHOR>
 * @date 2024/07/23
 */
@Service
@Slf4j
public class DictGwExportImpl implements DictGwExport{
    /**
     * dictApplication
     */
    @Resource
    DictApplication dictApplication;

    /**
     * 查询多个词典
     *
     * @param request request
     * @return {@link Response}
     */
    @Override
    @LogAndAlarm(jKey = "DictGwExportImpl.queryMultiDictList")
    public Response<Map<String, List<DictInfoDto>>> queryMultiDictList(Map<String, String> param) {
        DictRequest request = GwMapUtil.convertToParam(param, DictRequest.class);
        Map<String, List<DictInfoDto>> result = dictApplication.queryMultiDictList(request);
        return ResponseUtil.buildSuccResponse(result);
    }

    /**
     * 查询子分类词典
     *
     * @param request request
     * @return {@link Response}
     */
    @Override
    @LogAndAlarm(jKey = "DictGwExportImpl.querySubList")
    public Response<List<DictInfoDto>> querySubList(Map<String, String> param) {
        DictRequest request = GwMapUtil.convertToParam(param, DictRequest.class);
        List<DictInfoDto> result = dictApplication.querySubList(request);
        return ResponseUtil.buildSuccResponse(result);
    }
}
