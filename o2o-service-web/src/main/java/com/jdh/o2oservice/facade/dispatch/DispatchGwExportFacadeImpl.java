package com.jdh.o2oservice.facade.dispatch;

import com.jdh.o2oservice.application.dispatch.service.DispatchApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.annotation.UserOperationLimit;
import com.jdh.o2oservice.base.annotation.UserPinCheck;
import com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode;
import com.jdh.o2oservice.base.exception.errorcode.DynamicErrorCode;
import com.jdh.o2oservice.base.util.GwMapUtil;
import com.jdh.o2oservice.base.util.ResponseUtil;
import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.export.dispatch.DispatchGwExport;
import com.jdh.o2oservice.export.dispatch.cmd.AngelDispatchOperationCmd;
import com.jdh.o2oservice.export.dispatch.cmd.AngelDispatchSwitchCmd;
import com.jdh.o2oservice.export.dispatch.dto.AngelDispatchOperationDto;
import com.jdh.o2oservice.export.dispatch.dto.AngelDispatchSwitchDto;
import com.jdh.o2oservice.export.dispatch.dto.JdhDispatchDetailDto;
import com.jdh.o2oservice.export.dispatch.dto.JdhDispatchWaitingReceiveOverview;
import com.jdh.o2oservice.export.dispatch.query.DispatchWaitingReceiveListRequest;
import com.jdh.o2oservice.export.dispatch.query.DispatchWaitingReceiveOverviewRequest;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @ClassName DispatchGwExportFacadeImpl
 * @Description
 * <AUTHOR>
 * @Date 2024/4/23 17:41
 **/
@Service("dispatchGwExportFacadeImpl")
public class DispatchGwExportFacadeImpl implements DispatchGwExport {

    /**
     * dispatchApplication
     */
    @Resource
    private DispatchApplication dispatchApplication;

    /**
     * 查询派单明细列表
     * @param param
     * @return
     */
    @Override
    @UserPinCheck
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.dispatch.DispatchGwExportFacadeImpl.queryDispatchWaitingReceiveList")
    public Response<List<JdhDispatchDetailDto>> queryDispatchWaitingReceiveList(Map<String, String> param) {
        DispatchWaitingReceiveListRequest request = GwMapUtil.convertToParam(param, DispatchWaitingReceiveListRequest.class);
        List<JdhDispatchDetailDto> list = dispatchApplication.queryDispatchWaitingReceiveList(request);
        return ResponseUtil.buildSuccResponse(list);
    }

    /**
     * 查询待接单总览
     * @param param
     * @return
     */
    @Override
    @UserPinCheck
    @UserOperationLimit(timeInterval=5, maxLimitTimes=1)
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.dispatch.DispatchGwExportFacadeImpl.queryDispatchWaitingReceiveOverview")
    public Response<JdhDispatchWaitingReceiveOverview> queryDispatchWaitingReceiveOverview(Map<String, String> param) {
        DispatchWaitingReceiveOverviewRequest request = GwMapUtil.convertToParam(param, DispatchWaitingReceiveOverviewRequest.class);
        JdhDispatchWaitingReceiveOverview overview = dispatchApplication.queryDispatchWaitingReceiveOverview(request);
        return ResponseUtil.buildSuccResponse(overview);
    }

    /**
     * 服务者派单操作
     * @param param
     * @return
     */
    @Override
    @UserPinCheck
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.dispatch.DispatchGwExportFacadeImpl.angelDispatchOperate")
    public Response<AngelDispatchOperationDto> angelDispatchOperate(Map<String, String> param) {
        AngelDispatchOperationCmd cmd = GwMapUtil.convertToParam(param, AngelDispatchOperationCmd.class);
        AngelDispatchOperationDto res = dispatchApplication.angelDispatchOperate(cmd);
        if (Objects.nonNull(res) && StringUtils.isNotBlank(res.getErrorCode())) {
            return ResponseUtil.buildErrResponse(new DynamicErrorCode(res.getErrorCode(), res.getDescription()));
        }
        return ResponseUtil.buildSuccResponse(res);
    }

    /**
     * 服务者派单提醒
     *
     * @param param
     * @return
     */
    @Override
    @UserPinCheck
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.dispatch.DispatchGwExportFacadeImpl.angelDispatchTips")
    public Response<AngelDispatchOperationDto> angelDispatchTips(Map<String, String> param) {
        AngelDispatchOperationCmd cmd = GwMapUtil.convertToParam(param, AngelDispatchOperationCmd.class);
        AngelDispatchOperationDto res = dispatchApplication.angelDispatchTips(cmd);
        if (Objects.nonNull(res) && StringUtils.isNotBlank(res.getErrorCode())) {
            return ResponseUtil.buildErrResponse(new DynamicErrorCode(res.getErrorCode(), res.getDescription()));
        }
        return ResponseUtil.buildSuccResponse(res);
    }

    /**
     * 服务者操作接单按钮
     * @param param
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.dispatch.DispatchGwExportFacadeImpl.angelDispatchSwitchOperate")
    public Response<AngelDispatchSwitchDto> angelDispatchSwitchOperate(Map<String, String> param) {
        AngelDispatchSwitchCmd cmd = GwMapUtil.convertToParam(param, AngelDispatchSwitchCmd.class);
        if (Objects.isNull(cmd) || StringUtils.isBlank(cmd.getUserPin())) {
            return ResponseUtil.buildErrResponse(BusinessErrorCode.UNKNOWN_USER_LOGIN);
        }
        AngelDispatchSwitchDto res = dispatchApplication.angelDispatchSwitchOperate(cmd);
        return ResponseUtil.buildSuccResponse(res);
    }
}