package com.jdh.o2oservice.facade.angelpromise;

import com.jdh.o2oservice.application.angelpromise.service.AngelWorkReadApplication;
import com.jdh.o2oservice.base.util.AssertUtils;
import com.jdh.o2oservice.base.util.ResponseUtil;
import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.export.angelpromise.AngelWorkReadExport;
import com.jdh.o2oservice.export.angelpromise.dto.AngelWorkItemDto;
import com.jdh.o2oservice.export.angelpromise.query.AngelWorkItemQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @ClassName AngelWorkReadExportImpl
 * @Description
 * <AUTHOR>
 * @Date 2025/5/27 20:25
 */
@Service
@Slf4j
public class AngelWorkReadExportImpl implements AngelWorkReadExport {

    @Resource
    private AngelWorkReadApplication angelWorkReadApplication;

    /**
     * 查询工单下项目id
     *
     * @param angelWorkItemQuery
     * @return
     */
    @Override
    public Response<List<AngelWorkItemDto>> queryAngelWorkItemList(AngelWorkItemQuery angelWorkItemQuery) {
        //参数检查
        AssertUtils.nonNull(angelWorkItemQuery, "参数不能为空");
        AssertUtils.hasText(angelWorkItemQuery.getWorkId(), "工单id不能为空");
        return ResponseUtil.buildSuccResponse(angelWorkReadApplication.queryAngelWorkItemList(angelWorkItemQuery));
    }
}
