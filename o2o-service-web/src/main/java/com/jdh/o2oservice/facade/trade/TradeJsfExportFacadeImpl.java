package com.jdh.o2oservice.facade.trade;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jd.jim.cli.Cluster;
import com.jdh.o2oservice.application.trade.service.InspectionSheetApplication;
import com.jdh.o2oservice.application.trade.service.TradeApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.constatnt.NumConstant;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.enums.PartnerSourceEnum;
import com.jdh.o2oservice.base.enums.RedisKeyEnum;
import com.jdh.o2oservice.base.enums.SubPartnerSourceEnum;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode;
import com.jdh.o2oservice.base.util.AssertUtils;
import com.jdh.o2oservice.base.util.GwMapUtil;
import com.jdh.o2oservice.base.util.ResponseUtil;
import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.core.domain.trade.bo.InspectSheetInfoBO;
import com.jdh.o2oservice.core.domain.trade.bo.PartnerSourceOrderBO;
import com.jdh.o2oservice.core.domain.trade.bo.PartnerSourceOrderQuery;
import com.jdh.o2oservice.core.domain.trade.enums.RefundTypeEnum;
import com.jdh.o2oservice.core.domain.trade.enums.TradeErrorCode;
import com.jdh.o2oservice.export.promise.cmd.IntendedNurse;
import com.jdh.o2oservice.export.trade.TradeJsfExport;
import com.jdh.o2oservice.export.trade.cmd.CalcTradeServiceFeeCmd;
import com.jdh.o2oservice.export.trade.dto.*;
import com.jdh.o2oservice.export.trade.query.*;
import com.jdh.o2oservice.job.trade.refund.OrderRefundTaskJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @ClassName PromiseJsfExportFacadeImpl
 * @Description
 * <AUTHOR>
 * @Date 2024/1/23 14:53
 **/
@Service("tradeJsfExportFacade")
@Slf4j
public class TradeJsfExportFacadeImpl implements TradeJsfExport,ApplicationRunner {

    /**
     * tradeApplication
     */
    @Resource
    private TradeApplication tradeApplication;
    /**
     * inspectionSheetApplication
     */
    @Resource
    private InspectionSheetApplication inspectionSheetApplication;
    /**
     * orderRefundTaskJob
     */
    @Resource
    private OrderRefundTaskJob orderRefundTaskJob;

    /**
     * duccConfig
     */
    @Autowired
    private DuccConfig duccConfig;

    /**
     * 查询预约单列表
     *
     * @param request
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.trade.TradeJsfExportFacadeImpl.queryOrderPromiseByList")
    public Response<List<OrderPromiseDto>> queryOrderPromiseByList(OrderPromiseListRequest request) {
        return ResponseUtil.buildSuccResponse(tradeApplication.queryOrderPromiseByList(request));
    }

    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.trade.TradeJsfExportFacadeImpl.executeAction")
    @Override
    public Response<OrderUserActionDTO> executeAction(OrderUserActionParam orderUserActionParam) {
        String userPin = "";
        try {
            Response<OrderUserActionDTO> paramResult = checkExecuteActionParam(orderUserActionParam);
            if (paramResult != null) {
                return paramResult;
            }
            userPin = orderUserActionParam.getUserPin();

            // 转换出参
            OrderUserActionDTO orderUserActionDTO = tradeApplication.executeAction(orderUserActionParam);
            if (orderUserActionDTO == null) {
                orderUserActionDTO = new OrderUserActionDTO();
            }
            log.info("TradeJsfExportFacadeImpl executeAction 结算页用户行为 userPin:{} orderUserActionParam:{} orderUserActionDTO:{}",
                    userPin, JSONObject.toJSONString(orderUserActionParam), JSONObject.toJSONString(orderUserActionDTO));
            return Response.buildSuccessResult(orderUserActionDTO);
        } catch (BusinessException e) {
            log.error("TradeJsfExportFacadeImpl executeAction 结算页用户行为 business error userPin:{} orderUserActionParam:{} ", userPin,
                    JSONObject.toJSONString(orderUserActionParam), e);
            // 无地址的结算
            if("HJB-DM@1011-CD@2.200013".equalsIgnoreCase(e.getErrorCode().getCode())){
                return Response.buildErrorResult(TradeErrorCode.ORDER_NO_ADDRESS_ERROR.getCode(), TradeErrorCode.ORDER_NO_ADDRESS_ERROR.getDescription());
            }
            return Response.buildErrorResult(e.getErrorCode().getCode(), e.getErrorCode().getDescription());
        } catch (Exception e) {
            log.error("TradeJsfExportFacadeImpl executeAction 结算页用户行为 unknown error userPin:{} orderTradeParam:{}", userPin,
                    JSONObject.toJSONString(orderUserActionParam), e);
            return Response.buildUnknownErrorResult();
        }
    }

    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.trade.TradeJsfExportFacadeImpl.submitOrder")
    @Override
    public Response<SubmitOrderDTO> submitOrder(SubmitOrderParam submitOrderParam) {
        String userPin = "";
        try {
            Response<SubmitOrderDTO> paramResult = checkSubmitOrderParam(submitOrderParam);
            if (paramResult != null) {
                return paramResult;
            }
            userPin = submitOrderParam.getUserPin();
            if (Objects.isNull(submitOrderParam.getIntendedNurse())){
                submitOrderParam.setIntendedNurse(new IntendedNurseParam());
            }
            SubmitOrderDTO submitOrderDTO = tradeApplication.submitOrder(submitOrderParam);
            log.info("TradeJsfExportFacadeImpl submitOrder 提交订单 userPin:{} submitOrderParam:{} submitOrderDTO:{}",
                    userPin, JSONObject.toJSONString(submitOrderParam), JSONObject.toJSONString(submitOrderDTO));
            return Response.buildSuccessResult(submitOrderDTO);
        } catch (BusinessException e) {
            log.error("TradeJsfExportFacadeImpl submitOrder 提交订单 business error userPin:{} submitOrderParam:{} ", userPin,
                    JSONObject.toJSONString(submitOrderParam), e);
            return Response.buildErrorResult(e.getErrorCode().getCode(), e.getErrorCode().getDescription());
        } catch (Exception e) {
            log.error("TradeJsfExportFacadeImpl submitOrder 提交订单 unknown error userPin:{} submitOrderParam:{} ", userPin,
                    JSONObject.toJSONString(submitOrderParam), e);
            return Response.buildUnknownErrorResult();
        }
    }

    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.trade.TradeJsfExportFacadeImpl.queryAvaiableAppointmentTime")
    @Override
    public Response<AvaiableAppointmentTimeDTO> queryAvaiableAppointmentTime(AvaiableAppointmentTimeParam param) {
        String userPin = "";
        try {
            if (param == null) {
                return Response.buildErrorResult(BusinessErrorCode.ILLEGAL_ARG_ERROR.getCode(), BusinessErrorCode.ILLEGAL_ARG_ERROR.getDescription());
            }
            userPin = param.getUserPin();
            AvaiableAppointmentTimeDTO avaiableAppointmentTimeDTO = tradeApplication.queryAvaiableAppointmentTime(param);
            log.info("TradeJsfExportFacadeImpl queryAvaiableAppointmentTime 查询排期列表 userPin:{} param:{}",
                    userPin, JSONObject.toJSONString(param), JSONObject.toJSONString(avaiableAppointmentTimeDTO));
            return Response.buildSuccessResult(avaiableAppointmentTimeDTO);
        } catch (BusinessException e) {
            log.error("TradeJsfExportFacadeImpl queryAvaiableAppointmentTime 查询排期列表 business error userPin:{} param:{} ", userPin,
                    JSONObject.toJSONString(param), e);
            return Response.buildErrorResult(e.getErrorCode().getCode(), e.getErrorCode().getDescription());
        } catch (Exception e) {
            log.error("TradeJsfExportFacadeImpl queryAvaiableAppointmentTime 查询排期列表 unknown error userPin:{} param:{} ", userPin,
                    JSONObject.toJSONString(param), e);
            return Response.buildUnknownErrorResult();
        }
    }

    /**
     * 计算服务费
     *
     * @param cmd cmd
     * @return {@link Response}<{@link List}<{@link TradeServiceFeeInfoDTO}>>
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.trade.TradeJsfExportFacadeImpl.calcServiceFee")
    public Response<List<TradeServiceFeeInfoDTO>> calcServiceFee(CalcTradeServiceFeeCmd cmd) {
        try {
            return ResponseUtil.buildSuccResponse(tradeApplication.calcServiceFee(cmd));
        } catch (BusinessException e) {
            log.error("TradeJsfExportFacadeImpl calcServiceFee 计算服务费 business error cmd:{} ", JSONObject.toJSONString(cmd), e);
            return Response.buildErrorResult(e.getErrorCode().getCode(), e.getErrorCode().getDescription());
        } catch (Exception e) {
            log.error("TradeJsfExportFacadeImpl calcServiceFee 计算服务费 unknown error cmd:{}", JSONObject.toJSONString(cmd), e);
            return Response.buildUnknownErrorResult();
        }
    }

    /**
     * 申请退款
     * @param param
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.trade.TradeJsfExportFacadeImpl.xfylOrderRefund")
    public Response<Boolean> xfylOrderRefund(RefundOrderParam param) {
        return ResponseUtil.buildSuccResponse(tradeApplication.xfylOrderRefund(param));
    }

    /**
     * 获取订单详细信息
     *
     * @param request 请求
     * @return {@link Response}<{@link CompleteOrderDto}>
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.trade.TradeJsfExportFacadeImpl.findCompleteOrder")
    public Response<CompleteOrderDto> findCompleteOrder(CompleteOrderRequest request) {
        return ResponseUtil.buildSuccResponse(tradeApplication.findCompleteOrder(request));
    }

    /**
     * 获取订单信息
     *
     * @param request 请求
     * @return {@link Response}<{@link CompleteOrderDto}>
     */
    @Override
    public Response<JdOrderDTO> queryJdOrderDTO(CompleteOrderRequest request) {
        OrderDetailParam orderDetailParam = new OrderDetailParam();
        orderDetailParam.setOrderId(String.valueOf(request.getOrderId()));
        orderDetailParam.setPin(request.getPin());
        return ResponseUtil.buildSuccResponse(tradeApplication.queryJdOrderDTO(orderDetailParam));
    }

    /**
     * 批量获取订单详细信息
     *
     * @param request 请求
     * @return {@link Response}<{@link List}<{@link CompleteOrderDto}>>
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.trade.TradeJsfExportFacadeImpl.findCompleteOrderList")
    public Response<List<CompleteOrderDto>> findCompleteOrderList(CompleteOrderListRequest request) {
        List<CompleteOrderDto> result = new ArrayList<>();
        request.getOrderIds().forEach(ele -> result.add(tradeApplication.findCompleteOrder(CompleteOrderRequest.builder().pin(request.getPin()).orderId(ele).build())));
        return ResponseUtil.buildSuccResponse(result);
    }

    /**
     * 查询自定义订单列表
     * @param param
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.trade.TradeJsfExportFacadeImpl.queryCustomOrderList")
    public Response<List<CustomOrderInfoDTO>> queryCustomOrderList(QueryCustomOrderListParam param) {
        try {
            log.info("TradeJsfExportFacadeImpl queryCustomOrderList param={}", JSON.toJSONString(param));
            if (Objects.isNull(param)) {
                return Response.buildErrorResult(BusinessErrorCode.ILLEGAL_ARG_ERROR.getCode(), BusinessErrorCode.ILLEGAL_ARG_ERROR.getDescription());
            }
            if (CollectionUtils.isEmpty(param.getCustomOrderParamList())) {
                return Response.buildErrorResult(BusinessErrorCode.ORDER_NOT_EXIST.getCode(), BusinessErrorCode.ORDER_NOT_EXIST.getDescription());
            }
            param.setQueryCache(true);
            return ResponseUtil.buildSuccResponse(tradeApplication.queryCustomOrderList(param));
        } catch (BusinessException e) {
            log.error("TradeJsfExportFacadeImpl queryCustomOrderList 查询自定义订单列表 business error param:{} ", JSONObject.toJSONString(param), e);
            return Response.buildErrorResult(e.getErrorCode().getCode(), e.getErrorCode().getDescription());
        } catch (Exception e) {
            log.error("TradeJsfExportFacadeImpl queryCustomOrderList 查询自定义订单列表 unknown error param:{} ", JSONObject.toJSONString(param), e);
            return Response.buildUnknownErrorResult();
        }
    }

    /**
     * 收单
     * @param request
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.trade.TradeJsfExportFacadeImpl.receiveOrder")
    public Response<Integer> receiveOrder(ReceiveOrderRequest request) {
        if (request == null) {
            log.error("TradeJsfExportFacadeImpl receiveOrder 收单失败 request 为 null ");
            return Response.buildErrorResult(BusinessErrorCode.ILLEGAL_ARG_ERROR.getCode(), BusinessErrorCode.ILLEGAL_ARG_ERROR.getDescription());
        }
        Integer produceStatus = tradeApplication.receiveOrder(request);
        log.info("TradeJsfExportFacadeImpl receiveOrder 收单完成 request:{}", JSONObject.toJSONString(request));
        return Response.buildSuccessResult(produceStatus);
    }

    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.trade.TradeJsfExportFacadeImpl.findRefundable")
    public Response<RefundAbleInfoDTO> findRefundable(ReceiveOrderRequest request) {
        if (request == null) {
            log.error("TradeJsfExportFacadeImpl findRefundable 查询是否可退失败 request 为 null ");
            return Response.buildErrorResult(BusinessErrorCode.ILLEGAL_ARG_ERROR.getCode(), BusinessErrorCode.ILLEGAL_ARG_ERROR.getDescription());
        }
        log.info("TradeJsfExportFacadeImpl findRefundable 查询是否可退完成 request:{}", JSONObject.toJSONString(request));
        RefundAbleInfoDTO refundAbleInfoDTO = new RefundAbleInfoDTO();
        refundAbleInfoDTO.setRefundable(true);
        return Response.buildSuccessResult(refundAbleInfoDTO);
    }

    /**
     * 退款任务
     *
     * @return
     */
    @Override
    public Response<Boolean> orderRefundTaskJob() {
        orderRefundTaskJob.execute(null);
        return ResponseUtil.buildSuccResponse(true);
    }
    /**
     * 主站订单详情楼层
     *
     * @param param
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.trade.TradeJsfExportFacadeImpl.queryCustomOrderDetailFloor")
    public Response<CustomOrderDetailFloorDTO> queryCustomOrderDetailFloor(CustomOrderDetailParam param) {
        try {
            log.info("TradeJsfExportFacadeImpl queryCustomOrderDetailFloor param={}", JSON.toJSONString(param));
            if (Objects.isNull(param)) {
                return Response.buildErrorResult(BusinessErrorCode.ILLEGAL_ARG_ERROR.getCode(), BusinessErrorCode.ILLEGAL_ARG_ERROR.getDescription());
            }
            if (param.getOrderId() == null) {
                return Response.buildErrorResult(BusinessErrorCode.ORDER_NOT_EXIST.getCode(), BusinessErrorCode.ORDER_NOT_EXIST.getDescription());
            }
            param.setQueryCache(true);
            return ResponseUtil.buildSuccResponse(tradeApplication.queryCustomOrderDetailFloor(param));
        } catch (BusinessException e) {
            log.error("TradeJsfExportFacadeImpl queryCustomOrderDetailFloor 主站查询订单详情楼层 business error param:{} ", JSONObject.toJSONString(param), e);
            return Response.buildErrorResult(e.getErrorCode().getCode(), e.getErrorCode().getDescription());
        } catch (Exception e) {
            log.error("TradeJsfExportFacadeImpl queryCustomOrderDetailFloor 主站查询订单详情楼层 unknown error param:{} ", JSONObject.toJSONString(param), e);
            return Response.buildUnknownErrorResult();
        }
    }

    @Override
    @LogAndAlarm
    public Response<IntendedAngelDTO> queryIntendedAngel(QueryIntendedAngelParam param) {
        return ResponseUtil.buildSuccResponse(tradeApplication.queryIntendedAngel(param));
    }

    @Override
    @LogAndAlarm
    public Response<Boolean> receiveOrderAndAppointment(ReceiveOrderAndAppointmentParam param) {
        return ResponseUtil.buildSuccResponse(tradeApplication.receiveOrderAndAppointment(param));
    }

    @Override
    @LogAndAlarm
    public Response<Boolean> checkSubmitOrderAndAppointment(CheckSubmitOrderAndAppointmentParam param) {
        return ResponseUtil.buildSuccResponse(tradeApplication.checkSubmitOrderAndAppointment(param));
    }

    @Override
    public Response<Boolean> cancelAndRefundOrder(CancelRefundOrderParam cancelOrderParam) {
        try {
            log.info("TradeJsfExportFacadeImpl cancelAndRefundOrder cancelOrderParam={}", JSON.toJSONString(cancelOrderParam));
            AssertUtils.nonNull(cancelOrderParam, BusinessErrorCode.ILLEGAL_ARG_ERROR);
            AssertUtils.nonNull(cancelOrderParam.getOrderId(), TradeErrorCode.ORDER_ID_NULL);
            return ResponseUtil.buildSuccResponse(tradeApplication.cancelAndRefundOrder(cancelOrderParam));
        } catch (BusinessException e) {
            log.error("TradeJsfExportFacadeImpl cancelAndRefundOrder 取消订单（取消、退款） business error request:{} ", JSONObject.toJSONString(cancelOrderParam), e);
            return Response.buildErrorResult(e.getErrorCode().getCode(), e.getErrorCode().getDescription());
        } catch (Exception e) {
            log.error("TradeJsfExportFacadeImpl cancelAndRefundOrder 取消订单（取消、退款） unknown error request:{} ", JSONObject.toJSONString(cancelOrderParam), e);
            return Response.buildUnknownErrorResult();
        }
    }

    @Override
    public Response<StandardOrderDetailDto> queryStandardOrderDetail(StandardOrderDetailRequest request) {
        try {
            log.info("TradeJsfExportFacadeImpl queryStandardOrderDetail request={}", JSON.toJSONString(request));
            if (Objects.isNull(request)) {
                return Response.buildErrorResult(BusinessErrorCode.ILLEGAL_ARG_ERROR.getCode(), BusinessErrorCode.ILLEGAL_ARG_ERROR.getDescription());
            }
            if (Objects.isNull(request.getOrderId())) {
                return Response.buildErrorResult(BusinessErrorCode.ILLEGAL_ARG_ERROR.getCode(),"orderId不能为空");
            }
            return ResponseUtil.buildSuccResponse(tradeApplication.queryStandardOrderDetail(request));
        } catch (BusinessException e) {
            log.error("TradeJsfExportFacadeImpl queryStandardOrderDetail 查询订单详细信息 business error request:{} ", JSONObject.toJSONString(request), e);
            return Response.buildErrorResult(e.getErrorCode().getCode(), e.getErrorCode().getDescription());
        } catch (Exception e) {
            log.error("TradeJsfExportFacadeImpl queryStandardOrderDetail 查询订单详细信息 unknown error request:{} ", JSONObject.toJSONString(request), e);
            return Response.buildUnknownErrorResult();
        }
    }


    private <T> Response<T> checkExecuteActionParam(OrderUserActionParam orderUserActionParam) {
        if (orderUserActionParam == null) {
            return Response.buildErrorResult(BusinessErrorCode.ILLEGAL_ARG_ERROR.getCode(), BusinessErrorCode.ILLEGAL_ARG_ERROR.getDescription());
        }
        String userPin = orderUserActionParam.getUserPin();
        if (StringUtils.isEmpty(userPin)) {
            log.error("TradeJsfExportFacadeImpl checkExecuteActionParam pin为空 orderUserActionParam:{} ", JSONObject.toJSONString(orderUserActionParam));
            return Response.buildErrorResult(BusinessErrorCode.PIN_IS_NULL.getCode(), BusinessErrorCode.PIN_IS_NULL.getDescription());
        }
        return null;
    }

    private <T> Response<T> checkSubmitOrderParam(SubmitOrderParam submitOrderParam) {
        if (submitOrderParam == null) {
            return Response.buildErrorResult(BusinessErrorCode.ILLEGAL_ARG_ERROR.getCode(), BusinessErrorCode.ILLEGAL_ARG_ERROR.getDescription());
        }
        String userPin = submitOrderParam.getUserPin();
        if (StringUtils.isEmpty(userPin)) {
            log.error("OrderTradeInfoExportImpl checkSubmitOrderParam pin为空 submitOrderParam:{} ", JSONObject.toJSONString(submitOrderParam));
            return Response.buildErrorResult(BusinessErrorCode.PIN_IS_NULL.getCode(), BusinessErrorCode.PIN_IS_NULL.getDescription());
        }
        if(submitOrderParam.getAppointmentTimeParam() != null && submitOrderParam.getAppointmentTimeParam().getAppointmentEndTime() != null){
            long appointmentEndTime = 0;
            try {
                appointmentEndTime = DateUtil.parse(submitOrderParam.getAppointmentTimeParam().getAppointmentEndTime(), "yyyy-MM-dd HH:mm").getTime();
            }catch (Exception e){}
            if(appointmentEndTime > 0 && appointmentEndTime < new Date().getTime()){
                log.error("OrderTradeInfoExportImpl checkSubmitOrderParam 预约时间过期 submitOrderParam:{} ", JSONObject.toJSONString(submitOrderParam));
                return Response.buildErrorResult(BusinessErrorCode.APPOINTMENT_TIME_EXPIRE.getCode(), BusinessErrorCode.APPOINTMENT_TIME_EXPIRE.getDescription());
            }
        }
        if(submitOrderParam.getPartnerSource() != null && PartnerSourceEnum.JDH_NETDIAG.getCode().intValue() == submitOrderParam.getPartnerSource().intValue() && StringUtils.isNumeric(submitOrderParam.getPartnerSourceOrderId())){
            // 检验单状态是否可用
            InspectionSheetButtonParam inspectionSheetButtonParam = new InspectionSheetButtonParam();
            inspectionSheetButtonParam.setSheetId(Long.valueOf(submitOrderParam.getPartnerSourceOrderId()));
            inspectionSheetButtonParam.setUserPin(submitOrderParam.getUserPin());
            InspectSheetInfoBO inspectionSheet = inspectionSheetApplication.queryInspectionSheet(inspectionSheetButtonParam);
            if(inspectionSheet != null && inspectionSheet.getSheetStatus() != null && inspectionSheet.getSheetStatus() != 2){
                log.error("OrderTradeInfoExportImpl checkSubmitOrderParam 检验单状态无效 submitOrderParam:{}, partnerSourceOrderId:{}, inspectionSheet:{}", JSONObject.toJSONString(submitOrderParam), submitOrderParam.getPartnerSourceOrderId(), JSONObject.toJSONString(inspectionSheet));
                return Response.buildErrorResult(BusinessErrorCode.SUBMIT_ORDER_PARTNER_SOURCE_STATUS_FAIL.getCode(), BusinessErrorCode.SUBMIT_ORDER_PARTNER_SOURCE_STATUS_FAIL.getDescription());
            }
        } else if(PartnerSourceEnum.OUT_HOSPITAL_PAID_GUIDANCE_A.getCode().intValue() == submitOrderParam.getPartnerSource().intValue()){
            log.info("TradeApplicationImpl checkSubmitOrderParam 院内自费导诊解决方案A订单 submitOrderParam:{}", JSONObject.toJSONString(submitOrderParam));
            PartnerSourceOrderQuery query = PartnerSourceOrderQuery.builder()
                    .partnerSourceOrderId(submitOrderParam.getPartnerSourceOrderId())
                    .partnerSource(SubPartnerSourceEnum.OUT_HOSPITAL_PAID_GUIDANCE_SHANGHAI_CHILD_CENTER.getPartnerSourceEnum().getCode().intValue())
                    .saleChannelId(String.valueOf(SubPartnerSourceEnum.OUT_HOSPITAL_PAID_GUIDANCE_SHANGHAI_CHILD_CENTER.getSubCode()))
                    .userPin(submitOrderParam.getUserPin())
                    .build();
            if (submitOrderParam.getAddressUpdateParam() != null && submitOrderParam.getAddressUpdateParam().getId() > 0) {
                query.setAddressId(submitOrderParam.getAddressUpdateParam().getId());
            }
            PartnerSourceOrderBO partnerSourceOrderBO = inspectionSheetApplication.queryOuterOrder(query);
            if(Objects.isNull(partnerSourceOrderBO) || CollectionUtils.isEmpty(partnerSourceOrderBO.getSkuIds())){
                log.error("TradeApplicationImpl.checkSubmitOrderParam 院内自费导诊解决方案A订单 未查询到外部订单信息 query:{}", JSON.toJSONString(query));
                Response.buildErrorResult(BusinessErrorCode.SUBMIT_ORDER_PARTNER_SOURCE_STATUS_FAIL.getCode(), BusinessErrorCode.SUBMIT_ORDER_PARTNER_SOURCE_STATUS_FAIL.getDescription());
            }
        }
        return null;
    }




    /**
     * 预热
     */
    public void run(ApplicationArguments args){
        try {
            log.info("TradeJsfExportFacadeImpl run 预热 交易 算费接口 start");
            String calcFeeWarmUpParam = duccConfig.getCalcFeeWarmUpParam();
            if(StrUtil.isNotEmpty(calcFeeWarmUpParam)){
                CalcTradeServiceFeeCmd calcTradeServiceFeeCmd = JSON.parseObject(calcFeeWarmUpParam, CalcTradeServiceFeeCmd.class);
                TradeJsfExport tradeJsfExport = Convert.convert(TradeJsfExport.class, AopContext.currentProxy());
                for (int i = 0; i< NumConstant.NUM_50; i++){
                    tradeJsfExport.calcServiceFee(calcTradeServiceFeeCmd);
                }
            }
            log.info("TradeJsfExportFacadeImpl run 预热 交易 算费接口 end");
        }catch (Exception e){
            log.error("TradeJsfExportFacadeImpl run 预热 交易 算费接口 error",e);
        }
    }
}