package com.jdh.o2oservice.facade.angelpromise;
import com.alibaba.fastjson.JSON;
import com.jdh.o2oservice.application.angelpromise.service.AngelServiceRecordApplication;
import com.jdh.o2oservice.application.ztools.AngelWorkToolsApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.annotation.UserPinCheck;
import com.jdh.o2oservice.base.util.GwMapUtil;
import com.jdh.o2oservice.base.util.ResponseUtil;
import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.export.angelpromise.AngelServiceRecordGwExport;
import com.jdh.o2oservice.export.angelpromise.cmd.SubmitAngelServiceRecordCmd;
import com.jdh.o2oservice.export.angelpromise.dto.*;
import com.jdh.o2oservice.export.angelpromise.query.AngelServiceRecordFlowQuery;
import com.jdh.o2oservice.export.ztools.cmd.QueryAngelServiceRecordFlowCmd;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.Map;

/**
 * 护理单接口
 */
@Service
@Slf4j
public class AngelServiceRecordGwExportImpl implements AngelServiceRecordGwExport {

    @Resource
    private AngelServiceRecordApplication angelServiceRecordApplication;
    @Resource
    private AngelWorkToolsApplication angelWorkToolsApplication;
    /**
     * 提交护理单
     * @param param
     * @return
     */
    @Override
    @UserPinCheck
    @LogAndAlarm(jKey = "AngelServiceRecordGwExportImpl.submitAngelServiceRecord")
    public Response<SubmitAngelServiceRecordDto> submitAngelServiceRecord(Map<String, String> param) {
        SubmitAngelServiceRecordCmd cmd = GwMapUtil.convertToParam(param, SubmitAngelServiceRecordCmd.class);
        return ResponseUtil.buildSuccResponse(angelServiceRecordApplication.submitAngelServiceRecord(cmd));

    }


    /**
     * 查询服务者服务项详情
     * 1、每次查询返回最新创新的流程节点详情
     * 2、新的流程节点在上一个节点提交时完成创建
     * @param param
     * @return
     */
    @Override
    @UserPinCheck
    @LogAndAlarm(jKey = "AngelServiceRecordGwExportImpl.queryAngelServiceRecordFlow")
    public Response<AngelServiceRecordDto> queryAngelServiceRecordFlow(Map<String, String> param) {
        AngelServiceRecordFlowQuery query = GwMapUtil.convertToParam(param, AngelServiceRecordFlowQuery.class);
        return ResponseUtil.buildSuccResponse(angelServiceRecordApplication.queryAngelServiceRecordFlow(query));
    }


    /**
     * 运营端查看电子护理单
     * jdh_o2o_query_angel_record_flow
     * @param param
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "AngelServiceRecordGwExportImpl.queryAngelServiceManRecordFlow")
    public Response<AngelServiceRecordManDto> queryAngelServiceManRecordFlow(Map<String, String> param) {
        QueryAngelServiceRecordFlowCmd query = GwMapUtil.convertToParam(param, QueryAngelServiceRecordFlowCmd.class);
        log.info("AngelServiceRecordGwExportImpl -> queryAngelServiceManRecordFlow  query={}", JSON.toJSONString(query));
        return ResponseUtil.buildSuccResponse(angelWorkToolsApplication.queryAngelServiceRecordFlow(query));
    }
}
