package com.jdh.o2oservice.listener;

import com.alibaba.fastjson.JSON;
import com.jd.binlog.client.EntryMessage;
import com.jd.binlog.client.MessageDeserialize;
import com.jd.binlog.client.WaveEntry;
import com.jd.binlog.client.impl.JMQMessageDeserialize;
import com.jd.jmq.client.consumer.MessageListener;
import com.jd.jmq.client.springboot.annotation.JmqListener;
import com.jd.jmq.common.message.Message;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.common.enums.BusinessModeEnum;
import com.jdh.o2oservice.core.domain.promise.repository.db.VoucherRepository;
import com.jdh.o2oservice.core.domain.support.vertical.model.JdhVerticalBusiness;
import com.jdh.o2oservice.core.domain.support.vertical.repository.VerticalBusinessRepository;
import com.jdh.o2oservice.export.trade.cmd.JdOrderFullSaveCmd;
import com.jdh.o2oservice.listener.util.DBFieldChangeEventUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * @Description: 京东运单listener
 * @Author: wangpengfei144
 * @Date: 2024/6/26
 */
@Slf4j
@Service("jdhVoucherListener")
public class JdhVoucherListener implements MessageListener {

    /**
     * voucherRepository
     */
    @Resource
    private VoucherRepository voucherRepository;

    /**
     * verticalBusinessRepository
     */
    @Resource
    private VerticalBusinessRepository verticalBusinessRepository;

    /**
     * ducc
     */
    @Resource
    private DuccConfig duccConfig;

    /**
     * dbFieldChangeEventUtil
     */
    @Resource
    DBFieldChangeEventUtil dbFieldChangeEventUtil;

    /**
     * 序列化器
     */
    private static final MessageDeserialize<Message> DESERIALIZE = new JMQMessageDeserialize();

    /**
     * onMessage
     *
     * @param messages 消息
     * @throws Exception Exception
     */
    @Override
    @JmqListener(id= "jdhReachStoreConsumer", topics = {"${topics.jdhReachStoreConsumer.jdhVoucherBinlakeTopic}"})
    public void onMessage(List<Message> messages) throws Exception {
        if (messages == null || messages.isEmpty()) {
            return;
        }
        //将从JMQ消费者客户端获取的Binlake消费数据进行解析
        List<EntryMessage> entryMessages = DESERIALIZE.deserialize(messages);
        String dbConfig = duccConfig.getDbFieldChangeEventConfig();
        for(EntryMessage entryMessage : entryMessages){
            if (null == entryMessage.getRowChange()) {
                continue;
            }
            if (!entryMessage.getEntryType().equals(WaveEntry.EntryType.ROWDATA)) {
                continue;
            }
            List<WaveEntry.RowData> rowDatas = entryMessage.getRowChange().getRowDatasList();
            String tableName = entryMessage.getHeader().getTableName();
            log.info("JdhVoucherListener -> onMessage , rowData size={}", rowDatas.size());
            for (WaveEntry.RowData rowData : rowDatas) {
                try {
                    //组装参数
                    JdOrderFullSaveCmd cmd = convert2JdOrderFullSaveCmd(rowData);
                    log.info("JdhVoucherListener -> onMessage , cmd={}", JSON.toJSONString(cmd));
                    JdhVerticalBusiness verticalBusiness = verticalBusinessRepository.find(cmd.getVerticalCode());
                    log.info("JdhVoucherListener -> onMessage , verticalBusiness={}", JSON.toJSONString(verticalBusiness));
                    if(Objects.nonNull(verticalBusiness) &&
                            (BusinessModeEnum.SELF_TEST.getCode().equals(verticalBusiness.getBusinessModeCode())
                                    || BusinessModeEnum.ANGEL_TEST.getCode().equals(verticalBusiness.getBusinessModeCode())
                                    || BusinessModeEnum.ANGEL_TEST_NO_LABORATORY.getCode().equals(verticalBusiness.getBusinessModeCode())
                                    || BusinessModeEnum.ANGEL_CARE.getCode().equals(verticalBusiness.getBusinessModeCode())
                                    || BusinessModeEnum.SELF_TEST_TRANSPORT.getCode().equals(verticalBusiness.getBusinessModeCode()))){

                        // 表字段变更事件
                        dbFieldChangeEventUtil.dbFieldChange(dbConfig, tableName, rowData);
                    }
                }catch (Exception e){
                    log.error("JdhVoucherListener.onMessage --> error", e);
                    throw e;
                }
            }
        }
    }



    /**
     * 组装参数
     * @param rowData
     * @return
     */
    private JdOrderFullSaveCmd convert2JdOrderFullSaveCmd(WaveEntry.RowData rowData) {
        JdOrderFullSaveCmd resultDTO = new JdOrderFullSaveCmd();
        for (WaveEntry.Column column : rowData.getAfterColumnsList()) {
            if ("voucher_id".equals(column.getName()) && StringUtils.isNotBlank(column.getValue())) {
                resultDTO.setVoucherId(column.getValue());
            }else if ("yn".equals(column.getName()) && StringUtils.isNotBlank(column.getValue())) {
                resultDTO.setYn(column.getValue());
            }else if ("service_type".equals(column.getName()) && StringUtils.isNotBlank(column.getValue())) {
                resultDTO.setServiceType(column.getValue());
            }else if ("vertical_code".equals(column.getName()) && StringUtils.isNotBlank(column.getValue())) {
                resultDTO.setVerticalCode(column.getValue());
            }
        }
        return resultDTO;
    }
}

