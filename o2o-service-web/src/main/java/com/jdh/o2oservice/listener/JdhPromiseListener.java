package com.jdh.o2oservice.listener;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.jd.binlog.client.EntryMessage;
import com.jd.binlog.client.MessageDeserialize;
import com.jd.binlog.client.WaveEntry;
import com.jd.binlog.client.impl.JMQMessageDeserialize;
import com.jd.jmq.client.consumer.MessageListener;
import com.jd.jmq.client.springboot.annotation.JmqListener;
import com.jd.jmq.common.message.Message;
import com.jdh.o2oservice.application.promise.service.PromiseTransformApplication;
import com.jdh.o2oservice.application.trade.service.JdOrderFullApplication;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.enums.UmpKeyEnum;
import com.jdh.o2oservice.base.util.UmpUtil;
import com.jdh.o2oservice.common.enums.BusinessModeEnum;
import com.jdh.o2oservice.core.domain.support.vertical.model.JdhVerticalBusiness;
import com.jdh.o2oservice.core.domain.support.vertical.repository.VerticalBusinessRepository;
import com.jdh.o2oservice.export.trade.cmd.JdOrderFullSaveCmd;
import com.jdh.o2oservice.export.trade.dto.JdOrderFullDTO;
import com.jdh.o2oservice.listener.util.DBFieldChangeEventUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> limeng363
 * @Date : 2024-05-07 21:36
 * @Desc :
 */
@Slf4j
@Service("jdhPromiseListener")
public class JdhPromiseListener implements MessageListener {


    /**
     * jdOrderFullApplication
     */
    @Autowired
    private JdOrderFullApplication jdOrderFullApplication;

    /**
     * verticalBusinessRepository
     */
    @Autowired
    private VerticalBusinessRepository verticalBusinessRepository;

    @Resource
    private PromiseTransformApplication promiseTransformApplication;

    /**
     * ducc
     */
    @Resource
    private DuccConfig duccConfig;

    /**
     * dbFieldChangeEventUtil
     */
    @Resource
    DBFieldChangeEventUtil dbFieldChangeEventUtil;

    /**
     * 序列化器
     */
    private static final MessageDeserialize<Message> DESERIALIZE = new JMQMessageDeserialize();

    /**
     * onMessage
     *
     * @param messages 消息
     * @throws Exception Exception
     */
    @Override
    @JmqListener(id = "jdhReachStoreConsumer", topics = {"${topics.jdhReachStoreConsumer.jdhPromiseBinlakeTopic}"})
    public void onMessage(List<Message> messages) throws Exception {
        if (messages == null || messages.isEmpty()) {
            return;
        }
        //将从JMQ消费者客户端获取的Binlake消费数据进行解析
        List<EntryMessage> entryMessages = DESERIALIZE.deserialize(messages);
        String dbConfig = duccConfig.getDbFieldChangeEventConfig();
        for (EntryMessage entryMessage : entryMessages) {
            if (null == entryMessage.getRowChange()) {
                continue;
            }
            if (!entryMessage.getEntryType().equals(WaveEntry.EntryType.ROWDATA)) {
                continue;
            }
            List<WaveEntry.RowData> rowDatas = entryMessage.getRowChange().getRowDatasList();
            String tableName = entryMessage.getHeader().getTableName();
            for (WaveEntry.RowData rowData : rowDatas) {
                try {
                    JdOrderFullSaveCmd cmd = new JdOrderFullSaveCmd();
                    for (WaveEntry.Column column : rowData.getAfterColumnsList()) {
                        if ("promise_id".equals(column.getName()) && StringUtils.isNotBlank(column.getValue())) {
                            cmd.setPromiseId(column.getValue());
                        } else if ("yn".equals(column.getName()) && StringUtils.isNotBlank(column.getValue())) {
                            cmd.setYn(column.getValue());
                        } else if ("service_type".equals(column.getName()) && StringUtils.isNotBlank(column.getValue())) {
                            cmd.setServiceType(column.getValue());
                        } else if ("vertical_code".equals(column.getName()) && StrUtil.isNotBlank(column.getValue())) {
                            cmd.setVerticalCode(column.getValue());
                        }
                    }
                    log.info("jdhPromiseListener -> onMessage , cmd: {}", JSON.toJSONString(cmd));
                    JdhVerticalBusiness verticalBusiness = verticalBusinessRepository.find(cmd.getVerticalCode());
                    log.info("jdhPromiseListener -> onMessage , verticalBusiness={}", JSON.toJSONString(verticalBusiness));

                    //到店小程序新增index， 转存履约单到es
                    boolean transferResult = promiseTransformApplication.transformEs(verticalBusiness, cmd);
                    if (!transferResult) {
                        log.error("[dhPromiseListener -> onMessage],转存履约单信息到es失败!");
                        UmpUtil.showWarnMsg(UmpKeyEnum.TRANSFER_PROMISE_TO_ES_ERROR, "履约单数据转存到es失败");
                    }

                    if (Objects.nonNull(verticalBusiness) &&
                            (BusinessModeEnum.SELF_TEST.getCode().equals(verticalBusiness.getBusinessModeCode())
                                    || BusinessModeEnum.ANGEL_TEST.getCode().equals(verticalBusiness.getBusinessModeCode())
                                    || BusinessModeEnum.ANGEL_TEST_NO_LABORATORY.getCode().equals(verticalBusiness.getBusinessModeCode())
                                    || BusinessModeEnum.ANGEL_CARE.getCode().equals(verticalBusiness.getBusinessModeCode())
                                    || BusinessModeEnum.SELF_TEST_TRANSPORT.getCode().equals(verticalBusiness.getBusinessModeCode()))) {

                        // 表字段变更事件
                        dbFieldChangeEventUtil.dbFieldChange(dbConfig, tableName, rowData);

                        List<JdOrderFullDTO> jdOrderFullDTOList = jdOrderFullApplication.queryByPromiseId(cmd.getPromiseId());
                        log.info("jdhPromiseListener -> onMessage , jdOrderFullDTOList:{}", JSON.toJSONString(jdOrderFullDTOList));
                        if (CollectionUtils.isEmpty(jdOrderFullDTOList)) {
                            log.info("jdhPromiseListener -> onMessage , jdOrderFullDTOList isEmpty");
                            throw new RuntimeException("查不到数据，重试");
                        }
                        for (JdOrderFullDTO fullDTO : jdOrderFullDTOList) {
                            //如果是逻辑删除
                            if (StrUtil.isNotBlank(cmd.getYn()) && cmd.getYn().equals("0")) {
                                jdOrderFullApplication.deleteById(Collections.singletonList(fullDTO.getId()));
                                continue;
                            }
                            jdOrderFullApplication.reloadFullOrderInfo(Long.parseLong(fullDTO.getPromiseId()));
                        }
                    }
                } catch (Exception e) {
                    log.error("jdhPromiseListener -> onMessage , error", e);
                    throw e;
                }
            }
        }
    }
}

