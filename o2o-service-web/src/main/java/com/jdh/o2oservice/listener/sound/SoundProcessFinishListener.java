package com.jdh.o2oservice.listener.sound;

import com.google.common.collect.Lists;
import com.jd.jmq.client.consumer.MessageListener;
import com.jd.jmq.common.message.Message;
import com.jd.medicine.base.common.util.CollectionUtil;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jd.medicine.base.common.util.StringUtil;
import com.jdh.o2oservice.application.angelpromise.service.AngelPromiseApplication;
import com.jdh.o2oservice.core.domain.support.sound.bo.SoundProcessTopicBO;
import com.jdh.o2oservice.export.angelpromise.cmd.SubmitSoundRecordingCmd;
import com.jdh.o2oservice.export.angelpromise.dto.SubFile;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025/6/3
 */
@Slf4j
@Service("soundProcessFinishListener")
public class SoundProcessFinishListener implements MessageListener {


    /**
     * 自动注入的AngelPromiseApplication实例，用于获取相关配置或服务。
     */
    @Autowired
    private AngelPromiseApplication angelPromiseApplication;


    /**
     * @param list
     * @throws Exception
     */
    @Override
//    @JmqListener(id= "jdhReachStoreConsumer", topics = {"${topics.jdhReachStoreConsumer.soundProcessFinishTopic}"})
    public void onMessage(List<Message> list) throws Exception {

        log.info("SoundProcessFinishListener -> onMessage start");
        if (CollectionUtil.isEmpty(list) ) {
            return;
        }
        for(Message message : list){
            this.processMessage(message);
        }

    }



    /**
     * 处理消息，更新工单上的录音文件ID。
     * @param message 消息对象，包含要处理的信息。
     */
    private void processMessage(Message message){

        String text = message.getText();
        SoundProcessTopicBO soundProcessTopicBO = JsonUtil.parseObject(text, SoundProcessTopicBO.class);

        //消医&服务者工单场景
        if (!StringUtil.equals(soundProcessTopicBO.getTenantNo(),"xyfl") || !StringUtil.equals(soundProcessTopicBO.getScene(),"workService")){
            return;
        }

        log.info("SoundProcessFinishListener -> processMessage soundProcessTopicBO={}",JsonUtil.toJSONString(soundProcessTopicBO));

        //更新工单上的录音文件ID。

        SubmitSoundRecordingCmd cmd = new SubmitSoundRecordingCmd();

        cmd.setWorkId(Long.valueOf(soundProcessTopicBO.getBusinessId()));
        SubFile subFile = new SubFile();
        subFile.setFileId(String.valueOf(soundProcessTopicBO.getFileId()));
        cmd.setFiles(Lists.newArrayList(subFile));
        angelPromiseApplication.updateSoundRecording(cmd);


    }





}
