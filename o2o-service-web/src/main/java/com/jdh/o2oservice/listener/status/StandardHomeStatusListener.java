package com.jdh.o2oservice.listener.status;


import com.alibaba.fastjson.JSON;
import com.jd.jmq.client.producer.MessageProducer;
import com.jd.jmq.client.springboot.annotation.JmqProducer;
import com.jd.jmq.common.exception.JMQException;
import com.jd.jmq.common.message.Message;
import com.jdh.o2oservice.application.angelpromise.service.AngelWorkApplication;
import com.jdh.o2oservice.application.foward.MapperHandler;
import com.jdh.o2oservice.application.medicalpromise.MedicalPromiseApplication;
import com.jdh.o2oservice.application.promise.service.PromiseApplication;
import com.jdh.o2oservice.application.trade.service.TradeApplication;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.enums.OrderStatusEnum;
import com.jdh.o2oservice.base.event.Event;
import com.jdh.o2oservice.base.processor.MapAutowiredKey;
import com.jdh.o2oservice.common.enums.MedicalPromiseStatusEnum;
import com.jdh.o2oservice.common.enums.ServiceTypeEnum;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelWorkAggregateEnum;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelWorkStatusEnum;
import com.jdh.o2oservice.core.domain.medpromise.enums.MedPromiseAggregateEnum;
import com.jdh.o2oservice.core.domain.promise.enums.PromiseAggregateEnum;
import com.jdh.o2oservice.core.domain.support.basic.enums.JdhFreezeEnum;
import com.jdh.o2oservice.core.domain.support.basic.enums.JdhPromiseStatusEnum;
import com.jdh.o2oservice.core.domain.trade.enums.TradeAggregateEnum;
import com.jdh.o2oservice.export.angelpromise.dto.AngelWorkDto;
import com.jdh.o2oservice.export.medicalpromise.dto.MedicalPromiseDTO;
import com.jdh.o2oservice.export.promise.dto.PromiseDto;
import com.jdh.o2oservice.export.trade.dto.JdOrderDTO;
import com.jdh.o2oservice.export.trade.dto.OrderEventMessageDTO;
import com.jdh.o2oservice.export.trade.enums.StandardHomeStatusEnum;
import com.jdh.o2oservice.listener.handler.AbstractHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * 预约回调接口监听
 *
 * @author: yangxiyu
 * @date: 2022/4/20 2:26 下午
 * @version: 1.0
 */
@Slf4j
@Service("standardHomeStatusListener")
public class StandardHomeStatusListener extends AbstractHandler<Event> implements MapAutowiredKey {


    @Value("${topics.event.o2oCoreEventTopic}")
    private String handlerTopic;

    /**
     * promiseApplication
     */
    @Resource
    @Lazy
    private PromiseApplication promiseApplication;

    @Resource
    @Lazy
    private TradeApplication tradeApplication;

    @Resource
    @Lazy
    private AngelWorkApplication angelWorkApplication;

    @Resource
    @Lazy
    private MedicalPromiseApplication medicalPromiseApplication;

    @Autowired
    @Lazy
    private DuccConfig duccConfig;

    @JmqProducer(name = "reachStoreProducer")
    private MessageProducer reachStoreProducer;

    @Value("${topics.order.standardHomeStatusTopic}")
    private String standardHomeStatusTopic;


    @Override
    public String getMapKey() {
        return handlerTopic;
    }

    /**
     * 在解析消息体前进行消息处理的判断，不需要的消息直接过滤点或者不进行处理（使用场景消息处理限流等）
     * 如不需要这个过滤可给空实现(直接返回false，消息不被丢弃)
     *
     * @param message
     * @return true ：【丢弃】当前消息  false ：【保留】当前消息，后续对消息进行处理
     */
    @Override
    public boolean preFilterOfDiscardMessage(Message message) {
        //消息检查
        if (Objects.isNull(message) || StringUtils.isBlank(message.getText())) {
            return true;
        }
        return false;
    }

    /**
     * 解析消息
     *
     * @param message
     * @return
     */
    @Override
    public Event analysisMessage(Message message) {
        return JSON.parseObject(message.getText(), Event.class);
    }

    /**
     * 根据接到的mq消息解析出的对象，丢弃业务不需要的消息
     * 如不需要这个过滤可给空实现(直接返回false，消息不被丢弃)
     *
     * @param event
     * @return true ：【丢弃】当前消息  false ：【保留】当前消息，后续对消息进行处理
     */
    @Override
    public boolean filterOfDiscardMessage(Event event) {
        if (Objects.isNull(event)) {
            return true;
        }

        if (Objects.isNull(event.getAggregateCode())) {
            return true;
        }
        // 过滤掉非订单、履约、护士工单、实验室检测单的消息
        if (event.getAggregateCode().equalsIgnoreCase(TradeAggregateEnum.ORDER.getCode())
                || event.getAggregateCode().equalsIgnoreCase(PromiseAggregateEnum.PROMISE.getCode())
                || event.getAggregateCode().equalsIgnoreCase(AngelWorkAggregateEnum.WORK.getCode())
                || event.getAggregateCode().equalsIgnoreCase(MedPromiseAggregateEnum.MED_PROMISE.getCode())
        ) {
            return false;
        }

        return true;
    }

    /**
     * 转投环境
     *
     * @param message
     * @param event
     * @return
     */
    @Override
    public boolean transferToYf(Message message, Event event) {
        return false;
    }

    /**
     * 业务处理
     *
     * @param event
     */
    @Override
    public void dealMessage(Event event) {
        OrderEventMessageDTO orderEventMessageDTO = new OrderEventMessageDTO();
        Message message = new Message(standardHomeStatusTopic, JSON.toJSONString(orderEventMessageDTO), String.valueOf(event.getEventId()));
        try {
            reachStoreProducer.send(message);
        } catch (JMQException e) {
            log.error("StandardHomeStatusListener->dealMessage reachStoreProducer send error", e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 计算映射状态
     * @param jdOrderDTOList 子单列表
     * @param promiseDto 履约单信息
     * @param angelWorkDto 工单信息
     * @param medicalPromiseDTOList 检测单列表
     * @return 标准化状态枚举
     */
    private StandardHomeStatusEnum getStandardHomeStatusEnum(List<JdOrderDTO> jdOrderDTOList, PromiseDto promiseDto, AngelWorkDto angelWorkDto, List<MedicalPromiseDTO> medicalPromiseDTOList) {
        // 获取主要状态信息
        Integer orderStatus = null;
        Integer promiseStatus = null;
        Integer workStatus = null;
        Integer medicalPromiseStatus = null;
        Integer medPromiseFreeze = null;
        String serviceType = null;

        // 从订单列表中获取状态信息
        if (CollectionUtils.isNotEmpty(jdOrderDTOList)) {
            JdOrderDTO mainOrder = jdOrderDTOList.get(0);
            orderStatus = mainOrder.getOrderStatus();
            serviceType = mainOrder.getServiceType();
        }

        // 从履约单中获取状态信息
        if (Objects.nonNull(promiseDto)) {
            promiseStatus = promiseDto.getPromiseStatus();
            medPromiseFreeze = promiseDto.getFreeze();
        }

        // 从工单中获取状态信息
        if (Objects.nonNull(angelWorkDto)) {
            workStatus = angelWorkDto.getStatus();
        }

        // 从检测单中获取状态信息
        if (CollectionUtils.isNotEmpty(medicalPromiseDTOList)) {
            MedicalPromiseDTO medicalPromise = medicalPromiseDTOList.get(0);
            medicalPromiseStatus = medicalPromise.getStatus();
            medPromiseFreeze = medicalPromise.getFreeze();
        }

        // 按照优先级判断状态
        // 1. 退款相关状态（最高优先级）
        if (Objects.nonNull(medicalPromiseStatus) && Objects.nonNull(medPromiseFreeze) &&
            !MedicalPromiseStatusEnum.INVALID.getStatus().equals(medicalPromiseStatus) &&
            JdhFreezeEnum.FREEZE.getStatus().equals(medPromiseFreeze)) {
            return StandardHomeStatusEnum.ORDER_REFUNDING;
        }

        if ((Objects.nonNull(orderStatus) && OrderStatusEnum.ORDER_REFUND.getStatus().equals(orderStatus)) ||
            (Objects.nonNull(medicalPromiseStatus) && MedicalPromiseStatusEnum.INVALID.getStatus().equals(medicalPromiseStatus))) {
            return StandardHomeStatusEnum.ORDer_REFUNDED;
        }

        // 2. 预约取消相关状态
        if (Objects.nonNull(promiseStatus)) {
            if (JdhPromiseStatusEnum.CANCEL_ING.getStatus().equals(promiseStatus)) {
                return StandardHomeStatusEnum.PROMISE_WAIT_APPOINT; // 取消预约中，回到待预约状态
            }
            if (JdhPromiseStatusEnum.CANCEL_SUCCESS.getStatus().equals(promiseStatus) ||
                JdhPromiseStatusEnum.CANCEL_FAIL.getStatus().equals(promiseStatus)) {
                return StandardHomeStatusEnum.PROMISE_WAIT_APPOINT; // 取消成功或失败，回到待预约状态
            }
        }

        // 3. 检测流程状态
        if (Objects.nonNull(medicalPromiseStatus)) {
            if (MedicalPromiseStatusEnum.MEDICAL_CHECK_WAIT.getStatus().equals(medicalPromiseStatus)) {
                return StandardHomeStatusEnum.MEDICAL_PROMISE_GO_LAB;
            }
            if (MedicalPromiseStatusEnum.MEDICAL_CHECK_ING.getStatus().equals(medicalPromiseStatus)) {
                return StandardHomeStatusEnum.MEDICAL_PROMISE_TESTING;
            }
            if (MedicalPromiseStatusEnum.COLLECTED.getStatus().equals(medicalPromiseStatus)) {
                return StandardHomeStatusEnum.WORK_SAMPLED;
            }
            if (MedicalPromiseStatusEnum.COMPLETED.getStatus().equals(medicalPromiseStatus)) {
                if (ServiceTypeEnum.TEST.getServiceType().equals(serviceType)) {
                    return StandardHomeStatusEnum.MEDICAL_PROMISE_REPORTED;
                } else if (ServiceTypeEnum.CARE.getServiceType().equals(serviceType)) {
                    return StandardHomeStatusEnum.WORK_FINISHED;
                }
            }
        }

        // 4. 工单状态
        if (Objects.nonNull(workStatus) && AngelWorkStatusEnum.COMPLETED.getType().equals(workStatus)) {
            return StandardHomeStatusEnum.MEDICAL_PROMISE_DELIVERED;
        }

        // 5. 履约单状态
        if (Objects.nonNull(promiseStatus)) {
            if (JdhPromiseStatusEnum.WAIT_PROMISE.getStatus().equals(promiseStatus)) {
                return StandardHomeStatusEnum.PROMISE_WAIT_APPOINT;
            }
            if (JdhPromiseStatusEnum.APPOINTMENT_ING.getStatus().equals(promiseStatus) ||
                JdhPromiseStatusEnum.MODIFY_ING.getStatus().equals(promiseStatus)) {
                return StandardHomeStatusEnum.DISPATCH_WAIT_RECEIVE;
            }
            if (JdhPromiseStatusEnum.APPOINTMENT_SUCCESS.getStatus().equals(promiseStatus) ||
                JdhPromiseStatusEnum.MODIFY_SUCCESS.getStatus().equals(promiseStatus)) {
                return StandardHomeStatusEnum.WORK_STARTED;
            }
            if (JdhPromiseStatusEnum.SERVICE_READY.getStatus().equals(promiseStatus)) {
                return StandardHomeStatusEnum.WORK_GO_HOME;
            }
            if (JdhPromiseStatusEnum.SERVICING.getStatus().equals(promiseStatus)) {
                return StandardHomeStatusEnum.WORK_SERVICING;
            }
        }

        // 6. 默认状态 - 如果订单状态是待支付
        if (Objects.nonNull(orderStatus) && OrderStatusEnum.ORDER_WAIT_PAY.getStatus().equals(orderStatus)) {
            return StandardHomeStatusEnum.ORDER_WAIT_PAY;
        }

        // 默认返回待预约状态
        return StandardHomeStatusEnum.PROMISE_WAIT_APPOINT;
    }
}
