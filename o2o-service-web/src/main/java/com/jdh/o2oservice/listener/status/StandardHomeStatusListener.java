package com.jdh.o2oservice.listener.status;


import com.alibaba.fastjson.JSON;
import com.jd.jmq.client.producer.MessageProducer;
import com.jd.jmq.client.springboot.annotation.JmqProducer;
import com.jd.jmq.common.exception.JMQException;
import com.jd.jmq.common.message.Message;
import com.jdh.o2oservice.application.angelpromise.service.AngelWorkApplication;
import com.jdh.o2oservice.application.foward.MapperHandler;
import com.jdh.o2oservice.application.medicalpromise.MedicalPromiseApplication;
import com.jdh.o2oservice.application.promise.service.PromiseApplication;
import com.jdh.o2oservice.application.trade.service.TradeApplication;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.enums.OrderStatusEnum;
import com.jdh.o2oservice.base.event.Event;
import com.jdh.o2oservice.base.processor.MapAutowiredKey;
import com.jdh.o2oservice.common.enums.MedicalPromiseStatusEnum;
import com.jdh.o2oservice.common.enums.ServiceTypeEnum;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelWorkAggregateEnum;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelWorkStatusEnum;
import com.jdh.o2oservice.core.domain.medpromise.enums.MedPromiseAggregateEnum;
import com.jdh.o2oservice.core.domain.promise.enums.PromiseAggregateEnum;
import com.jdh.o2oservice.core.domain.support.basic.enums.JdhFreezeEnum;
import com.jdh.o2oservice.core.domain.support.basic.enums.JdhPromiseStatusEnum;
import com.jdh.o2oservice.core.domain.trade.enums.TradeAggregateEnum;
import com.jdh.o2oservice.export.angelpromise.dto.AngelWorkDto;
import com.jdh.o2oservice.export.promise.dto.PromiseDto;
import com.jdh.o2oservice.export.trade.dto.JdOrderDTO;
import com.jdh.o2oservice.export.trade.dto.OrderEventMessageDTO;
import com.jdh.o2oservice.export.trade.enums.StandardHomeStatusEnum;
import com.jdh.o2oservice.listener.handler.AbstractHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * 预约回调接口监听
 *
 * @author: yangxiyu
 * @date: 2022/4/20 2:26 下午
 * @version: 1.0
 */
@Slf4j
@Service("standardHomeStatusListener")
public class StandardHomeStatusListener extends AbstractHandler<Event> implements MapAutowiredKey {


    @Value("${topics.event.o2oCoreEventTopic}")
    private String handlerTopic;

    /**
     * promiseApplication
     */
    @Resource
    @Lazy
    private PromiseApplication promiseApplication;

    @Resource
    @Lazy
    private TradeApplication tradeApplication;

    @Resource
    @Lazy
    private AngelWorkApplication angelWorkApplication;

    @Resource
    @Lazy
    private MedicalPromiseApplication medicalPromiseApplication;

    @Autowired
    @Lazy
    private DuccConfig duccConfig;

    @JmqProducer(name = "reachStoreProducer")
    private MessageProducer reachStoreProducer;

    @Value("${topics.order.standardHomeStatusTopic}")
    private String standardHomeStatusTopic;


    @Override
    public String getMapKey() {
        return handlerTopic;
    }

    /**
     * 在解析消息体前进行消息处理的判断，不需要的消息直接过滤点或者不进行处理（使用场景消息处理限流等）
     * 如不需要这个过滤可给空实现(直接返回false，消息不被丢弃)
     *
     * @param message
     * @return true ：【丢弃】当前消息  false ：【保留】当前消息，后续对消息进行处理
     */
    @Override
    public boolean preFilterOfDiscardMessage(Message message) {
        //消息检查
        if (Objects.isNull(message) || StringUtils.isBlank(message.getText())) {
            return true;
        }
        return false;
    }

    /**
     * 解析消息
     *
     * @param message
     * @return
     */
    @Override
    public Event analysisMessage(Message message) {
        return JSON.parseObject(message.getText(), Event.class);
    }

    /**
     * 根据接到的mq消息解析出的对象，丢弃业务不需要的消息
     * 如不需要这个过滤可给空实现(直接返回false，消息不被丢弃)
     *
     * @param event
     * @return true ：【丢弃】当前消息  false ：【保留】当前消息，后续对消息进行处理
     */
    @Override
    public boolean filterOfDiscardMessage(Event event) {
        if (Objects.isNull(event)) {
            return true;
        }

        if (Objects.isNull(event.getAggregateCode())) {
            return true;
        }
        // 过滤掉非订单、履约、护士工单、实验室检测单的消息
        if (event.getAggregateCode().equalsIgnoreCase(TradeAggregateEnum.ORDER.getCode())
                || event.getAggregateCode().equalsIgnoreCase(PromiseAggregateEnum.PROMISE.getCode())
                || event.getAggregateCode().equalsIgnoreCase(AngelWorkAggregateEnum.WORK.getCode())
                || event.getAggregateCode().equalsIgnoreCase(MedPromiseAggregateEnum.MED_PROMISE.getCode())
        ) {
            return false;
        }

        return true;
    }

    /**
     * 转投环境
     *
     * @param message
     * @param event
     * @return
     */
    @Override
    public boolean transferToYf(Message message, Event event) {
        return false;
    }

    /**
     * 业务处理
     *
     * @param event
     */
    @Override
    public void dealMessage(Event event) {
        OrderEventMessageDTO orderEventMessageDTO = new OrderEventMessageDTO();
        Message message = new Message(standardHomeStatusTopic, JSON.toJSONString(orderEventMessageDTO), String.valueOf(event.getEventId()));
        try {
            reachStoreProducer.send(message);
        } catch (JMQException e) {
            log.error("StandardHomeStatusListener->dealMessage reachStoreProducer send error", e);
            throw new RuntimeException(e);
        }
    }

    private StandardHomeStatusEnum getStandardHomeStatusEnum(List<JdOrderDTO> jdOrderDTOList, PromiseDto promiseDto, AngelWorkDto angelWorkDto, ) {
        if(!MedicalPromiseStatusEnum.INVALID.getStatus().toString().equals(jdOrderFull.getMedicalPromiseStatus()) && JdhFreezeEnum.FREEZE.getStatus().toString().equals(jdOrderFull.getMedPromiseFreeze())){
            jdOrderFull.setCommonStatusStr("退款中");
        } else if(OrderStatusEnum.ORDER_REFUND.getStatus().toString().equals(jdOrderFull.getOrderStatus()) || MedicalPromiseStatusEnum.INVALID.getStatus().toString().equals(jdOrderFull.getMedicalPromiseStatus())){
            jdOrderFull.setCommonStatusStr("已退款");
        } else if(JdhPromiseStatusEnum.CANCEL_ING.getStatus().toString().equals(jdOrderFull.getPromiseStatus())){
            jdOrderFull.setCommonStatusStr("取消预约中");
        } else if(JdhPromiseStatusEnum.CANCEL_SUCCESS.getStatus().toString().equals(jdOrderFull.getPromiseStatus())){
            jdOrderFull.setCommonStatusStr("取消预约成功");
        } else if(JdhPromiseStatusEnum.CANCEL_FAIL.getStatus().toString().equals(jdOrderFull.getPromiseStatus())){
            jdOrderFull.setCommonStatusStr("取消预约失败");
        } else if(MedicalPromiseStatusEnum.MEDICAL_CHECK_WAIT.getStatus().toString().equals(jdOrderFull.getMedicalPromiseStatus())){
            jdOrderFull.setCommonStatusStr("送检中");
        } else if(MedicalPromiseStatusEnum.MEDICAL_CHECK_ING.getStatus().toString().equals(jdOrderFull.getMedicalPromiseStatus())){
            jdOrderFull.setCommonStatusStr("检测中");
        } else if( MedicalPromiseStatusEnum.COLLECTED.getStatus().toString().equals(jdOrderFull.getMedicalPromiseStatus())){
            jdOrderFull.setCommonStatusStr("采样完成");
        } else if( MedicalPromiseStatusEnum.COMPLETED.getStatus().toString().equals(jdOrderFull.getMedicalPromiseStatus())){
            if(ServiceTypeEnum.TEST.getServiceType().equals(jdOrderFull.getServiceType())){
                jdOrderFull.setCommonStatusStr("已出报告");
            }else if(ServiceTypeEnum.CARE.getServiceType().equals(jdOrderFull.getServiceType())){
                jdOrderFull.setCommonStatusStr("服务完成");
            }
        } else if(AngelWorkStatusEnum.COMPLETED.getType().toString().equals(jdOrderFull.getWorkStatus())){
            jdOrderFull.setCommonStatusStr("已送达");
        } else if(JdhPromiseStatusEnum.WAIT_PROMISE.getStatus().toString().equals(jdOrderFull.getPromiseStatus())){
            jdOrderFull.setCommonStatusStr("待预约");
        } else if(JdhPromiseStatusEnum.APPOINTMENT_ING.getStatus().toString().equals(jdOrderFull.getPromiseStatus()) || JdhPromiseStatusEnum.MODIFY_ING.getStatus().toString().equals(jdOrderFull.getPromiseStatus())){
            jdOrderFull.setCommonStatusStr("待接单");
        } else if(JdhPromiseStatusEnum.APPOINTMENT_SUCCESS.getStatus().toString().equals(jdOrderFull.getPromiseStatus()) || JdhPromiseStatusEnum.MODIFY_SUCCESS.getStatus().toString().equals(jdOrderFull.getPromiseStatus())){
            jdOrderFull.setCommonStatusStr("已接单");
        } else if(JdhPromiseStatusEnum.SERVICE_READY.getStatus().toString().equals(jdOrderFull.getPromiseStatus())){
            jdOrderFull.setCommonStatusStr("开始上门");
        } else if(JdhPromiseStatusEnum.SERVICING.getStatus().toString().equals(jdOrderFull.getPromiseStatus())){
            jdOrderFull.setCommonStatusStr("服务中");
        }
    }
}
