package com.jdh.o2oservice.test.appliacation.support;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.googlecode.aviator.AviatorEvaluator;
import com.googlecode.aviator.Expression;
import com.jdh.o2oservice.StartApplication;
import com.jdh.o2oservice.core.domain.support.price.context.PricingServiceCalculateContext;
import com.jdh.o2oservice.export.promise.dto.PromiseAppointmentTimeDto;
import com.jdh.o2oservice.export.promise.dto.PromiseDto;
import com.jdh.o2oservice.export.settlement.dto.AngelSettlementConfigDto;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName PriceCalucateTest
 * @Description
 * <AUTHOR>
 * @Date 2025/5/19 15:22
 **/
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = StartApplication.class)
public class PriceCalculateTest {


    public static void main(String[] args) {
        testNightDoorFee();
    }

    /**
     * 测试夜间服务费
     */
    //@Test
    public static void testNightDoorFee() {
        String expression = "use cn.hutool.core.date.DateUtil;" +
                "use cn.hutool.core.date.DateField;" +
                "let baseCheck = jdhSettlementAreaFeeConfig != nil && jdhSettlementAreaFeeConfig.nightDoorFee != nil && promiseDto != nil && promiseDto.appointmentTime != nil && promiseDto.appointmentTime.appointmentStartTime != nil && include(seq.list('nhHomeCare','xfylHomeCare','xfylVtpHomeCare'),promiseDto.verticalCode);" +
                "if (!baseCheck) { return baseCheck;} " +
                "let start = DateUtil.beginOfDay(promiseDto.appointmentTime.appointmentStartTime);" +
                "let careStartTime = DateUtil.offsetHour(start, 8);" +
                "let careStartTime = DateUtil.offsetMinute(careStartTime, 0);" +
                "let careStartTime = DateUtil.offsetSecond(careStartTime, 0);" +
                "" +
                "let careEndTime = DateUtil.offsetHour(start, 20);" +
                "let careEndTime = DateUtil.offsetMinute(careEndTime, 20);" +
                "let careEndTime = DateUtil.offsetSecond(careEndTime, 20);" +
                "return careEndTime;";

        // 编译表达式
        Expression compiledExp = AviatorEvaluator.compile(expression, true);
        HashMap<String, Object> map = new HashMap<>();
        PromiseDto promiseDto = new PromiseDto();
        promiseDto.setVerticalCode("xfylHomeCare");
        PromiseAppointmentTimeDto promiseAppointmentTimeDto = new PromiseAppointmentTimeDto();
        promiseAppointmentTimeDto.setAppointmentStartTime(DateUtil.parseDateTime("2025-06-11 20:15:00"));
        promiseDto.setAppointmentTime(promiseAppointmentTimeDto);
        map.put("promiseDto", promiseDto);

        AngelSettlementConfigDto angelSettlementConfigDto = new AngelSettlementConfigDto();
        angelSettlementConfigDto.setNightDoorFee("300");
        map.put("jdhSettlementAreaFeeConfig", angelSettlementConfigDto);


        // 计算结果
        System.out.println("Total Amount: " + compiledExp.execute(map));
    }

    /**
     * 测试夜间服务费
     */
    //@Test
    public static void testMaterialFee() {
        String expression = "let totalAmount = 0.0; \n" +
                "for medicalPromise in medicalPromiseDtoList {      \n" +
                "    let serviceItemId = medicalPromise.serviceItemId; \n" +
                "    if (serviceItemId == nil) {        continue;    } \n" +
                "    let item = itemFeeConfig[long(serviceItemId)];\n" +
                "    if (item == nil) {        continue;    } \n" +
                "    let materialIds = item.materialIdNeedList; \n" +
                "    if (materialIds != nil) {\n" +
                "        for materialId in materialIds {              \n" +
                "            let material = materialFeeConfig[materialId];             \n" +
                "            if (material == nil) {                continue;            }                 \n" +
                "            let price = (jdhAngelDto.jobNature == 1) ? material.selfAngelSettlementPrice : material.partAngelSettlementPrice;            \n" +
                "            totalAmount = totalAmount + (price != nil ? price : 0.0);          \n" +
                "        }\n" +
                "    }\n" +
                "}\n" +
                "return totalAmount * decimal(angelSettlementFeeRatioConfig.MATERIAL_FEE);";

        PricingServiceCalculateContext context = JSON.parseObject("{\"angelId\":164274347966823,\"factObjectMap\":{\"NIGHT_SERVICE_FEE\":100.00,\"HOME_VISIT\":80.00,\"NIGHT_SERVICE_FEE_SETTLE\":100.00,\"angelServiceFee\":41.16,\"nightServiceFee\":100.00,\"timePeriodAppointmentTypeImmediately\":0.00,\"homeVisit\":80.00,\"DYNAMIC_SETTLE\":0.00,\"MATERIAL_FEE\":42.00,\"itemFeeConfig\":{\"156076212225319\":{\"ageRange\":[\"0\",\"100\"],\"angelBasicSettlementPrice\":102.90,\"angelBasicSettlementPriceStr\":\"102.90\",\"angelSkillCodeList\":[\"155097740869705\"],\"angelSkillList\":[{\"angelSkillCode\":\"155097740869705\",\"angelSkillId\":155097740869705,\"angelSkillName\":\"检测类技能\",\"createTime\":1715867065000,\"id\":15,\"itemType\":2,\"updateTime\":1715867065000,\"yn\":1}],\"createTime\":1718387170000,\"id\":12849,\"indicatorString\":\"测试浓度公式计算、流感嗜血杆菌、肺炎链球菌、肺炎衣原体、人冠状病毒、人副流感病毒、合胞病毒、肺炎支原体、新型冠状病毒、人鼻病毒、人腺病毒、甲流\",\"itemId\":156076212225319,\"itemName\":\"呼吸道病毒细菌12联检\",\"itemNameEn\":\"12 joint tests for respiratory viruses and bacteria\",\"itemSource\":1,\"itemType\":2,\"materialIdNeedList\":[154639235285063],\"materialList\":[{\"materialPackageDetail\":\"针管\",\"materialPackageId\":154639235285063,\"materialPackageName\":\"测试耗材0506\",\"requiredFlag\":1}],\"reportShowType\":1,\"sampleType\":1,\"serviceDuration\":60,\"serviceIndicatorDtoList\":[{\"createTime\":1735261715000,\"createUser\":\"ext.yangkaihua1\",\"firstIndicatorCategory\":\"10000\",\"firstIndicatorCategoryName\":\"实验室诊查\",\"indicatorId\":\"165266123653210\",\"indicatorName\":\"测试浓度公式计算\",\"jdhWikiId\":\"1\",\"secondIndicatorCategory\":\"280000\",\"secondIndicatorCategoryName\":\"临床免疫学检验\",\"tags\":\"1\",\"testResultType\":1,\"thirdIndicatorCategory\":\"310000\",\"thirdIndicatorCategoryName\":\"感染免疫学检验\",\"updateTime\":1742378721000,\"updateUser\":\"ext.yangkaihua1\"},{\"createTime\":1718372199000,\"firstIndicatorCategory\":\"10000\",\"firstIndicatorCategoryName\":\"实验室诊查\",\"indicatorId\":\"156411152564983\",\"indicatorName\":\"流感嗜血杆菌\",\"jdhWikiId\":\"12082821\",\"secondIndicatorCategory\":\"340000\",\"secondIndicatorCategoryName\":\"临床微生物与寄生虫学检验\",\"testResultType\":1,\"thirdIndicatorCategory\":\"350000\",\"thirdIndicatorCategoryName\":\"细菌检验\",\"updateTime\":1741758977000,\"updateUser\":\"zhangyuchen29\"},{\"createTime\":1718372199000,\"firstIndicatorCategory\":\"10000\",\"firstIndicatorCategoryName\":\"实验室诊查\",\"indicatorId\":\"156411152564775\",\"indicatorName\":\"肺炎链球菌\",\"jdhWikiId\":\"12082770\",\"secondIndicatorCategory\":\"340000\",\"secondIndicatorCategoryName\":\"临床微生物与寄生虫学检验\",\"tags\":\"1\",\"testResultType\":1,\"thirdIndicatorCategory\":\"350000\",\"thirdIndicatorCategoryName\":\"细菌检验\",\"updateTime\":1736237091000,\"updateUser\":\"wangpengfei144\"},{\"createTime\":1718372199000,\"firstIndicatorCategory\":\"10000\",\"firstIndicatorCategoryName\":\"实验室诊查\",\"indicatorId\":\"156411152564567\",\"indicatorName\":\"肺炎衣原体\",\"jdhWikiId\":\"12082771\",\"secondIndicatorCategory\":\"340000\",\"secondIndicatorCategoryName\":\"临床微生物与寄生虫学检验\",\"testResultType\":1,\"thirdIndicatorCategory\":\"400000\",\"thirdIndicatorCategoryName\":\"病毒检验\",\"updateTime\":1736237105000,\"updateUser\":\"wangpengfei144\"},{\"createTime\":1718372199000,\"firstIndicatorCategory\":\"10000\",\"firstIndicatorCategoryName\":\"实验室诊查\",\"indicatorId\":\"156411152564714\",\"indicatorName\":\"人冠状病毒\",\"jdhWikiId\":\"12082808\",\"secondIndicatorCategory\":\"340000\",\"secondIndicatorCategoryName\":\"临床微生物与寄生虫学检验\",\"testResultType\":1,\"thirdIndicatorCategory\":\"400000\",\"thirdIndicatorCategoryName\":\"病毒检验\",\"updateTime\":1736237122000,\"updateUser\":\"wangpengfei144\"},{\"createTime\":1718372199000,\"firstIndicatorCategory\":\"10000\",\"firstIndicatorCategoryName\":\"实验室诊查\",\"indicatorId\":\"156411152564698\",\"indicatorName\":\"人副流感病毒\",\"jdhWikiId\":\"12082815\",\"secondIndicatorCategory\":\"340000\",\"secondIndicatorCategoryName\":\"临床微生物与寄生虫学检验\",\"testResultType\":1,\"thirdIndicatorCategory\":\"400000\",\"thirdIndicatorCategoryName\":\"病毒检验\",\"updateTime\":1736237135000,\"updateUser\":\"wangpengfei144\"},{\"createTime\":1718372199000,\"firstIndicatorCategory\":\"10000\",\"firstIndicatorCategoryName\":\"实验室诊查\",\"indicatorId\":\"156411152564551\",\"indicatorName\":\"合胞病毒\",\"jdhWikiId\":\"12082813\",\"secondIndicatorCategory\":\"340000\",\"secondIndicatorCategoryName\":\"临床微生物与寄生虫学检验\",\"testResultType\":1,\"thirdIndicatorCategory\":\"400000\",\"thirdIndicatorCategoryName\":\"病毒检验\",\"updateTime\":1742378721000,\"updateUser\":\"ext.yangkaihua3\"},{\"createTime\":1718372199000,\"firstIndicatorCategory\":\"10000\",\"firstIndicatorCategoryName\":\"实验室诊查\",\"indicatorId\":\"156411152564535\",\"indicatorName\":\"肺炎支原体\",\"jdhWikiId\":\"12082773\",\"secondIndicatorCategory\":\"340000\",\"secondIndicatorCategoryName\":\"临床微生物与寄生虫学检验\",\"testResultType\":1,\"thirdIndicatorCategory\":\"400000\",\"thirdIndicatorCategoryName\":\"病毒检验\",\"updateTime\":1742378721000,\"updateUser\":\"zhangyuchen29\"},{\"createTime\":1718372198000,\"firstIndicatorCategory\":\"10000\",\"firstIndicatorCategoryName\":\"实验室诊查\",\"indicatorId\":\"156411152564311\",\"indicatorName\":\"新型冠状病毒\",\"jdhWikiId\":\"12082816\",\"secondIndicatorCategory\":\"340000\",\"secondIndicatorCategoryName\":\"临床微生物与寄生虫学检验\",\"tags\":\"1\",\"testResultType\":1,\"thirdIndicatorCategory\":\"400000\",\"thirdIndicatorCategoryName\":\"病毒检验\",\"updateTime\":1736237048000,\"updateUser\":\"wangpengfei144\"},{\"createTime\":1718373689000,\"firstIndicatorCategory\":\"10000\",\"firstIndicatorCategoryName\":\"实验室诊查\",\"indicatorId\":\"156411933754426\",\"indicatorName\":\"人鼻病毒\",\"jdhWikiId\":\"12082814\",\"secondIndicatorCategory\":\"340000\",\"secondIndicatorCategoryName\":\"临床微生物与寄生虫学检验\",\"testResultType\":1,\"thirdIndicatorCategory\":\"400000\",\"thirdIndicatorCategoryName\":\"病毒检验\",\"updateTime\":1743071338000,\"updateUser\":\"huangxianbin7\"},{\"createTime\":1718373689000,\"firstIndicatorCategory\":\"10000\",\"firstIndicatorCategoryName\":\"实验室诊查\",\"indicatorId\":\"156411933754617\",\"indicatorName\":\"人腺病毒\",\"jdhWikiId\":\"12082817\",\"secondIndicatorCategory\":\"340000\",\"secondIndicatorCategoryName\":\"临床微生物与寄生虫学检验\",\"testResultType\":1,\"thirdIndicatorCategory\":\"400000\",\"thirdIndicatorCategoryName\":\"病毒检验\",\"updateTime\":1743071338000,\"updateUser\":\"huangxianbin7\"},{\"createTime\":1718373689000,\"firstIndicatorCategory\":\"10000\",\"firstIndicatorCategoryName\":\"实验室诊查\",\"indicatorId\":\"156411933754410\",\"indicatorName\":\"甲流\",\"jdhWikiId\":\"12082811\",\"secondIndicatorCategory\":\"340000\",\"secondIndicatorCategoryName\":\"临床微生物与寄生虫学检验\",\"testResultType\":1,\"thirdIndicatorCategory\":\"400000\",\"thirdIndicatorCategoryName\":\"病毒检验\",\"updateTime\":1743071338000,\"updateUser\":\"huangxianbin7\"}],\"serviceResourceType\":2,\"sex\":[\"1\",\"2\"],\"technicalLevel\":100,\"testWay\":1,\"updateTime\":1745573472000,\"updateUser\":\"zhangyuchen29\",\"vendorType\":1,\"version\":27,\"yn\":1}},\"cityLevelConfig\":{\"cityConfigId\":170765540917249,\"level\":\"一级\",\"platformServiceCoefficient\":\"0.1\",\"platformSubsidyCoefficient\":\"0.1\",\"provinceCode\":\"1\",\"provinceName\":\"北京市\",\"selfServiceCoefficient\":\"0.8\",\"sidelineServiceCoefficient\":\"1\"},\"dynamic\":0.00,\"PLATFORM_SERVICE_FEE_SETTLE\":0.00,\"TIME_PERIOD_APPOINTMENT_TYPE_IMMEDIATELY\":0,\"TIME_PERIOD_APPOINTMENT_TYPE_IMMEDIATELY_SETTLE\":0.00,\"MATERIAL_FEE_SETTLE\":5.25,\"jdhAngelDto\":{\"angelId\":164274347966823,\"angelName\":\"测试王鹏飞\",\"angelPin\":\"jd_48de9e9fbd832\",\"auditProcessStatus\":1,\"cityName\":\"蚌埠市\",\"countyName\":\"蚌山区\",\"createTime\":1733370054000,\"fullAddress\":\"安徽-蚌埠市-蚌山区\",\"fullDepartmentName\":\"白内障科\",\"gender\":1,\"headImg\":\"jfs/t1/228512/34/5324/24330/65682a85F1acd24e0/be9495b716e612f3.png\",\"id\":584,\"idCard\":\"131121199810163214\",\"idCardImgFrontUrl\":\"jfs/t1/224284/18/22286/48373/675ad74aF60fefe50/c0991b91d3639b37.jpg\",\"idCardImgOppositeUrl\":\"jfs/t1/227502/12/32159/37178/675ad74aF72b33b3d/d1371d6dfee407c4.jpg\",\"idCardIndex\":\"kjIsFQkNQvbAkjIsFQkjIsFQkf0FPwkjIsFQkjIsFQmrgcWwmrgcWwm0ZpDAkjIsFQk6vjPAkjIsFQlTuVhwkNQvbAkf0FPwkjIsFQl/58+g\",\"idCardType\":1,\"jobNature\":1,\"nethpDocId\":*************,\"phone\":\"15810163320\",\"phoneIndex\":\"kjIsFQlq3qhQm0ZpDAkjIsFQk6vjPAkjIsFQlTuVhwkNQvbAkNQvbAkf0FPwk6vjPA\",\"professionTitleCodeList\":[\"36\"],\"provinceName\":\"安徽\",\"stationId\":168451487825921,\"stationMaster\":\"\",\"takeOrderStatus\":1,\"twoDepartmentName\":\"白内障科\",\"updateTime\":1744103388000,\"workIdentity\":1,\"yn\":1},\"addressInfo\":{\"addressDefault\":false,\"addressDetail\":\"北京朝阳区垡头街道翠城馨园\",\"addressType\":0,\"cityId\":72,\"cityName\":\"朝阳区\",\"countyId\":55662,\"countyName\":\"垡头街道\",\"id\":0,\"latitude\":0.0,\"longitude\":0.0,\"provinceId\":1,\"provinceName\":\"北京\",\"selected\":false},\"DISPATCH_MARKUP_FEE_SETTLE\":20.00,\"ANGEL_SERVICE_FEE\":126.40,\"materialFeeConfig\":{\"154639235285063\":{\"partAngelSettlementPrice\":10.50,\"selfAngelSettlementPrice\":21.00}},\"TIME_PERIOD_TIME_TYPE_HOLIDAY\":0,\"PLATFORM_SERVICE_FEE\":-32.64,\"angelSettlementFeeRatioConfig\":{\"NIGHT_SERVICE_FEE\":\"1\",\"HOME_VISIT\":\"1\",\"ANGEL_SERVICE_FEE\":\"0.5\",\"MATERIAL_FEE\":\"0.5\",\"TIME_PERIOD_TIME_TYPE_HOLIDAY\":\"1\",\"DISPATCH_MARKUP_FEE\":\"1\",\"PLATFORM_SERVICE_FEE\":\"0\",\"TIME_PERIOD_APPOINTMENT_TYPE_IMMEDIATELY\":\"1\",\"DYNAMIC\":\"1\",\"PLATFORM_SUBSIDY_FEE\":\"0\"},\"DYNAMIC\":0,\"ANGEL_SERVICE_FEE_SETTLE\":41.16,\"TIME_PERIOD_TIME_TYPE_HOLIDAY_SETTLE\":0.00,\"HOME_VISIT_SETTLE\":80.00,\"promiseDto\":{\"appointmentPhone\":\"17600747539\",\"appointmentTime\":{\"appointmentEndTime\":1745496000000,\"appointmentStartTime\":1745492400000,\"dateType\":2,\"isImmediately\":false},\"branch\":\"yfb\",\"code\":\"1250\",\"codeId\":\"170624500105297\",\"codePwd\":\"5634\",\"createTime\":1745482007000,\"createUser\":\"jd_5b89d0cb1f009\",\"expireDate\":1777046399000,\"freeze\":1,\"id\":36667,\"patients\":[{\"birthday\":{\"age\":31,\"birth\":\"1993-09-09\"},\"credentialNum\":{\"credentialNo\":\"130637199309090039\",\"credentialType\":1},\"gender\":1,\"marriage\":2,\"patientId\":12814171042799,\"phoneNumber\":{\"phone\":\"17600747539\"},\"promiseId\":170624500105265,\"promisePatientId\":170624500105281,\"relativesType\":1,\"userName\":{\"name\":\"测试不要说话\"},\"userPin\":\"jd_5b89d0cb1f009\",\"version\":11}],\"promiseExtends\":[{\"attribute\":\"orderPhone\",\"orderPhone\":\"17600747539\",\"orderRemark\":\"\",\"promiseId\":170624500105265,\"value\":\"17600747539\"},{\"attribute\":\"orderId\",\"orderPhone\":\"\",\"orderRemark\":\"\",\"promiseId\":170624500105265,\"value\":\"313771950017\"},{\"attribute\":\"intendedNurse\",\"orderPhone\":\"\",\"orderRemark\":\"\",\"promiseId\":170624500105265,\"value\":\"{\\\"recommendType\\\":1}\"},{\"attribute\":\"appointmentUserName\",\"orderPhone\":\"\",\"orderRemark\":\"\",\"promiseId\":170624500105265,\"value\":\"测试张先生\"},{\"attribute\":\"firstGenerateReport\",\"orderPhone\":\"\",\"orderRemark\":\"\",\"promiseId\":170624500105265,\"value\":\"1\"},{\"attribute\":\"lastGenerateReport\",\"orderPhone\":\"\",\"orderRemark\":\"\",\"promiseId\":170624500105265,\"value\":\"1\"}],\"promiseId\":170624500105265,\"promiseStatus\":11,\"promiseType\":1,\"serialNum\":0,\"serviceType\":\"test\",\"services\":[{\"promiseId\":170624500105265,\"serviceId\":100123440630,\"serviceName\":\"京东到家快检 护士采样呼吸道12联 病毒细菌上门核酸咳嗽发热感冒新冠抗原甲乙流支原体衣原体流感腺病毒检测\",\"tags\":0},{\"promiseId\":170624500105265,\"serviceId\":100164437084,\"serviceName\":\"呼吸道病毒细菌15联检 京东到家快检 咳嗽上门核酸检测 支原体新冠甲乙流 偏肺博卡肠道病毒【互医】\",\"tags\":0}],\"sourceVoucherId\":\"313771950017\",\"store\":{\"cityCode\":72,\"cityName\":\"朝阳区\",\"districtCode\":55662,\"districtName\":\"垡头街道\",\"provinceCode\":1,\"provinceName\":\"北京\",\"storeAddr\":\"北京朝阳区垡头街道翠城馨园\",\"storeId\":\"10119813866\"},\"updateTime\":1745939063000,\"userPin\":\"jd_5b89d0cb1f009\",\"version\":11,\"verticalCode\":\"nhHomeTest\",\"voucherId\":170624500105217},\"totalAmount\":41.16,\"medicalPromiseDtoList\":[{\"serviceType\":\"test\",\"flag\":\"0\",\"settleStatus\":1,\"waitingTestTimeOutStatus\":0,\"testingTimeOutStatus\":0,\"verticalCode\":\"nhHomeTest\",\"branch\":\"yfb\",\"promiseId\":170624500105265,\"freeze\":1,\"serviceItemName\":\"呼吸道病毒细菌12联检\",\"yn\":1,\"providerId\":**********,\"voucherId\":170624500105217,\"medicalPromiseId\":170624500105345,\"stationName\":\"测试天算码\",\"stationPhone\":\"15800000000\",\"serviceId\":100164437084,\"reportShowType\":1,\"stationId\":\"S168634579157002\",\"serviceItemId\":\"156076212225319\",\"promisePatientId\":170624500105281,\"specimenCode\":\"JD01015044232\",\"statusDesc\":\"已完成\",\"reportStatus\":1,\"updateUser\":\"jd_5b89d0cb1f009\",\"updateTime\":1745484757000,\"version\":7,\"stationAddress\":\"北京市亦庄经济开发区京东总部2号楼\",\"userPin\":\"jd_5b89d0cb1f009\",\"checkTime\":1745484423000,\"createTime\":1745482008000,\"flagDesc\":\"普通项目\",\"createUser\":\"jd_5b89d0cb1f009\",\"reportTime\":1745484755000,\"status\":6}],\"skuServicePriceMap\":{\"100164437084\":82.32,\"100123440630\":44.08},\"timePeriodTimeTypeHoliday\":0.00,\"isLastService\":true,\"PLATFORM_SUBSIDY_FEE_SETTLE\":0.00,\"dispatchMarkupFee\":20.00,\"coefficient\":0.1,\"DISPATCH_MARKUP_FEE\":20.00,\"PLATFORM_SUBSIDY_FEE\":3.26},\"feeAmountMap\":{\"NIGHT_SERVICE_FEE\":100.00,\"HOME_VISIT\":80.00,\"ANGEL_SERVICE_FEE\":41.16,\"MATERIAL_FEE\":5.25,\"TIME_PERIOD_TIME_TYPE_HOLIDAY\":0.00,\"DISPATCH_MARKUP_FEE\":20.00,\"PLATFORM_SERVICE_FEE\":0.00,\"TIME_PERIOD_APPOINTMENT_TYPE_IMMEDIATELY\":0.00,\"DYNAMIC\":0.00,\"PLATFORM_SUBSIDY_FEE\":0.00},\"orderId\":313713606542,\"promiseId\":170624500105265,\"scene\":\"angelSettlementPriceCalculate\",\"skuServiceAmountMap\":{}}", PricingServiceCalculateContext.class);
        // 编译表达式
        Expression compiledExp = AviatorEvaluator.compile(expression, true);

        // 计算结果
        System.out.println("Total Amount: " + compiledExp.execute(context.getFactObjectMap()));
    }

    /**
     *
     */
    //@Test
    public static void testImmediately(){
        String expression = "use cn.hutool.core.date.DateUtil;" +
                "let baseCheck = jdhSettlementAreaFeeConfig != nil && jdhSettlementAreaFeeConfig.immediatelyFee != nil && promiseDto != nil && promiseDto.createTime != nil && promiseDto.appointmentTime != nil && promiseDto.appointmentTime.appointmentStartTime != nil && include(seq.list('nhHomeCare','xfylHomeCare'),promiseDto.verticalCode);" +
                "if (!baseCheck) { return baseCheck;}" +
                "let date = DateUtil.offsetMinute(promiseDto.createTime, 120); " +
                "return date > promiseDto.appointmentTime.appointmentStartTime;";
        // 编译表达式
        Expression compiledExp = AviatorEvaluator.compile(expression, true);
        HashMap<String, Object> map = new HashMap<>();
        PromiseDto promiseDto = new PromiseDto();
        promiseDto.setVerticalCode("xfylVtpHomeCare");
        PromiseAppointmentTimeDto promiseAppointmentTimeDto = new PromiseAppointmentTimeDto();
        promiseAppointmentTimeDto.setAppointmentStartTime(DateUtil.parse("2025-05-30 10:45", "yyyy-MM-dd HH:mm"));
        promiseDto.setAppointmentTime(promiseAppointmentTimeDto);
        promiseDto.setCreateTime(DateUtil.parse("2025-05-30 10:36:55", "yyyy-MM-dd HH:mm:ss"));
        map.put("promiseDto", promiseDto);

        AngelSettlementConfigDto angelSettlementConfigDto = new AngelSettlementConfigDto();
        angelSettlementConfigDto.setImmediatelyFee("10");
        map.put("jdhSettlementAreaFeeConfig", angelSettlementConfigDto);


        // 计算结果
        System.out.println("Total Amount: " + compiledExp.execute(map));
    }
}