package com.jdh.o2oservice.test.appliacation.support.tts;

import java.io.Serializable;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;

public class WaveHeader implements Serializable {

    public static final short MONO = 1; //单通道
    public static final short PCM  = 1; //PCM编码格式
    public static final short PCM_8_BIT  = 8;  //pcm数据位数
    public static final short PCM_16_BIT = 16; //pcm数据位数
    public static final short PCM_32_BIT = 32; //pcm数据位数
    public static final int   FMT_LENGTH = 16;

    private static final byte[] riffTag = "RIFF".getBytes();  //RIFF标记
    private static final byte[] waveTag = "WAVE".getBytes(); //WAVE标记
    private static final byte[] fmtTag  = "fmt ".getBytes(); //fmt标记, 4位，空格勿删
    private static final byte[] dataTag = "data".getBytes(); //data标记

    private int fileLength; //原始数据长度（不包含RIFF和本字段共8个字节)
    private int fmtLength = FMT_LENGTH;  //"fmt"字段的长度，存储该子块的字节数（不含前面的Subchunk1ID和Subchunk1Size这8个字节）
    private short formatTag = PCM; //存储音频文件的编码格式，PCM其存储值为1
    private short channels;  //通道数，单通道(Mono)值为1，双通道(Stereo)值为2
    private int samplesPerSec;   //采样率
    private int avgBytesPerSec;  //音频数据传送速率,采样率*通道数*采样深度/8。(每秒存储的bit数，其值=SampleRate * NumChannels * BitsPerSample/8)
    private short blockAlign;	 //块对齐/帧大小，NumChannels * BitsPerSample/8
    private short bitsPerSample;		//pcm数据位数，一般为8,16,32等
    private int dataLength;//data数据长度

    private ByteBuffer buffer = null;

    public WaveHeader(int dataLength){
        this(dataLength, 8000, MONO, PCM_16_BIT);
    }

    public WaveHeader(int dataLength, int samplesPerSec, short channels, short bitsPerSample){
        this.dataLength = dataLength;
        this.samplesPerSec = samplesPerSec;
        this.channels = channels;
        this.bitsPerSample = bitsPerSample;

        //原始数据长度（不包含RIFF和本字段共8个字节)
        this.fileLength = dataLength + 44 - 8;
        //块对齐/帧大小，NumChannels * BitsPerSample/8
        this.blockAlign = (short) (channels * bitsPerSample /8);
        //音频数据传送速率,采样率*通道数*采样深度/8。(每秒存储的bit数，其值=SampleRate * NumChannels * BitsPerSample/8)
        this.avgBytesPerSec = blockAlign * samplesPerSec;
    }

    private void setFormatTag(short formatTag){
        this.formatTag = formatTag;
    }

    private void setFmtLength(int fmtLength){
        this.fmtLength = fmtLength;
    }

    public byte[] serialize(){
        if(buffer != null){
            return buffer.array();
        }

        buffer = ByteBuffer.allocate(44).order(ByteOrder.LITTLE_ENDIAN);

        buffer.put(riffTag);                //0-3: RIFF标记
        buffer.putInt(fileLength);          //4-7: 原始数据长度（不包含RIFF和本字段共8个字节)
        buffer.put(waveTag);                //8-11: WAVE标记
        buffer.put(fmtTag);                 //12-15: fmt标记, 4位，空格勿删
        buffer.putInt(fmtLength);           //16-19: "fmt"字段的长度，存储该子块的字节数（不含前面的Subchunk1ID和Subchunk1Size这8个字节）
        buffer.putShort(formatTag);         //20-21: 存储音频文件的编码格式，PCM其存储值为1
        buffer.putShort(channels);          //22-23: 通道数，单通道(Mono)值为1，双通道(Stereo)值为2
        buffer.putInt(samplesPerSec);       //24-27: 采样率
        buffer.putInt(avgBytesPerSec);      //28-31: 音频数据传送速率
        buffer.putShort(blockAlign);        //32-33: 块对齐/帧大小
        buffer.putShort(bitsPerSample);     //34-35: pcm数据位数，一般为8,16,32等
        buffer.put(dataTag);                //36-39: data标记
        buffer.putInt(dataLength);          //40-43: data数据长度

        return buffer.array();
    }

    public void setSamplesPerSec(int samplesPerSec) {
        this.samplesPerSec = samplesPerSec;
    }

}
