package com.jdh.o2oservice.test.repository;

import com.jdh.o2oservice.StartApplication;
import com.jdh.o2oservice.infrastructure.repository.db.dao.JdhDispatchDetailPoMapper;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhDispatchDetailPo;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/6 14:04
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = StartApplication.class)
public class DispatchTest {

    @Resource
    private JdhDispatchDetailPoMapper jdhDispatchDetailPoMapper;

    @Test
    public void test(){
        List<JdhDispatchDetailPo> updateBatchList = new ArrayList<>();
        JdhDispatchDetailPo detailPo = new JdhDispatchDetailPo();
        detailPo.setDispatchDetailId(123456L);
        detailPo.setDispatchDetailStatus(2);
        detailPo.setFreeze(1);
        jdhDispatchDetailPoMapper.batchUpdate(updateBatchList);
    }
}
