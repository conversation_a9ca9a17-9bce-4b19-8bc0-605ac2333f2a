package com.jdh.o2oservice.core.domain.product.repository.db;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jdh.o2oservice.base.model.Repository;
import com.jdh.o2oservice.core.domain.product.context.*;
import com.jdh.o2oservice.core.domain.product.model.*;

import java.util.List;

/**
 * @InterfaceName:JdhServiceItemRepository
 * @Description:
 * @Author: yaoqinghai
 * @Date: 2024/3/22 11:22
 * @Vserion: 1.0
 **/
public interface JdhServiceItemRepository extends Repository<ServiceItem, ServiceItemIdentifier> {

    /**
     * 分页查询服务项目
     *
     * @param serviceItemQueryContext
     * @return
     */
    Page<ServiceItem> queryServiceItemPageInfo(ServiceItemQueryContext serviceItemQueryContext);

    /**
     * 查询服务项目
     *
     * @param itemListQuery
     * @return
     */
    List<ServiceItem> queryServiceItemList(JdhItemListQueryContext itemListQuery);

    /**
     * 查询标准京东项目
     *
     * @param serviceItemQueryContext
     * @return
     */
    List<ServiceItem> queryJdhItemList(ServiceItemQueryContext serviceItemQueryContext);

    /**
     * 精确查询服务项目列表
     *
     * @param exactQueryContext
     * @return
     */
    List<ServiceItem> queryServiceItemExactList(ServiceItemExactQueryContext exactQueryContext);


    /**
     * 查询项目指标列表
     *
     * @param queryContext
     * @return
     */
    List<ServiceItemIndicatorRel> queryServiceItemIndicatorRel(JdhItemIndicatorRelQueryContext queryContext);


    /**
     * 查询项目耗材列表
     *
     * @param queryContext
     * @return
     */
    List<ServiceItemMaterialPackageRel> queryServiceItemMaterialPackageRel(JdhItemMaterialPackageRelQueryContext queryContext);


    /**
     * 查询项目技能列表
     *
     * @param queryContext
     * @return
     */
    List<ServiceItemAngelSkillRel> queryServiceItemAngelSkillRel(JdhItemAngelSkillRelQueryContext queryContext);

    /**
     * 修改护理单配置标识
     * @param serviceItemId
     * @param careFormConfig
     * @return
     */
    int updateCareFlag(Long serviceItemId, String careFormConfig);
}
