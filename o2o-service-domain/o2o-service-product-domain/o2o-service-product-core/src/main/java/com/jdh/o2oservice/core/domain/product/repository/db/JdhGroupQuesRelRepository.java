package com.jdh.o2oservice.core.domain.product.repository.db;


import com.jdh.o2oservice.base.model.Repository;
import com.jdh.o2oservice.core.domain.product.model.JdhGroupQuesRel;
import com.jdh.o2oservice.core.domain.product.model.JdhGroupQuesRelIdentifier;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/23
 * @description 题库
 */
public interface JdhGroupQuesRelRepository extends Repository<JdhGroupQuesRel, JdhGroupQuesRelIdentifier> {

    JdhGroupQuesRel findByCodeAndItemId(Long serviceItemId, String groupCode, String quesCode);

    List<JdhGroupQuesRel> findByItemId(Long serviceItemId);

    Boolean update(JdhGroupQuesRel jdhGroupQuesRel);
}
