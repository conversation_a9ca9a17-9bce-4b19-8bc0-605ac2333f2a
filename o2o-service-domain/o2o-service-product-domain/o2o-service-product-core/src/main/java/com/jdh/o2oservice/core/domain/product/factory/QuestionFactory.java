package com.jdh.o2oservice.core.domain.product.factory;

import com.alibaba.fastjson.JSON;
import com.jd.common.util.StringUtils;
import com.jdh.o2oservice.base.enums.YnStatusEnum;
import com.jdh.o2oservice.base.factory.GenerateIdFactory;
import com.jdh.o2oservice.base.util.SpringUtil;
import com.jdh.o2oservice.core.domain.product.context.JdhQuestionContext;
import com.jdh.o2oservice.core.domain.product.model.JdhQuestion;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/5/25
 * @description 题库
 */
@Slf4j
@Component
public class QuestionFactory {

    @Resource
    private GenerateIdFactory generateIdFactory;

    public JdhQuestion buildJdhQuestion(JdhQuestionContext jdhQuestionContext){

        JdhQuestion jdhQuestion = JSON.parseObject(JSON.toJSONString(jdhQuestionContext),JdhQuestion.class);

        if(StringUtils.isBlank(jdhQuestionContext.getQuesCode())){
            jdhQuestion.setQuesCode(generateIdFactory.getIdStr());
        }else{
            jdhQuestion.setQuesCode(jdhQuestionContext.getQuesCode());
        }
        jdhQuestion.setQuesId(generateIdFactory.getId());

        jdhQuestion.setCreateUser(jdhQuestionContext.getCreateUser());
        jdhQuestion.setUpdateUser(jdhQuestion.getCreateUser());
        jdhQuestion.setCreateTime(new Date());
        jdhQuestion.setUpdateTime(jdhQuestion.getCreateTime());
        jdhQuestion.setYn(YnStatusEnum.YES.getCode());
        jdhQuestion.setBranch(SpringUtil.getProperty("spring.profiles.active"));
        jdhQuestion.setUnit(jdhQuestionContext.getUnit());
        jdhQuestion.setReadOnly(jdhQuestionContext.getReadOnly());
        jdhQuestion.setSource(jdhQuestionContext.getSource());


        return jdhQuestion;
    }
}
