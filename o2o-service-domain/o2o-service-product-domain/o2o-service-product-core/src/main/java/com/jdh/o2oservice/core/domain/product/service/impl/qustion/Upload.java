package com.jdh.o2oservice.core.domain.product.service.impl.qustion;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jdh.o2oservice.base.factory.GenerateIdFactory;
import com.jdh.o2oservice.core.domain.product.context.JdhQuestionContext;
import com.jdh.o2oservice.core.domain.product.model.JdhGroupQuesRel;
import com.jdh.o2oservice.core.domain.product.model.JdhQuestion;
import com.jdh.o2oservice.core.domain.product.service.question.QuestionTypeAbstract;
import com.jdh.o2oservice.core.domain.product.service.question.QuestionTypeInterface;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/29
 * @description 上传图片/文件
 */
@Component
@Slf4j
public class Upload extends QuestionTypeAbstract {

    @Autowired
    private GenerateIdFactory generateIdFactory;

    /**
     * 前置处理参数
     * @param jdhQuestionContext
     */
    @Override
    public void preBuild(JdhQuestionContext jdhQuestionContext) {
        return;
    }

    public List<JdhQuestion> parseToQues(JdhGroupQuesRel jdhGroupQuesRel, JdhQuestion question){

        JSONObject jsonObject = JSON.parseObject(jdhGroupQuesRel.getExtJson());
        Object value = jsonObject.get("value");
        if(value instanceof JSONArray){
            JSONArray array = (JSONArray)value;
            if(array.isEmpty()){
                log.info("数组为空,逻辑终止");
                return null;
            }
            question.setValue(array.toJSONString());
        }
        List<JdhQuestion> jdhQuestions = new ArrayList<>();
        jdhQuestions.add(question);
        return jdhQuestions;
    }

}
