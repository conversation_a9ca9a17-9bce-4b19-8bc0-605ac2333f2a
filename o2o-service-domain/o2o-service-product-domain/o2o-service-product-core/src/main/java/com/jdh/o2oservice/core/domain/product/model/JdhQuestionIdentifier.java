package com.jdh.o2oservice.core.domain.product.model;

import com.jdh.o2oservice.base.model.Identifier;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/5/23
 * @description 题库
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class JdhQuestionIdentifier implements Identifier {

    private Long quesId;//问题id

    @Override
    public String serialize() {
        return quesId+"";
    }
}
