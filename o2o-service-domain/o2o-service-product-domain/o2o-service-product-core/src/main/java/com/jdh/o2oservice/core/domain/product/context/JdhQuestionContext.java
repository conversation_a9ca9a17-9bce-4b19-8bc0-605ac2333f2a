package com.jdh.o2oservice.core.domain.product.context;

import com.alibaba.fastjson.JSON;
import lombok.Builder;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/25
 * @description 题库
 */
@Data
@Builder
public class JdhQuestionContext {

    private String name;//题目名称

    private String quesDesc;//题目说明

    private Integer type;//1单选 2多选 3填空 4图片/视频上传 5签字文件

    private Integer required;//1必填 0非必填

    private Long quesId;//题目唯一单据

    private String quesCode;//题编码

    private Integer highRisk;//1高危 0否

    private String unit;//单位

    private String extJson;//扩展字段 保存多选项

    private Integer readOnly;//1只读 0非读

    private Integer source;//1题库 2ducc


    /**
     * 数据来源分支
     */
    private String branch;
    /**
     * 版本号
     */
    private Integer version;

    /**
     * 是否有效 0：无效；1：有效
     */
    private Integer yn;

    /**
     * 创建人pin
     */
    private String createUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人pin
     */
    private String updateUser;

    /**
     * 更新时间
     */
    private Date updateTime;

    @Data
    public static class OptionBean{

        private String label;//标签

        private Integer highRisk;//是否高风险

        private String value;//提交字段名

    }

    /**
     * extjson转成对象
     * @return
     */
    public List<OptionBean> toExtJsonBean(){
        if(StringUtils.isNotEmpty(extJson)){
            return JSON.parseArray(JSON.parseArray(this.extJson).toJSONString(),OptionBean.class);
        }
        return null;
    }

}
