package com.jdh.o2oservice.core.domain.product.context.careform;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/28
 * @description 服务前评估
 */
@Data
public class PreServiceAssessmentCxt implements GroupCxt{

    private Integer show;//1展示 0不展示

    private String evaluatePoint;//评估重点

    private Integer evaluatePointShow;//1展示 0不展示

    private Integer customEvaluateShow;//1展示 0不展示

    private List<Long> customEvaluate;//个性化评估

    private Integer historyDisease;//历史疾病 1需要

    private Integer vitalSigns;//生命体征 1需要
}
