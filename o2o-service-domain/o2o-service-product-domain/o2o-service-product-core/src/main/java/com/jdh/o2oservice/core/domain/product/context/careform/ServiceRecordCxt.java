package com.jdh.o2oservice.core.domain.product.context.careform;

import com.jdh.o2oservice.export.product.dto.careform.QuestionDto;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/28
 * @description 服务记录
 */
@Data
public class ServiceRecordCxt implements GroupCxt{

    private Integer show;//1展示 0不展示

    private List<Long> customEvaluate;//服务记录

    private Integer customEvaluateShow;//1展示 0不展示

}
