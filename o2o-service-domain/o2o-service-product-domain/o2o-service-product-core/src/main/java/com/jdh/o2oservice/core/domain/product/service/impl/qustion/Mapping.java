package com.jdh.o2oservice.core.domain.product.service.impl.qustion;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.ducc.model.careform.QuestionConfig;
import com.jdh.o2oservice.base.ducc.model.careform.QuestionGroupConfig;
import com.jdh.o2oservice.core.domain.product.model.JdhGroupQuesRel;
import com.jdh.o2oservice.core.domain.product.model.JdhQuestion;
import com.jdh.o2oservice.core.domain.product.repository.db.JdhQuestionRepository;
import com.jdh.o2oservice.core.domain.product.repository.query.QuestionDbQuery;
import com.jdh.o2oservice.core.domain.product.service.question.QuestionTypeAbstract;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/4
 * @description 题映射
 */
@Component
@Slf4j
public class Mapping extends QuestionTypeAbstract {

    @Resource
    private DuccConfig duccConfig;

    @Resource
    private JdhQuestionRepository jdhQuestionRepository;

    @Override
    public List<JdhQuestion> parseToQues(JdhGroupQuesRel jdhGroupQuesRel, JdhQuestion question){

        List<QuestionGroupConfig> questionGroupConfigs = duccConfig.getQuestionGroupConfig();
        QuestionGroupConfig questionGroupConfig = questionGroupConfigs.stream().filter(t->t.getCode().equals(jdhGroupQuesRel.getGroupCode())).findFirst().orElse(null);
        if(questionGroupConfig==null){
            log.info("Mapping questionGroupConfig 为空 逻辑终止!!!");
            return null;
        }
        QuestionConfig questionConfig = questionGroupConfig.getQuestionDTOS().stream().filter(q->q.getQuesCode().equals(question.getQuesCode())).findFirst().orElse(null);
        if(questionConfig==null){
            log.info("Mapping questionConfig 为空 逻辑终止!!!");
            return null;
        }
        QuestionDbQuery questionDbQuery = new QuestionDbQuery();
        questionDbQuery.setQuesIds(questionConfig.getMappingQuesId());
        return jdhQuestionRepository.findList(questionDbQuery);
    }

}
