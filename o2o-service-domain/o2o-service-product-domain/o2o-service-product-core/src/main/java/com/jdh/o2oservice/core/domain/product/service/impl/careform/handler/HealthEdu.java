package com.jdh.o2oservice.core.domain.product.service.impl.careform.handler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.ducc.model.careform.QuestionConfig;
import com.jdh.o2oservice.base.ducc.model.careform.QuestionGroupConfig;
import com.jdh.o2oservice.common.enums.QuestionSourceEnum;
import com.jdh.o2oservice.core.domain.product.context.BindCareFormContext;
import com.jdh.o2oservice.core.domain.product.context.careform.GroupCxt;
import com.jdh.o2oservice.core.domain.product.model.JdhGroupQuesRel;
import com.jdh.o2oservice.core.domain.product.model.JdhQuestion;
import com.jdh.o2oservice.core.domain.product.repository.db.JdhQuestionRepository;
import com.jdh.o2oservice.core.domain.product.service.careform.GroupAbstract;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/29
 * @description
 */
@Component
@Slf4j
public class HealthEdu extends GroupAbstract {

    @Autowired
    private JdhQuestionRepository jdhQuestionRepository;

    @Override
    public GroupCxt buildGroup(BindCareFormContext bindCareFormContext) {
        return bindCareFormContext.getHealthEdu();
    }

    @Override
    @LogAndAlarm
    public List<JdhGroupQuesRel> buildJdhGroupQuesRel(BindCareFormContext bindCareFormContext, GroupCxt groupCxt, QuestionGroupConfig questionGroupConfig) {
        JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(groupCxt));
        if(jsonObject==null){
            log.info("ServiceRecord groupCxt=null,逻辑终止!");
            return null;
        }
        List<JdhGroupQuesRel> jdhGroupQuesRels = new ArrayList<>();

        QuestionConfig questionConfig = questionGroupConfig.getQuestionDTOS().stream().filter(t->t.getQuesCode().equals("content")).findFirst().orElse(null);
        if(questionConfig!=null){
            log.info("ServiceRecord questionConfig=null,逻辑终止! quesCode={}",questionConfig.getQuesCode());
            JdhQuestion jdhQuestion = jdhQuestionRepository.findByCode(questionConfig.getQuesCode());
            JdhGroupQuesRel jdhGroupQuesRel = new JdhGroupQuesRel();
            jdhGroupQuesRel.setQuesId(jdhQuestion.getQuesId()+"");
            jdhGroupQuesRel.setGroupCode(questionGroupConfig.getCode());
            jdhGroupQuesRel.setQuesCode(questionConfig.getQuesCode());
            jdhGroupQuesRel.setSort(questionConfig.getSort());
            jdhGroupQuesRel.setServiceItemId(bindCareFormContext.getServiceItemId());
            jdhGroupQuesRel.setCreateTime(new Date());
            jdhGroupQuesRel.setUpdateTime(new Date());
            jdhGroupQuesRel.setCreateUser(bindCareFormContext.getUserName());
            jdhGroupQuesRel.setUpdateUser(bindCareFormContext.getUserName());
            JSONObject extJson = new JSONObject();
            extJson.put("value",jsonObject.getString("content"));
            jdhGroupQuesRel.setExtJson(extJson.toJSONString());
            jdhGroupQuesRel.setType(jdhQuestion.getSource());
            jdhGroupQuesRels.add(jdhGroupQuesRel);
        }

        return jdhGroupQuesRels;
    }
}
