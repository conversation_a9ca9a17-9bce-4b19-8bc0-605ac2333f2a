package com.jdh.o2oservice.core.domain.product.service.question;

import com.jdh.o2oservice.core.domain.product.context.JdhQuestionContext;
import com.jdh.o2oservice.core.domain.product.model.JdhGroupQuesRel;
import com.jdh.o2oservice.core.domain.product.model.JdhQuestion;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/5/29
 * @description 题型
 */
public interface QuestionTypeInterface {

    void preBuild(JdhQuestionContext jdhQuestionContext);

    Map<String,?> toMap(JdhGroupQuesRel jdhGroupQuesRel);

    List<JdhQuestion> process(JdhGroupQuesRel jdhGroupQuesRel, JdhQuestion question);

    List<JdhQuestion> parseToQues(JdhGroupQuesRel jdhGroupQuesRel, JdhQuestion question);
}
