package com.jdh.o2oservice.core.domain.product.service.impl.qustion;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jdh.o2oservice.core.domain.product.model.JdhGroupQuesRel;
import com.jdh.o2oservice.core.domain.product.model.JdhQuestion;
import com.jdh.o2oservice.core.domain.product.service.question.QuestionTypeAbstract;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/29
 * @description 单选
 */
@Component
public class Sign extends QuestionTypeAbstract {

    public List<JdhQuestion> parseToQues(JdhGroupQuesRel jdhGroupQuesRel, JdhQuestion question){
        List<JdhQuestion> jdhQuestions = new ArrayList<>();
        JSONArray jsonArray = JSONArray.parseArray(question.getExtJson());

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("content",jsonArray);
        jsonObject.putAll(JSON.parseObject(jdhGroupQuesRel.getExtJson()));

        question.setExtJson(jsonObject.toJSONString());
        jdhQuestions.add(question);
        return jdhQuestions;
    }

}
