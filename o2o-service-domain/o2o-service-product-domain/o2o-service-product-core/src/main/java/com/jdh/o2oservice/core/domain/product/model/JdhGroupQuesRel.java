package com.jdh.o2oservice.core.domain.product.model;

import com.jdh.o2oservice.base.model.Entity;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/5/28
 * @description 服务项和节点关系
 */
@Data
public class JdhGroupQuesRel implements Entity<JdhGroupQuesRelIdentifier> {



    private Long id;//主键id

    private String quesId;//题id

    private String groupCode;//节点code

    private Integer type;//1题库中的题 2ducc配置的题

    private String extJson;//扩展字段 保存 客户签字和护士签字等内容

    private String quesCode;//题code

    private Integer sort;//排序由小到大

    private Long serviceItemId;//服务项目id



    /**
     * 数据来源分支
     */
    private String branch;

    /**
     * 是否有效 0：无效；1：有效
     */
    private Integer yn;

    /**
     * 创建人pin
     */
    private String createUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人pin
     */
    private String updateUser;

    /**
     * 更新时间
     */
    private Date updateTime;


    @Override
    public Integer version() {
        return null;
    }

    @Override
    public void versionIncrease() {

    }

    @Override
    public JdhGroupQuesRelIdentifier getIdentifier() {
        return null;
    }
}
