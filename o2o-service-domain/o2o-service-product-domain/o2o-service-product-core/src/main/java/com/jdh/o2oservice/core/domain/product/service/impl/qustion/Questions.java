package com.jdh.o2oservice.core.domain.product.service.impl.qustion;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.ducc.model.careform.QuestionConfig;
import com.jdh.o2oservice.base.ducc.model.careform.QuestionGroupConfig;
import com.jdh.o2oservice.core.domain.product.model.JdhGroupQuesRel;
import com.jdh.o2oservice.core.domain.product.model.JdhQuestion;
import com.jdh.o2oservice.core.domain.product.repository.db.JdhQuestionRepository;
import com.jdh.o2oservice.core.domain.product.repository.query.QuestionDbQuery;
import com.jdh.o2oservice.core.domain.product.service.question.QuestionTypeAbstract;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/6/2
 * @description 题集合
 */
@Component
@Slf4j
public class Questions extends QuestionTypeAbstract {

    @Autowired
    private JdhQuestionRepository jdhQuestionRepository;


    /**
     * 增强返回结果
     * @param jdhGroupQuesRel
     * @return
     */
    public Map<String,Object> toMap(JdhGroupQuesRel jdhGroupQuesRel){
        Map<String,Object> map = new HashMap<>();

        JSONObject jsonObject = JSONObject.parseObject(jdhGroupQuesRel.getExtJson());
        JSONArray jsonArray = jsonObject.getJSONArray("value");
        if(jsonArray!=null&&!jsonArray.isEmpty()){
            QuestionDbQuery questionDbQuery = new QuestionDbQuery();
            questionDbQuery.setQuesIds(jsonArray.toJavaList(Long.class));
            List<JdhQuestion> jdhQuestions =  jdhQuestionRepository.findList(questionDbQuery);
            if(CollectionUtils.isNotEmpty(jdhQuestions)){
                Map<Long,List<JdhQuestion>> quesIdMap = jdhQuestions.stream().collect(Collectors.groupingBy(JdhQuestion::getQuesId));
                //基于jsonArray 排序
                List<JdhQuestion> sortJdhQuestions = new ArrayList<>();
                questionDbQuery.getQuesIds().forEach(quesId->{
                    sortJdhQuestions.add(quesIdMap.get(quesId).get(0));
                });
                map.put(jdhGroupQuesRel.getQuesCode()+"Ques",sortJdhQuestions);
                return map;
            }
        }

        return null;
    }

    public List<JdhQuestion> parseToQues(JdhGroupQuesRel jdhGroupQuesRel, JdhQuestion question){
        JSONObject jsonObject = JSON.parseObject(jdhGroupQuesRel.getExtJson());
        QuestionDbQuery questionDbQuery = new QuestionDbQuery();
        questionDbQuery.setQuesIds(jsonObject.getJSONArray("value").toJavaList(Long.class));
        return jdhQuestionRepository.findList(questionDbQuery);
    }


}
