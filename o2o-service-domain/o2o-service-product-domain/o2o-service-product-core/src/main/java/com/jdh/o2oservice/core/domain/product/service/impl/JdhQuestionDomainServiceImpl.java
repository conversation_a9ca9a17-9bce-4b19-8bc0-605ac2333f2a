package com.jdh.o2oservice.core.domain.product.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.ducc.model.careform.QuestionGroupConfig;
import com.jdh.o2oservice.base.enums.YnStatusEnum;
import com.jdh.o2oservice.base.factory.GenerateIdFactory;
import com.jdh.o2oservice.base.util.SpringUtil;
import com.jdh.o2oservice.common.enums.QuestionTypeEnum;
import com.jdh.o2oservice.core.domain.product.bo.JdhQuestionBO;
import com.jdh.o2oservice.core.domain.product.context.BindCareFormContext;
import com.jdh.o2oservice.core.domain.product.context.JdhQuestionContext;
import com.jdh.o2oservice.core.domain.product.converter.JdhQuestionDomainConvert;
import com.jdh.o2oservice.core.domain.product.factory.QuestionFactory;
import com.jdh.o2oservice.core.domain.product.model.JdhItemQuesGroupRel;
import com.jdh.o2oservice.core.domain.product.model.JdhQuestion;
import com.jdh.o2oservice.core.domain.product.model.JdhQuestionIdentifier;
import com.jdh.o2oservice.core.domain.product.model.ServiceItem;
import com.jdh.o2oservice.core.domain.product.repository.cmd.QuestionRemoveDbCmd;
import com.jdh.o2oservice.core.domain.product.repository.db.JdhItemQuesGroupRelRepository;
import com.jdh.o2oservice.core.domain.product.repository.db.JdhQuestionRepository;
import com.jdh.o2oservice.core.domain.product.repository.db.JdhServiceItemRepository;
import com.jdh.o2oservice.core.domain.product.repository.query.QuestionDbQuery;
import com.jdh.o2oservice.core.domain.product.service.careform.GroupInterface;
import com.jdh.o2oservice.core.domain.product.service.question.QuestionTypeInterface;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.jdh.o2oservice.core.domain.product.service.JdhQuestionDomainService;

import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/25
 * @description 题库
 */
@Service
@Slf4j
public class JdhQuestionDomainServiceImpl implements JdhQuestionDomainService{

    @Autowired
    private JdhQuestionRepository jdhQuestionRepository;

    @Autowired
    private QuestionFactory questionFactory;

    @Autowired
    private GenerateIdFactory generateIdFactory;

    @Autowired
    private DuccConfig duccConfig;

    @Autowired
    private JdhItemQuesGroupRelRepository jdhItemQuesGroupRelRepository;

    @Autowired
    private JdhServiceItemRepository jdhServiceItemRepository;

    /**
     * 查询题库详情
     * @param quesId
     * @return
     */
    @Override
    public JdhQuestionBO findDetail(Long quesId) {
        JdhQuestion jdhQuestion = jdhQuestionRepository.find(JdhQuestionIdentifier.builder().quesId(quesId).build());
        return JdhQuestionDomainConvert.ins.toJdhQuestionBO(jdhQuestion);
    }

    /**
     * 查询题库分页
     * @param questionDbQuery
     * @return
     */
    @Override
    public Page<JdhQuestionBO> findPage(QuestionDbQuery questionDbQuery) {
        Page<JdhQuestion> page = jdhQuestionRepository.findPageList(questionDbQuery);
        return JSON.parseObject(JSON.toJSONString(page),new TypeReference<Page<JdhQuestionBO>>(){});
    }


    /**
     * 删除题
     * @param questionDbQuery
     * @return
     */
    @Override
    public Boolean remove(QuestionRemoveDbCmd questionDbQuery) {
        JdhQuestion jdhQuestion = JdhQuestion.builder()
                .quesId(questionDbQuery.getQuesId())
                .updateUser(questionDbQuery.getUserName()).build();
        return jdhQuestionRepository.remove(jdhQuestion)>0;
    }

    /**
     * 保存/修改题
     * @param jdhQuestionContext
     * @return
     */
    @Override
    public Boolean saveOrUpdate(JdhQuestionContext jdhQuestionContext) {

        QuestionTypeEnum questionTypeEnum = QuestionTypeEnum.getByCode(jdhQuestionContext.getType());
        if(questionTypeEnum!=null&& StringUtils.isNotEmpty(questionTypeEnum.getBeanName())){
            QuestionTypeInterface questionTypeInterface = SpringUtil.getBean(questionTypeEnum.getBeanName());
            questionTypeInterface.preBuild(jdhQuestionContext);
        }
        JdhQuestion jdhQuestion=null;
        if(jdhQuestionContext.getQuesId()==null){
            jdhQuestion = questionFactory.buildJdhQuestion(jdhQuestionContext);
            return jdhQuestionRepository.save(jdhQuestion)>0;
        }else{
            jdhQuestion = JdhQuestionDomainConvert.ins.toJdhQuestion(jdhQuestionContext);
            return jdhQuestionRepository.update(jdhQuestion)>0;
        }
    }

    /**
     * 配置护理单
     * @param bindCareFormContext
     * @return
     */
    @Override
    public Boolean bindCareForm(BindCareFormContext bindCareFormContext) {
        String config = JSON.toJSONString(bindCareFormContext);
        //判断走新增还是修改
        JdhItemQuesGroupRel jdhItemQuesGroupRel = new JdhItemQuesGroupRel();
        jdhItemQuesGroupRel.setServiceItemId(bindCareFormContext.getServiceItemId());

        List<QuestionGroupConfig> questionGroupConfigs = duccConfig.getQuestionGroupConfig();
        questionGroupConfigs.stream().forEach(questionGroupConfig -> {
            GroupInterface groupInterface =  SpringUtil.getBean(questionGroupConfig.getCode());
            try {
                groupInterface.process(bindCareFormContext,questionGroupConfig);
            } catch (IOException e) {
                log.info("bindCareForm 异常了",e);
                throw new RuntimeException(e);
            }
        });
        jdhServiceItemRepository.updateCareFlag(bindCareFormContext.getServiceItemId(),config);
        return true;
    }

}
