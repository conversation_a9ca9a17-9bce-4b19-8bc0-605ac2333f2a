package com.jdh.o2oservice.core.domain.product.repository.db;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jdh.o2oservice.base.model.Repository;
import com.jdh.o2oservice.core.domain.product.model.JdhQuestion;
import com.jdh.o2oservice.core.domain.product.model.JdhQuestionIdentifier;
import com.jdh.o2oservice.core.domain.product.repository.query.QuestionDbQuery;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/23
 * @description 题库
 */
public interface JdhQuestionRepository extends Repository<JdhQuestion, JdhQuestionIdentifier> {


    int update(JdhQuestion entity);

    /**
     * 查询服务站列表
     *
     * @param questionDbQuery
     * @return
     */
    List<JdhQuestion> findList(QuestionDbQuery questionDbQuery);

    /**
     * 分页查询服务站列表
     *
     * @param questionDbQuery
     */
    Page<JdhQuestion> findPageList(QuestionDbQuery questionDbQuery);

    /**
     *
     * @param quesCode
     * @return
     */
    JdhQuestion findByCode(String quesCode);
}
