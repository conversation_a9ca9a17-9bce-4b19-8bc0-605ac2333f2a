package com.jdh.o2oservice.core.domain.product.repository.db;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jdh.o2oservice.base.model.Repository;
import com.jdh.o2oservice.core.domain.product.model.JdhItemQuesGroupRel;
import com.jdh.o2oservice.core.domain.product.model.JdhItemQuesGroupRelIdentifier;
import com.jdh.o2oservice.core.domain.product.model.JdhQuestion;
import com.jdh.o2oservice.core.domain.product.model.JdhQuestionIdentifier;
import com.jdh.o2oservice.core.domain.product.repository.query.QuestionDbQuery;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/23
 * @description 题库
 */
public interface JdhItemQuesGroupRelRepository extends Repository<JdhItemQuesGroupRel, JdhItemQuesGroupRelIdentifier> {

    List<JdhItemQuesGroupRel> findList(JdhItemQuesGroupRel jdhItemQuesGroupRel);

    JdhItemQuesGroupRel findByCodeAndItemId(Long serviceItemId, String code);

    Boolean update(JdhItemQuesGroupRel jdhItemQuesGroupRelUpdate);
}
