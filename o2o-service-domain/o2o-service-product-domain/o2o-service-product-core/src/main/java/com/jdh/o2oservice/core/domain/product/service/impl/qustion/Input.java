package com.jdh.o2oservice.core.domain.product.service.impl.qustion;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jdh.o2oservice.core.domain.product.model.JdhGroupQuesRel;
import com.jdh.o2oservice.core.domain.product.model.JdhQuestion;
import com.jdh.o2oservice.core.domain.product.service.question.QuestionTypeAbstract;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/29
 * @description 单选
 */
@Component
public class Input extends QuestionTypeAbstract {


    @Override
    public List<JdhQuestion> parseToQues(JdhGroupQuesRel jdhGroupQuesRel, JdhQuestion question) {
        List<JdhQuestion> jdhQuestions = new ArrayList<>();
        JSONObject jsonObject = JSON.parseObject(jdhGroupQuesRel.getExtJson());
        question.setValue(jsonObject.getString("value"));
        jdhQuestions.add(question);
        return jdhQuestions;
    }
}
