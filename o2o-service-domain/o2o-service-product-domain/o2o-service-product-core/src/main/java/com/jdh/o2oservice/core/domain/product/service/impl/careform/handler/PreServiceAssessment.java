package com.jdh.o2oservice.core.domain.product.service.impl.careform.handler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.ducc.model.careform.QuestionConfig;
import com.jdh.o2oservice.base.ducc.model.careform.QuestionGroupConfig;
import com.jdh.o2oservice.base.enums.YnStatusEnum;
import com.jdh.o2oservice.common.enums.QuestionSourceEnum;
import com.jdh.o2oservice.core.domain.product.context.BindCareFormContext;
import com.jdh.o2oservice.core.domain.product.context.careform.GroupCxt;
import com.jdh.o2oservice.core.domain.product.context.careform.PreReceiveAssessmentCxt;
import com.jdh.o2oservice.core.domain.product.context.careform.PreServiceAssessmentCxt;
import com.jdh.o2oservice.core.domain.product.model.JdhGroupQuesRel;
import com.jdh.o2oservice.core.domain.product.model.JdhQuestion;
import com.jdh.o2oservice.core.domain.product.repository.db.JdhGroupQuesRelRepository;
import com.jdh.o2oservice.core.domain.product.repository.db.JdhQuestionRepository;
import com.jdh.o2oservice.core.domain.product.service.careform.GroupAbstract;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/28
 * @description 接单前评估
 */
@Component
@Slf4j
public class PreServiceAssessment extends GroupAbstract {

    @Autowired
    private JdhQuestionRepository jdhQuestionRepository;

    @Autowired
    private JdhGroupQuesRelRepository jdhGroupQuesRelRepository;

    @Override
    public PreServiceAssessmentCxt buildGroup(BindCareFormContext bindCareFormContext){
        return bindCareFormContext.getPreServiceAssessment();
    }

    @Override
    @LogAndAlarm
    public List<JdhGroupQuesRel> buildJdhGroupQuesRel(BindCareFormContext bindCareFormContext,GroupCxt groupCxt,QuestionGroupConfig questionGroupConfig) {
        JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(groupCxt));
        if(jsonObject==null){
            log.info("PreServiceAssessment groupCxt=null,逻辑终止!");
            return null;
        }
        List<JdhGroupQuesRel> jdhGroupQuesRels = new ArrayList<>();

        QuestionConfig questionConfig = questionGroupConfig.getQuestionDTOS().stream().filter(t->t.getQuesCode().equals("evaluatePoint")).findFirst().orElse(null);
        if(questionConfig!=null){
            log.info("PreServiceAssessment questionConfig=null,逻辑终止! quesCode={}",questionConfig.getQuesCode());
            JdhQuestion jdhQuestion = jdhQuestionRepository.findByCode(questionConfig.getQuesCode());

            JdhGroupQuesRel jdhGroupQuesRel = new JdhGroupQuesRel();
            jdhGroupQuesRel.setQuesId(jdhQuestion.getQuesId()+"");
            jdhGroupQuesRel.setGroupCode(questionGroupConfig.getCode());
            jdhGroupQuesRel.setQuesCode(questionConfig.getQuesCode());
            jdhGroupQuesRel.setSort(questionConfig.getSort());
            jdhGroupQuesRel.setServiceItemId(bindCareFormContext.getServiceItemId());
            jdhGroupQuesRel.setCreateTime(new Date());
            jdhGroupQuesRel.setUpdateTime(new Date());
            jdhGroupQuesRel.setCreateUser(bindCareFormContext.getUserName());
            jdhGroupQuesRel.setUpdateUser(bindCareFormContext.getUserName());
            JSONObject extJson = new JSONObject();
            extJson.put("value",jsonObject.getString("evaluatePoint"));
            jdhGroupQuesRel.setExtJson(extJson.toJSONString());
            jdhGroupQuesRel.setType(jdhQuestion.getSource());
            jdhGroupQuesRels.add(jdhGroupQuesRel);
        }

        //需要把之前的关联关系删除掉
        JdhGroupQuesRel updateJdhGroupQuesRel = new JdhGroupQuesRel();
        updateJdhGroupQuesRel.setServiceItemId(bindCareFormContext.getServiceItemId());
        updateJdhGroupQuesRel.setGroupCode(questionGroupConfig.getCode());
        updateJdhGroupQuesRel.setQuesCode("customEvaluate");

        updateJdhGroupQuesRel.setYn(YnStatusEnum.NO.getCode());
        jdhGroupQuesRelRepository.update(updateJdhGroupQuesRel);

        QuestionConfig questionConfig2 = questionGroupConfig.getQuestionDTOS().stream().filter(t->t.getQuesCode().equals("customEvaluate")).findFirst().orElse(null);
        if(questionConfig2!=null){
            log.info("PreServiceAssessment questionConfig=null,逻辑终止! quesCode={}",questionConfig2.getQuesCode());
            JdhQuestion jdhQuestion = jdhQuestionRepository.findByCode(questionConfig2.getQuesCode());
            JdhGroupQuesRel jdhGroupQuesRel = new JdhGroupQuesRel();
            jdhGroupQuesRel.setQuesId(jdhQuestion.getQuesId()+"");
            jdhGroupQuesRel.setGroupCode(questionGroupConfig.getCode());
            jdhGroupQuesRel.setQuesCode(questionConfig2.getQuesCode());
            jdhGroupQuesRel.setSort(questionConfig2.getSort());
            jdhGroupQuesRel.setServiceItemId(bindCareFormContext.getServiceItemId());
            jdhGroupQuesRel.setCreateTime(new Date());
            jdhGroupQuesRel.setUpdateTime(new Date());
            jdhGroupQuesRel.setCreateUser(bindCareFormContext.getUserName());
            jdhGroupQuesRel.setUpdateUser(bindCareFormContext.getUserName());
            JSONObject extJson = new JSONObject();
            extJson.put("value",jsonObject.getJSONArray("customEvaluate"));
            jdhGroupQuesRel.setExtJson(extJson.toJSONString());
            jdhGroupQuesRel.setType(jdhQuestion.getSource());
            jdhGroupQuesRels.add(jdhGroupQuesRel);
        }

        QuestionConfig questionConfig3 = questionGroupConfig.getQuestionDTOS().stream().filter(t->t.getQuesCode().equals("historyDisease")).findFirst().orElse(null);
        if(questionConfig3!=null){
            log.info("PreServiceAssessment questionConfig=null,逻辑终止! quesCode={}",questionConfig3.getQuesCode());
            JdhQuestion jdhQuestion = jdhQuestionRepository.findByCode(questionConfig3.getQuesCode());
            JdhGroupQuesRel jdhGroupQuesRel = new JdhGroupQuesRel();
            jdhGroupQuesRel.setQuesId(jdhQuestion.getQuesId()+"");
            jdhGroupQuesRel.setGroupCode(questionGroupConfig.getCode());
            jdhGroupQuesRel.setQuesCode(questionConfig3.getQuesCode());
            jdhGroupQuesRel.setSort(questionConfig3.getSort());
            jdhGroupQuesRel.setServiceItemId(bindCareFormContext.getServiceItemId());
            jdhGroupQuesRel.setCreateTime(new Date());
            jdhGroupQuesRel.setUpdateTime(new Date());
            jdhGroupQuesRel.setCreateUser(bindCareFormContext.getUserName());
            jdhGroupQuesRel.setUpdateUser(bindCareFormContext.getUserName());
            JSONObject extJson = new JSONObject();
            extJson.put("value",jsonObject.getInteger("historyDisease"));
            jdhGroupQuesRel.setExtJson(extJson.toJSONString());
            jdhGroupQuesRel.setType(jdhQuestion.getSource());
            jdhGroupQuesRels.add(jdhGroupQuesRel);
        }

        QuestionConfig questionConfig4 = questionGroupConfig.getQuestionDTOS().stream().filter(t->t.getQuesCode().equals("vitalSigns")).findFirst().orElse(null);
        if(questionConfig4!=null){
            log.info("PreServiceAssessment questionConfig=null,逻辑终止! quesCode={}",questionConfig4.getQuesCode());
            JdhQuestion jdhQuestion = jdhQuestionRepository.findByCode(questionConfig4.getQuesCode());
            JdhGroupQuesRel jdhGroupQuesRel = new JdhGroupQuesRel();
            jdhGroupQuesRel.setQuesId(jdhQuestion.getQuesId()+"");
            jdhGroupQuesRel.setGroupCode(questionGroupConfig.getCode());
            jdhGroupQuesRel.setQuesCode(questionConfig4.getQuesCode());
            jdhGroupQuesRel.setSort(questionConfig4.getSort());
            jdhGroupQuesRel.setServiceItemId(bindCareFormContext.getServiceItemId());
            jdhGroupQuesRel.setCreateTime(new Date());
            jdhGroupQuesRel.setUpdateTime(new Date());
            jdhGroupQuesRel.setCreateUser(bindCareFormContext.getUserName());
            jdhGroupQuesRel.setUpdateUser(bindCareFormContext.getUserName());
            JSONObject extJson = new JSONObject();
            extJson.put("value",jsonObject.getInteger("vitalSigns"));
            jdhGroupQuesRel.setExtJson(extJson.toJSONString());
            jdhGroupQuesRel.setType(jdhQuestion.getSource());
            jdhGroupQuesRels.add(jdhGroupQuesRel);
        }


        return jdhGroupQuesRels;
    }

}
