package com.jdh.o2oservice.core.domain.product.model;

import com.jdh.o2oservice.base.model.Identifier;
import lombok.Builder;
import lombok.Data;

/**
 * @ClassName:JdhServiceItemRelIdentifier
 * @Description:
 * @Author: yaoqinghai
 * @Date: 2024/3/28 13:25
 * @Vserion: 1.0
 **/
@Data
@Builder
public class JdhGroupQuesRelIdentifier implements Identifier {

    private Long id;

    /**
     * 序列化
     *
     * @return {@link String}
     */
    @Override
    public String serialize() {
        return String.valueOf(this.id);
    }
}
