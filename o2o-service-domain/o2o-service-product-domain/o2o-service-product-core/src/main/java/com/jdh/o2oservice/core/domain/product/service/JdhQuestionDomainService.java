package com.jdh.o2oservice.core.domain.product.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jdh.o2oservice.core.domain.product.bo.JdhQuestionBO;
import com.jdh.o2oservice.core.domain.product.context.BindCareFormContext;
import com.jdh.o2oservice.core.domain.product.context.JdhQuestionContext;
import com.jdh.o2oservice.core.domain.product.repository.cmd.QuestionRemoveDbCmd;
import com.jdh.o2oservice.core.domain.product.repository.query.QuestionDbQuery;

/**
 * <AUTHOR>
 * @date 2025/5/25
 * @description 题库
 */
public interface JdhQuestionDomainService {

    /**
     * 查询详情
     * @param quesId
     * @return
     */
    JdhQuestionBO findDetail(Long quesId);

    /**
     * 分页查询
     * @param questionDbQuery
     * @return
     */
    Page<JdhQuestionBO> findPage(QuestionDbQuery questionDbQuery);

    /**
     * 删除
     * @param questionRemoveDbCmd
     * @return
     */
    Boolean remove(QuestionRemoveDbCmd questionRemoveDbCmd);

    /**
     * 新增/修改
     * @param jdhQuestionContext
     * @return
     */
    Boolean saveOrUpdate(JdhQuestionContext jdhQuestionContext);

    /**
     * 配置护理单
     * @param bindCareFormContext
     * @return
     */
    Boolean bindCareForm(BindCareFormContext bindCareFormContext);
}
