package com.jdh.o2oservice.core.domain.product.service.careform;

import com.jdh.o2oservice.base.ducc.model.careform.QuestionGroupConfig;
import com.jdh.o2oservice.core.domain.product.context.BindCareFormContext;
import com.jdh.o2oservice.core.domain.product.context.careform.GroupCxt;
import com.jdh.o2oservice.core.domain.product.context.careform.PreReceiveAssessmentCxt;
import com.jdh.o2oservice.core.domain.product.model.JdhGroupQuesRel;

import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/28
 * @description 题节点接口
 */
public interface GroupInterface {

     void process(BindCareFormContext bindCareFormContext, QuestionGroupConfig questionGroupConfig) throws IOException;


     GroupCxt buildGroup(BindCareFormContext bindCareFormContext);

     List<JdhGroupQuesRel> buildJdhGroupQuesRel(BindCareFormContext bindCareFormContext,GroupCxt groupCxt,QuestionGroupConfig questionGroupConfig) throws IOException;
}
