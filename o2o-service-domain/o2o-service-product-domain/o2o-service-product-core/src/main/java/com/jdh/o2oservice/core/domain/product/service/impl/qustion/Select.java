package com.jdh.o2oservice.core.domain.product.service.impl.qustion;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.factory.GenerateIdFactory;
import com.jdh.o2oservice.core.domain.product.context.JdhQuestionContext;
import com.jdh.o2oservice.core.domain.product.model.JdhGroupQuesRel;
import com.jdh.o2oservice.core.domain.product.model.JdhQuestion;
import com.jdh.o2oservice.core.domain.product.service.question.QuestionTypeAbstract;
import com.jdh.o2oservice.core.domain.product.service.question.QuestionTypeInterface;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/5/29
 * @description 单选
 */
@Component
public class Select extends QuestionTypeAbstract {

    @Autowired
    private GenerateIdFactory generateIdFactory;

    /**
     * 前置处理参数
     * @param jdhQuestionContext
     */
    @Override
    public void preBuild(JdhQuestionContext jdhQuestionContext) {
        List<JdhQuestionContext.OptionBean> optionBeans = jdhQuestionContext.toExtJsonBean();
        if(CollectionUtils.isNotEmpty(optionBeans)){
            //每个选项生成唯一的value
            optionBeans.forEach(t->t.setValue(generateIdFactory.getIdStr()));
            jdhQuestionContext.setExtJson(JSON.toJSONString(optionBeans));
            //选项中 高危选项数量
            long highRisk = optionBeans.stream().filter(t-> t.getHighRisk()!=null&&t.getHighRisk()== CommonConstant.ONE).count();
            if(highRisk>0){
                jdhQuestionContext.setHighRisk(CommonConstant.ONE);
            }
        }
    }

    @Override
    public List<JdhQuestion> parseToQues(JdhGroupQuesRel jdhGroupQuesRel, JdhQuestion question){
        List<JdhQuestion> jdhQuestions = new ArrayList<>();
        jdhQuestions.add(question);
        return jdhQuestions;
    }

}
