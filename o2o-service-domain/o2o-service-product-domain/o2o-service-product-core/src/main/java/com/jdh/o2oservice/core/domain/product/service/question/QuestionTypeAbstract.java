package com.jdh.o2oservice.core.domain.product.service.question;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.ducc.model.careform.QuestionConfig;
import com.jdh.o2oservice.base.ducc.model.careform.QuestionGroupConfig;
import com.jdh.o2oservice.base.enums.YnStatusEnum;
import com.jdh.o2oservice.core.domain.product.context.JdhQuestionContext;
import com.jdh.o2oservice.core.domain.product.model.JdhGroupQuesRel;
import com.jdh.o2oservice.core.domain.product.model.JdhQuestion;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/6/2
 * @description 题型抽象类
 */
@Slf4j
public abstract class QuestionTypeAbstract implements QuestionTypeInterface{

    @Autowired
    private DuccConfig duccConfig;

    public void preBuild(JdhQuestionContext jdhQuestionContext){
        return;
    }

    public Map<String,Object> toMap(JdhGroupQuesRel jdhGroupQuesRel){
        return null;
    }

    @LogAndAlarm
    public List<JdhQuestion> process(JdhGroupQuesRel jdhGroupQuesRel, JdhQuestion question){

        JSONObject jsonObject = JSON.parseObject(jdhGroupQuesRel.getExtJson());
        Object value = jsonObject.get("value");
        if(value instanceof JSONArray){
            JSONArray array = (JSONArray)value;
            if(array.isEmpty()){
                log.info("数组为空,逻辑终止");
                return null;
            }
        }else if(value instanceof String){
            String v = (String)value;
            if(StringUtils.isEmpty(v)){
                log.info("value为空,逻辑终止");
                return null;
            }
        }else if(value instanceof Integer){
            Integer i = (Integer) value;
            if(i!=1){
                log.info("value!=1,逻辑终止");
                return null;
            }
        }
        List<JdhQuestion> jdhQuestions = parseToQues(jdhGroupQuesRel,question);
        //统一维护上sort
        for (int i = 0; i < jdhQuestions.size(); i++) {

            List<QuestionGroupConfig> questionGroupConfigs = duccConfig.getQuestionGroupConfig();
            QuestionGroupConfig questionGroupConfig = questionGroupConfigs.stream().filter(t->t.getCode().equals(jdhGroupQuesRel.getGroupCode())).findFirst().orElse(null);
            if(questionGroupConfig==null){
                log.info("Mapping questionGroupConfig 为空 逻辑终止!!!");
                return null;
            }
            QuestionConfig questionConfig = questionGroupConfig.getQuestionDTOS().stream().filter(q->q.getQuesCode().equals(question.getQuesCode())).findFirst().orElse(null);
            if(questionConfig==null){
                log.info("Mapping questionConfig 为空 逻辑终止!!!");
                return null;
            }

            jdhQuestions.get(i).setSort(jdhGroupQuesRel.getSort()+""+i);
            jdhQuestions.get(i).setTag(questionConfig.getTag());
            if(questionConfig.getAppendQuesId()!=null&& YnStatusEnum.YES.getCode().equals(questionConfig.getAppendQuesId())){
                jdhQuestions.get(i).setQuesCode(jdhGroupQuesRel.getServiceItemId()+jdhQuestions.get(i).getQuesCode());
            }
        }
        return jdhQuestions;
    }
}
