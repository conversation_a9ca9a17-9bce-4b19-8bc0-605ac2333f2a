package com.jdh.o2oservice.core.domain.product.model;

import com.jdh.o2oservice.base.model.Aggregate;
import com.jdh.o2oservice.base.model.AggregateCode;
import com.jdh.o2oservice.base.model.DomainCode;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/5/23
 * @description 题库
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class JdhQuestion implements Aggregate<JdhQuestionIdentifier> {

    /**
     * 主键
     */
    private Long id;

    private String name;//题目名称

    private String quesDesc;//题目说明

    private Integer type;//1单选 2多选 3填空 4图片/视频上传 5签字

    private Integer required;//1必填 0非必填

    private Long quesId;//题目唯一单据

    private String quesCode;//题编码

    private Integer highRisk;//1高危 0否

    private String unit;//单位

    private String extJson;//扩展字段 保存多选项

    private String serviceItemName;//服务项目名称

    private Integer readOnly;//1只读 0非读

    private Integer source;//1题库 2ducc

    private String value;//值 非持久化字段

    private String sort;//排序 非持久化字段

    private Integer tag;//null和1: input   2: textarea

    /**
     * 数据来源分支
     */
    private String branch;
    /**
     * 版本号
     */
    private Integer version;

    /**
     * 是否有效 0：无效；1：有效
     */
    private Integer yn;

    /**
     * 创建人pin
     */
    private String createUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人pin
     */
    private String updateUser;

    /**
     * 更新时间
     */
    private Date updateTime;

    @Override
    public Integer version() {
        return null;
    }

    @Override
    public void versionIncrease() {

    }

    @Override
    public JdhQuestionIdentifier getIdentifier() {
        return new JdhQuestionIdentifier(this.quesId);
    }

    @Override
    public DomainCode getDomainCode() {
        return null;
    }

    @Override
    public AggregateCode getAggregateCode() {
        return null;
    }
}
