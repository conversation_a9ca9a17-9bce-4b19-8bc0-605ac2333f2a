package com.jdh.o2oservice.core.domain.product.service.impl.careform.handler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.ducc.model.careform.QuestionConfig;
import com.jdh.o2oservice.base.ducc.model.careform.QuestionGroupConfig;
import com.jdh.o2oservice.common.enums.QuestionSourceEnum;
import com.jdh.o2oservice.core.domain.product.context.BindCareFormContext;
import com.jdh.o2oservice.core.domain.product.context.careform.GroupCxt;
import com.jdh.o2oservice.core.domain.product.context.careform.RecordUploadCxt;
import com.jdh.o2oservice.core.domain.product.model.JdhGroupQuesRel;
import com.jdh.o2oservice.core.domain.product.model.JdhQuestion;
import com.jdh.o2oservice.core.domain.product.repository.db.JdhQuestionRepository;
import com.jdh.o2oservice.core.domain.product.service.careform.GroupAbstract;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/29
 * @description
 */
@Component
@Slf4j
public class RecordUpload extends GroupAbstract {

    @Autowired
    private JdhQuestionRepository jdhQuestionRepository;

    @Override
    public GroupCxt buildGroup(BindCareFormContext bindCareFormContext) {
        return new RecordUploadCxt();
    }

    @Override
    @LogAndAlarm
    public List<JdhGroupQuesRel> buildJdhGroupQuesRel(BindCareFormContext bindCareFormContext, GroupCxt groupCxt, QuestionGroupConfig questionGroupConfig) {
        JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(groupCxt));
        if(jsonObject==null){
            log.info("ServiceRecord groupCxt=null,逻辑终止!");
            return null;
        }
        List<JdhGroupQuesRel> jdhGroupQuesRels = new ArrayList<>();

        QuestionConfig questionConfig = questionGroupConfig.getQuestionDTOS().stream().filter(t->t.getQuesCode().equals("inHomeImage")).findFirst().orElse(null);
        if(questionConfig!=null){
            log.info("SupplyVerification questionConfig=null,逻辑终止! quesCode={}",questionConfig.getQuesCode());
            JdhQuestion jdhQuestion = jdhQuestionRepository.findByCode(questionConfig.getQuesCode());

            JdhGroupQuesRel jdhGroupQuesRel = new JdhGroupQuesRel();
            jdhGroupQuesRel.setQuesId(jdhQuestion.getQuesId()+"");
            jdhGroupQuesRel.setGroupCode(questionGroupConfig.getCode());
            jdhGroupQuesRel.setQuesCode(questionConfig.getQuesCode());
            jdhGroupQuesRel.setSort(questionConfig.getSort());
            jdhGroupQuesRel.setServiceItemId(bindCareFormContext.getServiceItemId());
            jdhGroupQuesRel.setCreateTime(new Date());
            jdhGroupQuesRel.setUpdateTime(new Date());
            jdhGroupQuesRel.setCreateUser(bindCareFormContext.getUserName());
            jdhGroupQuesRel.setUpdateUser(bindCareFormContext.getUserName());
            JSONObject extJson = new JSONObject();
            extJson.put("value",1);
            jdhGroupQuesRel.setExtJson(extJson.toJSONString());
            jdhGroupQuesRel.setType(jdhQuestion.getSource());
            jdhGroupQuesRels.add(jdhGroupQuesRel);
        }

        QuestionConfig questionConfig2 = questionGroupConfig.getQuestionDTOS().stream().filter(t->t.getQuesCode().equals("postCollectionImage")).findFirst().orElse(null);
        if(questionConfig2!=null){
            log.info("SupplyVerification questionConfig=null,逻辑终止! quesCode={}",questionConfig2.getQuesCode());
            JdhQuestion jdhQuestion = jdhQuestionRepository.findByCode(questionConfig2.getQuesCode());

            JdhGroupQuesRel jdhGroupQuesRel = new JdhGroupQuesRel();
            jdhGroupQuesRel.setQuesId(jdhQuestion.getQuesId()+"");
            jdhGroupQuesRel.setGroupCode(questionGroupConfig.getCode());
            jdhGroupQuesRel.setQuesCode(questionConfig2.getQuesCode());
            jdhGroupQuesRel.setSort(questionConfig2.getSort());
            jdhGroupQuesRel.setServiceItemId(bindCareFormContext.getServiceItemId());
            jdhGroupQuesRel.setCreateTime(new Date());
            jdhGroupQuesRel.setUpdateTime(new Date());
            jdhGroupQuesRel.setCreateUser(bindCareFormContext.getUserName());
            jdhGroupQuesRel.setUpdateUser(bindCareFormContext.getUserName());
            JSONObject extJson = new JSONObject();
            extJson.put("value",1);
            jdhGroupQuesRel.setExtJson(extJson.toJSONString());
            jdhGroupQuesRel.setType(jdhQuestion.getSource());
            jdhGroupQuesRels.add(jdhGroupQuesRel);
        }

        QuestionConfig questionConfig3 = questionGroupConfig.getQuestionDTOS().stream().filter(t->t.getQuesCode().equals("wasteImage")).findFirst().orElse(null);
        if(questionConfig3!=null){
            log.info("SupplyVerification questionConfig=null,逻辑终止! quesCode={}",questionConfig3.getQuesCode());
            JdhQuestion jdhQuestion = jdhQuestionRepository.findByCode(questionConfig3.getQuesCode());

            JdhGroupQuesRel jdhGroupQuesRel = new JdhGroupQuesRel();
            jdhGroupQuesRel.setQuesId(jdhQuestion.getQuesId()+"");
            jdhGroupQuesRel.setGroupCode(questionGroupConfig.getCode());
            jdhGroupQuesRel.setQuesCode(questionConfig3.getQuesCode());
            jdhGroupQuesRel.setSort(questionConfig3.getSort());
            jdhGroupQuesRel.setServiceItemId(bindCareFormContext.getServiceItemId());
            jdhGroupQuesRel.setCreateTime(new Date());
            jdhGroupQuesRel.setUpdateTime(new Date());
            jdhGroupQuesRel.setCreateUser(bindCareFormContext.getUserName());
            jdhGroupQuesRel.setUpdateUser(bindCareFormContext.getUserName());
            JSONObject extJson = new JSONObject();
            extJson.put("value",1);
            jdhGroupQuesRel.setExtJson(extJson.toJSONString());
            jdhGroupQuesRel.setType(jdhQuestion.getSource());
            jdhGroupQuesRels.add(jdhGroupQuesRel);
        }

        QuestionConfig questionConfig4 = questionGroupConfig.getQuestionDTOS().stream().filter(t->t.getQuesCode().equals("otherImage")).findFirst().orElse(null);
        if(questionConfig4!=null){
            log.info("SupplyVerification questionConfig=null,逻辑终止! quesCode={}",questionConfig4.getQuesCode());
            JdhQuestion jdhQuestion = jdhQuestionRepository.findByCode(questionConfig4.getQuesCode());

            JdhGroupQuesRel jdhGroupQuesRel = new JdhGroupQuesRel();
            jdhGroupQuesRel.setQuesId(jdhQuestion.getQuesId()+"");
            jdhGroupQuesRel.setGroupCode(questionGroupConfig.getCode());
            jdhGroupQuesRel.setQuesCode(questionConfig4.getQuesCode());
            jdhGroupQuesRel.setSort(questionConfig4.getSort());
            jdhGroupQuesRel.setServiceItemId(bindCareFormContext.getServiceItemId());
            jdhGroupQuesRel.setCreateTime(new Date());
            jdhGroupQuesRel.setUpdateTime(new Date());
            jdhGroupQuesRel.setCreateUser(bindCareFormContext.getUserName());
            jdhGroupQuesRel.setUpdateUser(bindCareFormContext.getUserName());
            JSONObject extJson = new JSONObject();
            extJson.put("value",1);
            jdhGroupQuesRel.setExtJson(extJson.toJSONString());
            jdhGroupQuesRel.setType(jdhQuestion.getSource());
            jdhGroupQuesRels.add(jdhGroupQuesRel);
        }

        return jdhGroupQuesRels;
    }
}
