package com.jdh.o2oservice.core.domain.product.service.impl.careform.handler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Joiner;
import com.jdh.o2oservice.application.support.FileManageExtApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.ducc.model.careform.QuestionConfig;
import com.jdh.o2oservice.base.ducc.model.careform.QuestionGroupConfig;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.errorcode.DynamicErrorCode;
import com.jdh.o2oservice.base.queue.FixedSizeQueue;
import com.jdh.o2oservice.core.domain.product.context.BindCareFormContext;
import com.jdh.o2oservice.core.domain.product.context.careform.GroupCxt;
import com.jdh.o2oservice.core.domain.product.model.JdhGroupQuesRel;
import com.jdh.o2oservice.core.domain.product.model.JdhQuestion;
import com.jdh.o2oservice.core.domain.product.repository.db.JdhQuestionRepository;
import com.jdh.o2oservice.core.domain.product.service.careform.GroupAbstract;
import com.jdh.o2oservice.export.product.dto.careform.PdfDataDto;
import com.jdh.o2oservice.export.support.command.GenerateGetUrlCommand;
import com.jdh.o2oservice.export.support.dto.FilePreSignedUrlDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.text.PDFTextStripper;
import org.apache.pdfbox.text.TextPosition;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.net.URL;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/5/29
 * @description 知情签字
 */
@Component
@Slf4j
public class PreServiceSignature extends GroupAbstract {

    @Autowired
    private JdhQuestionRepository jdhQuestionRepository;

    @Resource
    private FileManageExtApplication fileManageExtApplication;

    @Override
    public GroupCxt buildGroup(BindCareFormContext bindCareFormContext) {
        return bindCareFormContext.getPreServiceSignature();
    }

    @Override
    @LogAndAlarm
    public List<JdhGroupQuesRel> buildJdhGroupQuesRel(BindCareFormContext bindCareFormContext, GroupCxt groupCxt, QuestionGroupConfig questionGroupConfig) throws IOException {
        JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(groupCxt));
        if(jsonObject==null){
            log.info("PreServiceSignature groupCxt=null,逻辑终止!");
            return null;
        }
        List<JdhGroupQuesRel> jdhGroupQuesRels = new ArrayList<>();

        QuestionConfig questionConfig = questionGroupConfig.getQuestionDTOS().stream().filter(t->t.getQuesCode().equals("pdfUrl")).findFirst().orElse(null);
        if(questionConfig!=null){
            log.info("PreServiceSignature questionConfig=null,逻辑终止! quesCode={}",questionConfig.getQuesCode());
            JdhQuestion jdhQuestion = jdhQuestionRepository.findByCode(questionConfig.getQuesCode());

            JdhGroupQuesRel jdhGroupQuesRel = new JdhGroupQuesRel();
            jdhGroupQuesRel.setQuesId(jdhQuestion.getQuesId()+"");
            jdhGroupQuesRel.setGroupCode(questionGroupConfig.getCode());
            jdhGroupQuesRel.setQuesCode(questionConfig.getQuesCode());
            jdhGroupQuesRel.setSort(questionConfig.getSort());
            jdhGroupQuesRel.setServiceItemId(bindCareFormContext.getServiceItemId());
            jdhGroupQuesRel.setCreateTime(new Date());
            jdhGroupQuesRel.setUpdateTime(new Date());
            jdhGroupQuesRel.setCreateUser(bindCareFormContext.getUserName());
            jdhGroupQuesRel.setUpdateUser(bindCareFormContext.getUserName());
            JSONObject extJson = new JSONObject();
            extJson.put("value",jsonObject.getJSONArray("pdfUrl"));
            jdhGroupQuesRel.setExtJson(extJson.toJSONString());
            jdhGroupQuesRel.setType(jdhQuestion.getSource());
            jdhGroupQuesRels.add(jdhGroupQuesRel);
        }

        if(groupCxt.getShow().equals(CommonConstant.ONE)){
            QuestionConfig questionConfig2 = questionGroupConfig.getQuestionDTOS().stream().filter(t->t.getQuesCode().equals("serviceSign")).findFirst().orElse(null);
            if(questionConfig2!=null){
                log.info("PreServiceSignature questionConfig=null,逻辑终止! quesCode={}",questionConfig2.getQuesCode());
                JdhQuestion jdhQuestion = jdhQuestionRepository.findByCode(questionConfig2.getQuesCode());

                JdhGroupQuesRel jdhGroupQuesRel = new JdhGroupQuesRel();
                jdhGroupQuesRel.setQuesId(jdhQuestion.getQuesId()+"");
                jdhGroupQuesRel.setGroupCode(questionGroupConfig.getCode());
                jdhGroupQuesRel.setQuesCode(questionConfig2.getQuesCode());
                jdhGroupQuesRel.setSort(questionConfig2.getSort());
                jdhGroupQuesRel.setServiceItemId(bindCareFormContext.getServiceItemId());
                jdhGroupQuesRel.setCreateTime(new Date());
                jdhGroupQuesRel.setUpdateTime(new Date());
                jdhGroupQuesRel.setCreateUser(bindCareFormContext.getUserName());
                jdhGroupQuesRel.setUpdateUser(bindCareFormContext.getUserName());
                JSONObject extJson = new JSONObject();
                extJson.put("value",jsonObject.getInteger("show"));

                if(jsonObject.containsKey("pdfUrl")&&!jsonObject.getJSONArray("pdfUrl").isEmpty()&& StringUtils.isNotEmpty(questionConfig2.getSignKey())){
                    Map<String,Map<String,String>> map = this.toMap(jsonObject,questionConfig2);
                    boolean needException = true;
                    for (Map.Entry<String,Map<String,String>> loopMap:map.entrySet()){
                        if (!loopMap.getValue().isEmpty()) {
                            needException = false;
                            break;
                        }
                    }
                    if(needException){
                        throw new BusinessException(new DynamicErrorCode("-1","pdf缺少用户签字内容"));
                    }
                    extJson.put("position",JSON.toJSONString(map));
                }

                jdhGroupQuesRel.setExtJson(extJson.toJSONString());
                jdhGroupQuesRel.setType(jdhQuestion.getSource());
                jdhGroupQuesRels.add(jdhGroupQuesRel);
            }
        }
        return jdhGroupQuesRels;
    }

    /**
     * 查询pdf关键字坐标
     * @param jsonObject
     * @param questionConfig
     * @return
     */
    private Map<String,Map<String,String>> toMap(JSONObject jsonObject,QuestionConfig questionConfig) throws IOException {

        //收集结果数据
        Map<String,Map<String,String>> mapResult = new HashMap<>();

        GenerateGetUrlCommand generateGetUrlCommand = new GenerateGetUrlCommand();
        generateGetUrlCommand.setFileIds((jsonObject.getJSONArray("pdfUrl").toJavaList(PdfDataDto.class)).stream().map(PdfDataDto::getFileId).collect(Collectors.toSet()));
        generateGetUrlCommand.setIsPublic(Boolean.TRUE);
        List<FilePreSignedUrlDto> filePreSignedUrlDtos = fileManageExtApplication.generateGetUrl(generateGetUrlCommand);
        for (FilePreSignedUrlDto filePreSignedUrlDto:filePreSignedUrlDtos) {
            PDDocument document = null;
            try {
                //记录页面和坐标
                Map<String,String> map = new HashMap<>();
                String url = filePreSignedUrlDto.getUrl();
                document = PDDocument.load(new URL(url).openStream());

                String[] signKeys = questionConfig.getSignKey().split(";");
                List<String> keyText = new ArrayList<>();
                Map<String,FixedSizeQueue<String>> codeAndQueueMap = new HashMap<>();
                for (String signKey:signKeys) {
                    String[] codeAndKeyword = signKey.split(":");
                    codeAndQueueMap.put(codeAndKeyword[0],new FixedSizeQueue<>(codeAndKeyword[1].length()));
                    keyText.add(codeAndKeyword[1]);
                }
                final int[] currentPageNumber = {0};
                PDFTextStripper stripper = new PDFTextStripper() {

                    @Override
                    protected void startPage(PDPage page) throws IOException {
                        currentPageNumber[0]++; // 每开始一页就增加页码
                        super.startPage(page);
                    }

                    @Override
                    protected void writeString(String text, List<TextPosition> textPositions) {
                        for (TextPosition textPosition : textPositions) {

                            codeAndQueueMap.forEach((k,v)->{
                                v.add(textPosition.getUnicode());
                            });
                            //log.info("PreServiceSignature fixedSizeQueue = {}", JSON.toJSONString(codeAndQueueMap));
                            Iterator<Map.Entry<String, FixedSizeQueue<String>>> iterator = codeAndQueueMap.entrySet().iterator();
                            while(iterator.hasNext()){
                                Map.Entry<String, FixedSizeQueue<String>> set = iterator.next();
                                if (keyText.get(Integer.parseInt(set.getKey())-1).equals(Joiner.on("").join(set.getValue().getQueue()))) {
                                    //log.info("找到文本: " + textPosition.getUnicode() + " X: " + textPosition.getX() + " Y: " + textPosition.getY());
                                    map.put(set.getKey(),currentPageNumber[0]+":"+textPosition.getX()+","+textPosition.getY());
                                }
                            }
                        }
                    }
                };
                stripper.getText(document);
                document.close();
                mapResult.put(filePreSignedUrlDto.getFileId()+"",map);
            } catch (Exception e) {
                log.error("toMap 异常",e);
                throw new BusinessException(new DynamicErrorCode("-1","http获取pdf文件异常"));
            }finally {
                if(document!=null){
                    document.close();
                }
            }
        }
        return mapResult;
    }
}
