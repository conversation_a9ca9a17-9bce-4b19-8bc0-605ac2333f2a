package com.jdh.o2oservice.core.domain.product.converter;

import com.jdh.o2oservice.core.domain.product.bo.JdhQuestionBO;
import com.jdh.o2oservice.core.domain.product.context.JdhQuestionContext;
import com.jdh.o2oservice.core.domain.product.model.JdhQuestion;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @date 2025/5/25
 * @description 题库
 */
@Mapper
public interface JdhQuestionDomainConvert {

    JdhQuestionDomainConvert ins = Mappers.getMapper(JdhQuestionDomainConvert.class);

    JdhQuestionBO toJdhQuestionBO(JdhQuestion jdhQuestion);

    JdhQuestion toJdhQuestion(JdhQuestionContext jdhQuestionContext);
}
