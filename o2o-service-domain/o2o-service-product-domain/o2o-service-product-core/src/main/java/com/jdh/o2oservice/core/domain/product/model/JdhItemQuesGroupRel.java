package com.jdh.o2oservice.core.domain.product.model;

import com.jd.fastjson.JSON;
import com.jdh.o2oservice.base.ducc.model.careform.QuestionGroupConfig;
import com.jdh.o2oservice.base.model.Entity;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/5/28
 * @description 服务项和节点关系
 */
@Data
public class JdhItemQuesGroupRel implements Entity<JdhItemQuesGroupRelIdentifier> {

    private Long id;//主键id

    private Long serviceItemId;//服务项目id

    private Integer show;//1展示 0不展示

    private Long version;//版本号

    private String groupCode;//节点code

    private String groupSnapshot;//节点快照数据

    private String extJson;//扩展字段

    private Integer sort;//排序由小到大




    /**
     * 数据来源分支
     */
    private String branch;

    /**
     * 是否有效 0：无效；1：有效
     */
    private Integer yn;

    /**
     * 创建人pin
     */
    private String createUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人pin
     */
    private String updateUser;

    /**
     * 更新时间
     */
    private Date updateTime;


    @Override
    public Integer version() {
        return null;
    }

    @Override
    public void versionIncrease() {

    }

    public QuestionGroupConfig groupSnapshotToBean(){
        return JSON.parseObject(this.groupSnapshot,QuestionGroupConfig.class);
    }

    @Override
    public JdhItemQuesGroupRelIdentifier getIdentifier() {
        return null;
    }
}
