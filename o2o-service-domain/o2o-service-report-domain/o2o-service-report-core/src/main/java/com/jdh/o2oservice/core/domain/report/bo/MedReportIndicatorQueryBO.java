package com.jdh.o2oservice.core.domain.report.bo;

import lombok.Data;

import java.util.Date;
import java.util.Set;

/**
 * <AUTHOR>
 * @description
 * @date 2024/11/20
 */
@Data
public class MedReportIndicatorQueryBO {

    /**
     * 实验室ID
     */
    private String stationId;

    /**
     * 项目ID
     */
    private Long serviceItemId;

    /**
     * 开始日期，用于筛选实验室报告指标的时间范围。
     */
    private Date startDate;

    /**
     * 结束日期，用于筛选实验室报告指标的时间范围。
     */
    private Date endDate;

    /**
     * 报告ID
     */
    private Set<String> reportIds;
    /**
     * 异常
     */
    private Boolean abnormalOnly;

    /**
     * 报告ID
     */
    private Set<Long> medicalPromiseIds;

    /**
     * 分页查询的起始ID，用于限制返回结果的范围。
     */
    private Integer startId;

    /**
     * 分页查询的结束ID，用于限制返回结果的范围。
     */
    private Integer endId;
}
