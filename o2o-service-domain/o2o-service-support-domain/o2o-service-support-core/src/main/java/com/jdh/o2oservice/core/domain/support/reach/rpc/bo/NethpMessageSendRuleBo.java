package com.jdh.o2oservice.core.domain.support.reach.rpc.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName NethpMessageSendRuleBo
 * @Description
 * <AUTHOR>
 * @Date 2025/6/3 16:35
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NethpMessageSendRuleBo {
    /**
     *
     */
    private Long id;

    /**
     *
     */
    private String userPin;

    /**
     *
     */
    private String userType;

    /**
     *
     */
    private String account;

    /**
     *
     */
    private String channelId;

    /**
     *
     */
    private String ruleContent;

    /**
     *
     */
    private Integer status;

    /**
     *
     */
    private String messageSwitchEnum;

}