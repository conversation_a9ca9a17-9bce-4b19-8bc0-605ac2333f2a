package com.jdh.o2oservice.core.domain.support.basic.enums;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Getter;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 派单明细状态枚举
 *
 * <AUTHOR>
 * @date 2024/04/16
 */
@Getter
public enum JdhDispatchDetailStatusEnum {

    /**
     * 派单事件
     */
    /** 派单 - 已派单 */
    DISPATCH_COMPLETED(2,  "已派单"),
    /** 派单 - 已接单 */
    DISPATCH_RECEIVED(3,  "已接单"),
    /** 派单 - 已作废 */
    DISPATCH_INVALID(4,  "已作废"),
    /** 派单 - 已过期 */
    DISPATCH_EXPIRED(5,  "已过期"),
    /** 派单 - 已拒绝 */
    DISPATCH_REJECT(6,  "已拒绝"),
    /** 派单 - 已取消 */
    DISPATCH_CANCEL(7,  "已取消"),
    ;


    /**
     * status
     */
    private Integer status;

    /**
     * desc
     */
    private String desc;


    public static final List<Integer> CONSISTENCY_STATUS = Lists.newArrayList(DISPATCH_RECEIVED.status, DISPATCH_REJECT.status, DISPATCH_CANCEL.status);

    /** */
    JdhDispatchDetailStatusEnum(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    private static final Map<Integer, JdhDispatchDetailStatusEnum> TYPE_MAP = Maps.newHashMap();
    static {
        for (JdhDispatchDetailStatusEnum value : values()) {
            TYPE_MAP.put(value.status, value);
        }
    }

    public static JdhDispatchDetailStatusEnum getByType(Integer type){
        if (Objects.isNull(type)){
            return null;
        }
        return TYPE_MAP.get(type);
    }

    public static String getDescByType(Integer type){
        if (Objects.isNull(type) || !TYPE_MAP.containsKey(type)){
            return null;
        }
        return TYPE_MAP.get(type).getDesc();
    }

}
