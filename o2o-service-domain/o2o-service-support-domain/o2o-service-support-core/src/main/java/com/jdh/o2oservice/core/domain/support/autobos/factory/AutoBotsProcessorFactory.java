package com.jdh.o2oservice.core.domain.support.autobos.factory;

import com.jdh.o2oservice.base.annotation.MapAutowired;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode;
import com.jdh.o2oservice.core.domain.support.autobos.repository.AutoBotsProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Map;
import java.util.StringJoiner;

/**
 * <AUTHOR>
 * @description
 * @date 2025/5/16
 */
@Slf4j
@Service
public class AutoBotsProcessorFactory {

    /**
     * 注入serviceMap
     */
    @MapAutowired
    private Map<String, AutoBotsProcessor> autoBotsProcessorMap;


    /**
     * 生成路由key
     * @return
     */
    public  static String createRouteKey(Object... args) {
        StringJoiner joiner = new StringJoiner(CommonConstant.CHARACTER_MIDDLE_BAR);
        Arrays.stream(args).forEach(s -> joiner.add(String.valueOf(s)));
        return joiner.toString();
    }

    /**
     * router
     * @param args
     * @return
     */
    public AutoBotsProcessor createDispatchRuleProcessor(Object... args) {
        log.info("DispatchProcessorFactory -> createDispatchRuleProcessor, args={}", args);
        if (autoBotsProcessorMap == null || args == null) {
            throw new BusinessException(BusinessErrorCode.UNKNOWN_ERROR);
        }
        String routeKey = createRouteKey(args);
        return autoBotsProcessorMap.get(routeKey);
    }

    /**
     * 根据路由获取分派方法
     * @param key
     * @return
     */
    public AutoBotsProcessor createDispatchRuleProcessor(String key){
        log.info("DispatchProcessorFactory -> createDispatchRuleProcessor, key={}", key);
        if (autoBotsProcessorMap == null || key == null) {
            throw new BusinessException(BusinessErrorCode.UNKNOWN_ERROR);
        }
        String routeKey = createRouteKey(key);
        return autoBotsProcessorMap.get(routeKey);
    };


}
