package com.jdh.o2oservice.core.domain.support.reach.rpc.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName MessageSwitchQuery
 * @Description
 * <AUTHOR>
 * @Date 2025/6/3 16:46
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MessageSwitchQuery {

    /**
     *
     */
    private String userPin;

    /**
     *
     */
    private String userType;

    /**
     *
     */
    private String account;

    /**
     *
     */
    private String channelId;

    /**
     *
     */
    private String messageSwitchEnum;
}