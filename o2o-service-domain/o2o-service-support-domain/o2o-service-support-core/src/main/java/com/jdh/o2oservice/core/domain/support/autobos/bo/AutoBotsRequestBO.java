package com.jdh.o2oservice.core.domain.support.autobos.bo;

import lombok.Data;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2025/5/13
 */
@Data
public class AutoBotsRequestBO {
    /**
     * 会话id，同一个会话相同（仅限中英文-）
     */
    private String traceId;
    /**
     * 每次问答请求，生成一个新请求
     */
    private String reqId;
    /**
     * 用户erp，必填
     */
    private String erp;
    /**
     * 回调url（流式回复使用）
     *智能体回答结果有两种获取方式：
     * 1、轮询调用searchAiResult方法获取回答结果
     * 2、如果传了callbackUrl入参，则回答的结果会通过回调url的方式主动发给调用方
     */
    private String callbackUrl;
    /**
     * 查询关键字
     */
    private String keyword;
    /**
     * 扩展参数
     *   fileUrls  List<String> 上传文件url  如何基于文档做问答，传上传文档的url
     *   messages List<String> 历史对话
     */
    private Map<String,Object> extParams;

    /**
     * 扩展ID
     */
    private String extendId;
    /**
     * 扩展ID类型
     */
    private Integer extendIdType;

    private String scene;
}
