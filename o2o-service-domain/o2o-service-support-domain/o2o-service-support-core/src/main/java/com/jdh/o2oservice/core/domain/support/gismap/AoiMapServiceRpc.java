package com.jdh.o2oservice.core.domain.support.gismap;

import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.core.domain.support.gismap.bo.*;
import com.jdh.o2oservice.core.domain.support.gismap.response.*;

import java.util.List;

/**
 * @InterfaceName:AoiMapService
 * @Description: 地图服务
 * @Author: yaoqinghai
 * @Date: 2024/4/26 10:53
 * @Vserion: 1.0
 **/
public interface AoiMapServiceRpc {

    /**
     * 创建地图
     *
     * @param createMapBo
     * @return
     */
    CreateMapResponse createMap(CreateMapBo createMapBo);

    /**
     * 创建图层
     *
     * @param createLayerBo
     * @return
     */
    CreateLayerResponse createLayer(CreateLayerBo createLayerBo);

    /**
     * 创建围栏
     *
     * @param createLayerBo
     * @return
     */
    CreateElementResponse createAngelStation(CreateElementBo createLayerBo);

    /**
     * 修改围栏基础信息
     *
     * @param modifyElementBaseBo
     * @return
     */
    Boolean modifyAngelStationBase(ModifyElementBaseBo modifyElementBaseBo);

    /**
     * 修改围栏地理数据
     *
     * @param modifyElementDataBo
     * @return
     */
    ModifyElementDataResponse modifyAngelStation(ModifyElementDataBo modifyElementDataBo);

    /**
     * 查询围栏数据
     *
     * @param queryElementBo
     * @return
     */
    QueryElementResponse queryElement(QueryElementBo queryElementBo);

    /**
     * 删除围栏
     *
     * @param removeElementBaseBo
     * @return
     */
    Boolean removeElement(RemoveElementBaseBo removeElementBaseBo);

    /**
     * 分单
     *
     * @param queryFenceBo
     * @return
     */
    List<QueryFenceResponse> queryElementList(QueryFenceBo queryFenceBo);

    /**
     * 与图:经纬度转换
     * https://joyspace.jd.com/h/personal/pages/D3ecYLP8cETHjsGZIwsI
     * 经度和纬度用","分割，经度在前，纬度在后，经纬度小数点后不得超过8位。多个坐标对之间用”;”进行分隔最多支持40对坐标。
     * @param geoCoordinateConversionBo
     * @return
     */
    String geoCoordinateConversion(GeoCoordinateConversionBo geoCoordinateConversionBo);
}
