package com.jdh.o2oservice.core.domain.support.autobos.model;


import com.jdh.o2oservice.base.model.Aggregate;
import com.jdh.o2oservice.base.model.AggregateCode;
import com.jdh.o2oservice.base.model.DomainCode;
import com.jdh.o2oservice.base.model.DomainEnum;
import com.jdh.o2oservice.core.domain.support.basic.enums.SupportAggregateEnum;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2025/5/15
 */
@Data
public class AutoBotsRecord implements Aggregate<AutoBotsRecordIdentifier>{



    private Long id;

    /**
     * businessId
     */
    private Long businessId;

    /**
     * 会话id，同一个会话相同（仅限中英文-）
     */
    private String traceId;
    /**
     * 每次问答请求，生成一个新请求
     */
    private String reqId;
    /**
     * 用户erp，必填
     */
    private String erp;
    /**
     * 回调url（流式回复使用）
     *智能体回答结果有两种获取方式：
     * 1、轮询调用searchAiResult方法获取回答结果
     * 2、如果传了callbackUrl入参，则回答的结果会通过回调url的方式主动发给调用方
     */
//    private String callbackUrl;
    /**
     * 查询关键字
     */
    private String keywordText;

    /**
     * 扩展参数
     *   fileUrls  List<String> 上传文件url  如何基于文档做问答，传上传文档的url
     *   messages List<String> 历史对话
     */
    private String extParams;
    /**
     * 扩展ID
     */
    private String extendId;
    /**
     * 扩展ID类型
     */
    private Integer extendIdType;

    /**
     * 场景，会以场景去路由
     */
    private String scene;

    /**
     * 是否有效
     */
    private Integer yn;

    /**
     * creator
     */
    private String createUser;

    /**
     * createTime
     */
    private Date createTime;

    /**
     * updater
     */
    private String updateUser;

    /**
     * updateTime
     */
    private Date updateTime;

    /**
     * 版本
     */
    private Integer version;

    /**
     * 状态
     */
    private String status;
    /**
     * 工作流ID，执行工作流时传工作流ID
     */
    private String workflowId;

    /**
     * 结果
     */
    private String result;


    /**
     * 聚合所属领域编码
     *
     * @return
     */
    @Override
    public DomainCode getDomainCode() {
        return DomainEnum.BASE;
    }

    /**
     * 获取聚合编码
     *
     * @return {@link AggregateCode}
     */
    @Override
    public AggregateCode getAggregateCode() {
        return SupportAggregateEnum.AUTO_BOTS;
    }

    /**
     * 版本
     *
     * @return {@link Integer}
     */
    @Override
    public Integer version() {
        return version;
    }

    /**
     * 版本增加
     */
    @Override
    public void versionIncrease() {
        version++;
    }

    /**
     * 获取标识符
     *
     * @return {@link ID}
     */
    @Override
    public AutoBotsRecordIdentifier getIdentifier() {
        return new AutoBotsRecordIdentifier(businessId);
    }
}
