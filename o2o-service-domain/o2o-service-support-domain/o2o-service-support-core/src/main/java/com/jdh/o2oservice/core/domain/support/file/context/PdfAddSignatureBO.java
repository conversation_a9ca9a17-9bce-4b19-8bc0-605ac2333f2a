package com.jdh.o2oservice.core.domain.support.file.context;

import com.alibaba.fastjson.JSON;
import lombok.Data;

import java.util.List;

/**
 * PDF追加签名BO
 * @author: yang<PERSON>yu
 * @date: 2024/4/30 4:56 下午
 * @version: 1.0
 */
@Data
public class PdfAddSignatureBO {
    /** 领域编码 */
    private String domainCode;
    /** 文件类型 */
    private String fileBizType;
    /** 签名位置 */
    private Float[] position;
    /** 签名图片大小 */
    private Float[] scale;
    /** PDF第几页 */
    private Integer pageNum;
    /**
     * 模版文件OSS路径
     */
    private String templateFilePath;
    /** 代理人签名位置 */
    private Float[] agentPosition;
    /** 签名时间位置 */
    private Float[] timePosition;
    /** 代理人关系位置 */
    private Float[] relationPosition;
    /** 服务者签字位置 */
    private Float[] nursePosition;

    private Integer signatureType;
    /**
     * 代签人关系
     * 1-配偶、2-父母、3-子女、4-其它
     **/
    private Integer relation;

    /**
     * 代签人关系选择其它时的补充内容
     **/
    private String relationStr;
}
