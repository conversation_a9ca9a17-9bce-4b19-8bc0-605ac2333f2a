package com.jdh.o2oservice.core.domain.support.ship.param;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/6/3
 * @description 下飞行需求
 */
@Data
public class AirportCreateParam {

    private String key;//加密key

    @JSONField(name="flight_work")
    private FlightWork flightWork;

    @Data
    public static class FlightWork{
        @JSONField(name="start_airport_id")
        private String startAirportId;//起飞机场ID

        @JSONField(name="end_airport_id")
        private String endAirportId;//终点机场ID

        @JSONField(name="order_no")
        private String orderNo;//业务单号

        @JSONField(name="expect_takeoff_time")
        private Long expectTakeoffTime;//期望起飞时间

        @JSONField(name="cargo_code")
        private String cargoCode="AAA";//获取交接码

        @JSONField(name="cargo_type")
        private String cargoType="BBB";//货品类型

        private String note="备注";//
    }
}
