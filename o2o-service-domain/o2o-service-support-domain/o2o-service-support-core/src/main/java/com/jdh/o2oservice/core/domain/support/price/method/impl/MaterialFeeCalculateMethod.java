package com.jdh.o2oservice.core.domain.support.price.method.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.TypeReference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jdh.o2oservice.core.domain.support.basic.enums.PricingServiceFactObjectEnum;
import com.jdh.o2oservice.core.domain.support.basic.enums.PricingServiceFeeCalculateEnum;
import com.jdh.o2oservice.core.domain.support.price.context.PricingServiceCalculateContext;
import com.jdh.o2oservice.core.domain.support.price.method.PricingServiceFeeCalculateMethod;
import com.jdh.o2oservice.export.medicalpromise.dto.MedicalPromiseDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.checkerframework.checker.units.qual.C;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @ClassName MaterialFeeCalculateMethod
 * @Description
 * <AUTHOR>
 * @Date 2025/6/12 21:24
 **/
@Service
@Slf4j
public class MaterialFeeCalculateMethod implements PricingServiceFeeCalculateMethod {

    /**
     * 根据事实对象计算费项金额
     * @param context
     * @return
     */
    @Override
    public BigDecimal calculatePrice(PricingServiceCalculateContext context) {
        log.info("MaterialFeeCalculateMethod -> calculatePrice start");
        Map<String, Object> factObjectMap = context.getFactObjectMap();
        // 1. 提取skuServicePriceMap列表
        Object o = factObjectMap.get(PricingServiceFactObjectEnum.MATERIAL_FEE_CONFIG.getCode());
        //如果不存在，则直接返回0
        if (Objects.isNull(o)) {
            return BigDecimal.ZERO;
        }
        Map<Long,BigDecimal> materialFeeConfig =  Convert.convert(new TypeReference<Map<Long,BigDecimal>>(){}, o);

        // 2.提取Promise数据
        Object factObject = factObjectMap.get(PricingServiceFactObjectEnum.MEDICAL_PROMISE_LIST.getCode());
        if (Objects.isNull(factObject)) {
            return null;
        }
        List<MedicalPromiseDTO> medicalPromiseDTOList = Convert.convert(new TypeReference<List<MedicalPromiseDTO>>() {}, factObject);

        if (CollectionUtils.isEmpty(medicalPromiseDTOList)) {
            return BigDecimal.ZERO;
        }

        // 3. 提取结算比例
        BigDecimal ratio = BigDecimal.ONE;
        Object settleConfigObject = factObjectMap.get(PricingServiceFactObjectEnum.ANGEL_SETTLEMENT_FEE_RATIO_CONFIG.getCode());
        //如果不存在，则直接返回0
        if (Objects.nonNull(settleConfigObject)) {
            JSONObject settleConfig = JSON.parseObject(JSON.toJSONString(settleConfigObject));
            ratio = Objects.nonNull(settleConfig) && Objects.nonNull(settleConfig.getBigDecimal("MATERIAL_FEE")) ? settleConfig.getBigDecimal("MATERIAL_FEE") : BigDecimal.ONE;
        }
        log.info("MaterialFeeCalculateMethod -> 提取结算比例 ratio={}", ratio);

        BigDecimal fee = BigDecimal.ZERO;
        //计算服务费
        //按人分组，每个人的检测单按sku分组。检测人ID：SKU：检测单列表
        Map<Long, Map<Long, List<MedicalPromiseDTO>>> patientSku2MedicalPromiseMap = medicalPromiseDTOList.stream().collect(
                Collectors.groupingBy(
                        MedicalPromiseDTO::getPromisePatientId, // 第一层分组，按serviceId
                        Collectors.groupingBy(
                                MedicalPromiseDTO::getServiceId // 第二层分组，按serviceItemId
                        )
                )
        );

        for (Map.Entry<Long, Map<Long, List<MedicalPromiseDTO>>> entry : patientSku2MedicalPromiseMap.entrySet()) {
            //检测人ID
            Long promisePatientId = entry.getKey();
            Map<Long, List<MedicalPromiseDTO>> sku2MedicalPromiseMap = entry.getValue();
            for (Map.Entry<Long, List<MedicalPromiseDTO>> skuEntry : sku2MedicalPromiseMap.entrySet()) {
                //sku编号
                Long skuId = skuEntry.getKey();
                BigDecimal angelBasicSettlementPrice = materialFeeConfig.get(skuId);
                if (Objects.isNull(angelBasicSettlementPrice)) {
                    continue;
                }
                fee = fee.add(angelBasicSettlementPrice);
            }
        }
        // 5. 计算最终结果
        return fee.multiply(ratio).setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * 获取map key
     * @return
     */
    @Override
    public String getMapKey() {
        return PricingServiceFeeCalculateEnum.MATERIAL_FEE_CALCULATE.getCode();
    }
}