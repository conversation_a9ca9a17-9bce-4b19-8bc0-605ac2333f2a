package com.jdh.o2oservice.core.domain.support.ship.model;

import com.jdh.o2oservice.base.model.Identifier;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName:AngelShipIdentifier
 * @Description:
 * @Author: yaoqing<PERSON>
 * @Date: 2024/4/21 14:42
 * @Vserion: 1.0
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UavAngelShipSupportIdentifier implements Identifier {

    /**
     * 运单号
     */
    public Long shipId;

    /**
     * 序列化
     *
     * @return {@link String}
     */
    @Override
    public String serialize() {
        return String.valueOf(shipId);
    }
}
