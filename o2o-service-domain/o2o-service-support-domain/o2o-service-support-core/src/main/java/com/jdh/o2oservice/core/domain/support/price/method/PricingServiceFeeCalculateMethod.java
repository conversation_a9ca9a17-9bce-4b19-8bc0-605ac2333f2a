package com.jdh.o2oservice.core.domain.support.price.method;

import com.jdh.o2oservice.base.processor.MapAutowiredKey;
import com.jdh.o2oservice.core.domain.support.price.context.PricingServiceCalculateContext;

import java.math.BigDecimal;
import java.util.Map;

/**
 * @ClassName PricingServiceFeeCalculateMethod
 * @Description
 * <AUTHOR>
 * @Date 2025/5/16 16:31
 **/
public interface PricingServiceFeeCalculateMethod extends MapAutowiredKey {

    /**
     * 根据事实对象计算费项金额
     * @param context
     * @return
     */
    BigDecimal calculatePrice(PricingServiceCalculateContext context);
}