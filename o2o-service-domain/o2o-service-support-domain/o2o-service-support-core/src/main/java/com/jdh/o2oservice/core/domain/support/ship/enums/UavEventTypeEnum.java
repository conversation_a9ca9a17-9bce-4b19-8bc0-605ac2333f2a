package com.jdh.o2oservice.core.domain.support.ship.enums;

import com.jdh.o2oservice.base.event.EventType;
import com.jdh.o2oservice.base.model.AggregateCode;
import com.jdh.o2oservice.core.domain.support.ship.event.ShunFengAngelShipSupportEventBody;
import com.jdh.o2oservice.core.domain.support.ship.event.UavAngelShipSupportEventBody;
import lombok.AllArgsConstructor;

/**
 * AngelWorkEventTypeEnum
 * @author: yaoqinghai
 * @date: 2024/01/22 11:13
 * @version: 1.0
 */
@AllArgsConstructor
public enum UavEventTypeEnum implements EventType {

    /**
     *顺丰特定取消事件
     */
    ANGEL_SHIP_RECEIVE(AngelWorkSupportAggregateEnum.SHIP, "uavReceive", "无人机接单", UavAngelShipSupportEventBody.class),

    ;

    private AngelWorkSupportAggregateEnum aggregateCode;

    private String code;

    private String desc;

    private Class<?> bodyClass;


    /**
     * 事件所属的领域
     */
    @Override
    public AggregateCode getAggregateType() {
        return aggregateCode;
    }

    /**
     * 事件编码
     */
    @Override
    public String getCode() {
        return code;
    }

    /**
     * 事件描述
     */
    @Override
    public String getDesc() {
        return desc;
    }

    /**
     * @return
     */
    @Override
    public Class<?> bodyClass() {
        return bodyClass;
    }
}
