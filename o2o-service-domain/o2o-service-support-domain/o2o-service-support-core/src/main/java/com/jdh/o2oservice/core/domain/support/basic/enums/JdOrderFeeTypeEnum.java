package com.jdh.o2oservice.core.domain.support.basic.enums;

import com.google.common.collect.Maps;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.Objects;

/**
 * JD订单费用类型列举
 *
 * <AUTHOR>
 * @date 2024/04/17
 */
@Getter
public enum JdOrderFeeTypeEnum {

    /**
     * 费项
     */
    TIME_PERIOD_APPOINTMENT_TYPE_IMMEDIATELY(11,FeeAggregateTypeEnum.TIME_PERIOD_FEE,"立即上门", ""),
    TIME_PERIOD_TIME_TYPE_HOLIDAY(12,FeeAggregateTypeEnum.TIME_PERIOD_FEE,"节假日", ""),

    HOME_VISIT(31,FeeAggregateTypeEnum.HOME_VISIT_FEE,"上门费", "本次上门收入"),

    UPGRADE_ANGEL(40,FeeAggregateTypeEnum.DYNAMIC_FEE,"升级护士费", ""),

    DYNAMIC(42,FeeAggregateTypeEnum.DYNAMIC_FEE,"动态调整费", ""),

    NIGHT_SERVICE_FEE(43,FeeAggregateTypeEnum.TIME_PERIOD_FEE,"夜间服务费", ""),

    PEAK_SERVICE_FEE(44,FeeAggregateTypeEnum.TIME_PERIOD_FEE,"高峰时段服务费", ""),

    MATERIAL_FEE(45,FeeAggregateTypeEnum.MATERIAL_FEE,"耗材补贴费", "耗材补贴收入"),

    ANGEL_SERVICE_FEE(51,FeeAggregateTypeEnum.SERVICE_FEE,"服务费", "实际服务项目收入"),

    PLATFORM_SERVICE_FEE(61,FeeAggregateTypeEnum.PLATFORM_SERVICE_FEE,"平台服务费", "由平台提供的保险、录音及其他信息服务所产生的费用"),

    PLATFORM_SUBSIDY_FEE(71,FeeAggregateTypeEnum.PLATFORM_SUBSIDY_FEE,"平台补贴", "根据履约情况平台给予相应补贴"),

    DISPATCH_MARKUP_FEE(81,FeeAggregateTypeEnum.DISPATCH_MARKUP_FEE,"派单加价费", ""),
    ;

    /** */
    JdOrderFeeTypeEnum(Integer type,FeeAggregateTypeEnum aggregateTypeEnum,String code, String description) {
        this.type = type;
        this.aggregateTypeEnum = aggregateTypeEnum;
        this.code = code;
        this.description = description;
    }

    /** */
    private Integer type;
    /** */
    private FeeAggregateTypeEnum aggregateTypeEnum;
    /** */
    private String code;

    /**
     * 描述
     */
    private String description;

    private static final Map<Integer, JdOrderFeeTypeEnum> TYPE_MAP = Maps.newHashMap();
    private static final Map<String, JdOrderFeeTypeEnum> NAME_MAP = Maps.newHashMap();
    private static final Map<String, JdOrderFeeTypeEnum> CODE_MAP = Maps.newHashMap();


    static {
        for (JdOrderFeeTypeEnum value : values()) {
            TYPE_MAP.put(value.type, value);
            NAME_MAP.put(value.name(), value);
            CODE_MAP.put(value.code, value);
        }
    }

    public static JdOrderFeeTypeEnum getByType(Integer type){
        if (Objects.isNull(type)){
            return null;
        }
        return TYPE_MAP.get(type);

    }

    public static JdOrderFeeTypeEnum getByName(String name){
        if (StringUtils.isEmpty(name)){
            return null;
        }
        return NAME_MAP.get(name);

    }

    public static JdOrderFeeTypeEnum getByCode(String code){
        if (StringUtils.isEmpty(code)){
            return null;
        }
        return CODE_MAP.get(code);

    }
}
