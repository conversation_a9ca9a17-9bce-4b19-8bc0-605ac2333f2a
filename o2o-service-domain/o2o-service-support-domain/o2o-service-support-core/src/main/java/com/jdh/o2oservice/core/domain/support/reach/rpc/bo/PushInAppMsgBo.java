package com.jdh.o2oservice.core.domain.support.reach.rpc.bo;

import com.alibaba.fastjson.JSON;
import com.jdh.o2oservice.core.domain.support.reach.context.ExecuteReachTaskContext;
import com.jdh.o2oservice.core.domain.support.reach.model.JdhReachChannelAccount;
import com.jdh.o2oservice.core.domain.support.reach.model.JdhReachMessage;
import com.jdh.o2oservice.core.domain.support.reach.model.JdhReachTemplate;
import com.jdh.o2oservice.core.domain.support.reach.valueobject.ReachMessageBizType;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalUnit;
import java.util.Objects;

/**
 * 推送APP站内消息的BO
 * @author: yangxiyu
 * @date: 2024/4/22 11:22 上午
 * @version: 1.0
 */
@Data
public class PushInAppMsgBo {
    /** */
    private String token;
    /** */
    private String uri;
    /** */
    private String accountNo;
    /** */
    private String pin;
    /** 过期时间时间戳 */
    private Long expireTime;
    /** 过期时间单位 */
    private TemporalUnit timeUnit;
    /** */
    private JdhReachMessage message;
    /**
     * 是否需要铃声通知
     */
    private Boolean bell;

    /**
     * 使用语音播报
     */
    private Integer useVoice;

    /**
     * 语音播报配置文件
     */
    private PushVoiceConfigBO pushVoiceConfig;

    /**
     *
     */
    public PushInAppMsgBo() {
    }

    /**
     *
     * @param message
     */
    public PushInAppMsgBo(JdhReachMessage message, ExecuteReachTaskContext context) {
        JdhReachChannelAccount account = context.getChannelAccount();
        ReachMessageBizType messageType = context.getMessageType();
        JdhReachTemplate template = context.getTemplate();
        this.message = message;
        this.token = account.getChannelToken();
        this.uri = account.getUri();
        this.accountNo = account.getChannelAccountNo();
        this.pin = message.getUserPin();
        this.expireTime = messageType.getExpireTime();
        this.timeUnit = ChronoUnit.SECONDS;
        this.bell = template.getBell();
        this.useVoice = Objects.nonNull(template.getExtend()) ? template.getExtend().getUseVoice() : 0;
        this.pushVoiceConfig = Objects.nonNull(template.getExtend()) && StringUtils.isNotBlank(template.getExtend().getVoiceConfig()) ? JSON.parseObject(template.getExtend().getVoiceConfig(), PushVoiceConfigBO.class) : null;
    }
}
