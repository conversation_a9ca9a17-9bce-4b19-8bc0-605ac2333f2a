
package com.jdh.o2oservice.core.domain.support.ship.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/3
 * @description 下飞行需求
 */
@Data
public class AirportUnbindOrderDto {

    private String key;//加密key

    @JSONField(name="flight_work_id")
    private String flightWorkId;//飞行需求ID

    @JSONField(name="order_nos")
    private List<String> orderNos;//业务单号
}
