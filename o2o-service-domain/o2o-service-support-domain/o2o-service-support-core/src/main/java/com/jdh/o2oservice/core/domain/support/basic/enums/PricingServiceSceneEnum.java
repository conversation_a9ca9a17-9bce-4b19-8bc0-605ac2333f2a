package com.jdh.o2oservice.core.domain.support.basic.enums;

import com.google.common.collect.Maps;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

/**
 * @ClassName PricingServiceSceneEnum
 * @Description
 * <AUTHOR>
 * @Date 2025/4/30 14:09
 **/
@Getter
public enum PricingServiceSceneEnum {

    ANGEL_PRE_INCOME_PRICE_CALCULATE("angelPreInComeCalculate","护士预估收入计算"),

    ANGEL_SETTLEMENT_PRICE_CALCULATE("angelSettlementPriceCalculate","护士结算价格计算"),

    ANGEL_SETTLEMENT_PRICE_SNAPSHOT_REFRESH_CALCULATE("angelSettlementPriceSnapshotRefreshCalculate","护士结算价格快照刷新计算"),
    ;

    /** */
    PricingServiceSceneEnum(String scene, String description) {
        this.scene = scene;
        this.description = description;
    }

    /** */
    private String scene;
    /** */
    private String description;

    private static final Map<String, PricingServiceSceneEnum> SCENE_MAP = Maps.newHashMap();

    static {
        for (PricingServiceSceneEnum value : values()) {
            SCENE_MAP.put(value.scene, value);
        }
    }

    /**
     *
     * @param scene
     * @return
     */
    public static PricingServiceSceneEnum getByScene(String scene){
        if (StringUtils.isEmpty(scene)){
            return null;
        }
        return SCENE_MAP.get(scene);
    }
}