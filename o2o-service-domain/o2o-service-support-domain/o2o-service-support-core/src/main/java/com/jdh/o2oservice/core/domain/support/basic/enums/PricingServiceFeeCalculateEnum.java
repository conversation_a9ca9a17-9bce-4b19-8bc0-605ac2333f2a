package com.jdh.o2oservice.core.domain.support.basic.enums;

import com.google.common.collect.Maps;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

/**
 * @ClassName PricingServiceFeeCalculateEnum
 * @Description
 * <AUTHOR>
 * @Date 2025/5/16 16:51
 **/
@Getter
public enum PricingServiceFeeCalculateEnum {

    ANGEL_SERVICE_FEE_CALCULATE("angelServiceFeeCalculate","护士服务费计算方法"),

    MATERIAL_FEE_CALCULATE("materialFeeCalculate","护士耗材费计算方法"),
    ;


    /** */
    PricingServiceFeeCalculateEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    /** */
    private String code;
    /** */
    private String description;

    private static final Map<String, PricingServiceFeeCalculateEnum> CODE_MAP = Maps.newHashMap();

    static {
        for (PricingServiceFeeCalculateEnum value : values()) {
            CODE_MAP.put(value.code, value);
        }
    }

    /**
     *
     * @param code
     * @return
     */
    public static PricingServiceFeeCalculateEnum getEnumByCode(String code){
        if (StringUtils.isEmpty(code)){
            return null;
        }
        return CODE_MAP.get(code);
    }
}