package com.jdh.o2oservice.core.domain.support.autobos.model;

import com.jdh.o2oservice.base.model.Identifier;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @date 2025/5/15
 */
@Data
@Builder
@AllArgsConstructor
public class AutoBotsRecordIdentifier implements Identifier {

    /**
     * 自动化业务记录的唯一标识符。
     */
    private Long businessId;

    /**
     * 序列化
     *
     * @return {@link String}
     */
    @Override
    public String serialize() {
        return String.valueOf(businessId);
    }
}
