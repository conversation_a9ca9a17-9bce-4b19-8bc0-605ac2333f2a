package com.jdh.o2oservice.core.domain.support.price.method.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.TypeReference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jdh.o2oservice.core.domain.support.basic.enums.PricingServiceFactObjectEnum;
import com.jdh.o2oservice.core.domain.support.basic.enums.PricingServiceFeeCalculateEnum;
import com.jdh.o2oservice.core.domain.support.price.context.PricingServiceCalculateContext;
import com.jdh.o2oservice.core.domain.support.price.method.PricingServiceFeeCalculateMethod;
import com.jdh.o2oservice.export.medicalpromise.dto.MedicalPromiseDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName AngelServiceFeeCalculateMethod
 * @Description
 * <AUTHOR>
 * @Date 2025/5/16 16:39
 **/
@Service
@Slf4j
public class AngelServiceFeeCalculateMethod implements PricingServiceFeeCalculateMethod {

    /**
     * 根据事实对象计算费项金额
     * @param context
     * @return
     */
    @Override
    public BigDecimal calculatePrice(PricingServiceCalculateContext context) {
        log.info("AngelServiceFeeCalculateMethod -> calculatePrice start");
        Map<String, Object> factObjectMap = context.getFactObjectMap();
        // 1. 提取skuServicePriceMap列表
        Object o = factObjectMap.get(PricingServiceFactObjectEnum.SKU_SERVICE_AMOUNT_MAP.getCode());
        //如果不存在，则直接返回0
        if (Objects.isNull(o)) {
            return BigDecimal.ZERO;
        }
        Map<Long,BigDecimal> skuServicePriceMap =  Convert.convert(new TypeReference<Map<Long,BigDecimal>>(){}, o);

        // 2.提取Promise数据
        Object factObject = factObjectMap.get(PricingServiceFactObjectEnum.MEDICAL_PROMISE_LIST.getCode());
        if (Objects.isNull(factObject)) {
            return null;
        }
        List<MedicalPromiseDTO> medicalPromiseDTOList = Convert.convert(new TypeReference<List<MedicalPromiseDTO>>() {}, factObject);

        if (CollectionUtils.isEmpty(medicalPromiseDTOList)) {
            return BigDecimal.ZERO;
        }
        // 3. 提取结算比例
        BigDecimal ratio = BigDecimal.ONE;
        Object settleConfigObject = factObjectMap.get(PricingServiceFactObjectEnum.ANGEL_SETTLEMENT_FEE_RATIO_CONFIG.getCode());
        //如果不存在，则直接返回0
        if (Objects.nonNull(settleConfigObject)) {
            JSONObject settleConfig = JSON.parseObject(JSON.toJSONString(settleConfigObject));
            ratio = Objects.nonNull(settleConfig) && Objects.nonNull(settleConfig.getBigDecimal("ANGEL_SERVICE_FEE")) ? settleConfig.getBigDecimal("ANGEL_SERVICE_FEE") : BigDecimal.ONE;
        }
        log.info("AngelServiceFeeCalculateMethod -> 提取结算比例 ratio={}", ratio);

        BigDecimal fee = BigDecimal.ZERO;
        //计算服务费
        //按人分组，每个人的检测单按sku分组。检测人ID：SKU：检测单列表
        Map<Long, Map<Long, List<MedicalPromiseDTO>>> patientSku2MedicalPromiseMap = medicalPromiseDTOList.stream().collect(
                Collectors.groupingBy(
                    MedicalPromiseDTO::getPromisePatientId, // 第一层分组，按serviceId
                    Collectors.groupingBy(
                        MedicalPromiseDTO::getServiceId // 第二层分组，按serviceItemId
                    )
                )
        );

        for (Map.Entry<Long, Map<Long, List<MedicalPromiseDTO>>> entry : patientSku2MedicalPromiseMap.entrySet()) {
            //检测人ID
            Long promisePatientId = entry.getKey();
            Map<Long, List<MedicalPromiseDTO>> sku2MedicalPromiseMap = entry.getValue();
            for (Map.Entry<Long, List<MedicalPromiseDTO>> skuEntry : sku2MedicalPromiseMap.entrySet()) {
                //sku编号
                Long skuId = skuEntry.getKey();
                BigDecimal angelBasicSettlementPrice = skuServicePriceMap.get(skuId);
                if (Objects.isNull(angelBasicSettlementPrice)) {
                    continue;
                }
                fee = fee.add(angelBasicSettlementPrice);
            }
        }
        // 5. 计算最终结果
        return fee.multiply(ratio).setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * 获取map key
     * @return
     */
    @Override
    public String getMapKey() {
        return PricingServiceFeeCalculateEnum.ANGEL_SERVICE_FEE_CALCULATE.getCode();
    }
}