package com.jdh.o2oservice.core.domain.support.price.enums;

import com.jdh.o2oservice.base.exception.errorcode.AbstractErrorCode;
import com.jdh.o2oservice.base.model.DomainEnum;
import lombok.ToString;

/**
 * @ClassName PricingServiceErrorCode
 * @Description
 * <AUTHOR>
 * @Date 2025/6/4 20:23
 **/
@ToString
public enum PricingServiceErrorCode implements AbstractErrorCode {

    /** 无用户上门地址 **/
    USER_ADDRESS_NOT_EXIST(DomainEnum.BASE, "09001", "无用户上门地址"),

    /** 用户上门地址无省市区信息 **/
    USER_ADDRESS_AREA_NOT_EXIST(DomainEnum.BASE, "09002", "用户上门地址无省市区信息"),

    /** 无护士信息 **/
    ANGEL_NOT_EXIST(DomainEnum.BASE, "09003", "无护士信息"),

    /** 无护士全兼职标签 **/
    ANGEL_NATURE_NOT_EXIST(DomainEnum.BASE, "09004", "无护士全兼职标签"),

    /** 无检测单数据 **/
    MEDICAL_PROMISE_NOT_EXIST(DomainEnum.BASE, "09005", "无检测单数据"),

    /** 无履约单数据 **/
    USER_PROMISE_NOT_EXIST(DomainEnum.BASE, "09006", "无履约单数据"),

    /** 无用户预约上门时间 **/
    USER_APPOINTMENT_TIME_NOT_EXIST(DomainEnum.BASE, "09007", "无用户预约上门时间"),

    ;

    /**
     * PromiseErrorCode
     *
     * @param domainEnum  域枚举
     * @param code        代码
     * @param description 描述
     */
    PricingServiceErrorCode(DomainEnum domainEnum, String code, String description) {
        this.domainEnum = domainEnum;
        this.code = code;
        this.description = description;
    }

    /** */
    private DomainEnum domainEnum;

    /** */
    private String code;
    /**
     * 展示给客户的错误提示，因为有的场景把系统异常传递给客户展示，体验不好
     */
    private String description;

    /**
     * getCode
     *
     * @return {@link String}
     */
    @Override
    public String getCode() {
        return this.code;
    }

    /**
     * getDescription
     *
     * @return {@link String}
     */
    @Override
    public String getDescription() {
        return this.description;
    }
}