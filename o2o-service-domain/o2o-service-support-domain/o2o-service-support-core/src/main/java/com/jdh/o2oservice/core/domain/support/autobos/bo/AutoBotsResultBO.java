package com.jdh.o2oservice.core.domain.support.autobos.bo;

import lombok.Data;
import java.util.List;
/**
 * 智能体结果BO
 * <AUTHOR>
 * @description
 * @date 2025/5/13
 */
@Data
public class AutoBotsResultBO {
    /**
     * 是否结束标志。
     */
    private Boolean finished;

    /**
     * 增量回复内容。
     */
    private String response;

    /**
     * 全量回复内容。
     */
    private String responseAll;

    /**
     * 结果类型。
     */
    private String responseType;

    /**
     * 状态。
     */
    private String status;

    /**
     * 跟踪ID。
     */
    private String traceId;

    /**
     * 回复耗时。
     */
    private Integer useTimes;

    /**
     * 消耗的token数量。
     */
    private Integer useTokens;

    /**
     * 关联文档列表。
     */
    private List<RelatedDocumentBO> relDoc;

}
