package com.jdh.o2oservice.core.domain.support.price.handler;

import com.jdh.o2oservice.base.processor.MapAutowiredKey;
import com.jdh.o2oservice.core.domain.support.price.context.PricingServiceCalculateContext;

/**
 * @ClassName PricingServiceFactObjectHandler
 * @Description
 * <AUTHOR>
 * @Date 2025/4/29 14:57
 **/
public interface PricingServiceFactObjectHandler extends MapAutowiredKey {

    /**
     * 获取事实对象
     * @param context
     * @return
     */
    Object getFactObject(PricingServiceCalculateContext context);
}