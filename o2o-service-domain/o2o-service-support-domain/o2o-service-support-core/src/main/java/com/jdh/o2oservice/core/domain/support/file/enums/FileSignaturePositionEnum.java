package com.jdh.o2oservice.core.domain.support.file.enums;



public enum FileSignaturePositionEnum {

    SELF_SIGNATURE(1, "本人签名"),
    AGENT_SIGNATURE(2, "代理人签名"),
    SIGNATURE_TIME(3, "签字时间"),
    RELATION(4, "与本人关系"),

    NURSE_SIGNATURE(5,"服务者签字"),
    ;

    FileSignaturePositionEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    private Integer code;
    private String desc;


    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }
}
