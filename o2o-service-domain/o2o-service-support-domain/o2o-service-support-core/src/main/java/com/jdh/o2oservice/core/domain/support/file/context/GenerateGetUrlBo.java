package com.jdh.o2oservice.core.domain.support.file.context;

import com.google.common.collect.Lists;
import com.jdh.o2oservice.base.util.AssertUtils;
import com.jdh.o2oservice.base.util.TimeUtils;
import com.jdh.o2oservice.core.domain.support.file.model.JdhFileIdentifier;
import lombok.Data;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * 生产文件put链接bo
 * @author: yangxiyu
 * @date: 2024/4/26 6:56 下午
 * @version: 1.0
 */
@Data
public class GenerateGetUrlBo {
    /** 默认的过期时间值 */
    private static final  Long DEFAULT_EXPIRE_VALUE = 30L;
    /** 文件ID */
    private Set<Long> fileIds;

    /** 是否公网访问 */
    private Boolean isPublic;
    /** 过期时间 */
    private Date expireTime;
    /**
     *
     */
    private List<JdhFileIdentifier> identifierList;
    /**
     *
     */
    public void init(){
        AssertUtils.isNotEmpty(fileIds, "fileIds is require");
        AssertUtils.nonNull(isPublic, "isPublic is require");
        identifierList = Lists.newArrayList();
        for (Long fileId : fileIds) {
            identifierList.add(new JdhFileIdentifier(fileId));
        }

        if (Objects.isNull(expireTime)){

            LocalDateTime localDateTime = LocalDateTime.now().plus(DEFAULT_EXPIRE_VALUE, ChronoUnit.MINUTES);
            expireTime = TimeUtils.localDateTimeToDate(localDateTime);
        }

    }
}
