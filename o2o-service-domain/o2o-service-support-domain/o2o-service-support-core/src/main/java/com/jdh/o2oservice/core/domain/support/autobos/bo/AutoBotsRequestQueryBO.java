package com.jdh.o2oservice.core.domain.support.autobos.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description
 * @date 2025/5/15
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AutoBotsRequestQueryBO {

    /**
     * 自增ID，用于标识每个AutoBotsRequestQueryBO实例的唯一性。
     */
    private Long id;

    /**
     * businessId
     */
    private Long businessId;
    /**
     * 会话id，同一个会话相同（仅限中英文-）
     */
    private String traceId;
    /**
     * 每次问答请求，生成一个新请求
     */
    private String reqId;
    /**
     * 用户erp，必填
     */
    private String erp;
    /**
     * 扩展ID
     */
    private String extendId;
    /**
     * 扩展ID类型
     */
    private Integer extendIdType;

    /**
     * 场景，会以场景去路由
     */
    private String scene;
    /**
     * 工作流ID，执行工作流时传工作流ID
     */
    private String workflowId;
}
