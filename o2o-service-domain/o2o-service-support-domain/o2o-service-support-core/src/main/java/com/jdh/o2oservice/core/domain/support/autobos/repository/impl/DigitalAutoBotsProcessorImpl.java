package com.jdh.o2oservice.core.domain.support.autobos.repository.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Joiner;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.processor.MapAutowiredKey;
import com.jdh.o2oservice.common.enums.AutoBotsSceneEnum;
import com.jdh.o2oservice.core.domain.report.bo.MedReportIndicatorQueryBO;
import com.jdh.o2oservice.core.domain.report.model.MedicalReport;
import com.jdh.o2oservice.core.domain.report.model.MedicalReportIndicator;
import com.jdh.o2oservice.core.domain.report.repository.db.MedicalReportIndicatorRepository;
import com.jdh.o2oservice.core.domain.report.repository.db.MedicalReportRepository;
import com.jdh.o2oservice.core.domain.support.autobos.bo.AutoBotsCallBackContext;
import com.jdh.o2oservice.core.domain.support.autobos.model.AutoBotsRecord;
import com.jdh.o2oservice.core.domain.support.autobos.repository.AutoBotsProcessor;
import com.jdh.o2oservice.core.domain.support.autobos.repository.AutoBotsRecordRepository;
import com.jdh.o2oservice.core.domain.support.file.service.FileManageService;
import com.jdh.o2oservice.export.report.AbnormalTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2025/5/16
 */
@Slf4j
@Service
public class DigitalAutoBotsProcessorImpl implements AutoBotsProcessor , MapAutowiredKey {

    /**
     * autoBotsRecordRepository
     */
    @Autowired
    private AutoBotsRecordRepository autoBotsRecordRepository;

    /**
     * 自动注入的医疗报告仓库，用于在自动化回调操作中更新报告表。
     */
    @Autowired
    private MedicalReportRepository medicalReportRepository;

    @Autowired
    private MedicalReportIndicatorRepository medicalReportIndicatorRepository;

    @Autowired
    private FileManageService fileManageService;


    /**
     * 获取map key
     *
     * @return
     */
    @Override
    public String getMapKey() {
        return AutoBotsSceneEnum.DIGITAL_REPORT_ANALYSE.getScene();
    }

    /**
     * 执行自动化回调操作
     *
     * @param context 自动化回调上下文对象
     * @return 是否成功执行回调操作
     */
    @Override
    @LogAndAlarm
    public Boolean callBack(AutoBotsCallBackContext context) {
        //1.判断是否完成,只更新完成的，或者加缓存？建议加缓存，后面改
        if (!Boolean.TRUE.equals(context.getFinished())){
            return Boolean.FALSE;
        }


        //2.如果完成，则先更新记录表
        AutoBotsRecord autoBotsRecord = context.getAutoBotsRecord();
        autoBotsRecord.setStatus(context.getStatus());
        autoBotsRecord.setResult(context.getResponseAll());
        autoBotsRecordRepository.save(autoBotsRecord);

        //再更新报告表
//        MedicalReport report = medicalReportRepository.getByMedicalPromiseId(Long.valueOf(autoBotsRecord.getExtendId()));
//        medicalReportRepository.update();


        //获取报告id
        String traceId = context.getTraceId();
        String[] ss = StringUtils.split(traceId,"-");
        String id = ss[ss.length-1];

        MedicalReport updateMedicalReport = new MedicalReport();
        updateMedicalReport.setId(Long.parseLong(id));

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("aiResponse",context.getResponseAll());


        MedicalReport medicalReport = medicalReportRepository.getById(Long.parseLong(id));
        if(!medicalReport.getStructReportOss().equals(context.getAutoBotsRecord().getExtendId())){
            log.info("DigitalAutoBotsProcessorImpl aiRead 报告id={} 报告已发生改变 ,终止逻辑",id);
            return true;
        }

        MedReportIndicatorQueryBO medReportIndicatorQueryBO = new MedReportIndicatorQueryBO();
        medReportIndicatorQueryBO.setReportIds(Collections.singleton(updateMedicalReport.getId()+""));
        List<MedicalReportIndicator> medicalReportIndicators = medicalReportIndicatorRepository.listMedicalReportIndicators(medReportIndicatorQueryBO);
        if(CollectionUtils.isEmpty(medicalReportIndicators)){
            log.info("DigitalAutoBotsProcessorImpl aiRead 非结构化报告,终止逻辑");
            return true;
        }
        List<String> indicatorNames = medicalReportIndicators.stream().filter(t->!AbnormalTypeEnum.NORMAL.getType().equals(t.getAbnormalMarkType())).map(MedicalReportIndicator::getIndicatorName).collect(Collectors.toList());
        //排序(升序)
        Collections.sort(indicatorNames);
        String cacheKey = Joiner.on(",").join(indicatorNames).trim();

        jsonObject.put("exceptionName",cacheKey);
        jsonObject.put("url",fileManageService.getPublicUrl(medicalReport.getStructReportOss(), true, DateUtil.offsetDay(new Date(), 1)));

        updateMedicalReport.setAiContent(jsonObject.toJSONString());
        medicalReportRepository.update(updateMedicalReport);

        return Boolean.TRUE;
    }
}
