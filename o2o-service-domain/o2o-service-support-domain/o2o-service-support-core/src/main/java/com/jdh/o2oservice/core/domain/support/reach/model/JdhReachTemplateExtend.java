package com.jdh.o2oservice.core.domain.support.reach.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName JdhReachTemplateExtend
 * @Description
 * <AUTHOR>
 * @Date 2025/6/4 21:10
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class JdhReachTemplateExtend {

    /**
     * 使用语音播报
     */
    private Integer useVoice;

    /**
     * 语音播报配置文件
     */
    private String voiceConfig;
}