package com.jdh.o2oservice.core.domain.support.price.context;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.convert.ConvertException;
import cn.hutool.core.lang.TypeReference;
import com.jdh.o2oservice.core.domain.support.basic.enums.JdOrderFeeTypeEnum;
import com.jdh.o2oservice.core.domain.support.basic.enums.PricingServiceFactObjectEnum;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * @ClassName PricingServiceCalculateContext
 * @Description
 * <AUTHOR>
 * @Date 2025/4/29 14:49
 **/
@Data
@Slf4j
public class PricingServiceCalculateContext {

    /**
     * 场景
     */
    private String scene;

    /**
     * 事实对象
     */
    private Map<String, Object> factObjectMap = new HashMap<>();

    /**
     * 必需, 订单号
     */
    private Long orderId;

    /**
     * promiseId
     */
    private Long promiseId;

    /**
     * 服务者id
     */
    private Long angelId;

    /**
     * 派单加价费用
     */
    private BigDecimal dispatchMarkupPrice;

    /**
     * 费项金额明细
     * {"MATERIAL_FEE":50.00,"HOME_VISIT":5.00,"TIME_PERIOD_APPOINTMENT_TYPE_IMMEDIATELY":20.00}
     * @see com.jdh.o2oservice.core.domain.support.basic.enums.JdOrderFeeTypeEnum
     */
    private Map<String, BigDecimal> feeAmountMap = new HashMap<>();

    /**
     * traceId
     * -- GETTER --
     * 获取traceId
     *
     * @return {@link String}
     */
    private String traceId;

    public PricingServiceCalculateContext() {
        try {
            this.setTraceId(MDC.get("PFTID"));
        } catch (Exception exception) {
            log.error("PricingServiceCalculateContext 构建 setTraceId exception", exception);
        }
    }

    /**
     * 获取上下文事实对象
     * @param key
     * @param type
     * @return
     * @param <T>
     * @throws ConvertException
     */
    public <T> T getFactObject(String key, Class<T> type) throws ConvertException {
        Object o = this.getFactObjectMap().get(key);
        if (Objects.isNull(o)) {
            return null;
        }
        return Convert.convert(type, o);
    }

    /**
     * 获取上下文事实对象
     * @param key
     * @param reference
     * @return
     * @param <T>
     * @throws ConvertException
     */
    public <T> T getFactObject(String key, TypeReference<T> reference) throws ConvertException {
        Object o = this.getFactObjectMap().get(key);
        if (Objects.isNull(o)) {
            return null;
        }
        return Convert.convert(reference, o);
    }

    /**
     * 重置计算结果，保留事实对象
     */
    public void resetCalculateResult(boolean retainFactObject) {
        feeAmountMap.clear();
        if (!retainFactObject) {
            factObjectMap.clear();
            return;
        }
        for (JdOrderFeeTypeEnum jdOrderFeeTypeEnum : JdOrderFeeTypeEnum.values()) {
            factObjectMap.remove(jdOrderFeeTypeEnum.name());
        }
        //删除全兼职相关的服务费金额，重新计算
        factObjectMap.remove(PricingServiceFactObjectEnum.SKU_SERVICE_AMOUNT_MAP.getCode());
    }
}