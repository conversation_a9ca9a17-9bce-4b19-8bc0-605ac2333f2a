package com.jdh.o2oservice.core.domain.support.reach.rpc;

import com.jdh.o2oservice.core.domain.support.reach.repository.rpc.bo.BatchSendSmsBO;
import com.jdh.o2oservice.core.domain.support.reach.rpc.bo.*;

import java.util.List;

/**
 * 互医消息推送平台
 * @author: yang<PERSON>yu
 * @date: 2024/4/22 11:20 上午
 * @version: 1.0
 */
public interface NewnethpDiagMsgRpc {

    /**
     * 推送站外消息
     * @param msgBo
     */
    ReachSendResult pushAppMsg(PushAppMsgBo msgBo);

    /**
     * 推送站内信通知
     * 接口：https://joyspace.jd.com/pages/16OdToqj8Pcf0Eun0pdy
     * @param msgBo
     */
    ReachSendResult pushInAppMsg(PushInAppMsgBo msgBo);

    /**
     * 互医短信接口，互医会基于护士的免打扰配置过滤，并提供黑名单等配置
     * @param sendSmsBO
     */
    ReachSendResult sendSms(BatchSendSmsBO sendSmsBO);

    /**
     * 查询医生端消息配置规则
     * @param query
     * @return
     */
    List<NethpMessageSendRuleBo> getMessageSwitch(MessageSwitchQuery query);
}
