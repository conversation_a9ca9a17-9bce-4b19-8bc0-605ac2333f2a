package com.jdh.o2oservice.core.domain.support.ship.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/6/3
 * @description 下飞行需求
 */
@Data
public class AirportCreateDto {

    @JSONField(name="flight_work_id")
    private String flightWorkId;//飞行需求ID

    @JSONField(name="save_code")
    private String saveCode;//存货码

    @JSONField(name="take_code")
    private String takeCode;//取货码
}
