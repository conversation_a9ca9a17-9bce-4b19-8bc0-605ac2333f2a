package com.jdh.o2oservice.core.domain.support.ship.event;

import com.jdh.o2oservice.base.event.EventBody;
import lombok.Data;

/**
 * @ClassName:AngelShipEventBody
 * @Description:
 * @Author: yaoqing<PERSON>
 * @Date: 2024/6/5 14:48
 * @Vserion: 1.0
 **/
@Data
public class UavAngelShipSupportEventBody implements EventBody {

    private String flightWorkId;//航班id

    private String connectName;//联系人姓名

    private String connectPhone;//联系人电话

}
