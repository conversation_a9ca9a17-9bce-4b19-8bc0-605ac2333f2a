package com.jdh.o2oservice.core.domain.support.ship;

import com.jdh.o2oservice.core.domain.support.ship.dto.AirportCreateDto;
import com.jdh.o2oservice.core.domain.support.ship.dto.AirportUnbindOrderDto;
import com.jdh.o2oservice.core.domain.support.ship.param.AirportChangeInfoParam;
import com.jdh.o2oservice.core.domain.support.ship.param.AirportCreateParam;
import com.jdh.o2oservice.core.domain.support.ship.param.AirportFlightScheduleParam;
import com.jdh.o2oservice.core.domain.support.ship.param.AirportUnbindOrderParam;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/3
 * @description 无人机
 */
public interface UavRpc {

    /**
     * 查询航班时刻
     * @param airportFlightScheduleParam
     * @return
     */
    List<Long> airportFlightSchedule(AirportFlightScheduleParam airportFlightScheduleParam);

    /**
     * 下飞行需求
     * @param airportCreateParam
     * @return
     */
    AirportCreateDto create(AirportCreateParam airportCreateParam);

    /**
     * 追加飞行需求
     * @param airportChangeInfoParam
     * @return
     */
    String changeInfo(AirportChangeInfoParam airportChangeInfoParam);

    /**
     * 解绑飞行需求
     * @param airportUnbindOrderParam
     * @return
     */
    AirportUnbindOrderDto unbindOrder(AirportUnbindOrderParam airportUnbindOrderParam);
}
