package com.jdh.o2oservice.core.domain.support.ship.param;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/6/3
 * @description 下飞行需求
 */
@Data
public class AirportUnbindOrderParam {

    private String key;//加密key

    @JSONField(name="flight_work_id")
    private String flightWorkId;//飞行需求ID

    @JSONField(name="order_no")
    private String orderNo;//业务单号
}
