package com.jdh.o2oservice.core.domain.support.reach.enums;

/**
 * 触达类型枚举
 * <AUTHOR>
 * @date 2023-12-21-4:51 下午
 */
public enum ReachTypeEnum {

    /**
     * 短信
     */
    REACH_SMS(1, "短信"),

    /**
     * 邮件
     */
    REACH_EMAIL(2, "email"),

    /**
     * 京麦商家消息
     */
    REACH_JM(3, "京麦商家消息"),

    /**
     * 京东医生APP站内消息通知
     */
    APP_NOTIFY(4, "APP站内消息通知"),
    /**
     *
     */
    PUSH(5, "消息推送（站外）"),
    /**
     *
     */
    JM_MESSAGE(6, "京麦消息触达"),
    /**
     *
     */
    MASTER_APP_MESSAGE(7, "主站APP消息推送"),

    /**
     *
     */
    STORE_PROGRAM_MQ_MESSAGE(8, "到店小程序mq消息通推送"),

    /**
     *
     */
    ME_ROBOT_MESSAGE(9, "京ME机器人")
    ;

    /**
     * 类型码
     */
    private Integer code;

    /**
     * 描述
     */
    private String desc;

    /**
     *
     * @param code
     * @param desc
     */
    ReachTypeEnum(Integer code, String desc){
        this.code = code;
        this.desc = desc;
    }

    /**
     *
     * @return
     */
    public Integer getCode(){
        return this.code;
    }

    public String getDesc(){
        return desc;
    }

    /**
     *
     * @param code
     * @return
     */
    public static ReachTypeEnum parseCode(Integer code){
        if(code == null){
            return null;
        }
        for (ReachTypeEnum reachTypeEnum : ReachTypeEnum.values()){
            if(reachTypeEnum.getCode().equals(code)){
                return reachTypeEnum;
            }
        }
        return null;
    }
}
