package com.jdh.o2oservice.core.domain.support.price;

import com.google.common.collect.Lists;
import com.googlecode.aviator.AviatorEvaluator;
import com.jdh.o2oservice.core.domain.support.price.model.PricingServiceFee;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @ClassName PricingServiceFormulaParser
 * @Description
 * <AUTHOR>
 * @Date 2025/4/28 16:55
 **/
@Slf4j
public class PricingServiceFormulaParser {

    /**
     * 运算符列表
     */
    final static List<String> MATHEMATICAL_OPERATOR = Lists.newArrayList("+","-","*","/","(",")");

    /**
     * 提取公式中的费项code
     * @param formula
     * @return
     */
    public static Set<String> extractFeeConfigId(String formula) {
        // 正则表达式匹配变量名
        Pattern variablePattern = Pattern.compile("[a-zA-Z_][a-zA-Z0-9_]*");
        Matcher matcher = variablePattern.matcher(formula);

        Set<String> variables = new HashSet<>();
        while (matcher.find()) {
            String variable = matcher.group();
            // 排除数学运算符
            if (!MATHEMATICAL_OPERATOR.contains(variable)) {
                variables.add(variable);
            }
        }
        return variables;
    }

    /**
     * 计算公式
     * @param formula
     * @param pricingServiceFeeList
     * @return
     */
    public static BigDecimal calculateFormulaAmount(String formula, List<PricingServiceFee> pricingServiceFeeList) {
        try {
            Object execute = null;
            if (CollectionUtils.isEmpty(pricingServiceFeeList)) {
                log.info("PricingServiceFormulaParser -> calculateFormulaAmount 未命中费项");
                execute = AviatorEvaluator.compile(formula, Boolean.TRUE).execute(new HashMap<>());
            } else {
                Map<String, Object> param = new HashMap<>();
                for (PricingServiceFee pricingServiceFee : pricingServiceFeeList) {
                    param.put(pricingServiceFee.getFeeConfigId(), pricingServiceFee.getFeeAmount());
                }
                execute = AviatorEvaluator.compile(formula, Boolean.TRUE).execute(param);
            }
            log.info("PricingServiceFee -> calculateFormulaAmount 计算公式结果：{}={}", formula, execute);
            // 验证返回的amount类型，应该返回的是0
            if (Objects.isNull(execute)) {
                return BigDecimal.ZERO;
            } else if (execute instanceof BigDecimal) {
                return (BigDecimal) execute;
            } else {
                return new BigDecimal(execute.toString());
            }
        } catch (Throwable e) {
            log.error("PricingServiceFormulaParser -> calculateFormulaAmount 执行报错, 默认返回0", e);
        }
        return BigDecimal.ZERO;
    }
}