package com.jdh.o2oservice.core.domain.support.sound.bo;

import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @date 2025/6/3
 */
@Data
public class SoundProcessTopicBO {
    /**
     * 租户，xfyl
     */
    private String tenantNo;

    /**
     * 描述音频所属的场景信息。
     */
    private String scene;

    /**
     * 音频文件的唯一标识符。
     */
    private Long fileId;

    /**
     * 音频文件的本地存储路径。
     */
    private String filePath;

    /**
     * 音频文件的网络访问地址。
     */
    private String fileUrl;

    /**
     * 音频文件的唯一标识符。
     */
    private Long soundId;

    /**
     * 业务ID，用于关联相关业务信息。
     */
    private String businessId;
}
