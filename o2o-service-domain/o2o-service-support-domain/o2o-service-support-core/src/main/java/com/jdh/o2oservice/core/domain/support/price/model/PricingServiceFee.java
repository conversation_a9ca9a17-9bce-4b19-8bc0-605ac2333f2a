package com.jdh.o2oservice.core.domain.support.price.model;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.googlecode.aviator.AviatorEvaluator;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.ducc.model.price.PricingServiceFeeCalculateRule;
import com.jdh.o2oservice.base.ducc.model.price.PricingServiceFeeConfig;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.util.SpringUtil;
import com.jdh.o2oservice.core.domain.support.price.factory.PricingServiceFeeCalculateMethodFactory;
import com.jdh.o2oservice.core.domain.support.price.factory.PricingServiceFeeFactory;
import com.jdh.o2oservice.core.domain.support.price.context.PricingServiceCalculateContext;
import com.jdh.o2oservice.core.domain.support.price.handler.PricingServiceFactObjectHandler;
import com.jdh.o2oservice.core.domain.support.price.method.PricingServiceFeeCalculateMethod;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Objects;

/**
 * @ClassName PricingServiceFee
 * @Description
 * <AUTHOR>
 * @Date 2025/4/29 14:41
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Slf4j
public class PricingServiceFee extends PricingServiceFeeConfig {

    /**
     * 费项金额
     */
    private BigDecimal feeAmount = BigDecimal.ZERO;

    /**
     * 减分费项
     */
    private boolean isSubtract = false;

    /**
     * 获取事实对象
     */
    public void obtainFactualObjects(PricingServiceCalculateContext context) {
        if (CollectionUtils.isEmpty(dependFactObjectCodes)) {
            return;
        }
        PricingServiceFeeFactory pricingServiceFeeFactory = SpringUtil.getBean(PricingServiceFeeFactory.class);
        for (String dependFactObjectCode : dependFactObjectCodes) {
            if (context.getFactObjectMap().containsKey(dependFactObjectCode)) {
                continue;
            }
            PricingServiceFactObjectHandler factObjectHandler = pricingServiceFeeFactory.handlerMap.get(dependFactObjectCode);
            Object object = Objects.isNull(factObjectHandler) ? null : factObjectHandler.getFactObject(context);
            context.getFactObjectMap().put(dependFactObjectCode, object);
        }
    }

    /**
     * 费项价格
     * @return
     */
    @LogAndAlarm
    public BigDecimal calculateFeeAmount(PricingServiceCalculateContext context, boolean cached) {
        if (!cached) {
            return calculateFeeAmount(context);
        }
        feeAmount = calculateFeeAmount(context);
        context.getFeeAmountMap().put(this.feeCode, isSubtract ? feeAmount.negate() : feeAmount);
        context.getFactObjectMap().put(this.feeConfigId, feeAmount);
        log.info("PricingServiceFee {}-> calculateFeeAmount 计算结果={}", this.feeCode, feeAmount);
        return feeAmount;
    }

    /**
     * 费项价格
     * @return
     */
    private BigDecimal calculateFeeAmount(PricingServiceCalculateContext context) {
        try {
            //获取计算所需事实对象
            obtainFactualObjects(context);
            //获取计算规则
            PricingServiceFeeCalculateRule rule = obtainFeeCalculateRule(context);
            if (rule == null) {
                log.info("PricingServiceFee {}-> calculateFeeAmount 未命中费项规则", this.feeCode);
                return BigDecimal.ZERO;
            }
            log.info("PricingServiceFee {}-> calculateFeeAmount 命中费项规则：{}, rule={}", this.feeCode, rule.getSceneName(), JSON.toJSONString(rule));
            //规则方法不为空，查找本地方法实现价格计算
            if (StringUtils.isNotEmpty(rule.getCalculateMethod())) {
                PricingServiceFeeCalculateMethod calculateMethod = SpringUtil.getBean(PricingServiceFeeCalculateMethodFactory.class).getPricingServiceFeeCalculateMethod(rule.getCalculateMethod());
                return Objects.isNull(calculateMethod) ? BigDecimal.ZERO : calculateMethod.calculatePrice(context);
            } else {
                Object execute = AviatorEvaluator.compile(rule.getCalculateExpression(), Boolean.TRUE).execute(context.getFactObjectMap());
                BigDecimal result = null;
                // 验证返回的amount类型，应该返回的是0
                if (Objects.isNull(execute)) {
                    result =  BigDecimal.ZERO;
                } else if (execute instanceof BigDecimal) {
                    result = (BigDecimal) execute;
                } else {
                    result = new BigDecimal(execute.toString());
                }
                return result.setScale(2, RoundingMode.HALF_UP);
            }
        } catch (BusinessException e) {
            log.error("PricingServiceFee {}-> calculateFeeAmount 执行报错, businessException", this.feeCode, e);
            throw e;
        } catch (Throwable e) {
            log.error("PricingServiceFee {}-> calculateFeeAmount 执行报错, 默认返回0", this.feeCode, e);
        }
        return BigDecimal.ZERO;
    }

    /**
     * 获取将要执行的费项规则
     * @param context
     * @return
     */
    public PricingServiceFeeCalculateRule obtainFeeCalculateRule(PricingServiceCalculateContext context) {
        List<PricingServiceFeeCalculateRule> hitRules = Lists.newArrayList();
        for (PricingServiceFeeCalculateRule rule : calculateRuleList) {
            if ((boolean) AviatorEvaluator.compile(rule.getSceneExpression(), Boolean.TRUE).execute(context.getFactObjectMap())) {
                log.info("PricingServiceFee -> obtainFeeCalculateRule hit rule {}", rule.getSceneName());
                hitRules.add(rule);
            }
        }
        if (CollectionUtils.isEmpty(hitRules)) {
            return null;
        }
        //取优先级最高的规则
        hitRules.sort((o1, o2) -> o2.getSalience().compareTo(o1.getSalience()));
        return hitRules.get(0);
    }
}