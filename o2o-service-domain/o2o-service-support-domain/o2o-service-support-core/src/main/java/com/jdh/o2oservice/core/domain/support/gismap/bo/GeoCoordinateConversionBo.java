package com.jdh.o2oservice.core.domain.support.gismap.bo;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/6/8
 * @description 经纬度转换
 */
@Data
public class GeoCoordinateConversionBo {

    /**
     * 经度和纬度用","分割，经度在前，纬度在后，经纬度小数点后不得超过8位。多个坐标对之间用”;”进行分隔最多支持40对坐标。
     */
    private String locations;//经纬度

    /**
     * 对应枚举：
     * CoordinateCovertEnum {
     *     TENCENT_TO_TENCENT(1, "腾讯转腾讯"),
     *     BAIDU_TO_TENCENT(2, "百度转腾讯"),
     *     WGS84_TO_TENCENT(3, "国际标准转腾讯");
     * }
     * 字段默认值为3，可根据业务需要调整
     */
    private Integer coordinateSys;
}
