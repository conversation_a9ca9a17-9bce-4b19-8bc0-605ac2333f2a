package com.jdh.o2oservice.core.domain.support.price.factory;

import com.jdh.o2oservice.base.annotation.MapAutowired;
import com.jdh.o2oservice.core.domain.support.price.method.PricingServiceFeeCalculateMethod;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * @ClassName PricingServiceFeeCalculateMethodFactory
 * @Description
 * <AUTHOR>
 * @Date 2025/5/16 16:21
 **/
@Slf4j
@Component
public class PricingServiceFeeCalculateMethodFactory {

    /**
     * 事实对象处理器
     */
    @MapAutowired
    private Map<String, PricingServiceFeeCalculateMethod> methodMap;

    /**
     * 获取费项计算方法对象
     * @param methodCode
     * @return
     */
    public PricingServiceFeeCalculateMethod getPricingServiceFeeCalculateMethod(String methodCode) {
        if (StringUtils.isBlank(methodCode)) {
            return null;
        }
        return methodMap.get(methodCode);
    }
}