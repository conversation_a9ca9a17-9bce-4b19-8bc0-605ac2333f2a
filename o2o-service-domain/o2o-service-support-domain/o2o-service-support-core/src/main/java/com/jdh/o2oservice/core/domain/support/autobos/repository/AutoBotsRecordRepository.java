package com.jdh.o2oservice.core.domain.support.autobos.repository;

import com.jdh.o2oservice.core.domain.support.autobos.bo.AutoBotsRequestQueryBO;
import com.jdh.o2oservice.core.domain.support.autobos.model.AutoBotsRecord;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025/5/15
 */
public interface AutoBotsRecordRepository {


    /**
     * 保存AutoBotsRecord对象到数据库中。
     * @param autoBotsRecord 要保存的AutoBotsRecord对象。
     * @return 如果保存成功则返回true，否则返回false。
     */
    Long save(AutoBotsRecord autoBotsRecord);



    /**
     * 根据查询条件获取 AutoBots 记录
     * @param queryBO 查询条件对象
     * @return AutoBots 记录实体
     */
    AutoBotsRecord queryAutoBotsRecord(AutoBotsRequestQueryBO queryBO);


    /**
     * 根据查询条件获取AutoBotsRecord列表
     * @param queryBO 查询条件对象
     * @return AutoBotsRecord列表
     */
    List<AutoBotsRecord> queryAutoBotsRecordList(AutoBotsRequestQueryBO queryBO);

}
