package com.jdh.o2oservice.core.domain.support.promisego.rpc.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description:
 * @Author: wangpengfei144
 * @Date: 2024/11/7
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserPromisegoRequestBo {

    /**
     * 履约单id
     */
    private Long promiseId;

    /**
     * 业务模式
     */
    private String businessMode;

    /**
     * 是否查询出报告时间预估
     */
    private Boolean queryTermScript;

    /**
     *  聚合状态
     */
    private String aggregateStatus;

    /**
     * 预约时间
     */
    private PromisegoRequestAppointmentTime appointmentTime;

    /**
     * 地址信息
     */
    private PromisegoRequestAddress appointmentAddress;

    /**
     * 是否已有采样盒
     */
    private Boolean preSampleFlag;

    private Integer deliveryType;

}
