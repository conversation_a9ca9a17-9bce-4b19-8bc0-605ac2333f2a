package com.jdh.o2oservice.core.domain.support.reach.context;

import lombok.Data;

/**
 * CreateReachTemplateContext
 *
 * <AUTHOR>
 * @date 2024/07/24
 */
@Data
public class CreateReachTemplateContext {

    /**
     * templateId
     */
    private Long templateId;

    /**
     * templateName
     */
    private String templateName;
    /**
     * 模版绑定的触达任务类型
     */
    private Integer type;
    /**
     * 消息编码类型
     */
    private String messageBizType;
    /**
     * 模版内容
     */
    private String content;
    /**
     * 参数填充规则
     */
    private String paramParse;

    /**
     * 跳转的url解析
     */
    private String urlParse;

    /**
     * 触达渠道模版ID
     */
    private String channelTemplateId;
    /**
     *
     */
    private String accountId;
    /**
     * 使用有铃声
     */
    private Integer bell;

    /**
     * 描述
     */
    private String desc;

    /**
     * 使用语音播报
     */
    private Integer useVoice;

    /**
     * 语音播报配置文件
     */
    private String voiceConfig;
}
