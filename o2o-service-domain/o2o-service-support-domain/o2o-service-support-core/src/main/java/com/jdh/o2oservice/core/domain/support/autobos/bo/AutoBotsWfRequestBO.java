package com.jdh.o2oservice.core.domain.support.autobos.bo;

import lombok.Data;
import java.util.Map;
/**
 * <AUTHOR>
 * @description
 * @date 2025/5/15
 */
@Data
public class AutoBotsWfRequestBO {

    /**
     * 会话ID,每次工作流调用唯一标志，不可重复
     * 建议用UUID
     */
    private String traceId;
    /**
     * 用户erp,必填
     */
    private String erp;
    /**
     * 回调url，POST请求
     * url不为空时，工作流运行结束会调用此url回传工作流运行结果。（http body为工作流运行结果）
     */
    private String callbackUrl;
    /**
     * 工作流ID，执行工作流时传工作流ID
     */
    private String workflowId;

    /**
     * 工作流入参放入此map中
     */
    private Map<String,Object> extParams;
    /**
     * 扩展ID
     */
    private String extendId;
    /**
     * 扩展ID类型
     */
    private Integer extendIdType;


}
