package com.jdh.o2oservice.core.domain.support.price.factory;

import com.google.common.collect.Lists;
import com.jdh.o2oservice.base.annotation.MapAutowired;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.ducc.model.price.PricingServiceFeeConfig;
import com.jdh.o2oservice.core.domain.support.price.handler.PricingServiceFactObjectHandler;
import com.jdh.o2oservice.core.domain.support.price.model.PricingServiceFee;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jgrapht.Graph;
import org.jgrapht.graph.DefaultDirectedGraph;
import org.jgrapht.graph.DefaultEdge;
import org.jgrapht.traverse.TopologicalOrderIterator;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName PricingServiceFeeFactory
 * @Description
 * <AUTHOR>
 * @Date 2025/4/29 14:31
 **/
@Slf4j
@Component
public class PricingServiceFeeFactory {

    /**
     * 事实对象处理器
     */
    @MapAutowired
    public Map<String, PricingServiceFactObjectHandler> handlerMap;

    /**
     * duccConfig
     */
    @Resource
    private DuccConfig duccConfig;

    /**
     * 获取费项领域对象
     * @param feeConfigId
     * @return
     */
    public PricingServiceFee getPricingServiceFee(String feeConfigId) {
        return getPricingServiceFee(feeConfigId, Lists.newArrayList());
    }

    /**
     * 获取费项领域对象
     * @param feeConfigId
     * @return
     */
    public PricingServiceFee getPricingServiceFee(String feeConfigId, List<String> subtractFeeCodeList) {
        if (StringUtils.isBlank(feeConfigId)) {
            return null;
        }
        Map<String, PricingServiceFeeConfig> pricingServiceFeeConfigMap = duccConfig.getPricingServiceFeeConfigMap();
        if (!pricingServiceFeeConfigMap.containsKey(feeConfigId)) {
            return null;
        }
        PricingServiceFee pricingServiceFee = new PricingServiceFee();
        BeanUtils.copyProperties(pricingServiceFeeConfigMap.get(feeConfigId), pricingServiceFee);
        pricingServiceFee.setSubtract(CollectionUtils.isNotEmpty(subtractFeeCodeList) && subtractFeeCodeList.contains(feeConfigId));
        pricingServiceFee.setFeeConfigId(feeConfigId);
        return pricingServiceFee;
    }
    /**
     * 获取费项领域对象
     * @param feeConfigIds
     * @return
     */
    public List<PricingServiceFee> getPricingServiceFee(Set<String> feeConfigIds) {
        return getPricingServiceFee(feeConfigIds, Lists.newArrayList());
    }

    /**
     * 获取费项领域对象
     * @param feeConfigIds
     * @param subtractFeeCodeList
     * @return
     */
    public List<PricingServiceFee> getPricingServiceFee(Set<String> feeConfigIds, List<String> subtractFeeCodeList) {
        if (CollectionUtils.isEmpty(feeConfigIds)) {
            return Collections.emptyList();
        }
        // 获取所有的 PricingServiceFee 对象
        Map<String, PricingServiceFee> feeMap = feeConfigIds.stream()
                .map(feeConfigId -> getPricingServiceFee(feeConfigId, subtractFeeCodeList))
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(PricingServiceFee::getFeeConfigId, fee -> fee));

        // 构建依赖图
        Map<String, List<String>> fee2DependMap = feeMap.values().stream().collect(Collectors.toMap(PricingServiceFee::getFeeConfigId, PricingServiceFee::getDependFeeConfigIds));

        // 进行拓扑排序
        List<String> sortedFeeCodes = dependSort(fee2DependMap);

        // 根据排序结果获取 PricingServiceFee 对象
        return sortedFeeCodes.stream().map(feeMap::get).filter(Objects::nonNull).collect(Collectors.toList());
    }

    /**
     * 使用JGraphT实现拓扑排序
     * @param fee2DependMap
     * @return
     */
    public List<String> dependSort(Map<String, List<String>> fee2DependMap) {
        Graph<String, DefaultEdge> graph = new DefaultDirectedGraph<>(DefaultEdge.class);

        fee2DependMap.keySet().forEach(graph::addVertex);

        // 构建依赖图
        fee2DependMap.forEach((key, value) -> value.forEach(dep -> {
            graph.addEdge(dep, key);
        }));

        // 创建拓扑排序迭代器
        TopologicalOrderIterator<String, DefaultEdge> iterator = new TopologicalOrderIterator<>(graph);

        // 将迭代结果收集到一个列表中
        List<String> sortedList = new ArrayList<>();
        iterator.forEachRemaining(sortedList::add);
        return sortedList;
    }

    /*public static void main(String[] args) {
        Map<String, PricingServiceFee> feeMap = new HashMap<>();
        feeMap.put(JdOrderFeeTypeEnum.ANGEL_SERVICE_FEE.getCode(), PricingServiceFee.builder().feeCode(JdOrderFeeTypeEnum.ANGEL_SERVICE_FEE.getCode()).dependFeeCodes(Lists.newArrayList()).build());
        feeMap.put(JdOrderFeeTypeEnum.MATERIAL_FEE.getCode(), PricingServiceFee.builder().feeCode(JdOrderFeeTypeEnum.MATERIAL_FEE.getCode()).dependFeeCodes(Lists.newArrayList()).build());
        feeMap.put(JdOrderFeeTypeEnum.HOME_VISIT.getCode(), PricingServiceFee.builder().feeCode(JdOrderFeeTypeEnum.HOME_VISIT.getCode()).dependFeeCodes(Lists.newArrayList(JdOrderFeeTypeEnum.MATERIAL_FEE.getCode())).build());
        feeMap.put(JdOrderFeeTypeEnum.TIME_PERIOD_APPOINTMENT_TYPE_IMMEDIATELY.getCode(), PricingServiceFee.builder().feeCode(JdOrderFeeTypeEnum.TIME_PERIOD_APPOINTMENT_TYPE_IMMEDIATELY.getCode()).dependFeeCodes(Lists.newArrayList(JdOrderFeeTypeEnum.HOME_VISIT.getCode(), JdOrderFeeTypeEnum.ANGEL_SERVICE_FEE.getCode())).build());

        // 构建依赖图
        Map<String, List<String>> fee2DependMap = feeMap.values().stream().collect(Collectors.toMap(PricingServiceFee::getFeeCode, PricingServiceFee::getDependFeeCodes));
        // 进行拓扑排序
        List<String> sortedFeeCodes = new PricingServiceFeeFactory().dependSort(fee2DependMap);
        System.out.println(sortedFeeCodes);
    }*/
}