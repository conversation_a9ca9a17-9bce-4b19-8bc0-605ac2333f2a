package com.jdh.o2oservice.core.domain.support.basic.enums;

import com.jdh.o2oservice.base.exception.errorcode.AbstractErrorCode;
import com.jdh.o2oservice.base.model.DomainEnum;
import lombok.ToString;

/**
 * 履约领域错误码
 * 支撑域的错误码以0开头
 * @author: yang<PERSON>yu
 * @date: 2023/12/18 8:11 下午
 * @version: 1.0
 */
@ToString
public enum SupportErrorCode implements AbstractErrorCode {
    /**
     * 履约域错误码
     */

    /** VIA 配置错误码 */
    VIA_CONFIG_NOT_EXIT(DomainEnum.BASE, "00001", "页面配置信息不存在"),
    GETHER_PAGE_TYPE_NOT_EXIT(DomainEnum.BASE, "00002", "聚合页页面类型信息不存在"),
    DETAIL_PROMISE_ID_NOT_EXIT(DomainEnum.BASE, "00003", "详情页面单号信息不存在"),
    DETAIL_PROMISE_STATUS_MAPPING_CONFIG_NOT_EXIT(DomainEnum.BASE, "00004", "详情页面单号状态映射信息不存在"),
    VIA_PROMISE_INFO_NOT_EXIT(DomainEnum.BASE, "00005", "履约单信息不存在"),
    VIA_PROMISE_PAGE_INFO_NOT_EXIT(DomainEnum.BASE, "00006", "履约单列表信息不存在"),
    VIA_ORDER_ID_NOT_EXIT(DomainEnum.BASE, "00007", "履约单列表信息不存在"),
    VIA_STATUS_MAPPING_ERROR(DomainEnum.BASE, "00008", "状态码映射错误"),
    VIA_ORDER_INFO_NOT_EXIT(DomainEnum.BASE, "00009", "订单信息不存在"),
    VIA_FLOOR_HAND_ERROR(DomainEnum.BASE, "00010", "楼层信息异常"),
    VIA_FLOOR_VOUCHER_ID_NOT_EXIT(DomainEnum.BASE, "00011", "服务单信息不存在"),
    VIA_FLOOR_VOUCHER_FREEZE_ERROR(DomainEnum.BASE, "00012", "服务单已被冻结"),
    VIA_FLOOR_VOUCHER_INVALID_ERROR(DomainEnum.BASE, "00013", "服务单已被作废"),
    VIA_FLOOR_NOT_FOUND_WAIT_PROMISE(DomainEnum.BASE, "00014", "没有找到可预约的单据"),
    VIA_FLOOR_NOT_FOUND_SKU_INFO(DomainEnum.BASE, "00015", "商品信息不存在"),
    VIA_FLOOR_NOT_SUPPORT_APPOINTMENT(DomainEnum.BASE, "00016", "该商品已经爆单，暂不支持预约"),


    /**
     * 患者信息错误码
     */
    PATIENT_DELETE_FAIL(DomainEnum.BASE,"01001", "常用体检人删除失败"),
    PATIENT_UPDATE_FAIL(DomainEnum.BASE,"01002", "常用体检人更新失败"),
    PATIENT_QUERY_FAIL(DomainEnum.BASE,"01003", "常用体检人查询失败"),
    PATIENT_ADD_FAIL(DomainEnum.BASE,"01004", "常用体检人添加失败"),

    PATIENT_PATIENT_ID_MISS(DomainEnum.BASE,"01005", "患者id信息缺失"),
    PATIENT_NAME_MISS(DomainEnum.BASE,"01006", "姓名信息缺失"),
    PATIENT_PHONE_MISS(DomainEnum.BASE,"01007", "电话信息缺失"),
    PATIENT_CREDENTIAL_NO_MISS(DomainEnum.BASE,"01008", "证件号码信息缺失"),
    PATIENT_CREDENTIAL_TYPE_MISS(DomainEnum.BASE,"01009", "证件类型信息缺失"),
    PATIENT_MARRIAGE_MISS(DomainEnum.BASE,"01010", "婚否信息缺失"),
    PATIENT_GENDER_MISS(DomainEnum.BASE,"01011", "性别信息缺失"),
    PATIENT_RELATIVES_TYPE_MISS(DomainEnum.BASE,"01012", "与本人关系信息缺失"),

    PATIENT_RSA_FAIL(DomainEnum.BASE,"01013", "常用体检人加解密异常"),
    PATIENT_NAME_ILLEGAL(DomainEnum.BASE,"01014", "姓名不合法"),

    /**
     * 触达错误码
     */
    SMS_SEND_FAIL(DomainEnum.BASE, "02001", "预约短信发送失败"),
    SMS_CODE_NULL_ERROR(DomainEnum.BASE, "02002", "验证码不能为空"),
    PRIVACY_NUMBER_GET_ERROR(DomainEnum.BASE, "02003", "虚拟号获取异常"),
    PRIVACY_NUMBER_PHONE_NUMBER_NULL_ERROR(DomainEnum.BASE, "02004", "获取虚拟号手机号为空异常"),
    SMS_CODE_SEND_LIMIT_ERROR(DomainEnum.BASE, "02005", "验证码发送频繁，请稍后重试"),
    UN_SUPPORT_REACT_TYPE(DomainEnum.BASE, "02006", "不支持的触达类型"),
    REACH_TARGET_IS_EMPTY(DomainEnum.BASE, "02007", "触发目标为空"),
    REACH_GROUP_NOT_EXIST(DomainEnum.BASE, "02008", "消息触达的分组不存在"),
    REACH_MESSAGE_NOT_EXIST(DomainEnum.BASE, "02009", "触达消息不存在"),
    REACH_TASK_NO_EXIST(DomainEnum.BASE, "02010", "触达任务不存在"),
    CREATE_REACH_TEMPLATE_ERROR(DomainEnum.BASE, "02011", "创建触达模板错误:{0}"),
    SMS_EXCEED_UPPER_LIMIT(DomainEnum.BASE, "02012", "短信达到当日上限"),
    REACH_SELECT_DATA_FAIL(DomainEnum.BASE, "02013", "获取数据失败：{}"),
    REACH_TEMPLATE_IS_INVALID(DomainEnum.BASE, "02014", "触达模版无效"),


    /**
     * 文件管理错误码
     */
    SUPPORT_FILE_EXPORT_LIMITING_CODE(DomainEnum.BASE, "03000", "导出任务过多，请稍后再试"),
    SUPPORT_FILE_PDF_ADD_SIGNATURE_(DomainEnum.BASE, "03001", "导出任务过多，请稍后再试"),
    SUPPORT_FILE_UPLOAD_FILE_NOT_EXIST(DomainEnum.BASE, "03002", "上传文件不存在"),
    SUPPORT_FILE_IMPORT_LIMITING_CODE(DomainEnum.BASE, "03003", "导入任务过多，请稍后再试"),
    SUPPORT_FILE_IMPORT_ERROR(DomainEnum.BASE, "03004", "导入任务执行出错"),
    SUPPORT_FILE_IMPORT_PARAM_MISS_ERROR(DomainEnum.BASE, "03005", "导入任务参数缺失"),
    SUPPORT_FILE_DOWN_LIMIT(DomainEnum.BASE, "03006", "导出数据超过限制{}"),
    /**
     * 延迟队列
     */
    DELAY_QUEUE_ADD_ERROR(DomainEnum.BASE,"04001", "发送延时消息异常"),

    /**
     *
     */
    CORN_PARSE_ERROR(DomainEnum.BASE,"04002", "corn表达式解析失败"),

    PDF_CREATE_TEMPLATE_EMPTY(DomainEnum.BASE,"05001","生成PDF模版为空"),

    PDF_CREATE_TEMPLATE_PROCESSOR_EMPTY(DomainEnum.BASE,"05002","生成PDF模版对应方法为空"),

    TEMPLATE_DYNAMIC_FUNCTION_INIT_ERROR(DomainEnum.BASE,"05003","模板动态函数配置异常"),


    /**
     * 智能体
     */
    AUTO_BOTS_SEARCH_REQ_REPEAT(DomainEnum.BASE,"06001","autobots调用智能体请求ID重复"),
    AUTO_BOTS_TRACE_REQ_REPEAT(DomainEnum.BASE,"06002","autobots调用工作流traceID重复"),

    ;


    /**
     * PromiseErrorCode
     *
     * @param domainEnum  域枚举
     * @param code        代码
     * @param description 描述
     */
    SupportErrorCode(DomainEnum domainEnum, String code, String description) {
        this.domainEnum = domainEnum;
        this.code = code;
        this.description = description;
    }

    /** */
    private DomainEnum domainEnum;

    /** */
    private String code;
    /**
     * 展示给客户的错误提示，因为有的场景把系统异常传递给客户展示，体验不好
     */
    private String description;

    /**
     * getCode
     *
     * @return {@link String}
     */
    @Override
    public String getCode() {
        return this.code;
    }

    /**
     * getDescription
     *
     * @return {@link String}
     */
    @Override
    public String getDescription() {
        return this.description;
    }

}
