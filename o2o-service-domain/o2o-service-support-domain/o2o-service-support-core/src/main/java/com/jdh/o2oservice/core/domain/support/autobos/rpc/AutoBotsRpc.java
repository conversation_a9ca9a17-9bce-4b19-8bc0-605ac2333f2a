package com.jdh.o2oservice.core.domain.support.autobos.rpc;

import com.jdh.o2oservice.core.domain.support.autobos.bo.AutoBotsRequestBO;
import com.jdh.o2oservice.core.domain.support.autobos.bo.AutoBotsResultBO;
import com.jdh.o2oservice.core.domain.support.autobos.bo.AutoBotsWfRequestBO;

/**
 * 智能体RPC
 * <AUTHOR>
 * @description
 * @date 2025/5/13
 */
public interface AutoBotsRpc {


    /**
     * 发送智能问答请求，同一次会话traceId一致，reqId每次需要是新的
     * @param agentId 代表要搜索的agent的唯一标识符。
     * @param token 用于验证请求的授权令牌。
     * @param autoBotsRequestBO 包含搜索条件的对象。
     * @return 搜索结果的AutoBotsResultBO对象。
     */
    AutoBotsResultBO searchAiRequest(String agentId, String token, AutoBotsRequestBO autoBotsRequestBO);


    /**
     * 获取智能问答结果，入参需要与searchAiRequest一致
     * @param agentId 代表要搜索的agent的唯一标识符。
     * @param token 用于验证请求的授权令牌。
     * @param autoBotsRequestBO 包含搜索条件的对象。
     * @return 搜索结果的AutoBotsResultBO对象。
     */
    AutoBotsResultBO searchAiResult(String agentId, String token, AutoBotsRequestBO autoBotsRequestBO);


    /**
     * 执行工作流程。
     * @param agentId 代理人ID。
     * @param token 访问令牌。
     * @param request 工作流请求对象。
     * @return 工作流执行结果。
     */
    AutoBotsResultBO runWorkflow(String agentId, String token, AutoBotsWfRequestBO request);


    /**
     * 获取工作流结果
     * @param agentId 代理人ID
     * @param token 认证令牌
     * @param request 自动机请求对象
     * @return 自动机结果对象
     */
    AutoBotsResultBO getWorkflowResult(String agentId, String token, AutoBotsWfRequestBO request);

}
