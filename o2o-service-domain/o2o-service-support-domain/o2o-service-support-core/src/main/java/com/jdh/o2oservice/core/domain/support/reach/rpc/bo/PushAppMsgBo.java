package com.jdh.o2oservice.core.domain.support.reach.rpc.bo;

import com.jdh.o2oservice.core.domain.support.reach.model.JdhReachChannelAccount;
import lombok.Data;

import java.util.Map;

/**
 * 推送APP站外消息BO
 * @author: yang<PERSON><PERSON>
 * @date: 2024/4/22 11:22 上午
 * @version: 1.0
 */
@Data
public class PushAppMsgBo {
    /** */
    private String toUserPin;
    /** */
    private Long messageId;
    /** */
    private String title;
    /** */
    private String content;
    /** */
    private String uri;
    /** */
    private Map<String,String> extras;

    /**
     * 使用语音播报
     */
    private Integer useVoice;

    /**
     * 语音播报配置文件
     */
    private PushVoiceConfigBO pushVoiceConfig;
}
