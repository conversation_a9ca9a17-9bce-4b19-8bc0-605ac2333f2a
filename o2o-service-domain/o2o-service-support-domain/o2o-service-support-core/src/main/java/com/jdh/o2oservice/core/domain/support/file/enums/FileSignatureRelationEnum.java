package com.jdh.o2oservice.core.domain.support.file.enums;



public enum FileSignatureRelationEnum {

    /**
     * 关系
     * * 0-本人、1-配偶、2-父母、3-子女、4-其它
     **/
    SELF(0, "本人"),
    SPOUSE(1, "配偶"),
    PARENTS(2, "父母"),
    CHILDREN(3, "子女"),
    OTHER(4, "其它"),
    ;

    FileSignatureRelationEnum(Integer relation, String desc) {
        this.relation = relation;
        this.desc = desc;
    }

    private Integer relation;
    private String desc;

    public Integer getRelation() {
        return relation;
    }

    public void setRelation(Integer relation) {
        this.relation = relation;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
