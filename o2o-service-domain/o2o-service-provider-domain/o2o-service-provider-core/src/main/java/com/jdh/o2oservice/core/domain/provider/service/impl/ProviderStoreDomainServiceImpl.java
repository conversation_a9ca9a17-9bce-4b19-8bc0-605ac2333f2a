package com.jdh.o2oservice.core.domain.provider.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.github.pagehelper.PageInfo;
import com.jd.health.xfyl.merchant.export.dto.store.StoreInfoDTO;
import com.jd.health.xfyl.merchant.export.param.supplier.SupplierMerchantStoreInfoParam;
import com.jd.health.xfyl.merchant.export.param.supplier.store.StoreInfoQueryParam;
import com.jd.health.xfyl.open.export.param.InStoreThirdStoreParam;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.enums.JdhStoreTransferStationTypeEnum;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.errorcode.DynamicErrorCode;
import com.jdh.o2oservice.base.factory.GenerateIdFactory;
import com.jdh.o2oservice.common.enums.BusinessModeEnum;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.core.domain.provider.bo.ListStoreScheduleBO;
import com.jdh.o2oservice.core.domain.provider.bo.StoreInfoBo;
import com.jdh.o2oservice.core.domain.provider.bo.XfylAppointDateBO;
import com.jdh.o2oservice.core.domain.provider.context.AppointmentMigrationContext;
import com.jdh.o2oservice.core.domain.provider.context.ProviderStoreContext;
import com.jdh.o2oservice.core.domain.provider.context.UpdateMerchantStoreBySelectContext;
import com.jdh.o2oservice.core.domain.provider.converter.JdhProviderStoreConverter;
import com.jdh.o2oservice.core.domain.provider.model.JdhStationServiceItemRel;
import com.jdh.o2oservice.core.domain.provider.model.JdhStoreTransferStation;
import com.jdh.o2oservice.core.domain.provider.model.JdhStoreTransferStationRel;
import com.jdh.o2oservice.core.domain.provider.query.ProviderStoreDetailQuery;
import com.jdh.o2oservice.core.domain.provider.query.QueryMerchantStoreListByParamQuery;
import com.jdh.o2oservice.core.domain.provider.repository.db.JdhStoreTransferStationRelRepository;
import com.jdh.o2oservice.core.domain.provider.repository.db.JdhStoreTransferStationRepository;
import com.jdh.o2oservice.core.domain.provider.repository.db.ProviderStoreRepository;
import com.jdh.o2oservice.core.domain.provider.repository.query.StoreDateScheduleQuery;
import com.jdh.o2oservice.core.domain.provider.rpc.ProviderStoreExportServiceRpc;
import com.jdh.o2oservice.core.domain.provider.rpc.ProviderStoreScheduleRpc;
import com.jdh.o2oservice.core.domain.provider.service.ProviderStoreDomainService;
import com.jdh.o2oservice.export.provider.cmd.JdhStoreTransferStationAddCmd;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName ProviderStoreDomainServiceImpl
 * @Description
 * <AUTHOR>
 * @Date 2024/1/2 16:21
 **/
@Service
@Slf4j
public class ProviderStoreDomainServiceImpl implements ProviderStoreDomainService {

    /**
     * providerStoreDateScheduleRpc
     */
    @Autowired
    private ProviderStoreScheduleRpc providerStoreScheduleRpc;

    @Resource
    private ProviderStoreExportServiceRpc providerStoreExportServiceRpc;

    @Resource
    private ProviderStoreRepository providerStoreRepository;

    @Resource
    private JdhStoreTransferStationRepository jdhStoreTransferStationRepository;

    @Resource
    private JdhStoreTransferStationRelRepository jdhStoreTransferStationRelRepository;

    /**
     * generateIdFactory
     */
    @Resource
    private GenerateIdFactory generateIdFactory;

    /**
     * 查询门店排期列表
     * @param query
     * @return
     */
    @Override
    public List<XfylAppointDateBO> queryStoreDateScheduleList(StoreDateScheduleQuery query) {
        if (Objects.equals(query.getVerticalBusiness().getBusinessModeCode(), BusinessModeEnum.POP_LOC.getCode())){
            log.info("ProviderStoreDomainServiceImpl -> queryStoreDateScheduleList pop查排期流程");
            ListStoreScheduleBO storeScheduleBO = ListStoreScheduleBO.builder()
                    .skuNo(query.getServiceId())
                    .storeId(query.getStoreId())
                    .build();
            return providerStoreScheduleRpc.queryPopStoreSchedule(storeScheduleBO);
        }
        else if (Objects.equals(query.getVerticalBusiness().getBusinessModeCode(), BusinessModeEnum.YK.getCode())){
            log.info("ProviderStoreDomainServiceImpl -> queryStoreDateScheduleList 一卡万店查排期流程");
            return null;
        }
        return null;
    }

    /**
     * 创建实验室
     * @param providerStoreContext
     * @return
     */
    @Override
    @LogAndAlarm
    public Boolean addQuickMerchantStore(ProviderStoreContext providerStoreContext) {
        InStoreThirdStoreParam inStoreThirdStoreParam = JdhProviderStoreConverter.INSTANCE.toPushStoreInfo(providerStoreContext);
        Boolean ret = providerStoreExportServiceRpc.pushStoreInfo(inStoreThirdStoreParam);
        if (CollUtil.isEmpty(providerStoreContext.getTransferStations())) {
            // 如果前端没传自配送，服务端兜底处理
            List<JdhStoreTransferStation> transferStations = new ArrayList<>();
            transferStations.add(buildDefaultJdhStoreTransferStation(null, inStoreThirdStoreParam.getStoreName(), inStoreThirdStoreParam.getStoreAddr(), inStoreThirdStoreParam.getStoreLat(), inStoreThirdStoreParam.getStoreLng()));
            providerStoreContext.setTransferStations(transferStations);
        } else {
            // 如果前端没传自配送，服务端兜底处理
            if (providerStoreContext.getTransferStations().stream().noneMatch(s -> JdhStoreTransferStationTypeEnum.SELF_RECEIVE_SAMPLE.getType().equals(s.getStationType()))) {
                providerStoreContext.getTransferStations().add(0, buildDefaultJdhStoreTransferStation(null, inStoreThirdStoreParam.getStoreName(), inStoreThirdStoreParam.getStoreAddr(), inStoreThirdStoreParam.getStoreLat(), inStoreThirdStoreParam.getStoreLng()));
            }
        }

        for (JdhStoreTransferStation jdhStoreTransferStation : providerStoreContext.getTransferStations()) {
            Long stationId = generateIdFactory.getId();
            if (JdhStoreTransferStationTypeEnum.SELF_RECEIVE_SAMPLE.getType().equals(jdhStoreTransferStation.getStationType()) && (jdhStoreTransferStation.getJdStationLatitude() == null || jdhStoreTransferStation.getJdStationLongitude() == null)) {
                jdhStoreTransferStation.setJdStationLatitude(inStoreThirdStoreParam.getStoreLat());
                jdhStoreTransferStation.setJdStationLongitude(inStoreThirdStoreParam.getStoreLng());
            }
            jdhStoreTransferStation.setJdStationId(stationId);
            jdhStoreTransferStationRepository.save(jdhStoreTransferStation);
            jdhStoreTransferStationRelRepository.save(JdhStoreTransferStationRel.builder().jdStoreId(inStoreThirdStoreParam.getJdStoreId()).jdTransferStationId(stationId).build());
        }
        return ret;
    }

    /**
     * 查询实验室详情
     * @param providerStoreDetailQuery
     * @return
     */
    @Override
    @LogAndAlarm
    public StoreInfoBo queryMerchantStoreDetailByParam(ProviderStoreDetailQuery providerStoreDetailQuery) {
        return providerStoreExportServiceRpc.queryByStoreId(providerStoreDetailQuery.getJdStoreId());
    }

    /**
     * 编辑实验室
     * @param providerStoreContext
     * @return
     */
    @Override
    @LogAndAlarm
    public Boolean updateQuickMerchantStore(ProviderStoreContext providerStoreContext) {
        InStoreThirdStoreParam inStoreThirdStoreParam = JdhProviderStoreConverter.INSTANCE.toPushStoreInfo(providerStoreContext);
        Boolean ret = providerStoreExportServiceRpc.updateMerchantStore(inStoreThirdStoreParam);

        List<JdhStoreTransferStationRel> relList = jdhStoreTransferStationRelRepository.queryList(JdhStoreTransferStationRel.builder().jdStoreId(inStoreThirdStoreParam.getJdStoreId()).build());

        List<JdhStoreTransferStation> insertList = new ArrayList<>();
        List<JdhStoreTransferStation> updateList = new ArrayList<>();
        Collection<Long> deleteList = new ArrayList<>();
        for (JdhStoreTransferStation jdhStoreTransferStation : providerStoreContext.getTransferStations()) {
            Long stationId = jdhStoreTransferStation.getJdStationId();
            if (stationId == null) {
                insertList.add(jdhStoreTransferStation);
            } else {
                updateList.add(jdhStoreTransferStation);
            }
        }
        if (CollectionUtils.isNotEmpty(relList)) {
            List<Long> dbRelStationId = relList.stream().map(JdhStoreTransferStationRel::getJdTransferStationId).collect(Collectors.toList());
            List<Long> paramRelStationId = updateList.stream().map(JdhStoreTransferStation::getJdStationId).collect(Collectors.toList());
            deleteList = CollectionUtils.subtract(dbRelStationId, paramRelStationId);
        }

        if (CollectionUtils.isNotEmpty(insertList)) {
            for (JdhStoreTransferStation jdhStoreTransferStation : insertList) {
                Long stationId = generateIdFactory.getId();
                if (JdhStoreTransferStationTypeEnum.SELF_RECEIVE_SAMPLE.getType().equals(jdhStoreTransferStation.getStationType())) {
                    jdhStoreTransferStation = buildDefaultJdhStoreTransferStation(stationId, inStoreThirdStoreParam.getStoreName(), inStoreThirdStoreParam.getStoreAddr(), inStoreThirdStoreParam.getStoreLat(), inStoreThirdStoreParam.getStoreLng());
                }
                jdhStoreTransferStation.setJdStationId(stationId);
                jdhStoreTransferStationRepository.save(jdhStoreTransferStation);
                jdhStoreTransferStationRelRepository.save(JdhStoreTransferStationRel.builder().jdStoreId(inStoreThirdStoreParam.getJdStoreId()).jdTransferStationId(stationId).build());
            }
        }
        if (CollectionUtils.isNotEmpty(updateList)) {
            for (JdhStoreTransferStation jdhStoreTransferStation : updateList) {
                if (JdhStoreTransferStationTypeEnum.SELF_RECEIVE_SAMPLE.getType().equals(jdhStoreTransferStation.getStationType())) {
                    jdhStoreTransferStation = buildDefaultJdhStoreTransferStation(jdhStoreTransferStation.getJdStationId(), inStoreThirdStoreParam.getStoreName(), inStoreThirdStoreParam.getStoreAddr(), inStoreThirdStoreParam.getStoreLat(), inStoreThirdStoreParam.getStoreLng());
                }
                jdhStoreTransferStationRepository.update(jdhStoreTransferStation);
            }
        }
        // 如果新增、更新无自配送，兜底增加自配送
        if (updateList.stream().noneMatch(s -> JdhStoreTransferStationTypeEnum.SELF_RECEIVE_SAMPLE.getType().equals(s.getStationType())) && insertList.stream().noneMatch(s -> JdhStoreTransferStationTypeEnum.SELF_RECEIVE_SAMPLE.getType().equals(s.getStationType()))) {
            Long stationId = generateIdFactory.getId();
            jdhStoreTransferStationRepository.save(buildDefaultJdhStoreTransferStation(stationId, inStoreThirdStoreParam.getStoreName(), inStoreThirdStoreParam.getStoreAddr(), inStoreThirdStoreParam.getStoreLat(), inStoreThirdStoreParam.getStoreLng()));
            jdhStoreTransferStationRelRepository.save(JdhStoreTransferStationRel.builder().jdStoreId(inStoreThirdStoreParam.getJdStoreId()).jdTransferStationId(stationId).build());
        }
        if (CollectionUtils.isNotEmpty(deleteList)) {
            for (Long deleteId : deleteList) {
                jdhStoreTransferStationRelRepository.delete(JdhStoreTransferStationRel.builder().jdStoreId(inStoreThirdStoreParam.getJdStoreId()).jdTransferStationId(deleteId).build());
            }
        }
        return ret;
    }

    /**
     * 查询实验室列表分页
     * @param queryMerchantStoreListByParamQuery
     * @return
     */
    @Override
    @LogAndAlarm
    public PageDto<StoreInfoBo> queryMerchantStoreListByParam(QueryMerchantStoreListByParamQuery queryMerchantStoreListByParamQuery) {
        StoreInfoQueryParam storeInfoQueryParam = JdhProviderStoreConverter.INSTANCE.toStoreInfoQueryParam(queryMerchantStoreListByParamQuery);
        PageInfo<StoreInfoDTO> pageInfo = providerStoreExportServiceRpc.queryMerchantStoreListByParam(storeInfoQueryParam);
        PageDto<StoreInfoBo> pageDto = new PageDto<>();
        pageDto.setPageSize(pageInfo.getPageSize());
        pageDto.setPageNum(pageInfo.getPageNum());
        pageDto.setList(JdhProviderStoreConverter.INSTANCE.merchantDtoToBo(pageInfo.getList()));
        pageDto.setTotalCount(pageInfo.getTotal());
        return pageDto;
    }

    /**
     * 更新实验室迁移配置
     * @param updateMerchantStoreBySelectContext
     * @return
     */
    @Override
    @LogAndAlarm
    public Boolean updateMerchantStoreBySelect(UpdateMerchantStoreBySelectContext updateMerchantStoreBySelectContext) {
        SupplierMerchantStoreInfoParam supplierMerchantStoreInfoParam = JdhProviderStoreConverter.INSTANCE.toSupplierMerchantStoreInfoParam(updateMerchantStoreBySelectContext);
        return providerStoreExportServiceRpc.updateMerchantStoreBySelect(supplierMerchantStoreInfoParam);
    }

    /**
     * 实验室迁移
     * @param appointmentMigrationContext
     * @return
     */
    @Override
    @LogAndAlarm
    public Boolean appointmentMigration(AppointmentMigrationContext appointmentMigrationContext) {

        //查询原实验室检测项目
        JdhStationServiceItemRel jdhStationServiceItemRel = new JdhStationServiceItemRel();
        jdhStationServiceItemRel.setStationId(appointmentMigrationContext.getFromJdStoreId());
        List<JdhStationServiceItemRel> fromJdhStationServiceItemRels = providerStoreRepository.queryStationServiceItemList(jdhStationServiceItemRel);
        if(CollectionUtils.isEmpty(fromJdhStationServiceItemRels)){
            log.error("原实验室检测项目为空,逻辑终止");
            throw new BusinessException(new DynamicErrorCode("-1","原实验室检测项目为空"));
        }

        //查询目标实验室检测项目
        JdhStationServiceItemRel targetJdhStationServiceItemRel = new JdhStationServiceItemRel();
        targetJdhStationServiceItemRel.setStationId(appointmentMigrationContext.getTargetJdStoreId());
        List<JdhStationServiceItemRel> targetJdhStationServiceItemRels = providerStoreRepository.queryStationServiceItemList(targetJdhStationServiceItemRel);
        if(CollectionUtils.isEmpty(targetJdhStationServiceItemRels)){
            log.error("目标实验室检测项目为空,逻辑终止");
            throw new BusinessException(new DynamicErrorCode("-1","目标实验室检测项目为空"));
        }


        Set<Long> targetServiceItemIds = targetJdhStationServiceItemRels.stream().map(JdhStationServiceItemRel::getServiceItemId).collect(Collectors.toSet());
        Set<Long> fromServiceItemIds = fromJdhStationServiceItemRels.stream().map(JdhStationServiceItemRel::getServiceItemId).collect(Collectors.toSet());

        //判断新实验室技能包含原实验室技能
        if(!targetServiceItemIds.containsAll(fromServiceItemIds)){
            log.error("目标实验室不支持原实验室所有检测项目,逻辑终止");
            throw new BusinessException(new DynamicErrorCode("-1","目标实验室检测项目为空"));
        }

        //保存实验室迁移配置
        SupplierMerchantStoreInfoParam supplierMerchantStoreInfoParam = JdhProviderStoreConverter.INSTANCE.toSupplierMerchantStoreInfoParam(appointmentMigrationContext);
        Boolean result = providerStoreExportServiceRpc.updateMerchantStoreBySelect(supplierMerchantStoreInfoParam);
        if(!result){
            log.error("实验室迁移配置保存失败");
            throw new BusinessException(new DynamicErrorCode("-1","实验室迁移配置保存失败"));
        }

        return true;
    }

    /**
     * 添加默认接驳点
     *
     * @param jdhStoreTransferStationAddCmd
     * @return
     */
    @Override
    @LogAndAlarm
    public Boolean addDefaultTransformStation(JdhStoreTransferStationAddCmd jdhStoreTransferStationAddCmd) {
        if (jdhStoreTransferStationAddCmd == null || StringUtils.isBlank(jdhStoreTransferStationAddCmd.getJdStoreId())) {
            return false;
        }
        List<JdhStoreTransferStationRel> relList = jdhStoreTransferStationRelRepository.queryList(JdhStoreTransferStationRel.builder().jdStoreId(jdhStoreTransferStationAddCmd.getJdStoreId()).build());
        List<JdhStoreTransferStation> jdhStoreTransferStations = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(relList)) {
            jdhStoreTransferStations = jdhStoreTransferStationRepository.queryList(JdhStoreTransferStation.builder().jdStationIds(relList.stream().map(JdhStoreTransferStationRel::getJdTransferStationId).collect(Collectors.toList())).build());
        }
        if(CollectionUtils.isEmpty(relList) || CollUtil.isEmpty(jdhStoreTransferStations.stream().filter(s -> JdhStoreTransferStationTypeEnum.SELF_RECEIVE_SAMPLE.getType().equals(s.getStationType())).collect(Collectors.toList()))){
            StoreInfoBo storeInfoBo = providerStoreExportServiceRpc.queryByStoreId(jdhStoreTransferStationAddCmd.getJdStoreId());
            if(storeInfoBo == null){
                storeInfoBo = new StoreInfoBo();
                storeInfoBo.setStoreName(jdhStoreTransferStationAddCmd.getJdStationName());
                storeInfoBo.setStoreAddr(jdhStoreTransferStationAddCmd.getJdStationAddress());
                storeInfoBo.setLat(String.valueOf(jdhStoreTransferStationAddCmd.getJdStationLatitude()));
                storeInfoBo.setLng(String.valueOf(jdhStoreTransferStationAddCmd.getJdStationLongitude()));
            }
            Long stationId = generateIdFactory.getId();
            jdhStoreTransferStationRepository.save(buildDefaultJdhStoreTransferStation(stationId, storeInfoBo.getStoreName(), storeInfoBo.getStoreAddr(), Double.parseDouble(storeInfoBo.getLat()), Double.parseDouble(storeInfoBo.getLng())));
            jdhStoreTransferStationRelRepository.save(JdhStoreTransferStationRel.builder().jdStoreId(jdhStoreTransferStationAddCmd.getJdStoreId()).jdTransferStationId(stationId).build());
            return true;
        }

        return false;
    }

    /**
     * 构建自配送
     * @param storeName
     * @param storeAddress
     * @param storeLat
     * @param storeLng
     * @return
     */
    private JdhStoreTransferStation buildDefaultJdhStoreTransferStation(Long transformStationId, String storeName, String storeAddress, Double storeLat, Double storeLng) {
        JdhStoreTransferStation jdhStoreTransferStation = new JdhStoreTransferStation();
        if (transformStationId == null) {
            jdhStoreTransferStation.setJdStationId(generateIdFactory.getId());
        } else {
            jdhStoreTransferStation.setJdStationId(transformStationId);
        }
        jdhStoreTransferStation.setStationType(JdhStoreTransferStationTypeEnum.SELF_RECEIVE_SAMPLE.getType());
        jdhStoreTransferStation.setJdStationName(storeName);
        jdhStoreTransferStation.setJdStationAddress(storeAddress);
        jdhStoreTransferStation.setJdStationLatitude(storeLat);
        jdhStoreTransferStation.setJdStationLongitude(storeLng);
        return jdhStoreTransferStation;
    }
}