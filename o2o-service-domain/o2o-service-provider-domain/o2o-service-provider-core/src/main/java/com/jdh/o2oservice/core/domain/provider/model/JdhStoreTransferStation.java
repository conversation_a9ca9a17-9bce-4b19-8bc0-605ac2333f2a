package com.jdh.o2oservice.core.domain.provider.model;

import java.util.Date;
import java.util.List;

import com.jdh.o2oservice.base.model.Aggregate;
import com.jdh.o2oservice.base.model.AggregateCode;
import com.jdh.o2oservice.base.model.DomainCode;
import com.jdh.o2oservice.base.model.DomainEnum;
import com.jdh.o2oservice.common.result.request.AbstractPageQuery;
import lombok.*;
/**
 * 
 */
@Data
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class JdhStoreTransferStation extends AbstractPageQuery implements Aggregate<JdhStoreTransferStationIdentifier> {

    /** 主键 */
    private Long id;

    /** 接驳点类型 1-实验室自收样  2-无人机 */
    private Integer stationType;

    /** 京东接驳点id */
    private Long jdStationId;

    /** 京东接驳点id */
    private List<Long> jdStationIds;

    /** 京东接驳点名称 */
    private String jdStationName;

    /** 京东接驳点地址 */
    private String jdStationAddress;

    /** 京东接驳点维度 */
    private Double jdStationLatitude;

    /** 京东接驳点经度 */
    private Double jdStationLongitude;

    /** 下一个京东站点id */
    private Long jdStationTargetId;

    /** 外部系统接驳点id */
    private String thirdStationId;

    /** 下一个外部系统接驳点id */
    private String thirdStationTargetId;

    /** 创建时间 */
    private Date createTime;

    /** 创建人 */
    private String createUser;

    /** 更新时间 */
    private Date updateTime;

    /** 修改人 */
    private String updateUser;

    /** 有效标志 0 无效 1有效 */
    private Integer yn;

    /** 数据来源分支 */
    private String branch;

    /** 版本号 */
    private Integer version;

    /**
     * 聚合所属领域编码
     *
     * @return
     */
    @Override
    public DomainCode getDomainCode() {
        return DomainEnum.PROVIDER;
    }

    /**
     * 获取聚合编码
     *
     * @return {@link AggregateCode}
     */
    @Override
    public AggregateCode getAggregateCode() {
        return null;
    }

    /**
     * 版本
     *
     * @return {@link Integer}
     */
    @Override
    public Integer version() {
        return this.version;
    }

    /**
     * 版本增加
     */
    @Override
    public void versionIncrease() {
        version++;
    }

    /**
     * 获取标识符
     *
     * @return
     */
    @Override
    public JdhStoreTransferStationIdentifier getIdentifier() {
        return JdhStoreTransferStationIdentifier.builder().jdStationId(jdStationId).build();
    }
}