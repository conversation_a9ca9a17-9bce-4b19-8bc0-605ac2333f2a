package com.jdh.o2oservice.core.domain.provider.model;

import com.jdh.o2oservice.base.model.Aggregate;
import com.jdh.o2oservice.base.model.AggregateCode;
import com.jdh.o2oservice.base.model.DomainCode;
import com.jdh.o2oservice.base.model.DomainEnum;
import com.jdh.o2oservice.common.result.request.AbstractPageQuery;
import lombok.*;

import java.util.Date;
import java.util.List;

/**
 * 
 */
@Data
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class JdhStoreTransferStationRel extends AbstractPageQuery implements Aggregate<JdhStoreTransferStationRelIdentifier> {

    /** 主键 */
    private Long id;

    /** 京东系统实验室id */
    private String jdStoreId;

    /** 京东系统实验室id */
    private List<String> jdStoreIds;

    /** 京东接驳点id */
    private Long jdTransferStationId;

    /** 创建时间 */
    private Date createTime;

    /** 创建人 */
    private String createUser;

    /** 更新时间 */
    private Date updateTime;

    /** 修改人 */
    private String updateUser;

    /** 有效标志 0 无效 1有效 */
    private Integer yn;

    /** 数据来源分支 */
    private String branch;

    /** 版本号 */
    private Integer version;

    /**
     * 聚合所属领域编码
     *
     * @return
     */
    @Override
    public DomainCode getDomainCode() {
        return DomainEnum.PROVIDER;
    }

    /**
     * 获取聚合编码
     *
     * @return {@link AggregateCode}
     */
    @Override
    public AggregateCode getAggregateCode() {
        return null;
    }

    /**
     * 版本
     *
     * @return {@link Integer}
     */
    @Override
    public Integer version() {
        return this.version;
    }

    /**
     * 版本增加
     */
    @Override
    public void versionIncrease() {
        version++;
    }

    /**
     * 获取标识符
     *
     * @return
     */
    @Override
    public JdhStoreTransferStationRelIdentifier getIdentifier() {
        return JdhStoreTransferStationRelIdentifier.builder().id(id).build();
    }
}