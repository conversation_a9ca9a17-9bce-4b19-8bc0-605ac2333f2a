package com.jdh.o2oservice.core.domain.provider.service;

import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.core.domain.provider.bo.StoreInfoBo;
import com.jdh.o2oservice.core.domain.provider.bo.XfylAppointDateBO;
import com.jdh.o2oservice.core.domain.provider.context.AppointmentMigrationContext;
import com.jdh.o2oservice.core.domain.provider.context.ProviderStoreContext;
import com.jdh.o2oservice.core.domain.provider.context.UpdateMerchantStoreBySelectContext;
import com.jdh.o2oservice.core.domain.provider.query.ProviderStoreDetailQuery;
import com.jdh.o2oservice.core.domain.provider.query.QueryMerchantStoreListByParamQuery;
import com.jdh.o2oservice.core.domain.provider.repository.query.StoreDateScheduleQuery;
import com.jdh.o2oservice.export.provider.cmd.JdhStoreTransferStationAddCmd;
import com.jdh.o2oservice.export.provider.query.JdhStationServiceItemRelRequest;

import java.util.List;

/**
 * @ClassName ProviderStoreDomainService
 * @Description
 * <AUTHOR>
 * @Date 2024/1/2 16:20
 **/
public interface ProviderStoreDomainService {

    /**
     * 查询门店排期列表
     * @param query
     * @return
     */
    List<XfylAppointDateBO> queryStoreDateScheduleList(StoreDateScheduleQuery query);

    /**
     * 创建实验室
     * @param providerStoreContext
     * @return
     */
    Boolean addQuickMerchantStore(ProviderStoreContext providerStoreContext);

    /**
     * 查询实验室详情
     * @param providerStoreDetailQuery
     * @return
     */
    StoreInfoBo queryMerchantStoreDetailByParam(ProviderStoreDetailQuery providerStoreDetailQuery);

    /**
     * 编辑实验室
     * @param providerStoreContext
     * @return
     */
    Boolean updateQuickMerchantStore(ProviderStoreContext providerStoreContext);

    /**
     * 查询实验室列表分页
     * @param queryMerchantStoreListByParamQuery
     * @return
     */
    PageDto<StoreInfoBo> queryMerchantStoreListByParam(QueryMerchantStoreListByParamQuery queryMerchantStoreListByParamQuery);

    /**
     * 更新实验室迁移配置
     * @param updateMerchantStoreBySelectContext
     * @return
     */
    Boolean updateMerchantStoreBySelect(UpdateMerchantStoreBySelectContext updateMerchantStoreBySelectContext);

    /**
     * 实验室迁移
     * @param appointmentMigrationContext
     * @return
     */
    Boolean appointmentMigration(AppointmentMigrationContext appointmentMigrationContext);

    /**
     * 添加默认接驳点
     * @param jdhStoreTransferStationAddCmd
     * @return
     */
    Boolean addDefaultTransformStation(JdhStoreTransferStationAddCmd jdhStoreTransferStationAddCmd);
}