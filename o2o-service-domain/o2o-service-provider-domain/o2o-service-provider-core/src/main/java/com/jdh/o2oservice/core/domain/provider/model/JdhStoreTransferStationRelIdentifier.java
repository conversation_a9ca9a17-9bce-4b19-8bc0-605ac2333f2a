package com.jdh.o2oservice.core.domain.provider.model;

import com.jdh.o2oservice.base.model.Identifier;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 商家门店唯一
 *
 * <AUTHOR>
 * @date 2024/04/25
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class JdhStoreTransferStationRelIdentifier implements Identifier {

    /**
     * 京东接驳点id
     */
    private Long id;

    @Override
    public String serialize() {
        return String.valueOf(id);
    }
}