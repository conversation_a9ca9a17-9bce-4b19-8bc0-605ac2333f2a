package com.jdh.o2oservice.core.domain.provider.repository.db;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jdh.o2oservice.base.model.Repository;
import com.jdh.o2oservice.core.domain.provider.model.*;

import java.util.List;

/**
 * 实验室接驳点数据仓
 *
 * <AUTHOR>
 * @date 2024/04/25
 */
public interface JdhStoreTransferStationRepository extends Repository<JdhStoreTransferStation, JdhStoreTransferStationIdentifier> {

    /**
     * 保存接驳点
     *
     * @param jdhStoreTransferStation jdhStoreTransferStation
     * @return count
     */
    int save(JdhStoreTransferStation jdhStoreTransferStation);

    /**
     * 更新接驳点
     *
     * @param jdhStoreTransferStation jdhStoreTransferStation
     * @return count
     */
    int update(JdhStoreTransferStation jdhStoreTransferStation);

    /**
     * 删除接驳点
     *
     * @param jdhStoreTransferStation jdhStoreTransferStation
     * @return count
     */
    int delete(JdhStoreTransferStation jdhStoreTransferStation);

    /**
     * 查询门店项目关系
     *
     * @param jdhStoreTransferStation jdhStoreTransferStation
     * @return list
     */
    List<JdhStoreTransferStation> queryList(JdhStoreTransferStation jdhStoreTransferStation);

    /**
     * 分页查询门店项目关系
     *
     */
    Page<JdhStoreTransferStation> queryPageList(JdhStoreTransferStation jdhStoreTransferStation);

    /**
     * 查询门店项目关系
     *
     */
    JdhStoreTransferStation query(JdhStoreTransferStation jdhStoreTransferStation);
}