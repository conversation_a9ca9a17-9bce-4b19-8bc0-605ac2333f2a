package com.jdh.o2oservice.core.domain.provider.repository.db;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jdh.o2oservice.base.model.Repository;
import com.jdh.o2oservice.core.domain.provider.model.JdhStoreTransferStation;
import com.jdh.o2oservice.core.domain.provider.model.JdhStoreTransferStationIdentifier;
import com.jdh.o2oservice.core.domain.provider.model.JdhStoreTransferStationRel;
import com.jdh.o2oservice.core.domain.provider.model.JdhStoreTransferStationRelIdentifier;

import java.util.List;

/**
 * 实验室接驳点数据仓
 *
 * <AUTHOR>
 * @date 2024/04/25
 */
public interface JdhStoreTransferStationRelRepository extends Repository<JdhStoreTransferStationRel, JdhStoreTransferStationRelIdentifier> {

    /**
     * 保存接驳点
     *
     * @param jdhStoreTransferStationRel jdhStoreTransferStationRel
     * @return count
     */
    int save(JdhStoreTransferStationRel jdhStoreTransferStationRel);

    /**
     * 更新接驳点
     *
     * @param jdhStoreTransferStationRel jdhStoreTransferStationRel
     * @return count
     */
    int update(JdhStoreTransferStationRel jdhStoreTransferStationRel);

    /**
     * 删除接驳点
     *
     * @param jdhStoreTransferStationRel jdhStoreTransferStationRel
     * @return count
     */
    int delete(JdhStoreTransferStationRel jdhStoreTransferStationRel);

    /**
     * 查询门店项目关系
     *
     * @param jdhStoreTransferStationRel jdhStoreTransferStationRel
     * @return list
     */
    List<JdhStoreTransferStationRel> queryList(JdhStoreTransferStationRel jdhStoreTransferStationRel);

    /**
     * 分页查询门店项目关系
     *
     */
    Page<JdhStoreTransferStationRel> queryPageList(JdhStoreTransferStationRel jdhStoreTransferStationRel);

    /**
     * 查询门店项目关系
     *
     */
    JdhStoreTransferStationRel query(JdhStoreTransferStationRel jdhStoreTransferStationRel);
}