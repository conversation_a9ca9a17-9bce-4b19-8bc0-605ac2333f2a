package com.jdh.o2oservice.core.domain.promise.service.ability.promise.submit;

import com.jdh.o2oservice.common.enums.BusinessModeEnum;
import com.jdh.o2oservice.core.domain.promise.context.PromiseSubmitAbilityContext;
import com.jdh.o2oservice.core.domain.promise.enums.PromiseExtendKeyEnum;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromise;
import com.jdh.o2oservice.core.domain.promise.model.PromiseStation;
import com.jdh.o2oservice.core.domain.support.basic.rpc.JdhAddressRpc;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.AddressDetailBO;
import com.jdh.o2oservice.core.domain.support.vertical.model.JdhVerticalBusiness;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 履约人信息校验
 * 这三种来源的区别是 供应商对预约人的限制，业务模式本身对规则的限制，业务身份对规则的限制
 *
 * @author: yangxiyu
 * @date: 2022/9/9 9:43 上午
 * @version: 1.0
 */
@Component
@Slf4j
public class AppointmentUserParseAbility implements SubmitAbility {

    @Resource
    private JdhAddressRpc jdhAddressRpc;
    /**
     *
     * @return
     */
    @Override
    @SuppressWarnings("JdAvoidMethodReturnEnum")
    public SubmitAbilityCode getAbilityCode() {
        return SubmitAbilityCode.APPOINTMENT_USER_PARSE;
    }

    @Override
    public void execute(PromiseSubmitAbilityContext context) {
        JdhVerticalBusiness verticalBusiness = context.getVerticalBusiness();

        // 到家服务，根据地址ID获取预约人信息
        if(Objects.equals(verticalBusiness.getBusinessModeCode(), BusinessModeEnum.SELF_TEST.getCode())
                || Objects.equals(verticalBusiness.getBusinessModeCode(), BusinessModeEnum.ANGEL_TEST.getCode())
                || Objects.equals(verticalBusiness.getBusinessModeCode(), BusinessModeEnum.ANGEL_CARE.getCode())
                || Objects.equals(verticalBusiness.getBusinessModeCode(), BusinessModeEnum.SELF_TEST_TRANSPORT.getCode())
                || Objects.equals(verticalBusiness.getBusinessModeCode(), BusinessModeEnum.ANGEL_TEST_NO_LABORATORY.getCode())){

            try {
                PromiseStation station = context.getPromise().getStore();
                JdhPromise promise = context.getPromise();
                promise.setAppointmentPhone(context.getAppointmentPhone());
                String appointmentUserName = context.getAppointmentUserName();
                if(StringUtils.isNotBlank(promise.getUserPin()) && StringUtils.isNotBlank(station.getStoreId()) && StringUtils.isNumeric(station.getStoreId())){
                    AddressDetailBO addressDetailBO = jdhAddressRpc.getAddressDetail(promise.getUserPin(), Long.valueOf(station.getStoreId()));
                    if(Objects.nonNull(addressDetailBO)){
                        promise.setAppointmentPhone(addressDetailBO.decryptMobile());
                        appointmentUserName = addressDetailBO.getName();
                    }
                }
                if(StringUtils.isNotBlank(appointmentUserName)){
                    promise.refreshExtend(PromiseExtendKeyEnum.APPOINTMENT_USER_NAME, appointmentUserName);
                }
            }catch (Exception e){
                // 填充预约人手机号失败，不影响主流程
                log.warn("AppointmentUserParseAbility->execute error", e);
            }
        }else{
            throw new UnsupportedOperationException();
        }
    }
}
