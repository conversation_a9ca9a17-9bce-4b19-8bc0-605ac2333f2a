package com.jdh.o2oservice.core.domain.promise.service.ability.promise.modify;

import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.Lists;
import com.jdh.o2oservice.common.enums.OperatorRoleTypeEnum;
import com.jdh.o2oservice.core.domain.promise.context.PromiseModifySubmitAbilityContext;
import com.jdh.o2oservice.core.domain.promise.enums.PromiseExtendKeyEnum;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromiseExtend;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * 门店条件LocStoreInfoRpc
 * @author: yangxiyu
 * @date: 2022/9/9 9:43 上午
 * @version: 1.0
 */
@Component
@Slf4j
public class ModifyOperationUserPromiseAbility implements ModifyAbility {


    /**
     *
     * @return
     */
    @Override
    @SuppressWarnings("JdAvoidMethodReturnEnum")
    public ModifyAbilityCode getAbilityCode() {
        return ModifyAbilityCode.MODIFY_OPERATION_USER;
    }

    /**
     * 修改人信息
     * @param context
     */
    @Override
    public void execute(PromiseModifySubmitAbilityContext context) {
        if(context.getPromise() == null) {
            return;
        }
        String operatorPin = context.getUserPin();
        if (StringUtils.isNotBlank(context.getOperator())) {
            operatorPin = context.getOperator();
        }

        Integer operatorRoleType = context.getOperatorRoleType();
        if (operatorRoleType == null) {
            operatorRoleType = OperatorRoleTypeEnum.USER_SELF.getType();
        }
        context.getPromise().setOperator(operatorPin);
        context.getPromise().setOperatorRoleType(operatorRoleType);
        context.getPromise().setOperatorReason(context.getReason());
        context.getPromise().setOperatorTime(new Date());
        Long promiseId = context.getPromise().getPromiseId();
        // 操作人
        JdhPromiseExtend operationExtend = new JdhPromiseExtend();
        operationExtend.setAttribute(PromiseExtendKeyEnum.OPERATOR.getFiledKey());
        operationExtend.setValue(operatorPin);
        operationExtend.setPromiseId(promiseId);
        // 操作人
        JdhPromiseExtend operationRoleTypeExtend = new JdhPromiseExtend();
        operationRoleTypeExtend.setAttribute(PromiseExtendKeyEnum.OPERATOR_ROLE_TYPE.getFiledKey());
        operationRoleTypeExtend.setValue(String.valueOf(operatorRoleType));
        operationRoleTypeExtend.setPromiseId(promiseId);
        List<JdhPromiseExtend> extList = Lists.newArrayList(operationExtend, operationRoleTypeExtend);
        if (CollUtil.isEmpty(context.getPromise().getPromiseExtends())) {
            context.getPromise().setPromiseExtends(extList);
        } else {
            context.getPromise().getPromiseExtends().addAll(extList);
        }
    }
}
