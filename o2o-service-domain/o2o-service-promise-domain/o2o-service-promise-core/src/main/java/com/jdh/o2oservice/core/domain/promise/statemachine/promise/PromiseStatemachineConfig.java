package com.jdh.o2oservice.core.domain.promise.statemachine.promise;

import com.jdh.o2oservice.base.statemachine.StateContext;
import com.jdh.o2oservice.base.statemachine.StateMachine;
import com.jdh.o2oservice.base.statemachine.builder.StateMachineBuilder;
import com.jdh.o2oservice.base.statemachine.builder.StateMachineBuilderFactory;
import com.jdh.o2oservice.core.domain.promise.enums.PromiseEventTypeEnum;
import com.jdh.o2oservice.core.domain.promise.statemachine.promise.action.*;
import com.jdh.o2oservice.core.domain.promise.statemachine.promise.condition.*;
import com.jdh.o2oservice.core.domain.promise.statemachine.promise.failcallback.PromiseStatemachineFailCallBack;
import com.jdh.o2oservice.core.domain.support.basic.enums.JdhPromiseStatusEnum;
import com.jdh.o2oservice.core.domain.support.basic.enums.JdhVoucherStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;
import java.util.Arrays;

/**
 * JDH服务单状态机器配置
 *
 * <AUTHOR>
 * @date 2023/12/12
 */
@Slf4j
@Configuration
public class PromiseStatemachineConfig {
    /** 状态机ID */
    private static final String MACHINE_ID = "promiseStatemachine";

    /**
     * promiseSubmitCondition
     */
    @Resource
    private PromiseSubmitCondition promiseSubmitCondition;

    /**
     * promiseSubmitAction
     */
    @Resource
    private PromiseSubmitAction promiseSubmitAction;

    /**
     * userModifyAppointCondition
     */
    @Resource
    private UserModifyAppointCondition userModifyAppointCondition;

    /**
     * userModifyAppointAction
     */
    @Resource
    private UserModifyAppointAction userModifyAppointAction;

    /**
     * userCancelAppointCondition
     */
    @Resource
    private UserCancelAppointCondition userCancelAppointCondition;

    /**
     * userCancelAppointAction
     */
    @Resource
    private UserCancelAppointAction userCancelAppointAction;
    /** */
    @Resource
    private CallbackCondition callbackCondition;
    /** */
    @Resource
    private CallbackAction callbackAction;
    /** */
    @Resource
    private WriteOffAction writeOffAction;
    /** */
    @Resource
    private WriteOffCondition writeOffCondition;
    /** */
    @Resource
    private PromiseInvalidCondition promiseInvalidCondition;
    /** */
    @Resource
    private PromiseInvalidAction promiseInvalidAction;

    /**
     * promiseStatemachineFailCallBack
     */
    @Resource
    private PromiseStatemachineFailCallBack promiseStatemachineFailCallBack;

    /**
     * 履约单状态机配置
     * <a href="https://joyspace.jd.com/pages/W2ny0DGODuZHQzoqpwUu">...</a>
     * @return {@link StateMachine}<{@link JdhVoucherStatusEnum}, {@link PromiseEventTypeEnum}, {@link StateContext}>
     */
    @Bean
    public StateMachine<JdhPromiseStatusEnum, PromiseEventTypeEnum, StateContext> promiseStatemachine(){
        StateMachineBuilder<JdhPromiseStatusEnum, PromiseEventTypeEnum, StateContext> builder = StateMachineBuilderFactory.create();

        //==============>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>
        // 用户提交预约：待履约 -> 预约中
        builder.externalTransitions()
                .fromAmong(JdhPromiseStatusEnum.WAIT_PROMISE,JdhPromiseStatusEnum.CANCEL_SUCCESS,JdhPromiseStatusEnum.APPOINTMENT_FAIL)
                .to(JdhPromiseStatusEnum.APPOINTMENT_ING)
                .on(PromiseEventTypeEnum.PROMISE_SUBMIT)
                .when(promiseSubmitCondition)
                .perform(promiseSubmitAction);

        // 买约一体自动提交预约：待履约 -> 预约中
        builder.externalTransition()
                .from(JdhPromiseStatusEnum.WAIT_PROMISE)
                .to(JdhPromiseStatusEnum.APPOINTMENT_ING)
                .on(PromiseEventTypeEnum.PROMISE_AUTO_SUBMIT)
                .when(promiseSubmitCondition)
                .perform(promiseSubmitAction);

        // 回调预约成功
        builder.externalTransition()
                .from(JdhPromiseStatusEnum.APPOINTMENT_ING)
                .to(JdhPromiseStatusEnum.APPOINTMENT_SUCCESS)
                .on(PromiseEventTypeEnum.PROMISE_CALLBACK_APPOINTMENT_SUCCESS)
                .when(callbackCondition)
                .perform(callbackAction);

        // 回调预约失败
        builder.externalTransition()
                .from(JdhPromiseStatusEnum.APPOINTMENT_ING)
                .to(JdhPromiseStatusEnum.APPOINTMENT_FAIL)
                .on(PromiseEventTypeEnum.PROMISE_CALLBACK_APPOINTMENT_FAIL)
                .when(callbackCondition)
                .perform(callbackAction);

        // 用户修改预约： 预约成功/修改预约成功 -> 修改预约中
        builder.externalTransitions()
                .fromAmong(JdhPromiseStatusEnum.MODIFY_ING,JdhPromiseStatusEnum.WAIT_PROMISE,JdhPromiseStatusEnum.APPOINTMENT_ING,JdhPromiseStatusEnum.APPOINTMENT_SUCCESS,JdhPromiseStatusEnum.APPOINTMENT_FAIL,JdhPromiseStatusEnum.MODIFY_SUCCESS,JdhPromiseStatusEnum.MODIFY_FAIL,JdhPromiseStatusEnum.CANCEL_FAIL,JdhPromiseStatusEnum.SERVICE_READY,JdhPromiseStatusEnum.SERVICING)
                .to(JdhPromiseStatusEnum.MODIFY_ING)
                .on(PromiseEventTypeEnum.PROMISE_USER_SUBMIT_MODIFY)
                .when(userModifyAppointCondition)
                .perform(userModifyAppointAction);

        // 回调修改成功
        builder.externalTransition()
                .from(JdhPromiseStatusEnum.MODIFY_ING)
                .to(JdhPromiseStatusEnum.MODIFY_SUCCESS)
                .on(PromiseEventTypeEnum.PROMISE_CALLBACK_MODIFY_SUCCESS)
                .when(callbackCondition)
                .perform(callbackAction);

        // 回调修改失败
        builder.externalTransition()
                .from(JdhPromiseStatusEnum.MODIFY_ING)
                .to(JdhPromiseStatusEnum.MODIFY_FAIL)
                .on(PromiseEventTypeEnum.PROMISE_CALLBACK_MODIFY_FAIL)
                .when(callbackCondition)
                .perform(callbackAction);

        // 用户取消预约：预约成功/修改预约成功 -> 取消预约中
        builder.externalTransitions()
                .fromAmong(JdhPromiseStatusEnum.CANCEL_FAIL,JdhPromiseStatusEnum.MODIFY_FAIL,JdhPromiseStatusEnum.APPOINTMENT_ING,
                        JdhPromiseStatusEnum.MODIFY_SUCCESS,JdhPromiseStatusEnum.APPOINTMENT_SUCCESS,JdhPromiseStatusEnum.MODIFY_ING)
                .to(JdhPromiseStatusEnum.CANCEL_ING)
                .on(PromiseEventTypeEnum.PROMISE_USER_SUBMIT_CANCEL)
                .when(userCancelAppointCondition)
                .perform(userCancelAppointAction);

        // 回调取消成功
        builder.externalTransition()
                .from(JdhPromiseStatusEnum.CANCEL_ING)
                .to(JdhPromiseStatusEnum.CANCEL_SUCCESS)
                .on(PromiseEventTypeEnum.PROMISE_CALLBACK_CANCEL_SUCCESS)
                .when(callbackCondition)
                .perform(callbackAction);

        // 回调取消失败
        builder.externalTransition()
                .from(JdhPromiseStatusEnum.CANCEL_ING)
                .to(JdhPromiseStatusEnum.CANCEL_FAIL)
                .on(PromiseEventTypeEnum.PROMISE_CALLBACK_CANCEL_FAIL)
                .when(callbackCondition)
                .perform(callbackAction);

        // 到店
        builder.externalTransitions()
                .fromAmong(JdhPromiseStatusEnum.WAIT_PROMISE,JdhPromiseStatusEnum.APPOINTMENT_ING,JdhPromiseStatusEnum.MODIFY_ING
                        ,JdhPromiseStatusEnum.CANCEL_ING,JdhPromiseStatusEnum.APPOINTMENT_FAIL,JdhPromiseStatusEnum.APPOINTMENT_SUCCESS,JdhPromiseStatusEnum.MODIFY_FAIL,
                        JdhPromiseStatusEnum.MODIFY_SUCCESS,JdhPromiseStatusEnum.CANCEL_FAIL,JdhPromiseStatusEnum.CANCEL_SUCCESS)
                .to(JdhPromiseStatusEnum.SERVICE_COMPLETE)
                .on(PromiseEventTypeEnum.PROMISE_CALLBACK_NOTICE_COMPLETE)
                .when(callbackCondition)
                .perform(callbackAction);

        // 核销
        builder.externalTransitions()
                .fromAmong(JdhPromiseStatusEnum.WAIT_PROMISE,JdhPromiseStatusEnum.APPOINTMENT_ING,JdhPromiseStatusEnum.MODIFY_ING
                        ,JdhPromiseStatusEnum.CANCEL_ING,JdhPromiseStatusEnum.APPOINTMENT_FAIL,JdhPromiseStatusEnum.APPOINTMENT_SUCCESS,JdhPromiseStatusEnum.MODIFY_FAIL,
                        JdhPromiseStatusEnum.MODIFY_SUCCESS,JdhPromiseStatusEnum.CANCEL_FAIL,JdhPromiseStatusEnum.CANCEL_SUCCESS,JdhPromiseStatusEnum.SERVICING)
                .to(JdhPromiseStatusEnum.COMPLETE)
                .on(PromiseEventTypeEnum.PROMISE_CALLBACK_NOTICE_WRITE_OFF)
                .when(writeOffCondition)
                .perform(writeOffAction);

        // 用户核销
        builder.externalTransitions()
                .fromAmong(JdhPromiseStatusEnum.APPOINTMENT_SUCCESS,JdhPromiseStatusEnum.MODIFY_SUCCESS,JdhPromiseStatusEnum.MODIFY_FAIL,
                        JdhPromiseStatusEnum.CANCEL_FAIL)
                .to(JdhPromiseStatusEnum.COMPLETE)
                .on(PromiseEventTypeEnum.PROMISE_WRITE_OFF)
                .when(writeOffCondition)
                .perform(writeOffAction);

        // 回传派单成功：预约中 -> 预约成功
        /**
         * 正常逻辑都是预约中-> 预约成功；但是对于到家的场景可能先收到待服务的消息，再收到派单成功的消息，这种类型的状态转换也是允许的，
         * 但是不需要配置其action\condition，不执行逻辑，空白跳过
         */
        builder.externalTransitions()
                .fromAmong(JdhPromiseStatusEnum.APPOINTMENT_ING, JdhPromiseStatusEnum.SERVICE_READY, JdhPromiseStatusEnum.MODIFY_ING)
                .to(JdhPromiseStatusEnum.APPOINTMENT_SUCCESS)
                .on(PromiseEventTypeEnum.PROMISE_CALLBACK_DISPATCH_SUCCESS)
                .when(callbackCondition)
                .perform(callbackAction);

        /**
         * 服务者待服务： 预约成功 -> 待服务
         * 在到家的场景下，无法保证派单成功的事件肯定比护士出门、或者骑手接单的事件先抵达，所以支持状态直接从预约中跳到待服务。
         * 当派单成功的事件后置抵达时，为了避免系统报错，需要配置可
         */
        builder.externalTransitions()
                .fromAmong(JdhPromiseStatusEnum.APPOINTMENT_SUCCESS, JdhPromiseStatusEnum.APPOINTMENT_ING, JdhPromiseStatusEnum.MODIFY_SUCCESS,
                        JdhPromiseStatusEnum.MODIFY_ING, JdhPromiseStatusEnum.MODIFY_FAIL,JdhPromiseStatusEnum.SERVICE_READY)
                .to(JdhPromiseStatusEnum.SERVICE_READY)
                .on(PromiseEventTypeEnum.PROMISE_CALLBACK_SERVICE_READY)
                .when(callbackCondition)
                .perform(callbackAction);

        /**
         *
         */
        // 服务者开始服务：待服务 -> 服务中
        builder.externalTransitions()
                .fromAmong(JdhPromiseStatusEnum.SERVICE_READY,JdhPromiseStatusEnum.APPOINTMENT_SUCCESS,JdhPromiseStatusEnum.APPOINTMENT_ING,
                        JdhPromiseStatusEnum.MODIFY_SUCCESS,JdhPromiseStatusEnum.MODIFY_ING,JdhPromiseStatusEnum.MODIFY_FAIL)
                .to(JdhPromiseStatusEnum.SERVICING)
                .on(PromiseEventTypeEnum.PROMISE_CALLBACK_SERVICING)
                .when(callbackCondition)
                .perform(callbackAction);

        // 服务者(骑手/护士)服务完成：服务中 -> 完成
        builder.externalTransitions()
                .fromAmong(JdhPromiseStatusEnum.APPOINTMENT_ING, JdhPromiseStatusEnum.APPOINTMENT_SUCCESS, JdhPromiseStatusEnum.APPOINTMENT_FAIL
                        , JdhPromiseStatusEnum.MODIFY_ING , JdhPromiseStatusEnum.MODIFY_SUCCESS, JdhPromiseStatusEnum.MODIFY_FAIL
                        , JdhPromiseStatusEnum.SERVICING, JdhPromiseStatusEnum.SERVICE_READY)
                .to(JdhPromiseStatusEnum.SERVICE_COMPLETE)
                .on(PromiseEventTypeEnum.PROMISE_CALLBACK_SERVICE_COMPLETE)
                .when(callbackCondition)
                .perform(callbackAction);

        // 履约完成
        builder.externalTransitions()
                .fromAmong(JdhPromiseStatusEnum.SERVICE_COMPLETE, JdhPromiseStatusEnum.APPOINTMENT_ING, JdhPromiseStatusEnum.APPOINTMENT_SUCCESS, JdhPromiseStatusEnum.APPOINTMENT_FAIL
                        , JdhPromiseStatusEnum.MODIFY_ING , JdhPromiseStatusEnum.MODIFY_SUCCESS, JdhPromiseStatusEnum.MODIFY_FAIL
                        , JdhPromiseStatusEnum.SERVICING, JdhPromiseStatusEnum.SERVICE_READY)
                .to(JdhPromiseStatusEnum.COMPLETE)
                .on(PromiseEventTypeEnum.PROMISE_CALLBACK_COMPLETE)
                .when(callbackCondition)
                .perform(callbackAction);

        // 所有状态数据都支持作废
        builder.externalTransitions()
                .fromAmong(Arrays.stream(JdhPromiseStatusEnum.values()).filter(ele -> ele != JdhPromiseStatusEnum.INVALID).toArray(JdhPromiseStatusEnum[]::new))
                .to(JdhPromiseStatusEnum.INVALID)
                .on(PromiseEventTypeEnum.PROMISE_INVALID)
                .when(promiseInvalidCondition)
                .perform(promiseInvalidAction);

        //==============>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>

        builder.setFailCallback(promiseStatemachineFailCallBack);

        StateMachine<JdhPromiseStatusEnum, PromiseEventTypeEnum, StateContext> stateMachine = builder.build(MACHINE_ID);
        log.info("========>>>>>>>>>> promiseStatemachine 状态机初始化完成,machineId:{},PlantUML <<<<<<<<<<========",stateMachine.getMachineId());
        log.info(stateMachine.generatePlantUML());
        log.info("========>>>>>>>>>> promiseStatemachine 状态机初始化完成 <<<<<<<<<<========");
        return stateMachine;
    }
}
