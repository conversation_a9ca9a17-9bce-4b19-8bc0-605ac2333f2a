package com.jdh.o2oservice.core.domain.promise.service.ability.promise.submit;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.jdh.o2oservice.application.angel.StationExtApplication;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.common.enums.BusinessModeEnum;
import com.jdh.o2oservice.core.domain.promise.context.PromiseSubmitAbilityContext;
import com.jdh.o2oservice.core.domain.promise.enums.PromiseErrorCode;
import com.jdh.o2oservice.core.domain.promise.model.PromiseStation;
import com.jdh.o2oservice.core.domain.support.basic.rpc.AddressRpc;
import com.jdh.o2oservice.core.domain.support.basic.rpc.JdhAddressRpc;
import com.jdh.o2oservice.core.domain.support.basic.rpc.PopLocStoreInfoRpc;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.AddressDetailBO;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.BaseAddressBo;
import com.jdh.o2oservice.core.domain.support.vertical.model.JdhVerticalBusiness;
import com.jdh.o2oservice.export.angel.dto.JdhStationDto;
import com.jdh.o2oservice.export.angel.query.AddressDetail;
import com.jdh.o2oservice.export.angel.query.StationGeoQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.jdh.o2oservice.core.domain.promise.model.PromiseService;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 门店条件LocStoreInfoRpc
 * @author: yangxiyu
 * @date: 2022/9/9 9:43 上午
 * @version: 1.0
 */
@Component
@Slf4j
public class StoreCheckAbility implements SubmitAbility {

    /** 健康档案服务 */
    @Resource
    private PopLocStoreInfoRpc popLocStoreInfoRpc;

    /**
     * jdhAddressRpc
     */
    @Autowired
    private JdhAddressRpc jdhAddressRpc;

    /**
     * stationExtApplication
     */
    @Autowired
    private StationExtApplication stationExtApplication;

    /**
     * addressRpc
     */
    @Autowired
    private AddressRpc addressRpc;

    /**
     *
     * @return
     */
    @Override
    @SuppressWarnings("JdAvoidMethodReturnEnum")
    public SubmitAbilityCode getAbilityCode() {
        return SubmitAbilityCode.STORE_CHECK;
    }

    /**
     * POP的需要调LOC接口校验获取门店
     * @param context
     */
    @Override
    public void execute(PromiseSubmitAbilityContext context) {
        JdhVerticalBusiness verticalBusiness = context.getVerticalBusiness();
        if (Objects.equals(verticalBusiness.getBusinessModeCode(), BusinessModeEnum.POP_LOC.getCode())
                || Objects.equals(verticalBusiness.getBusinessModeCode(), BusinessModeEnum.POP_FREE.getCode()) ){
            popLoc(context);
        }else if(Objects.equals(verticalBusiness.getBusinessModeCode(), BusinessModeEnum.SELF_TEST.getCode())
                || Objects.equals(verticalBusiness.getBusinessModeCode(), BusinessModeEnum.ANGEL_TEST.getCode())
                || Objects.equals(verticalBusiness.getBusinessModeCode(), BusinessModeEnum.ANGEL_TEST_NO_LABORATORY.getCode())
                || Objects.equals(verticalBusiness.getBusinessModeCode(), BusinessModeEnum.ANGEL_CARE.getCode())
                || Objects.equals(verticalBusiness.getBusinessModeCode(), BusinessModeEnum.SELF_TEST_TRANSPORT.getCode())){
            home(context);
        }else{
            throw new UnsupportedOperationException();
        }
    }

    /**
     * POP——LOC模式门店处理
     * @param context
     */
    private void popLoc(PromiseSubmitAbilityContext context){
        String storeId = context.getStation().getStoreId();
        PromiseStation store = context.getPromise().getStore();
        store.fill(()-> popLocStoreInfoRpc.queryById(Long.valueOf(storeId)));
        context.getPromise().setChannelNo(store.getChannelNo());
    }

    /**
     * 到家业务 用户地址 处理
     * @param context
     */
    private void home(PromiseSubmitAbilityContext context){
        JdhVerticalBusiness verticalBusiness = context.getVerticalBusiness();
        PromiseStation station = context.getStation();
        PromiseStation store = context.getPromise().getStore();
        if (Objects.isNull(store)){
            store = new PromiseStation();
        }

        //storeId & store地址都为空，报错
        if(StrUtil.isBlank(station.getStoreId()) && StrUtil.isBlank(station.getStoreAddr())){
            throw new BusinessException(PromiseErrorCode.PROMISE_SUBMIT_APPOINTMENT_ADDRESS_MISS_ERROR);
        }

        //如果传了storeId 并且 storeAddr没有传，补充storeAddr信息
        if(StrUtil.isNotBlank(station.getStoreId()) && StrUtil.isBlank(station.getStoreAddr())){
            List<AddressDetailBO> addressDetailBOList = jdhAddressRpc.queryAddressList(context.getUserPin());
            if(CollUtil.isEmpty(addressDetailBOList)){
                throw new BusinessException(PromiseErrorCode.PROMISE_SUBMIT_APPOINTMENT_ADDRESS_ERROR);
            }
            Optional<AddressDetailBO> first = addressDetailBOList.stream().filter(ele -> ele.getAddressId().equals(Long.parseLong(station.getStoreId()))).findFirst();
            if(first.isPresent()){
               station.setStoreAddr(first.get().getFullAddress());
            }else{
                throw new BusinessException(PromiseErrorCode.PROMISE_SUBMIT_APPOINTMENT_ADDRESS_ERROR);
            }

            // 非快检模式不校验服务站，其他到家场景需校验
            if (!Objects.equals(verticalBusiness.getBusinessModeCode(), BusinessModeEnum.SELF_TEST_TRANSPORT.getCode())) {
                //门店命中服务站校验
                Set<Long> skuIdSet = context.getServices().stream().map(PromiseService::getServiceId).collect(Collectors.toSet());
                for (Long skuNo : skuIdSet) {
                    AddressDetail addressDetail = new AddressDetail();
                    addressDetail.setAddressId(String.valueOf(station.getStoreId()));
                    addressDetail.setFullAddress(station.getStoreAddr());
                    StationGeoQuery stationGeoQuery = new StationGeoQuery();
                    stationGeoQuery.setSkuNo(skuNo);
                    stationGeoQuery.setAddressDetailList(Collections.singletonList(addressDetail));
                    List<JdhStationDto> jdhStationDtoList = stationExtApplication.queryJdhStationGeo(stationGeoQuery);
                    if(CollUtil.isEmpty(jdhStationDtoList)){
                        throw new BusinessException(PromiseErrorCode.PROMISE_SUBMIT_STATION_NOT_FOUND_ERROR);
                    }
                }
            }
        }
        store.setStoreAddr(station.getStoreAddr());
        store.setStoreId(station.getStoreId());


        // 补充jd 地址信息
        fillJdAddressInfo(store);

        log.info("StoreCheckAbility home store:{}", JSON.toJSONString(store));

    }

    /**
     * 补充jd 地址信息
     *
     * @param store 店
     */
    private void fillJdAddressInfo(PromiseStation store){
        try {
            BaseAddressBo jdAddressFromAddress = addressRpc.getJDAddressFromAddress(store.getStoreAddr());
            store.setProvinceCode(jdAddressFromAddress.getProvinceCode());
            store.setProvinceName(jdAddressFromAddress.getProvinceName());
            store.setCityCode(jdAddressFromAddress.getCityCode());
            store.setCityName(jdAddressFromAddress.getCityName());
            store.setDistrictCode(jdAddressFromAddress.getDistrictCode());
            store.setDistrictName(jdAddressFromAddress.getDistrictName());
            store.setTownCode(jdAddressFromAddress.getTownCode());
            store.setTownName(jdAddressFromAddress.getTownName());
        } catch (Exception e) {
            log.error("StoreCheckAbility -> fillJdAddressInfo", e);
        }
    }

}
