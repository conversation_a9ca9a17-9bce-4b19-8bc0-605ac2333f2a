package com.jdh.o2oservice.core.domain.promise.service.ability.promise.submit;

import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.common.enums.BusinessModeEnum;
import com.jdh.o2oservice.core.domain.promise.context.PromiseSubmitAbilityContext;
import com.jdh.o2oservice.core.domain.promise.enums.PromiseErrorCode;
import com.jdh.o2oservice.core.domain.promise.model.JdhVoucher;
import com.jdh.o2oservice.core.domain.support.basic.enums.JdhVoucherStatusEnum;
import com.jdh.o2oservice.core.domain.support.vertical.model.JdhVerticalBusiness;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 预约时间校验 能力
 *
 * <AUTHOR>
 * @date 2024/08/30
 */
@Slf4j
@Component
public class VoucherCheckAbility implements SubmitAbility{


    /**
     * 当前能力点匹配的能力编码
     *
     * @return
     */
    @Override
    public SubmitAbilityCode getAbilityCode() {
        return SubmitAbilityCode.APPOINTMENT_VOUCHER_CHECK;
    }

    /**
     * 处理
     *
     * @param context
     */
    @Override
    public void execute(PromiseSubmitAbilityContext context) {
        JdhVerticalBusiness verticalBusiness = context.getVerticalBusiness();
        //到家
        if(BusinessModeEnum.ANGEL_CARE.getCode().equals(verticalBusiness.getBusinessModeCode())
                || BusinessModeEnum.ANGEL_TEST.getCode().equals(verticalBusiness.getBusinessModeCode())
                || BusinessModeEnum.ANGEL_TEST_NO_LABORATORY.getCode().equals(verticalBusiness.getBusinessModeCode())
                || BusinessModeEnum.SELF_TEST.getCode().equals(verticalBusiness.getBusinessModeCode())){
            homeCheck(context);
        }
    }

    /**
     * homeCheck
     *
     * @param context 上下文
     */
    private void homeCheck(PromiseSubmitAbilityContext context){
        JdhVoucher jdhVoucher = context.getJdhVoucher();
        if(JdhVoucherStatusEnum.EXPIRED.getStatus().equals(jdhVoucher.getStatus())){
            throw new BusinessException(PromiseErrorCode.PROMISE_VOUCHER_EXPIRE_ERROR);
        }
        if(JdhVoucherStatusEnum.INVALID.getStatus().equals(jdhVoucher.getStatus())){
            throw new BusinessException(PromiseErrorCode.PROMISE_VOUCHER_INVALID_ERROR);
        }
    }
}
