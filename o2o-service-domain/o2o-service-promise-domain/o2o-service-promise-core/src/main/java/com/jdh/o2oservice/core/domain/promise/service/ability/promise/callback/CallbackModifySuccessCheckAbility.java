package com.jdh.o2oservice.core.domain.promise.service.ability.promise.callback;

import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.util.AssertUtils;
import com.jdh.o2oservice.common.enums.AppointCallBackEnum;
import com.jdh.o2oservice.common.enums.BusinessModeEnum;
import com.jdh.o2oservice.core.domain.promise.context.PromiseCallbackAbilityContext;
import com.jdh.o2oservice.core.domain.promise.enums.PromiseErrorCode;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 预约成功校验
 * @author: yang<PERSON>yu
 * @date: 2023/12/26 5:30 下午
 * @version: 1.0
 */
@Component
public class CallbackModifySuccessCheckAbility implements PromiseCallbackAbility {


    @Override
    public CallbackAbilityCode getAbilityCode() {
        return CallbackAbilityCode.MODIFY_SUCCESS_CHECK;
    }

    @Override
    public void execute(PromiseCallbackAbilityContext context) {
        if(!StringUtils.equals(context.getCallbackCode(), AppointCallBackEnum.MODIFY_SUCC.getCode())){
            throw new BusinessException(PromiseErrorCode.PROMISE_STATUS_NO_SUPPORT);
        }
        // 无校验逻辑，满足状态机的from + event 即可进行流转至to的状态
        String businessModeCode = context.getVerticalBusiness().getBusinessModeCode();

        // 到家场景不校验
        if(BusinessModeEnum.SELF_TEST.getCode().equals(businessModeCode)
                || BusinessModeEnum.ANGEL_TEST.getCode().equals(businessModeCode)
                || BusinessModeEnum.ANGEL_CARE.getCode().equals(businessModeCode)
                || BusinessModeEnum.SELF_TEST_TRANSPORT.getCode().equals(businessModeCode)
                || BusinessModeEnum.ANGEL_TEST_NO_LABORATORY.getCode().equals(businessModeCode)){
            return;
        }

        Long channelNo = context.getPromise().getChannelNo();
        AssertUtils.nonEqualsObject(channelNo, context.getChannelNo(), PromiseErrorCode.PROMISE_CHANNEL_NO_ILLEGAL);
        AssertUtils.nonNull(context.getChannelAppointmentId(), PromiseErrorCode.PROMISE_CHANNEL_APPOINTMENT_NO_ILLEGAL);
        // 校验商家预约单号
        if (!Objects.equals(context.getChannelAppointmentId(), context.getPromise().getChannelAppointmentId())){
            throw new BusinessException(PromiseErrorCode.PROMISE_CHANNEL_APPOINTMENT_ID_ILLEGAL);

        }
    }
}
