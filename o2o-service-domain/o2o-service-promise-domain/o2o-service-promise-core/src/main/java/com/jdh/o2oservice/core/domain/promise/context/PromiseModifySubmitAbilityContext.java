package com.jdh.o2oservice.core.domain.promise.context;

import com.jdh.o2oservice.core.domain.promise.enums.PromiseEventTypeEnum;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromise;
import lombok.Data;

/**
 * ModifyPromiseContext
 *
 * <AUTHOR>
 * @date 2023/12/25
 */
@Data
public class PromiseModifySubmitAbilityContext extends AbstractPromiseStateAbilityContext {

    /**
     * 当前履约单筷子，在处理上下文的过程中不要进行调整
     */
    private JdhPromise snapshot;

    /**
     * 履约单
     */
    private JdhPromise promise;

    /**
     * modifyTime
     */
    private PromiseTime appointmentTime;
    /**
     * sourceEvent
     */
    private PromiseEventTypeEnum sourceEvent;

    /**
     * 预约地址
     */
    private String storeId;//预约地址

    /**
     * 修改原因
     */
    private String reason;

    public void init(JdhPromise snapshot, PromiseEventTypeEnum sourceEvent) {
        this.promise = snapshot.copyInstance();
        this.snapshot = snapshot;
        this.triggerCommand = sourceEvent;
        this.sourceStatus = snapshot.getPromiseStatus();
        super.init();
    }
}
