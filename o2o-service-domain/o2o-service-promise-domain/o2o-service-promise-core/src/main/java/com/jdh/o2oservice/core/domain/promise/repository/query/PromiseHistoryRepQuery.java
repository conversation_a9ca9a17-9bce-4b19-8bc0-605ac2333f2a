package com.jdh.o2oservice.core.domain.promise.repository.query;

import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * PromiseHistoryRepQuery
 */
@Data
@Builder
public class PromiseHistoryRepQuery {

    /**
     * promiseId
     */
    private Long promiseId;

    /**
     * promiseIdList
     */
    private List<Long> promiseIdList;

    /**
     * 操作之后状态
     */
    private Integer afterStatus;

    /**
     * 事件编号
     */
    private String eventCode;

    /**
     * 创建时间排序
     */
    private Boolean createTimeOrderByDesc;
}
