package com.jdh.o2oservice.core.domain.angel.model;

import com.alibaba.fastjson.JSON;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.ducc.model.AngelStationInventoryConfig;
import com.jdh.o2oservice.base.ducc.model.InventoryInitConfig;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.groovy.core.GroovyModuleExecutor;
import com.jdh.o2oservice.base.groovy.core.GroovyScript;
import com.jdh.o2oservice.base.util.AssertUtils;
import com.jdh.o2oservice.base.util.SpringUtil;
import com.jdh.o2oservice.base.util.TimeIntervalIntersection;
import com.jdh.o2oservice.base.util.TimeUtils;
import com.jdh.o2oservice.common.enums.AngelTypeEnum;
import com.jdh.o2oservice.base.model.Aggregate;
import com.jdh.o2oservice.base.model.AggregateCode;
import com.jdh.o2oservice.base.model.DomainCode;
import com.jdh.o2oservice.base.model.DomainEnum;
import com.jdh.o2oservice.core.domain.angel.context.AngelStationInventoryFlagContext;
import com.jdh.o2oservice.core.domain.angel.context.AngelStationSaveContext;
import com.jdh.o2oservice.core.domain.angel.context.JdhInventoryReadjustContext;
import com.jdh.o2oservice.core.domain.angel.enums.*;
import com.jdh.o2oservice.core.domain.angel.vo.AngelJobTimeIntervalVo;
import com.jdh.o2oservice.core.domain.angel.vo.FenceBoundaryVo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.geotools.referencing.GeodeticCalculator;
import org.locationtech.jts.geom.*;

import java.text.MessageFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @ClassName:JdhStation
 * @Description:
 * @Author: yaoqinghai
 * @Date: 2024/4/22 19:01
 * @Vserion: 1.0
 **/
@Data
@Builder
@Slf4j
@AllArgsConstructor
@NoArgsConstructor
public class JdhStation implements Aggregate<JdhStationIdentifier> {

    /**
     * 主键
     */
    private Long id;

    /**
     * 服务站id
     */
    private Long angelStationId;

    /**
     * 围栏id
     */
    private String fenceId;

    /**
     * 服务站名称
     */
    private String angelStationName;

    /**
     * 地图id
     */
    private Long mapId;

    /**
     * 图层id
     */
    private Long layerId;

    /**
     * 业务模式 1护士-全职 2护士-兼职
     */
    private Integer stationModeType;

    /**
     * 范围的衡量类型（1：公里数，2：分钟）
     */
    private Integer fenceRangeType;

    /**
     * 范围的衡量值（如xx公里或xx分钟）
     */
    private Integer fenceRangeRadius;

    /**
     * 圆中心点纬度
     */
    private String fenceRangeCenterLat;

    /**
     * 圆中心点经度
     */
    private String fenceRangeCenterLng;

    /**
     * 围栏信息: -围栏地理数据:围栏边界点
     */
    private String fenceBoundaryList;

    /**
     * 站长erp
     */
    private String stationMaster;

    /**
     * 省编码
     */
    private String provinceCode;

    /**
     * 省名称
     */
    private String provinceName;

    /**
     * 市编码
     */
    private String cityCode;

    /**
     * 市名称
     */
    private String cityName;

    /**
     * 区编码
     */
    private String districtCode;

    /**
     * 区名称
     */
    private String districtName;

    /**
     * 县编码
     */
    private String countyCode;

    /**
     * 县名称
     */
    private String countyName;

    /**
     * 详细地址
     */
    private String addressDetail;

    /**
     * 全地址
     */
    private String fullAddress;

    /**
     * 站点状态：1启用 2停用
     */
    private Integer stationStatus;

    /**
     * 开始营业时间(24小时)
     */
    private String openHour;

    /**
     * 停止营业时间(24小时)
     */
    private String closeHour;

    /**
     * 是否有效 0-无效 1-有效
     */
    private Integer yn;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人
     */
    private String updateUser;

    /**
     * 修改时间
     */
    private Date updateTime;


    /**
     * 服务资源类型 二进制编码 右向左 1位骑手 2位护士
     */
    private Integer angelType;

    /**
     * 骑手供应商 服务资源类型 二进制编码 右向左 1位达达 2位顺丰
     */
    private Integer deliverySupplier;

    /**
     * 实验室id
     */
    private String stationId;

    /**
     * 实验室名称
     */
    private String stationName;

    /**
     * 服务资源数量
     */
    private Integer angelNum;

    /**
     * 护士资源数量
     */
    private Integer nurseNum;

    /**
     * 三方店铺id
     */
    private String outShopId;

    /**
     * 围栏形状
     * @see com.jdh.o2oservice.core.domain.angel.enums.FenceShapeTypeEnum
     */
    private Integer fenceShapeType;

    /**
     * 服务站工作时间段列表
     */
    private List<AngelJobTimeIntervalVo> jobTimeIntervalVoList;

    /**
     * 服务站库存异常调整量
     */
    private List<JdhInventoryReadjustRecord> jdhInventoryReadjustRecordList;

    /**
     * 变更bite位
     */
    private int changeBitMap= CommonConstant.ZERO;

    /**
     * 中心点是否变更 0否 1是
     */
    public static final Integer CENTER_INDEX = 1;

    /**
     * 名称是否变更 0否  1是
     */
    public static final Integer NAME_INDEX = 1 << 1;

    /**
     * 圆心是否变更 0否  1是
     */
    public static final Integer RADIUS_INDEX = 1 << 2;

    /**
     * 状态是否变更 0否  1是
     */
    public static final Integer STATUS_INDEX = 1 << 3;

    /**
     * 数量是否变更 0否  1是
     */
    public static final Integer ANGEL_NUM_INDEX = 1 << 4;

    /**
     * 异常库存调整是否有变化 0否  1是
     */
    public static final Integer INVENTORY_READJUST_NUM_INDEX = 1 << 5;

    /**
     * 是否是全职 0兼职 1全职
     */
    public static final Integer FULL_TIME = 1 << 8;

    /**
     * 执行结果是否成功 0失败  1成功
     */
    public static final Integer EXECUTE_RESULT = 1 << 9;

    /**
     * 变更类型是否修改 0新增  1修改
     */
    public static final Integer CHANGE_TYPE = 1 << 10;

    /**
     * 护士数量是否变更 0否  1是
     */
    public static final Integer NURSE_NUM_INDEX = 1 << 11;

    /**
     * 护士异常库存调整是否有变化 0否  1是
     */
    public static final Integer NURSE_INVENTORY_READJUST_NUM_INDEX = 1 << 12;

    /**
     * 接驳点id
     */
    private Long jdTransferStationId;

    /**
     * 版本
     *
     * @return {@link Integer}
     */
    @Override
    public Integer version() {
        return version;
    }

    /**
     * 版本增加
     */
    @Override
    public void versionIncrease() {
        this.version++;
    }

    /**
     * 获取标识符
     *
     */
    @Override
    public JdhStationIdentifier getIdentifier() {
        return new JdhStationIdentifier(this.angelStationId);
    }

    /**
     * 生成围栏边界坐标点
     *
     */
    public void generateFenceBoundary(Double centerLat, Double centerLng, Double radius, Integer numberOfPoints) {
        // 使用GeodeticCalculator计算多边形顶点
        Coordinate[] coords = new Coordinate[numberOfPoints + 1];
        GeodeticCalculator gc = new GeodeticCalculator();

        for (int i = 0; i < numberOfPoints; i++) {
            double angle = (360.0 / numberOfPoints) * i;
            gc.setStartingGeographicPoint(centerLng, centerLat);
            gc.setDirection(angle, radius);
            java.awt.geom.Point2D destPoint = gc.getDestinationGeographicPoint();

            coords[i] = new Coordinate(destPoint.getX(), destPoint.getY());
        }
        // 确保多边形是闭合的
        coords[numberOfPoints] = coords[0];

        // 创建多边形
        GeometryFactory geometryFactory = new GeometryFactory();
        LinearRing ring = geometryFactory.createLinearRing(coords);
        Polygon bufferPolygon = geometryFactory.createPolygon(ring, null);

        Coordinate[] coordinates = bufferPolygon.getExteriorRing().getCoordinates();
        FenceBoundaryVo fenceBoundaryVo = new FenceBoundaryVo();

        List<List<Double[]>> fenceLats = Lists.newArrayList();
        List<Double[]> coordinateList = Lists.newArrayList();
        fenceLats.add(coordinateList);
        fenceBoundaryVo.setCoordinates(fenceLats);
        fenceBoundaryVo.setType(bufferPolygon.getGeometryType());
        for (Coordinate coordinate : coordinates) {
            log.info("经度: " + coordinate.x + ", 纬度: " + coordinate.y);
            Double[] latLngArr = new Double[2];
            latLngArr[0] = coordinate.x;
            latLngArr[1] = coordinate.y;
            coordinateList.add(latLngArr);
        }
        this.fenceBoundaryList = JSON.toJSONString(fenceBoundaryVo);
        // 输出多边形的WKT表示
        log.info("[JdhStation.generateFenceBoundary],fenceBoundaryList={}", bufferPolygon.toText());
        log.info("[JdhStation.generateFenceBoundary],coordinateList={}", JSON.toJSONString(fenceBoundaryVo));
    }


    /**
     * 赋值修改的信息
     *
     * @param angelStationSaveContext
     */
    public void diff(AngelStationSaveContext angelStationSaveContext) {
        this.fenceRangeType = FenceRangeTypeEnum.RANGE_TYPE_KILOMETRE.getType();
        this.updateUser = angelStationSaveContext.getOperator();
        this.mapId = angelStationSaveContext.getMapId();
        this.layerId = angelStationSaveContext.getLayerId();
        this.openHour = angelStationSaveContext.getOpenHour();
        this.closeHour = angelStationSaveContext.getCloseHour();
        this.stationModeType = angelStationSaveContext.getStationModeType();
        this.fullAddress = angelStationSaveContext.getFullAddress();
        this.angelType = toIntFromIndex(angelStationSaveContext.getAngelTypes());
        this.deliverySupplier = toIntFromIndex(angelStationSaveContext.getDeliverySuppliers());
        this.stationId = angelStationSaveContext.getStationId();
        this.fenceShapeType = angelStationSaveContext.getFenceShapeType();
        this.stationName = angelStationSaveContext.getStationName();

        if(AngelStationModeTypeEnum.FULL_TIME.getCode().equals(angelStationSaveContext.getStationModeType())){
            fillChangeBitMap(FULL_TIME);
        }

        if(angelStationSaveContext.getFenceRangeRadius()!=null){
            //检查地址信息是否变更
            boolean isChange = !angelStationSaveContext.getFenceRangeRadius().equals(fenceRangeRadius);
            if(isChange) {
                this.fenceRangeRadius = angelStationSaveContext.getFenceRangeRadius();
                fillChangeBitMap(RADIUS_INDEX);
            }
        }

        //检查是否变化了地理数据
        boolean centerNoChange = this.fenceRangeCenterLng.equals(angelStationSaveContext.getFenceRangeCenterLng())
                && this.fenceRangeCenterLat.equals(angelStationSaveContext.getFenceRangeCenterLat());
        if(!centerNoChange){
            this.fenceRangeCenterLat = angelStationSaveContext.getFenceRangeCenterLat();
            this.fenceRangeCenterLng = angelStationSaveContext.getFenceRangeCenterLng();
            fillChangeBitMap(CENTER_INDEX);
        }

        //检查围栏名称是否变更
        boolean isNameChange = !this.angelStationName.equals(angelStationSaveContext.getAngelStationName());
        if(isNameChange){
            this.angelStationName = angelStationSaveContext.getAngelStationName();
            fillChangeBitMap(NAME_INDEX);
        }

        //骑手数量是否变更
        if(Objects.isNull(angelStationSaveContext.getAngelNum())) {
            angelStationSaveContext.setAngelNum(0);
        }
        if(Objects.isNull(angelNum)) {
            angelNum = 0;
        }
        if(angelNum != angelStationSaveContext.getAngelNum()) {
            this.angelNum = angelStationSaveContext.getAngelNum();
            fillChangeBitMap(ANGEL_NUM_INDEX);
        }

        //护士数量是否变更
        if(Objects.isNull(angelStationSaveContext.getNurseNum())) {
            angelStationSaveContext.setNurseNum(0);
        }
        if(Objects.isNull(nurseNum)) {
            nurseNum = 0;
        }
        if(nurseNum != angelStationSaveContext.getNurseNum()) {
            this.nurseNum = angelStationSaveContext.getNurseNum();
            fillChangeBitMap(NURSE_NUM_INDEX);
        }
    }

    /**
     * 从给定的数组 [1, 2] 中解读出它所表示的数字的二进制形式，我们需要根据数组中每个元素所指示的二进制位（从右往左数，最右边是位置1）来构建二进制数。
     * @param lists
     * @return
     */
    public Integer toIntFromIndex(List<Integer> lists){
        if(CollectionUtils.isEmpty(lists)){
            return null;
        }
        AtomicReference<Integer> result = new AtomicReference<>(0);
        lists.forEach(t->{
            int i = 1 << (t-1);
            result.set(result.get() + i);
        });
        return result.get();
    }

    /**
     * 将数字转成[1,2]
     * @param type
     * @return
     */
    public List<Integer> toIndexFromInt(Integer type){
        if(type==null){
            return null;
        }
        List<Integer> lists = new ArrayList<>();
        String b = Integer.toBinaryString(type);
        byte[] bytes = b.getBytes();
        for (int i = bytes.length; i>0; i--) {
            if(bytes[i-1]!=48){
                lists.add(bytes.length-i+1);
            }
        }
        return lists;
    }



    /**
     * 检查是否变化了地理数据
     *
     * @return boolean
     */
    public boolean checkElementCenterChange(String fenceRangeCenterLat, String fenceRangeCenterLng) {
        boolean isChange = this.fenceRangeCenterLng.equals(fenceRangeCenterLng)
                && this.fenceRangeRadius.equals(fenceRangeRadius);
        if(!isChange){
            this.fenceRangeCenterLat = fenceRangeCenterLat;
            this.fenceRangeCenterLng = fenceRangeCenterLng;

            fillChangeBitMap(CENTER_INDEX);
        }
        return !isChange;
    }

    /**
     * 检查围栏名称是否变更
     *
     * @param angelStationName
     * @return
     */
    public boolean diffName(String angelStationName) {
        boolean isChange = !this.angelStationName.equals(angelStationName);
        if(isChange){
            this.angelStationName = angelStationName;

            fillChangeBitMap(NAME_INDEX);
        }
        return isChange;
    }

    /**
     * 检查开始截止时间
     */
    public boolean checkHour() {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm");
        if(StringUtils.isNotBlank(this.openHour) && StringUtils.isNotBlank(this.closeHour)){
            LocalTime open = LocalTime.parse(this.openHour, formatter);
            LocalTime close = LocalTime.parse(this.closeHour, formatter);
            //2025-02-19 取消营业开始时间必须小于结束时间校验。兼容跨夜营业的场景（夜间服务费），22:00-07:00
            /*if(open.isAfter(close)){
                throw new BusinessException(AngelErrorCode.BUSINESS_HOUR_ERROR);
            }*/
            if (open.getMinute() != 0 || (close.getMinute() != 0 && close.getMinute() != 59)) {
                throw new BusinessException(AngelErrorCode.BUSINESS_HOUR_ERROR);
            }
        }
        return true;
    }

    /**
     * 聚合所属领域编码
     *
     * @return
     */
    @Override
    public DomainCode getDomainCode() {
        return DomainEnum.ANGEL;
    }

    /**
     * 获取聚合编码
     *
     * @return {@link AggregateCode}
     */
    @Override
    public AggregateCode getAggregateCode() {
        return AngelAggregateEnum.ANGEL_STATION;
    }

    /**
     * 检查地址信息是否变更
     *
     * @return boolean
     */
    public boolean checkElementRadiusChange(Integer fenceRangeRadius) {
        boolean isChange = !fenceRangeRadius.equals(this.fenceRangeRadius);
        if(isChange) {
            this.fenceRangeRadius = fenceRangeRadius;
            fillChangeBitMap(RADIUS_INDEX);
        }
        return isChange;
    }

    /**
     * 判断点是否在围栏地理范围内
     *
     * @param lat
     * @param lng
     */
    public boolean getGeoCoordinate(Double lat, Double lng) {
        GeometryFactory geometryFactory = new GeometryFactory();
        String fenceBoundarys = this.fenceBoundaryList;
        if(StringUtils.isBlank(fenceBoundarys)){
            log.error("[JdhStation.getGeoCoordinate],无地理位置");
            return false;
        }

        FenceBoundaryVo fenceBoundaryVo = JSON.parseObject(fenceBoundarys, FenceBoundaryVo.class);
        if(Objects.isNull(fenceBoundaryVo)){
            log.error("[JdhStation.getGeoCoordinate],地理位置集合为空");
            return false;
        }
        List<List<Double[]>> coordinates = fenceBoundaryVo.getCoordinates();
        List<Double[]> doubles = coordinates.get(0);
        Coordinate[] coordinateArr = doubles.stream().filter(coor -> ArrayUtils.isNotEmpty(coor) && coor.length == 2)
                .map(coor -> new Coordinate(coor[0], coor[1]))
                .collect(Collectors.toList()).toArray(new Coordinate[0]);

        LinearRing ring = geometryFactory.createLinearRing(coordinateArr);
        Polygon polygon = geometryFactory.createPolygon(ring, null);

        Point point = geometryFactory.createPoint(new Coordinate(lng, lat));

        boolean isWithin = point.within(polygon);
        log.info("[JdhStation.getGeoCoordinate],地点维度{},经度{},是否在该服务站围栏内={}，stationId={}", lat, lng, isWithin, angelStationId);
        return isWithin;
    }

    /**
     * 新增/编辑服务站-参数校验
     * @return
     */
    public Boolean checkParams(){
        if(AngelStationModeTypeEnum.PART_TIME.getCode().equals(this.getStationModeType())){
            return true;
        }
        if(this.getAngelType()==null){
            throw new BusinessException(AngelErrorCode.INVENTORY_ANGEL_TYPE_EMPTY_ERROR);
        }
        if((this.getAngelType()&AngelTypeEnum.DELIVERY.getType())==AngelTypeEnum.DELIVERY.getType()){
            AssertUtils.hasText(this.getStationId(),"实验室id不能为空");
            AssertUtils.hasText(this.getStationName(),"实验室名称不能为空");
            AssertUtils.nonNull(this.getDeliverySupplier(),"骑手供应商不能为空");
            AssertUtils.nonNull(this.getAngelNum(),"服务资源数量不能为空");
            AssertUtils.nonNull(this.getJdTransferStationId(),"接驳点id不能为空");
        }
        return true;
    }

    /**
     * bit 补位
     * @param bit
     */
    public void fillChangeBitMap(Integer bit) {
        if(Objects.isNull(bit)){
            return;
        }
        this.changeBitMap = changeBitMap | bit;
    }

    /**
     * bit 与位检查
     * @param bits
     */
    public boolean checkChangeAndBitMap(Integer... bits) {
        if(ArrayUtils.isEmpty(bits)){
            return false;
        }

        Integer bitTotal = 0;
        for (Integer bit : bits) {
            bitTotal = bitTotal | bit;
        }
        return (this.changeBitMap & bitTotal) == bitTotal;
    }

    /**
     * bit 或位检查
     * @param bits
     */
    public boolean checkChangeOrBitMap(Integer... bits) {
        if(ArrayUtils.isEmpty(bits)){
            return false;
        }
        Integer bitTotal = 0;
        for (Integer bit : bits) {
            bitTotal = bitTotal | bit;
        }

        return (this.changeBitMap & bitTotal) > 0;
    }

    /**
     * 切割服务者履约时效
     *
     * @param inventoryInitConfig
     */
    public List<AngelJobTimeIntervalVo> splitScheduleSlot(InventoryInitConfig inventoryInitConfig, LocalDate inventoryDate, Integer initDays) {
        LocalDate nowDate = Objects.isNull(inventoryDate) ? TimeUtils.getCurrentLocalDate() : inventoryDate;
        LocalDateTime startDateTime;
        LocalDateTime endDateTime;
        if(inventoryInitConfig.isUseAngelStationTime()){
            if(StringUtils.isNotBlank(this.openHour)){
                LocalTime startLocalTime = LocalTime.parse(this.openHour);
                startDateTime = nowDate.atTime(startLocalTime);
            }else {
                startDateTime = nowDate.atTime(0, 0, 0);
            }
            if(StringUtils.isNotBlank(this.closeHour)){
                LocalTime endLocalTime = LocalTime.parse(this.closeHour);
                endDateTime = nowDate.atTime(endLocalTime);
            }else {
                endDateTime = nowDate.atTime(23, 59, 59);
            }
        }else {
            startDateTime = nowDate.atTime(0, 0, 0);
            endDateTime = nowDate.atTime(23, 59, 59);
        }

        List<AngelJobTimeIntervalVo> angelJobTimeIntervalVoList = Lists.newArrayList();
        do {
            log.info("[JdhStation -> splitScheduleSlot],initDays={}", initDays);
            List<TimeIntervalIntersection.TimeInterval> timeIntervals = TimeIntervalIntersection.splitTimeIntervals(
                    new TimeIntervalIntersection.TimeInterval(
                            LocalTime.of(startDateTime.getHour(), startDateTime.getMinute()),
                            LocalTime.of(endDateTime.getHour(), endDateTime.getMinute()),
                            inventoryInitConfig.getIntervalMinutes()));

            LocalDateTime finalStartDateTime = startDateTime;
            Optional.ofNullable(timeIntervals).map(List::stream).orElseGet(Stream::empty).forEach(item -> {
                String begin = item.getStart().format(DateTimeFormatter.ofPattern("HH:mm"));
                String end = item.getEnd().format(DateTimeFormatter.ofPattern("HH:mm"));
                AngelJobTimeIntervalVo angelJobTimeIntervalVo = new AngelJobTimeIntervalVo();
                angelJobTimeIntervalVo.setScheduleTime(MessageFormat.format("{0}-{1}", begin, end));
                angelJobTimeIntervalVo.setScheduleDay(finalStartDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                angelJobTimeIntervalVo.setBaseNum(this.angelNum);
                angelJobTimeIntervalVo.setNurseBaseNum(this.nurseNum);
                angelJobTimeIntervalVo.setAngelStationId(this.angelStationId);
                angelJobTimeIntervalVoList.add(angelJobTimeIntervalVo);
            });
//            log.info("[JdhStation -> splitScheduleSlot],angelJobTimeIntervalVoList={}", JSON.toJSONString(angelJobTimeIntervalVoList));
            startDateTime = startDateTime.plusDays(CommonConstant.ONE);
            endDateTime = endDateTime.plusDays(CommonConstant.ONE);
        }while (--initDays > 0);

        this.jobTimeIntervalVoList = angelJobTimeIntervalVoList;
        return jobTimeIntervalVoList;
    }

    public static void main(String[] args) {
/*        GeometryFactory geometryFactory = new GeometryFactory();
        String fenceBoundarys = "{\"coordinates\":[[[116.558047,39.83013249363094],[116.56171444783728,39.830043574709144],[116.56536739367182,39.82977716954419],[116.56899139318074,39.82933433153905],[116.57257211716596,39.828716811709896],[116.57609540853089,39.82792705172286],[116.57954733855703,39.826968174185076],[116.58291426225202,39.8258439702297],[116.5861828725446,39.8245588844451],[116.58934025310741,39.82311799720968],[116.59237392959409,39.82152700450375],[116.59527191908427,39.81979219528059],[116.5980227775383,39.81792042648823],[116.6006156450715,39.81591909584307],[116.60304028886752,39.81379611246528],[116.60528714356106,39.811559865494914],[116.60734734893016,39.80921919081485],[116.60921278475048,39.8067833360148],[116.61087610267573,39.80426192373732],[116.6123307550213,39.80166491355275],[116.61357102034076,39.799002562516115],[116.61459202569854,39.79628538456363],[116.61538976555593,39.793524108911186],[116.61596111720107,39.790729637620466],[116.61630385266837,39.78791300250184],[116.61641664710648,39.785085321525],[116.6162990835685,39.78225775491031],[116.61595165421272,39.77944146107456],[116.61537575791579,39.77664755260521],[116.61457369431498,39.77388705243663],[116.61354865431011,39.771170850400694],[116.6123047070686,39.76850966032254],[116.61084678359211,39.76591397782944],[116.60918065691475,39.76339403903798],[116.60731291901689,39.760959780280736],[116.60525095455003,39.75862079902978],[116.60300291148067,39.756386316168474],[116.60057766877233,39.75426513975866],[116.59798480123565,39.752265630443404],[116.59523454168716,39.75039566861947],[116.59233774056668,39.74866262350625],[116.58930582317267,39.74707332423067],[116.58615074468365,39.74563403303958],[116.58288494314102,39.74435042074272],[116.57952129057654,39.74322754448061],[116.57607304247374,39.74226982790311],[116.57255378575887,39.741481043834256],[116.56897738552152,39.74086429949037],[116.56535793067006,39.740422024307655],[116.56170967873048,39.740155960426236],[116.558047,39.740067155866804],[116.55438432126952,39.740155960426236],[116.55073606932994,39.74042202430766],[116.54711661447848,39.740864299490376],[116.54354021424113,39.741481043834256],[116.54002095752627,39.74226982790311],[116.53657270942347,39.74322754448061],[116.53320905685898,39.74435042074271],[116.52994325531635,39.74563403303958],[116.52678817682734,39.74707332423067],[116.52375625943333,39.74866262350625],[116.52085945831284,39.75039566861947],[116.51810919876435,39.75226563044341],[116.51551633122767,39.75426513975865],[116.51309108851933,39.756386316168474],[116.51084304544997,39.75862079902978],[116.50878108098311,39.760959780280736],[116.50691334308526,39.763394039037976],[116.5052472164079,39.765913977829456],[116.50378929293139,39.76850966032254],[116.5025453456899,39.771170850400694],[116.50152030568502,39.77388705243663],[116.50071824208422,39.77664755260521],[116.50014234578728,39.77944146107456],[116.4997949164315,39.78225775491032],[116.49967735289353,39.785085321525],[116.49979014733164,39.78791300250184],[116.50013288279894,39.790729637620466],[116.50070423444407,39.793524108911186],[116.50150197430145,39.79628538456364],[116.50252297965925,39.799002562516115],[116.5037632449787,39.801664913552756],[116.50521789732427,39.80426192373732],[116.5068812152495,39.8067833360148],[116.50874665106984,39.80921919081485],[116.51080685643895,39.811559865494914],[116.5130537111325,39.81379611246528],[116.5154783549285,39.81591909584307],[116.5180712224617,39.81792042648824],[116.52082208091574,39.81979219528059],[116.52372007040591,39.82152700450375],[116.5267537468926,39.82311799720968],[116.5299111274554,39.8245588844451],[116.53317973774799,39.8258439702297],[116.53654666144297,39.826968174185076],[116.53999859146911,39.82792705172286],[116.54352188283404,39.828716811709896],[116.54710260681927,39.82933433153906],[116.55072660632818,39.82977716954419],[116.55437955216273,39.830043574709144],[116.558047,39.83013249363094]]],\"type\":\"Polygon\"}\t";
        FenceBoundaryVo fenceBoundaryVo = JSON.parseObject(fenceBoundarys, FenceBoundaryVo.class);
        if(Objects.isNull(fenceBoundaryVo)){
            log.error("[JdhStation.getGeoCoordinate],地理位置集合为空");
            return;
        }
        List<List<Double[]>> coordinates = fenceBoundaryVo.getCoordinates();
        List<Double[]> doubles = coordinates.get(0);
        Coordinate[] coordinateArr = doubles.stream().filter(coor -> ArrayUtils.isNotEmpty(coor) && coor.length == 2)
                .map(coor -> new Coordinate(coor[0], coor[1]))
                .collect(Collectors.toList()).toArray(new Coordinate[0]);

        LinearRing ring = geometryFactory.createLinearRing(coordinateArr);
        Polygon polygon = geometryFactory.createPolygon(ring, null);

        Point point = geometryFactory.createPoint(new Coordinate(116.231000561, 40.220698899));
        boolean isWithin = point.within(polygon);
        System.out.println(isWithin);*/

//        List<Integer> lists = new ArrayList<>();
//        String b = Integer.toBinaryString(11);
//        byte[] bytes = b.getBytes();
//        for (int i = bytes.length; i>0; i--) {
//            if(bytes[i-1]!=48){
//                lists.add(bytes.length-i+1);
//            }
//        }
        Integer CHANGE_TYPE = 2 << 10;
        System.out.println(CHANGE_TYPE);
    }

    /**
     * 异常库存检查和修改项处理
     *
     * @param angelStationSaveContext
     */
    public void diffReadjust(AngelStationSaveContext angelStationSaveContext) {
        //检查异常库存时间是否正确
        List<JdhInventoryReadjustContext> jdhInventoryReadjustContextList = angelStationSaveContext.getJdhInventoryReadjustContextList();
        if(CollectionUtils.isEmpty(jdhInventoryReadjustContextList)) {
            log.info("[JdhStation -> diffReadjust],更新后没有库存调整项!");
            jdhInventoryReadjustContextList = Lists.newArrayList();
        }
        if(CollectionUtils.isEmpty(jdhInventoryReadjustRecordList)){
            log.info("[JdhStation -> diffReadjust],更新前没有库存调整项!");
            jdhInventoryReadjustRecordList = Lists.newArrayList();
        }

        List<JdhInventoryReadjustRecord> riderReadjustInventory =
                jdhInventoryReadjustRecordList.stream().filter(item -> InventoryChannelEnum.RIDER_CHANNEL.getInventoryChannel().equals(item.getInventoryChannelNo())).collect(Collectors.toList());

        //只有新增没有修改的情况
        if(CollectionUtils.isNotEmpty(jdhInventoryReadjustContextList) && CollectionUtils.isEmpty(riderReadjustInventory)) {
            fillChangeBitMap(INVENTORY_READJUST_NUM_INDEX);
            return;
        }

        List<Long> timeAndNumModifyReadjustIdList = Lists.newArrayList();
        List<Long> timeModifyReadjustIdList = Lists.newArrayList();
        List<Long> numModifyReadjustIdList = Lists.newArrayList();
        List<Long> removeReadjustIdList = Lists.newArrayList();

        Map<Long, JdhInventoryReadjustRecord> readjustRecordMap = riderReadjustInventory.stream().collect(Collectors.toMap(JdhInventoryReadjustRecord::getReadjustRecordId, Function.identity(), (k1, k2) -> k1));
        Iterator<JdhInventoryReadjustContext> iterator = jdhInventoryReadjustContextList.iterator();
        while (iterator.hasNext()) {
            JdhInventoryReadjustContext readjustContext = iterator.next();
            //检查修改的异常库存
            if(Objects.nonNull(readjustContext.getReadjustRecordId())) {
                JdhInventoryReadjustRecord readjustRecord = readjustRecordMap.get(readjustContext.getReadjustRecordId());
                riderReadjustInventory.remove(readjustRecord);
                //时间是否有调整
                boolean timeNoChangeFlag = readjustRecord.getReadjustStartTime().equals(readjustContext.getStartDate()) && readjustRecord.getReadjustEndTime().equals(readjustContext.getEndDate());
                //数量是否有调整
                boolean numNoChangeFlag = readjustRecord.getReadjustNum().equals(readjustContext.getReadjustNum());
                if(!timeNoChangeFlag && !numNoChangeFlag) {
                    timeAndNumModifyReadjustIdList.add(readjustContext.getReadjustRecordId());
                }else if(!timeNoChangeFlag){
                    timeModifyReadjustIdList.add(readjustContext.getReadjustRecordId());
                }else if(!numNoChangeFlag) {
                    numModifyReadjustIdList.add(readjustContext.getReadjustRecordId());
                }else {
                    iterator.remove();
                }
            }
        }

        //检查是否有删除
        if(CollectionUtils.isNotEmpty(riderReadjustInventory)) {
            removeReadjustIdList.addAll(riderReadjustInventory.stream().map(JdhInventoryReadjustRecord::getReadjustRecordId).collect(Collectors.toList()));
        }

        angelStationSaveContext.setTimeAndNumModifyReadjustIdList(timeAndNumModifyReadjustIdList);
        angelStationSaveContext.setTimeModifyReadjustIdList(timeModifyReadjustIdList);
        angelStationSaveContext.setNumModifyReadjustIdList(numModifyReadjustIdList);
        angelStationSaveContext.setRemoveReadjustIdList(removeReadjustIdList);

        //判断异常库存数量是否有调整
        boolean numChange = jdhInventoryReadjustContextList.size() > 0;
        boolean timeAndNumChange = CollectionUtils.isNotEmpty(timeAndNumModifyReadjustIdList);
        boolean timeChange = CollectionUtils.isNotEmpty(timeModifyReadjustIdList);
        boolean readjustNumChange = CollectionUtils.isNotEmpty(numModifyReadjustIdList);
        boolean removeChange = CollectionUtils.isNotEmpty(removeReadjustIdList);

        if(numChange || timeAndNumChange || timeChange || readjustNumChange || removeChange) {
            fillChangeBitMap(INVENTORY_READJUST_NUM_INDEX);
        }
    }

    /**
     * 护士异常库存调整
     *
     * @param angelStationSaveContext
     */
    public void nurseDiffReadjust(AngelStationSaveContext angelStationSaveContext) {
        //检查异常库存时间是否正确
        List<JdhInventoryReadjustContext> jdhNurseInventoryReadjustContextList = angelStationSaveContext.getJdhNurseInventoryReadjustContextList();
        if(CollectionUtils.isEmpty(jdhNurseInventoryReadjustContextList)) {
            log.info("[JdhStation -> nurseDiffReadjust],更新后没有库存调整项!");
            jdhNurseInventoryReadjustContextList = Lists.newArrayList();
        }
        if(CollectionUtils.isEmpty(jdhInventoryReadjustRecordList)){
            log.info("[JdhStation -> nurseDiffReadjust],更新前没有库存调整项!");
            jdhInventoryReadjustRecordList = Lists.newArrayList();
        }

        List<JdhInventoryReadjustRecord> nurseReadjustInventory =
                jdhInventoryReadjustRecordList.stream().filter(item -> InventoryChannelEnum.NURSE_CHANNEL.getInventoryChannel().equals(item.getInventoryChannelNo())).collect(Collectors.toList());

        //只有新增没有修改的情况
        if(CollectionUtils.isNotEmpty(jdhNurseInventoryReadjustContextList) && CollectionUtils.isEmpty(nurseReadjustInventory)) {
            fillChangeBitMap(NURSE_INVENTORY_READJUST_NUM_INDEX);
            return;
        }

        List<Long> timeAndNumModifyReadjustIdList = Lists.newArrayList();
        List<Long> timeModifyReadjustIdList = Lists.newArrayList();
        List<Long> numModifyReadjustIdList = Lists.newArrayList();
        List<Long> removeReadjustIdList = Lists.newArrayList();

        Map<Long, JdhInventoryReadjustRecord> readjustRecordMap = nurseReadjustInventory.stream().collect(Collectors.toMap(JdhInventoryReadjustRecord::getReadjustRecordId, Function.identity(), (k1, k2) -> k1));
        Iterator<JdhInventoryReadjustContext> iterator = jdhNurseInventoryReadjustContextList.iterator();
        while (iterator.hasNext()) {
            JdhInventoryReadjustContext readjustContext = iterator.next();
            //检查修改的异常库存
            if(Objects.nonNull(readjustContext.getReadjustRecordId())) {
                JdhInventoryReadjustRecord readjustRecord = readjustRecordMap.get(readjustContext.getReadjustRecordId());
                nurseReadjustInventory.remove(readjustRecord);
                //时间是否有调整
                boolean timeNoChangeFlag = readjustRecord.getReadjustStartTime().equals(readjustContext.getStartDate()) && readjustRecord.getReadjustEndTime().equals(readjustContext.getEndDate());
                //数量是否有调整
                boolean numNoChangeFlag = readjustRecord.getReadjustNum().equals(readjustContext.getReadjustNum());
                if(!timeNoChangeFlag && !numNoChangeFlag) {
                    timeAndNumModifyReadjustIdList.add(readjustContext.getReadjustRecordId());
                }else if(!timeNoChangeFlag){
                    timeModifyReadjustIdList.add(readjustContext.getReadjustRecordId());
                }else if(!numNoChangeFlag) {
                    numModifyReadjustIdList.add(readjustContext.getReadjustRecordId());
                }else {
                    iterator.remove();
                }
            }
        }

        //检查是否有删除
        if(CollectionUtils.isNotEmpty(nurseReadjustInventory)) {
            removeReadjustIdList.addAll(nurseReadjustInventory.stream().map(JdhInventoryReadjustRecord::getReadjustRecordId).collect(Collectors.toList()));
        }

        angelStationSaveContext.setNurseTimeAndNumModifyReadjustIdList(timeAndNumModifyReadjustIdList);
        angelStationSaveContext.setNurseTimeModifyReadjustIdList(timeModifyReadjustIdList);
        angelStationSaveContext.setNurseNumModifyReadjustIdList(numModifyReadjustIdList);
        angelStationSaveContext.setNurseRemoveReadjustIdList(removeReadjustIdList);

        //判断异常库存数量是否有调整
        boolean numChange = jdhNurseInventoryReadjustContextList.size() > 0;
        boolean timeAndNumChange = CollectionUtils.isNotEmpty(timeAndNumModifyReadjustIdList);
        boolean timeChange = CollectionUtils.isNotEmpty(timeModifyReadjustIdList);
        boolean readjustNumChange = CollectionUtils.isNotEmpty(numModifyReadjustIdList);
        boolean removeChange = CollectionUtils.isNotEmpty(removeReadjustIdList);

        if(numChange || timeAndNumChange || timeChange || readjustNumChange || removeChange) {
            fillChangeBitMap(NURSE_INVENTORY_READJUST_NUM_INDEX);
        }
    }

    public Integer getInventoryFlag(Integer inventoryChannel, AngelStationInventoryConfig angelStationInventoryConfig) {
        //检查库存标
        AngelStationInventoryFlagContext context = new AngelStationInventoryFlagContext();
        JdhAngelStationInfoB stationInfoB = JdhAngelStationInfoB.builder()
                .angelStationId(angelStationId)
                .stationId(stationId)
                .stationName(stationName)
                .angelNum(angelNum)
                .nurseNum(nurseNum)
                .openHour(openHour)
                .closeHour(closeHour)
                .build();
        context.setJdhAngelStationInfoBs(Lists.newArrayList(stationInfoB));
        context.setInventoryChannelNo(inventoryChannel);
        context.setAngelType(angelType);
        context.setAngelStationInventoryConfig(angelStationInventoryConfig);
        try{
            if(Objects.isNull(stationInfoB.getAngelStationId())) {
                log.error("JdhAngelStation -> getInventoryFlag, 服务站ID为空");
                return InventoryFlagEnum.LIMIT_INVENTORY.getFlag();
            }
            GroovyModuleExecutor groovyModuleExecutor = SpringUtil.getBean(GroovyModuleExecutor.class);
            GroovyScript<AngelStationInventoryFlagContext, Boolean> handler = groovyModuleExecutor.getInstance("angelStation_InventoryFlagHandler");
            boolean invoke = handler.invoke(context);
            log.info("JdhAngelStation -> getInventoryFlag, 执行结果.invoke={}", invoke);
        }catch (Exception ex) {
            log.error("JdhAngelStation -> getInventoryFlag, 库存打标失败，兜底限制库存", ex);
            return InventoryFlagEnum.LIMIT_INVENTORY.getFlag();
        }
        return stationInfoB.getInventoryFlag();
    }
}