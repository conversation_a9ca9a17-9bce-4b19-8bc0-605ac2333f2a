package com.jdh.o2oservice.core.domain.angel.context;

import com.google.common.collect.Lists;
import com.jd.fastjson.JSON;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import lombok.Builder;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.*;

/**
 * @ClassName:AngelStationSaveContext
 * @Description:
 * @Author: yaoqinghai
 * @Date: 2024/4/26 22:25
 * @Vserion: 1.0
 **/
@Data
@Builder
public class AngelStationSaveContext {

    /**
     * 服务站名称
     */
    private Long angelStationId;

    /**
     * 服务站名称
     */
    private String angelStationName;

    /**
     * 范围的衡量值（如xx公里或xx分钟）
     */
    private Integer fenceRangeRadius;

    /**
     * 中心点纬度
     */
    private String fenceRangeCenterLat;

    /**
     * 中心点经度
     */
    private String fenceRangeCenterLng;

    /**
     * 服务站地址
     */
    private String addressDetail;

    /**
     * 服务站全地址
     */
    private String fullAddress;

    /**
     * 站长erp
     */
    private String stationMaster;

    /**
     * 围栏id
     */
    private String fenceId;

    /**
     * 地图id
     */
    private Long mapId;

    /**
     * 三方地图id
     */
    private String thirdMapId;

    /**
     * 图层id
     */
    private Long layerId;

    /**
     * 三方图层id
     */
    private String thirdLayerId;

    /**
     * 业务模式 1护士-全职 2护士-兼职
     */
    private Integer stationModeType;

    /**
     * 开始营业时间（24小时）
     */
    private String openHour;

    /**
     * 停止营业时间（24小时）
     */
    private String closeHour;

    /**
     * 站点状态：1启用 2停用
     */
    private Integer stationStatus;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 是否修改了地理数据
     */
    private boolean modifyElementData;

    //---用于兼职服务站保存的多级透传地址字段

    /**
     * 省编码
     */
    private String provinceCode;

    /**
     * 省名称
     */
    private String provinceName;

    /**
     * 市编码
     */
    private String cityCode;

    /**
     * 市名称
     */
    private String cityName;

    /**
     * 区编码
     */
    private String districtCode;

    /**
     * 区名称
     */
    private String districtName;

    /**
     * 县编码
     */
    private String countyCode;

    /**
     * 县名称
     */
    private String countyName;

    /**
     * 兼职服务站标识
     */
    private Boolean partStationFlag;

    /**
     * 创建 兼职服务站的 服务者id
     */
    private Long angelId;

    /**
     * 围栏形状 空+1:圆形 2多边形
     * @see com.jdh.o2oservice.core.domain.angel.enums.FenceShapeTypeEnum
     */
    private Integer fenceShapeType;

    /**
     * 坐标集合 数据格式: [ [116.562913321,39.79562447840648],[116.562913321,39.79562447840648] ]
     */
    private List<List<Double>> fenceBoundaryList;

    /**
     * 服务资源类型 二进制编码 右向左 1位骑手 2位护士
     */
    private List<Integer> angelTypes;

    /**
     * 骑手供应商  二进制编码 右向左 1位达达 2位顺丰
     */
    private List<Integer> deliverySuppliers;

    /**
     * 实验室id
     */
    private String stationId;

    /**
     * 实验室名称
     */
    private String stationName;
    /**
     * 三方店铺id
     */
    private String outShopId;
    /**
     * 服务资源数量
     */
    private Integer angelNum;

    /**
     * 护士资源数量
     */
    private Integer nurseNum;

    /**
     * 实验室联系人名称
     */
    private String contactName;
    /**
     * 实验室联系人电话
     */
    private String contactPhone;

    /**
     * 库存数量调整上下文
     */
    private List<JdhInventoryReadjustContext> jdhInventoryReadjustContextList;

    /**
     * 库存数量调整上下文
     */
    private List<JdhInventoryReadjustContext> jdhNurseInventoryReadjustContextList;

    /**
     * 异常库存
     */
    private List<Long> timeAndNumModifyReadjustIdList;

    /**
     * 异常库存
     */
    private List<Long> timeModifyReadjustIdList;

    /**
     * 异常库存
     */
    private List<Long> numModifyReadjustIdList;

    /**
     * 移除的异常库存
     */
    private List<Long> removeReadjustIdList;

    /**
     * 护士异常库存
     */
    private List<Long> nurseTimeAndNumModifyReadjustIdList;

    /**
     * 护士异常库存
     */
    private List<Long> nurseTimeModifyReadjustIdList;

    /**
     * 护士异常库存
     */
    private List<Long> nurseNumModifyReadjustIdList;

    /**
     * 护士移除的异常库存
     */
    private List<Long> nurseRemoveReadjustIdList;

    /**
     * 三方店铺id
     */
    private Long jdTransferStationId;

    /**
     * 生成服务站全地址 用于兼职服务者 将多级地址拼接成全地址
     * 可用于生成经纬度
     */
    public void generateFullAddress() {
        StringBuilder sb = new StringBuilder();
        if (provinceName != null) {
            sb.append(provinceName);
        }
        if (cityName != null) {
            sb.append(cityName);
        }
        if (districtName != null) {
            sb.append(districtName);
        }
        if (countyName != null) {
            sb.append(countyName);
        }
        if (addressDetail != null) {
            sb.append(addressDetail);
        }
        this.fullAddress = sb.toString().replaceAll("\\s", "");
        this.partStationFlag = Boolean.TRUE;
    }

    /**
     * 生成与图识别的坐标格式
     * @return
     */
    public String generateElementGeometry(){
        if(CollectionUtils.isEmpty(this.fenceBoundaryList)){
            return null;
        }
        Map<String,Object> map = new HashMap<>();
        map.put("type","Polygon");
        List<List<List<Double>>> arrayList = new ArrayList(1);
        arrayList.add(this.fenceBoundaryList);
        map.put("coordinates", arrayList);
        return JSON.toJSONString(map);
    }

    /**
     * 补全异常库存调整信息
     */
    public void initReadjust() {
        if(CollectionUtils.isNotEmpty(jdhInventoryReadjustContextList)){
            jdhInventoryReadjustContextList.stream().forEach(readjust -> {
                readjust.setAngelStationId(this.getAngelStationId());
                readjust.setBaseNum(this.angelNum);
                readjust.setOperator(this.operator);
            });
        }

        if(CollectionUtils.isNotEmpty(jdhNurseInventoryReadjustContextList)){
            jdhNurseInventoryReadjustContextList.stream().forEach(readjust -> {
                readjust.setAngelStationId(this.getAngelStationId());
                readjust.setBaseNum(this.nurseNum);
                readjust.setOperator(this.operator);
            });
        }

        //护士数量如果不维护则默认是999999,并置空护士的异常库存
        if(Objects.isNull(this.nurseNum)){
            this.nurseNum = CommonConstant.SUPPER_INVENTORY_NUM;
        }
    }
}
