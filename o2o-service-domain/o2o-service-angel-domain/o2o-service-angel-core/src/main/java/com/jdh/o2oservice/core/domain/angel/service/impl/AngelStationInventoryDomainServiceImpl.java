package com.jdh.o2oservice.core.domain.angel.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.jd.medicine.base.common.util.DateUtil;
import com.jd.medicine.base.common.util.NumberUtil;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.ducc.model.AngelStationInventoryConfig;
import com.jdh.o2oservice.base.ducc.model.InventoryInitConfig;
import com.jdh.o2oservice.base.event.Event;
import com.jdh.o2oservice.base.event.EventCoordinator;
import com.jdh.o2oservice.base.event.factory.EventFactory;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.factory.GenerateIdFactory;
import com.jdh.o2oservice.base.groovy.core.GroovyModuleExecutor;
import com.jdh.o2oservice.base.groovy.core.GroovyScript;
import com.jdh.o2oservice.base.util.*;
import com.jdh.o2oservice.core.domain.angel.bo.InventoryNumProcessBo;
import com.jdh.o2oservice.core.domain.angel.context.*;
import com.jdh.o2oservice.core.domain.angel.converter.AngelStationInventoryConverter;
import com.jdh.o2oservice.core.domain.angel.enums.*;
import com.jdh.o2oservice.core.domain.angel.event.AngelStationInventoryEventBody;
import com.jdh.o2oservice.core.domain.angel.factory.JdhInventoryFactory;
import com.jdh.o2oservice.core.domain.angel.factory.JdhInventoryPreemptionRecordFactory;
import com.jdh.o2oservice.core.domain.angel.factory.JdhInventoryReadjustFactory;
import com.jdh.o2oservice.core.domain.angel.model.*;
import com.jdh.o2oservice.core.domain.angel.repository.cmd.ConfirmPreemptionInventoryRepositoryCmd;
import com.jdh.o2oservice.core.domain.angel.repository.cmd.JdhInventoryModifyCmd;
import com.jdh.o2oservice.core.domain.angel.repository.cmd.ReduceInventoryRepositoryCmd;
import com.jdh.o2oservice.core.domain.angel.repository.cmd.ReleaseInventoryRepositoryCmd;
import com.jdh.o2oservice.core.domain.angel.repository.db.*;
import com.jdh.o2oservice.core.domain.angel.repository.query.JdhInventoryPreeQuery;
import com.jdh.o2oservice.core.domain.angel.repository.query.JdhInventoryQuery;
import com.jdh.o2oservice.core.domain.angel.repository.query.JdhInventoryReadjustQuery;
import com.jdh.o2oservice.core.domain.angel.service.AngelStationInventoryDomainService;
import com.jdh.o2oservice.core.domain.angel.service.inventory.*;
import com.jdh.o2oservice.core.domain.angel.vo.AngelJobTimeIntervalVo;
import com.jdh.o2oservice.core.domain.angel.vo.JdhInventoryReadjustIIntervalVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.assertj.core.util.Sets;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.text.ParseException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * @ClassName AngelStationInventoryQueryDomainService
 * @Description 查询sku库存
 * <AUTHOR>
 * @Date 2024/6/12 11:30 AM
 * @Version 1.0
 **/
@Service
@Slf4j
public class AngelStationInventoryDomainServiceImpl implements AngelStationInventoryDomainService {

    @Resource
    private AngelStationInventoryHelper angelStationInventoryHelper;

    @Resource
    private JdhInventoryPreemptionRecordRepository jdhInventoryPreemptionRecordRepository;

    @Resource
    private JdhInventoryRepository jdhInventoryRepository;

    @Resource
    private JdhInventoryReadjustRecordRepository jdhInventoryReadjustRecordRepository;

    @Resource
    private DuccConfig duccConfig;

    @Resource
    private JdhStationRepository jdhStationRepository;

    @Resource
    private EventCoordinator eventCoordinator;

    @Resource
    private GroovyModuleExecutor groovyModuleExecutor;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveJdhInventory(List<JdhInventorySaveContext> inventorySaveContextList, List<JdhInventoryReadjustContext> jdhInventoryReadjustContextList, Boolean genReadjust) {
        log.info("[AngelStationInventoryDomainServiceImpl->saveJdhInventory],执行保存 start！");

        List<JdhInventory> inventoryList = JdhInventoryFactory.createInventoryList(inventorySaveContextList);
        if(CollectionUtils.isEmpty(inventoryList)){
            log.error("[AngelStationInventoryDomainServiceImpl->saveJdhInventory],服务站库存信息为空！");
            return false;
        }
        log.info("[AngelStationInventoryDomainServiceImpl->saveJdhInventory],执行保存 start！");
        //写库
        if(CollectionUtils.isEmpty(jdhInventoryReadjustContextList)){
            jdhInventoryRepository.batchInsert(inventoryList);
        }else {
            //解析异常库存调整量
            List<JdhInventoryReadjustRecord> readjustRecordList = JdhInventoryReadjustFactory.createReadjustRecordList(jdhInventoryReadjustContextList);
            List<JdhInventoryReadjustIIntervalVo> readjustIIntervalVoList = readjustRecordList.stream().flatMap(record -> record.getReadjustIIntervalVoList().stream()).collect(Collectors.toList());
            Map<String, JdhInventoryReadjustIIntervalVo> intervalVoMap = readjustIIntervalVoList.stream().collect(Collectors.toMap(item -> item.getScheduleDay().concat(item.getScheduleTime()), Function.identity(), (k1, k2) -> k1));
            inventoryList.forEach(item -> {
                JdhInventoryReadjustIIntervalVo jdhInventoryReadjustIIntervalVo = intervalVoMap.get(item.getScheduleDay().concat(item.getScheduleTime()));
                if(Objects.nonNull(jdhInventoryReadjustIIntervalVo)){
                    item.setReadjustNum(jdhInventoryReadjustIIntervalVo.getAddNum());
                }
            });

            if(genReadjust){
                jdhInventoryReadjustRecordRepository.batchInsert(readjustRecordList);
            }
            jdhInventoryRepository.batchInsert(inventoryList);
        }
        log.info("[AngelStationInventoryDomainServiceImpl->saveJdhInventory],服务站库存数据.inventoryList={}, jdhInventoryReadjustContextList={}", JSON.toJSONString(inventoryList), JSON.toJSONString(jdhInventoryReadjustContextList));

        //刷新缓存
        inventoryList.forEach(item -> {
            log.info("[AngelStationInventoryDomainServiceImpl->saveJdhInventory],缓存数据.item={}", JSON.toJSONString(item));
            InventoryOpCmd inventoryOpCmd = InventoryOpCmd.builder()
                    .inventoryCountOpType(InventoryCountOpType.INIT)
                    .opCount(item.getBaseNum()-item.getPreeNum()+item.getReadjustNum())
                    .day(item.getScheduleDay())
                    .timeSlot(item.getScheduleTime())
                    .storeId(String.valueOf(item.getAngelStationId()))
                    .inventoryChannelNo(item.getInventoryChannelNo())
                    .build();
            angelStationInventoryHelper.inventoryOp(inventoryOpCmd);
        });
        return true;
    }

    /**
     * 保存护士库存
     *
     * @param inventorySaveContextList
     * @param jdhNurseInventoryReadjustContextList
     */
    @Override
    @LogAndAlarm
    public Boolean saveJdhNurseInventory(List<JdhInventorySaveContext> inventorySaveContextList, List<JdhInventoryReadjustContext> jdhNurseInventoryReadjustContextList, boolean genReadjust) {
        log.info("[AngelStationInventoryDomainServiceImpl->saveJdhNurseInventory],执行保存 start！");

        List<JdhInventory> inventoryList = JdhInventoryFactory.createInventoryList(inventorySaveContextList);
        if(CollectionUtils.isEmpty(inventoryList)){
            log.error("[AngelStationInventoryDomainServiceImpl->saveJdhNurseInventory],服务站库存信息为空！");
            return false;
        }
        //写库
        if(CollectionUtils.isEmpty(jdhNurseInventoryReadjustContextList)){
            jdhInventoryRepository.batchInsert(inventoryList);
        }else {
            //解析异常库存调整量
            List<JdhInventoryReadjustRecord> nurseReadjustRecordList = JdhInventoryReadjustFactory.createNurseReadjustRecordList(jdhNurseInventoryReadjustContextList);
            List<JdhInventoryReadjustIIntervalVo> readjustIIntervalVoList = nurseReadjustRecordList.stream().flatMap(record -> record.getReadjustIIntervalVoList().stream()).collect(Collectors.toList());
            Map<String, JdhInventoryReadjustIIntervalVo> intervalVoMap = readjustIIntervalVoList.stream().collect(Collectors.toMap(item -> item.getScheduleDay().concat(item.getScheduleTime()), Function.identity(), (k1, k2) -> k1));
            inventoryList.forEach(item -> {
                JdhInventoryReadjustIIntervalVo jdhInventoryReadjustIIntervalVo = intervalVoMap.get(item.getScheduleDay().concat(item.getScheduleTime()));
                if(Objects.nonNull(jdhInventoryReadjustIIntervalVo)){
                    item.setReadjustNum(jdhInventoryReadjustIIntervalVo.getAddNum());
                }
            });

            if(genReadjust){
                jdhInventoryReadjustRecordRepository.batchInsert(nurseReadjustRecordList);
            }
            jdhInventoryRepository.batchInsert(inventoryList);
        }
        log.info("[AngelStationInventoryDomainServiceImpl->saveJdhNurseInventory],服务站库存数据.inventoryList={}, nurseReadjustRecordList={}", JSON.toJSONString(inventoryList), JSON.toJSONString(jdhNurseInventoryReadjustContextList));

        //刷新缓存
        inventoryList.forEach(item -> {
            log.info("[AngelStationInventoryDomainServiceImpl->saveJdhNurseInventory],缓存数据.item={}", JSON.toJSONString(item));
            InventoryOpCmd inventoryOpCmd = InventoryOpCmd.builder()
                    .inventoryCountOpType(InventoryCountOpType.INIT)
                    .opCount(item.getBaseNum()-item.getPreeNum()+item.getReadjustNum())
                    .day(item.getScheduleDay())
                    .timeSlot(item.getScheduleTime())
                    .storeId(String.valueOf(item.getAngelStationId()))
                    .inventoryChannelNo(item.getInventoryChannelNo())
                    .build();
            angelStationInventoryHelper.inventoryOp(inventoryOpCmd);
        });
        return true;
    }

    /**
     * 写库存数据
     *
     * @param jdhStationList
     * @param inventoryChannelNo
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "AngelStationInventoryDomainServiceImpl.rollJdhStationInventory")
    public Boolean rollJdhStationInventory(List<JdhStation> jdhStationList, LocalDate genDate, Boolean genReadjust, Integer inventoryChannelNo) {
        if(CollectionUtils.isEmpty(jdhStationList)) {
            log.info("[AngelStationEventConsumer->angelStationInventoryAdd],服务站列表为空!");
            return true;
        }
        InventoryInitConfig scheduleTimeSlice = duccConfig.getScheduleTimeSlice();
        for (JdhStation jdhStation : jdhStationList) {
            if(Objects.isNull(jdhStation)) {
                log.error("[AngelStationEventConsumer->angelStationInventoryAdd],服务站信息不存在!");
                continue;
            }

            if(!Objects.equals(AngelStationModeTypeEnum.FULL_TIME.getCode(), jdhStation.getStationModeType())) {
                log.error("[AngelStationEventConsumer->angelStationInventoryAdd],非兼职服务站不需要处理!");
                continue;
            }
            JdhInventoryReadjustQuery readjustQuery = JdhInventoryReadjustQuery.builder().build();
            readjustQuery.setAngelStationId(jdhStation.getAngelStationId());
            readjustQuery.setInventoryChannelNo(inventoryChannelNo);
            List<JdhInventoryReadjustRecord> readjustRecordList = jdhInventoryReadjustRecordRepository.findList(readjustQuery);
            List<JdhInventoryReadjustRecord> riderReadjustRecordList = Lists.newArrayList();
            List<JdhInventoryReadjustRecord> nurseReadjustRecordList = Lists.newArrayList();
            if(CollectionUtils.isEmpty(readjustRecordList)) {
                riderReadjustRecordList = readjustRecordList.stream().filter(item -> InventoryChannelEnum.RIDER_CHANNEL.getInventoryChannel().equals(item.getInventoryChannelNo())).collect(Collectors.toList());
                nurseReadjustRecordList = readjustRecordList.stream().filter(item -> InventoryChannelEnum.NURSE_CHANNEL.getInventoryChannel().equals(item.getInventoryChannelNo())).collect(Collectors.toList());
            }
            List<JdhInventoryReadjustContext> riderReadjustContextList = AngelStationInventoryConverter.INS.convertToJdhInventoryReadjustContextList(riderReadjustRecordList);
            List<JdhInventoryReadjustContext> nurseReadjustContextList = AngelStationInventoryConverter.INS.convertToJdhInventoryReadjustContextList(nurseReadjustRecordList);


            List<AngelJobTimeIntervalVo> jobTimeIntervalVoList = jdhStation.splitScheduleSlot(scheduleTimeSlice, genDate, scheduleTimeSlice.getRollDays());
            if(CollectionUtils.isEmpty(jobTimeIntervalVoList)){
                log.error("[AngelStationEventConsumer->angelStationInventoryAdd],没有服务站工作时间信息!jdhStation={}", JSON.toJSONString(jdhStation));
                continue;
            }

            //库存标
            AngelStationInventoryConfig angelStationInventoryConfig = duccConfig.getAngelStationInventoryConfig();
            Integer riderInventoryFlag = jdhStation.getInventoryFlag(InventoryChannelEnum.RIDER_CHANNEL.getInventoryChannel(), angelStationInventoryConfig);
            Integer nurseInventoryFlag = jdhStation.getInventoryFlag(InventoryChannelEnum.NURSE_CHANNEL.getInventoryChannel(), angelStationInventoryConfig);

            List<JdhInventorySaveContext> inventorySaveContextList = Lists.newArrayList();
            List<JdhInventorySaveContext> inventoryNurseContextList = Lists.newArrayList();
            jobTimeIntervalVoList.stream().forEach(vo -> {
                if(Objects.isNull(inventoryChannelNo) || InventoryChannelEnum.RIDER_CHANNEL.getInventoryChannel().equals(inventoryChannelNo)) {
                    if(InventoryFlagEnum.LIMIT_INVENTORY.getFlag().equals(riderInventoryFlag) || InventoryFlagEnum.TIME_LIMITLESS_INVENTORY.getFlag().equals(riderInventoryFlag)){
                        JdhInventorySaveContext context = new JdhInventorySaveContext();
                        context.setAngelStationId(jdhStation.getAngelStationId());
                        context.setInventoryChannelNo(InventoryChannelEnum.RIDER_CHANNEL.getInventoryChannel());
                        context.setScheduleDay(vo.getScheduleDay());
                        context.setScheduleTime(vo.getScheduleTime());
                        context.setBaseNum(NumberUtil.isGreaterThanZero(vo.getBaseNum()) ? vo.getBaseNum() : CommonConstant.SUPPER_INVENTORY_NUM);
                        inventorySaveContextList.add(context);
                    }
                }

                if(Objects.isNull(inventoryChannelNo) || InventoryChannelEnum.NURSE_CHANNEL.getInventoryChannel().equals(inventoryChannelNo)) {
                    if(InventoryFlagEnum.LIMIT_INVENTORY.getFlag().equals(nurseInventoryFlag) || InventoryFlagEnum.TIME_LIMITLESS_INVENTORY.getFlag().equals(nurseInventoryFlag)){
                        JdhInventorySaveContext nurseContext = new JdhInventorySaveContext();
                        nurseContext.setAngelStationId(jdhStation.getAngelStationId());
                        nurseContext.setInventoryChannelNo(InventoryChannelEnum.NURSE_CHANNEL.getInventoryChannel());
                        nurseContext.setScheduleDay(vo.getScheduleDay());
                        nurseContext.setScheduleTime(vo.getScheduleTime());
                        nurseContext.setBaseNum(NumberUtil.isGreaterThanZero(vo.getNurseBaseNum()) ? vo.getNurseBaseNum() : CommonConstant.SUPPER_INVENTORY_NUM);
                        inventoryNurseContextList.add(nurseContext);
                    }
                }
            });

            saveJdhInventory(inventorySaveContextList, riderReadjustContextList, genReadjust);
            saveJdhNurseInventory(inventoryNurseContextList, nurseReadjustContextList, genReadjust);
        }
        return true;
    }

    /**
     * 根据sku查询服务站的服务重叠时间
     * @param queryBatchSkuTimeSlotContext
     * @return
     */
    @Override
    @LogAndAlarm
    public QueryBatchSkuTimeSlotResult queryBatchSkuTimeSlot(QueryBatchSkuTimeSlotContext queryBatchSkuTimeSlotContext) {
        QueryBatchSkuTimeSlotResult queryBatchSkuTimeSlotResult = QueryBatchSkuTimeSlotResult.builder().build();

        //多个sku时间取交集
        Optional<TimeIntervalIntersection.TimeInterval> timeIntervalOption = TimeIntervalIntersection.calculateIntersection(queryBatchSkuTimeSlotContext.getSkuInfos().stream()
                .map(t->new TimeIntervalIntersection.TimeInterval( LocalTime.parse(JSON.parseArray(t.getDayTimeFrame()).get(0).toString().split("-")[0], DateTimeFormatter.ofPattern("HH:mm")),LocalTime.parse(JSON.parseArray(t.getDayTimeFrame()).get(0).toString().split("-")[1], DateTimeFormatter.ofPattern("HH:mm"))))
                .collect(Collectors.toList()));
        if(!timeIntervalOption.isPresent()){
            log.info("AngelStationInventoryQueryDomainServiceImpl.queryBatchSkuTimeSlot timeIntervalOption为空!!");
            return queryBatchSkuTimeSlotResult;
        }
        log.info("AngelStationInventoryQueryDomainServiceImpl.queryBatchSkuTimeSlot timeIntervalOption={}",JSON.toJSONString(timeIntervalOption.get()));

        //多个服务站时间取并集
        List<TimeIntervalIntersection.TimeInterval> timeIntervalList = TimeIntervalIntersection.mergeIntervals(queryBatchSkuTimeSlotContext.getJdhAngelStationInfos().stream()
                .flatMap(t-> {
                        LocalTime openTime = LocalTime.parse(Optional.ofNullable(t.getOpenHour()).orElse("00:00"), DateTimeFormatter.ofPattern(CommonConstant.HM));
                        LocalTime closeTime = LocalTime.parse(Optional.ofNullable(t.getCloseHour()).orElse("23:59"), DateTimeFormatter.ofPattern(CommonConstant.HM));
                        if (closeTime.isAfter(openTime)) {
                            return Stream.of(new TimeIntervalIntersection.TimeInterval(openTime,closeTime));
                        } else {
                            //跨夜的情况，开始时间段比结束时间段晚
                            return Stream.of(new TimeIntervalIntersection.TimeInterval(openTime,LocalTime.parse("23:59", DateTimeFormatter.ofPattern(CommonConstant.HM)))
                                    ,new TimeIntervalIntersection.TimeInterval(LocalTime.parse("00:00", DateTimeFormatter.ofPattern(CommonConstant.HM)), closeTime));
                        }
                }).collect(Collectors.toList()));

        if(CollectionUtils.isEmpty(timeIntervalList)){
            log.info("AngelStationInventoryQueryDomainServiceImpl.queryBatchSkuTimeSlot timeIntervalList为空!!");
            return queryBatchSkuTimeSlotResult;
        }
        log.info("AngelStationInventoryQueryDomainServiceImpl.queryBatchSkuTimeSlot timeIntervalList={}",JSON.toJSONString(timeIntervalList));

        //timeIntervalList.add(timeIntervalOption.get());
        //以上两个结果合并后取交集
        Optional<List<TimeIntervalIntersection.TimeInterval>> result = TimeIntervalIntersection.groupCalculateIntersection(timeIntervalOption.get(), timeIntervalList);
        if(!result.isPresent()){
            log.info("AngelStationInventoryQueryDomainServiceImpl.queryBatchSkuTimeSlot result为空!!");
            return queryBatchSkuTimeSlotResult;
        }
        //queryBatchSkuTimeSlotResult.setBookStartTime(result.get().getStart().toString());
        //queryBatchSkuTimeSlotResult.setBookEndTime(result.get().getEnd().toString());
        List<QueryBatchSkuTimeSlotRangeResult> slotRangeList = result.get().stream().map(timeInterval -> QueryBatchSkuTimeSlotRangeResult.builder()
                .bookStartTime(timeInterval.getStart().toString())
                .bookEndTime(timeInterval.getEnd().toString())
                .build()).collect(Collectors.toList());
        queryBatchSkuTimeSlotResult.setSlotRangeList(slotRangeList);
        queryBatchSkuTimeSlotResult.setCommonMaxScheduleDays(queryBatchSkuTimeSlotContext.getSkuInfos().stream().min(Comparator.comparing(SkuInfo::getMaxScheduleDays)).orElse(SkuInfo.builder().build()).getMaxScheduleDays());
        return queryBatchSkuTimeSlotResult;
    }

    /**
     * 查询服务站库存明细
     * @param queryBatchAngelStationInventoryContext
     * @return
     */
    @Override
    @LogAndAlarm(logSwitch = false)
    public QueryBatchAngelStationInventoryResult queryBatchAngelStationInventory(QueryBatchAngelStationInventoryContext queryBatchAngelStationInventoryContext) {
        if(Objects.isNull(queryBatchAngelStationInventoryContext.getInventoryChannelNo())) {
            //默认库存渠道为骑手，兼容历史骑手逻辑
            queryBatchAngelStationInventoryContext.setInventoryChannelNo(InventoryChannelEnum.RIDER_CHANNEL.getInventoryChannel());
        }

        QueryBatchAngelStationInventoryResult queryBatchAngelStationInventoryResult = QueryBatchAngelStationInventoryResult.builder().build();
        List<AngelStationInventory> angelStationInventories = new ArrayList<>();
        queryBatchAngelStationInventoryResult.setAngelStationInventories(angelStationInventories);

        if(queryBatchAngelStationInventoryContext.getCommonMaxScheduleDays()==null){
            log.info("AngelStationInventoryQueryDomainServiceImpl.queryBatchAngelStationInventory queryBatchAngelStationInventoryContext.getMaxScheduleDays()为空!!");
            return queryBatchAngelStationInventoryResult;
        }
        if(CollectionUtils.isEmpty(queryBatchAngelStationInventoryContext.getJdhAngelStationInfoBs())){
            log.info("AngelStationInventoryQueryDomainServiceImpl.queryBatchAngelStationInventory queryBatchAngelStationInventoryContext.getJdhAngelStationInfoBs()为空!!");
            return queryBatchAngelStationInventoryResult;
        }
        queryBatchAngelStationInventoryContext.getJdhAngelStationInfoBs().forEach(t->{
            AngelStationInventory angelStationInventory = new AngelStationInventory();
            //维护->服务站id
            angelStationInventory.setAngelStationId(t.getAngelStationId());
            //维护->实验室id
            angelStationInventory.setStationId(t.getStationId());
            //维护->实验室名称
            angelStationInventory.setStationName(t.getStationName());
            //维护->接驳点id
            angelStationInventory.setJdTransferStationId(t.getJdTransferStationId());
            List<AngelStationDayInventory> angelStationDayInventories = new ArrayList<>();
            //根据服务站营业时间分成服务站可扣减库存时间段
            if(StringUtils.isBlank(t.getOpenHour())) {
                t.setOpenHour("00:00");
            }
            if(StringUtils.isBlank(t.getOpenHour())) {
                t.setCloseHour("23:59");
            }
            List<TimeIntervalIntersection.TimeInterval> timeIntervals = TimeIntervalIntersection.splitTimeIntervalsContainsAcrossNight(
                    new TimeIntervalIntersection.TimeInterval(
                            LocalTime.parse(Optional.ofNullable(t.getOpenHour()).orElse("00:00"), DateTimeFormatter.ofPattern(CommonConstant.HM)),
                            LocalTime.parse(Optional.ofNullable(t.getCloseHour()).orElse("23:59"), DateTimeFormatter.ofPattern(CommonConstant.HM)),
                            duccConfig.getScheduleTimeSlice().getIntervalMinutes()));
            for (int i = 0; i <= queryBatchAngelStationInventoryContext.getCommonMaxScheduleDays(); i++) {
                AngelStationDayInventory angelStationDayInventory = AngelStationDayInventory.builder().build();
                angelStationDayInventory.setScheduleDay(DateUtil.formatDate(DateUtil.addDays(new Date(),i),CommonConstant.YMD));
                if(Objects.nonNull(t.getInventoryFlag()) && InventoryFlagEnum.ALL_LIMITLESS_INVENTORY.getFlag().equals(t.getInventoryFlag())) {
                    angelStationDayInventory.setTimeSlotInventories(this.getTimeSlotInventoryAllLimitless(t, queryBatchAngelStationInventoryContext.getSlotRangeList()));
                }else if(Objects.nonNull(t.getInventoryFlag()) && InventoryFlagEnum.NUM_LIMITLESS_INVENTORY.getFlag().equals(t.getInventoryFlag())) {
                    angelStationDayInventory.setTimeSlotInventories(this.getTimeSlotInventoryLimitless(t, queryBatchAngelStationInventoryContext.getSlotRangeList()));
                } else {
                    angelStationDayInventory.setTimeSlotInventories(this.getTimeSlotInventory(t.getAngelStationId(),queryBatchAngelStationInventoryContext.getInventoryChannelNo(),angelStationDayInventory.getScheduleDay(),queryBatchAngelStationInventoryContext.getSlotRangeList(), timeIntervals));
                }
                angelStationDayInventories.add(angelStationDayInventory);
            }
            angelStationInventory.setAngelStationDayInventories(angelStationDayInventories);
            angelStationInventories.add(angelStationInventory);
        });

        return queryBatchAngelStationInventoryResult;
    }

    private List<TimeSlotInventory> getTimeSlotInventoryAllLimitless(JdhAngelStationInfoB stationInfoB, List<QueryBatchSkuTimeSlotRangeResult> slotRangeList) {
        log.info("AngelStationInventoryQueryDomainServiceImpl.getTimeSlotInventoryAllLimitless angelStationId={},scheduleDay={},slotRangeList={}",JSON.toJSONString(stationInfoB),JSON.toJSONString(slotRangeList));
        if (CollectionUtils.isEmpty(slotRangeList)) {
            return Collections.emptyList();
        }
        List<TimeSlotInventory> timeSlotInventories = new ArrayList<>();

        LocalTime openTime = LocalTime.parse("00:00");
        LocalTime closeTime = LocalTime.parse("23:59");
        for (QueryBatchSkuTimeSlotRangeResult queryBatchSkuTimeSlotRangeResult : slotRangeList) {
            if (StringUtils.isBlank(queryBatchSkuTimeSlotRangeResult.getBookStartTime()) || StringUtils.isBlank(queryBatchSkuTimeSlotRangeResult.getBookEndTime())) {
                continue;
            }
            //预定开始时间
            LocalTime startTime = LocalTime.parse("00:00", DateTimeFormatter.ofPattern("HH:mm"));
            //预定结束时间
            LocalTime endTime = LocalTime.parse("23:59", DateTimeFormatter.ofPattern("HH:mm"));
            TimeIntervalIntersection.TimeInterval commonInterval = new TimeIntervalIntersection.TimeInterval(startTime,endTime);
            List<TimeIntervalIntersection.TimeInterval> bookTimeStageList = commonInterval.split();
            bookTimeStageList.forEach(item -> {
                TimeSlotInventory timeSlotInventory = new TimeSlotInventory();
                boolean openTimeInner = openTime.equals(item.getStart()) || openTime.isBefore(item.getStart());
                boolean closeTimeInner = closeTime.equals(item.getEnd()) || closeTime.isAfter(item.getEnd());
                if(openTimeInner && closeTimeInner){
                    timeSlotInventory.setTimeSpan(item.localTimeFormat());
                    timeSlotInventory.setInventoryNum(CommonConstant.SUPPER_INVENTORY_NUM);
                }else {
                    timeSlotInventory.setTimeSpan(item.localTimeFormat());
                    timeSlotInventory.setInventoryNum(0);
                }
                timeSlotInventories.add(timeSlotInventory);
            });
        }
        log.info("AngelStationInventoryQueryDomainServiceImpl.getTimeSlotInventoryAllLimitless all timeSlotInventories={}",JSON.toJSONString(timeSlotInventories));
        return timeSlotInventories;
    }

    private List<TimeSlotInventory> getTimeSlotInventoryLimitless(JdhAngelStationInfoB jdhAngelStationInfoB, List<QueryBatchSkuTimeSlotRangeResult> slotRangeList) {
        log.info("AngelStationInventoryQueryDomainServiceImpl.getTimeSlotInventoryLimitless angelStationId={},scheduleDay={},slotRangeList={}",JSON.toJSONString(jdhAngelStationInfoB),JSON.toJSONString(slotRangeList));
        if (CollectionUtils.isEmpty(slotRangeList)) {
            return Collections.emptyList();
        }
        List<TimeSlotInventory> timeSlotInventories = new ArrayList<>();

        for (QueryBatchSkuTimeSlotRangeResult queryBatchSkuTimeSlotRangeResult : slotRangeList) {
            if (StringUtils.isBlank(queryBatchSkuTimeSlotRangeResult.getBookStartTime()) || StringUtils.isBlank(queryBatchSkuTimeSlotRangeResult.getBookEndTime())) {
                continue;
            }
            //预定开始时间
            LocalTime startTime = LocalTime.parse(queryBatchSkuTimeSlotRangeResult.getBookStartTime(), DateTimeFormatter.ofPattern("HH:mm"));
            //预定结束时间
            LocalTime endTime = LocalTime.parse(queryBatchSkuTimeSlotRangeResult.getBookEndTime(), DateTimeFormatter.ofPattern("HH:mm"));
            TimeIntervalIntersection.TimeInterval commonInterval = new TimeIntervalIntersection.TimeInterval(startTime,endTime);
            List<TimeIntervalIntersection.TimeInterval> bookTimeStageList = commonInterval.split();
            bookTimeStageList.forEach(item -> {
                TimeSlotInventory timeSlotInventory = new TimeSlotInventory();
                timeSlotInventory.setTimeSpan(item.localTimeFormat());
                timeSlotInventory.setInventoryNum(CommonConstant.SUPPER_INVENTORY_NUM);
                timeSlotInventories.add(timeSlotInventory);
            });
        }
        log.info("AngelStationInventoryQueryDomainServiceImpl.getTimeSlotInventoryLimitless all timeSlotInventories={}",JSON.toJSONString(timeSlotInventories));
        return timeSlotInventories;
    }

    /**
     * 预占库存,返回库存单业务主键id,非表主键id;
     * @param preemptionInventoryContext
     * @return
     */
    @Override
    @LogAndAlarm
    public Long preemptionInventory(PreemptionInventoryContext preemptionInventoryContext) {
        AngelStationInventoryConfig angelStationInventoryConfig = duccConfig.getAngelStationInventoryConfig();
        if(!angelStationInventoryConfig.getAngelStationInventoryGlobalOpen()){
            log.info("StationApplicationImpl.preemptionInventory 不走库存逻辑");
            return 0L;
        }

        //校验服务站服务资源类型必须包含骑手
        JdhStation jdhStation = jdhStationRepository.find(JdhStationIdentifier.builder().angelStationId(preemptionInventoryContext.getAngelStationId()).build());
        AssertUtils.nonNull(jdhStation,"服务站不存在!!!");
        if(jdhStation.getAngelType()==null){
            throw new BusinessException(AngelErrorCode.ANGEL_INVENTORY_CHECK_OP_PARAM_ERROR_MESSAGE.formatDescription("服务站资源类型未配置"));
        }

        //检查库存标
        Integer inventoryFlag = jdhStation.getInventoryFlag(preemptionInventoryContext.getInventoryChannelNo(), angelStationInventoryConfig);

        log.info("AngelStationInventoryQueryDomainServiceImpl -> preemptionInventory, inventoryFlag={}",JSON.toJSONString(inventoryFlag));
        List<TimeIntervalIntersection.TimeInterval> timeIntervalList = TimeIntervalIntersection.hitTimeIntervals(new TimeIntervalIntersection.TimeInterval(LocalTime.parse(preemptionInventoryContext.getScheduleTime().split("-")[0],DateTimeFormatter.ofPattern(CommonConstant.HM))
                ,LocalTime.parse(preemptionInventoryContext.getScheduleTime().split("-")[1],DateTimeFormatter.ofPattern(CommonConstant.HM))));
        if(CollectionUtils.isEmpty(timeIntervalList)){
            log.info("AngelStationInventoryQueryDomainServiceImpl.preemptionInventory 命中时间段为空!!!");
            throw new BusinessException(AngelErrorCode.ANGEL_INVENTORY_CHECK_OP_PARAM_ERROR_MESSAGE.formatDescription("预占库存失败!!!"));
        }

        //记录对应的时间段游标
        int index=-1;
        Long inventoryId;
        String realScheduleTime;
        Integer remainingCount;

        if(InventoryFlagEnum.LIMIT_INVENTORY.getFlag().equals(inventoryFlag)) {
            for (int i = 0; i < timeIntervalList.size(); i++) {
                //判断库存是否满足;-->不满足->退出抛异常
                InventoryOpCmd inventoryOpCmd = InventoryOpCmd.builder()
                        .storeId(preemptionInventoryContext.getAngelStationId()+"")
                        .inventoryChannelNo(preemptionInventoryContext.getInventoryChannelNo())
                        .inventoryCountOpType(InventoryCountOpType.SUBTRACT)
                        .opCount(preemptionInventoryContext.getPreemptionNum())
                        .day(preemptionInventoryContext.getScheduleDay())
                        .timeSlot( timeIntervalList.get(i).getStart().toString()+"-"+timeIntervalList.get(i).getEnd().toString())
                        .cacheExpirationDay((int) ChronoUnit.DAYS.between(LocalDate.parse(preemptionInventoryContext.getScheduleDay(), DateTimeFormatter.ofPattern(CommonConstant.YMD)),LocalDate.now())+2)
                        .build();
                Boolean opResult = angelStationInventoryHelper.inventoryOp(inventoryOpCmd);
                if(opResult){
                    index = i;
                    //查询当前时段库存是否为0,如果是发送事件出来->消费事件->发咚咚告警
                    TimeSlotInventoryQuery inventoryQuery = TimeSlotInventoryQuery.builder()
                            .storeId(preemptionInventoryContext.getAngelStationId() + "")
                            .inventoryChannelNo(preemptionInventoryContext.getInventoryChannelNo())
                            .day(preemptionInventoryContext.getScheduleDay())
                            .timeSlot(timeIntervalList.get(i).getStart().toString() + "-" + timeIntervalList.get(i).getEnd().toString())
                            .build();
                    InventoryResult inventoryResult = angelStationInventoryHelper.timeSlotInventoryQuery(inventoryQuery);
                    if(inventoryResult.getRemainingCount()<=0){
                        AngelStationInventoryEventBody angelStationInventoryEventBody = new AngelStationInventoryEventBody();
                        angelStationInventoryEventBody.setAngelStationName(jdhStation.getAngelStationName());
                        angelStationInventoryEventBody.setAngelStationId(jdhStation.getAngelStationId());
                        try {
                            angelStationInventoryEventBody.setDay(TimeUtils.dateTimeToStr(DateUtils.parseDate(preemptionInventoryContext.getScheduleDay(),"yyyy-MM-dd"),"yyyy年MM月dd日"));
                        } catch (ParseException e) {
                            log.error("preemptionInventory 时间转换异常,不影响业务流程,影响咚咚报警文案展示");
                        }
                        angelStationInventoryEventBody.setTimeSlot(timeIntervalList.get(i).getStart().toString()+"-"+timeIntervalList.get(i).getEnd().toString());
                        Event event = EventFactory.newDefaultEvent(jdhStation, AngelStationEventTypeEnum.ANGEL_STATION_INVENTORY_IS_OUT, angelStationInventoryEventBody);
                        eventCoordinator.publish(event);
                    }
                    break;
                }
            }
            //如果=-1说明扣减库存失败
            if(index==-1){
                log.info("AngelStationInventoryQueryDomainServiceImpl.preemptionInventory 扣减失败!!");
                throw new BusinessException(AngelErrorCode.ANGEL_INVENTORY_CHECK_OP_PARAM_ERROR_MESSAGE.formatDescription("当前时段无运力，请切换时间下单"));
            }
            realScheduleTime = timeIntervalList.get(index).getStart().toString()+"-"+timeIntervalList.get(index).getEnd().toString();
            TimeSlotInventoryQuery inventoryQuery = TimeSlotInventoryQuery.builder()
                    .timeSlot(realScheduleTime)
                    .storeId(preemptionInventoryContext.getAngelStationId() + "")
                    .inventoryChannelNo(preemptionInventoryContext.getInventoryChannelNo())
                    .day(preemptionInventoryContext.getScheduleDay())
                    .build();
            InventoryResult inventoryResult =  angelStationInventoryHelper.timeSlotInventoryQuery(inventoryQuery);

            //新增逻辑:查询库存数量表,一定有值;维护库存数量表数据
            JdhInventory jdhInventory = jdhInventoryRepository.findOneByCondition(preemptionInventoryContext.getAngelStationId(),preemptionInventoryContext.getScheduleDay(),realScheduleTime, preemptionInventoryContext.getInventoryChannelNo());
            jdhInventory.setPreeNum(preemptionInventoryContext.getPreemptionNum());
            jdhInventory.setUpdateUser(preemptionInventoryContext.getPin());
            jdhInventoryRepository.addPreeNum(jdhInventory);

            inventoryId = jdhInventory.getInventoryId();
            remainingCount = inventoryResult.getRemainingCount();
        }else {
            inventoryId = SpringUtil.getBean(GenerateIdFactory.class).getId();
            remainingCount = CommonConstant.SUPPER_INVENTORY_NUM;
            realScheduleTime = preemptionInventoryContext.getScheduleTime();
        }

        JdhInventoryPreemptionRecord jdhInventoryPreemptionRecord = JdhInventoryPreemptionRecordFactory.create(preemptionInventoryContext);
        //库存数量id
        jdhInventoryPreemptionRecord.setInventoryId(inventoryId);
        //预占数量
        jdhInventoryPreemptionRecord.setPreemptionNum(preemptionInventoryContext.getPreemptionNum());
        //预占时间
        jdhInventoryPreemptionRecord.setPreemptionTime(new Date());
        //维护状态
        jdhInventoryPreemptionRecord.setRecordStatus(AngelStationInventoryStatusEnum.PREEMPTION.getType());
        //维护当前实时库存
        jdhInventoryPreemptionRecord.setCurInventoryNum(remainingCount);
        //维护服务站id
        jdhInventoryPreemptionRecord.setAngelStationId(preemptionInventoryContext.getAngelStationId());
        //维护预约日期
        jdhInventoryPreemptionRecord.setScheduleDay(preemptionInventoryContext.getScheduleDay());
        //维护真实确定的预约时间
        jdhInventoryPreemptionRecord.setScheduleTime(realScheduleTime);
        //生成业务主键
        jdhInventoryPreemptionRecord.setPreeRecordId(SpringUtil.getBean(GenerateIdFactory.class).getId());
        //记录用户计划预约时间
        jdhInventoryPreemptionRecord.setPlanScheduleTime(preemptionInventoryContext.getScheduleTime());
        if(jdhInventoryPreemptionRecordRepository.save(jdhInventoryPreemptionRecord)>0){
            return jdhInventoryPreemptionRecord.getPreeRecordId();
        }
        log.info("AngelStationInventoryQueryDomainServiceImpl.preemptionInventory 预占库存数据库操作失败!!!");
        throw new BusinessException(AngelErrorCode.ANGEL_INVENTORY_CHECK_OP_PARAM_ERROR,"预占库存失败!!!");
    }

    /**
     * 确定-预占服务站库存
     * @param confirmPreemptionInventoryContext
     * @return
     */
    @Override
    @LogAndAlarm
    public Boolean confirmPreemptionInventory(ConfirmPreemptionInventoryContext confirmPreemptionInventoryContext) {
        ConfirmPreemptionInventoryRepositoryCmd confirmPreemptionInventoryRepositoryCmd = AngelStationInventoryConverter.INS.convertToConfirmPreemptionInventoryRepositoryCmd(confirmPreemptionInventoryContext);
        boolean result = jdhInventoryPreemptionRecordRepository.confirmPreemptionInventory(confirmPreemptionInventoryRepositoryCmd)>0;
        if(!result){
            log.info("AngelStationInventoryQueryDomainServiceImpl.preemptionInventory 确定预占库存数据库操作失败!!!");
            throw new BusinessException(AngelErrorCode.ANGEL_INVENTORY_CHECK_OP_PARAM_ERROR_MESSAGE.formatDescription("确定预占库存数据库操作失败!!!"));
        }
        return true;
    }

    /**
     * 修改库存
     *
     * @param modifyInventoryContext
     * @return
     */
    @Override
    @LogAndAlarm
    public List<JdhInventoryPreemptionRecord> modifyInventory(ModifyInventoryContext modifyInventoryContext) {
        List<JdhInventoryPreemptionRecord> resultPreRecordList = Lists.newArrayList();

        JdhInventoryPreeQuery preeQuery = new JdhInventoryPreeQuery();
        preeQuery.setBusinessNo(modifyInventoryContext.getBusinessId());
        preeQuery.setAngelStationId(modifyInventoryContext.getAngelStationId());
        preeQuery.setRecordStatus(Lists.newArrayList(AngelStationInventoryStatusEnum.PREEMPTION.getType(), AngelStationInventoryStatusEnum.REDUCE.getType()));
        preeQuery.setPlanScheduleTime(modifyInventoryContext.getScheduleTime());
        preeQuery.setScheduleDay(modifyInventoryContext.getScheduleDay());
        preeQuery.setInventoryChannelNo(modifyInventoryContext.getInventoryChannelNo());
        List<JdhInventoryPreemptionRecord> preeRecordList = jdhInventoryPreemptionRecordRepository.findByPreeRecordList(preeQuery);
        if(CollectionUtils.isEmpty(preeRecordList)) {
            log.info("AngelStationInventoryDomainServiceImpl -> modifyInventory,预占记录数据不正确");
            throw new BusinessException(AngelErrorCode.ANGEL_INVENTORY_CHECK_OP_PARAM_ERROR_MESSAGE.formatDescription("库存预占数据不正确!!!"));
        }

        log.info("AngelStationInventoryDomainServiceImpl -> modifyInventory,preeRecordList1={}", JSON.toJSONString(preeRecordList));
        int preSum = preeRecordList.stream().mapToInt(JdhInventoryPreemptionRecord::getPreemptionNum).sum();
        if (preSum < modifyInventoryContext.getModifyReemptionNum()) {
            log.info("AngelStationInventoryDomainServiceImpl -> modifyInventory,调整数量大于扣减数量不能调整");
            throw new BusinessException(AngelErrorCode.ANGEL_INVENTORY_CHECK_OP_PARAM_ERROR_MESSAGE.formatDescription("调整数量大于扣减数量不能调整!!!"));
        }

        Integer modifyReemptionNum = modifyInventoryContext.getModifyReemptionNum();
        for (JdhInventoryPreemptionRecord pree : preeRecordList) {
            int preeStatus = pree.getRecordStatus();
            if (pree.getPreemptionNum() < modifyReemptionNum) {
                pree.setPreemptionNum(CommonConstant.ZERO);
                pree.setRecordStatus(AngelStationInventoryStatusEnum.RELEASE.getType());
                modifyReemptionNum = modifyReemptionNum - pree.getPreemptionNum();
            } else {
                int newPreeNum = pree.getPreemptionNum() - modifyReemptionNum;
                pree.setPreemptionNum(newPreeNum);
                if (newPreeNum <= CommonConstant.ZERO) {
                    pree.setRecordStatus(AngelStationInventoryStatusEnum.RELEASE.getType());
                }
            }
            jdhInventoryPreemptionRecordRepository.updatePreeNumAndStatus(pree);
            pree.setRecordStatus(preeStatus);
        }

        Iterator<JdhInventoryPreemptionRecord> iterator = preeRecordList.iterator();
        while(iterator.hasNext()) {
            JdhInventoryPreemptionRecord preemptionRecord = iterator.next();
            resultPreRecordList.add(preemptionRecord);
            if(CommonConstant.SUPPER_INVENTORY_NUM.equals(preemptionRecord.getCurInventoryNum())) {
                log.info("AngelStationInventoryDomainServiceImpl -> modifyInventory,不限制库存移出");
                iterator.remove();
            }
            log.info("AngelStationInventoryDomainServiceImpl -> modifyInventory,preeRecordList2={}", JSON.toJSONString(preeRecordList));
        }
        log.info("AngelStationInventoryDomainServiceImpl -> modifyInventory,preeRecordList3={}", JSON.toJSONString(preeRecordList));
        if(CollectionUtils.isEmpty(preeRecordList)) {
            log.info("AngelStationInventoryDomainServiceImpl -> modifyInventory,没有要修改库存的数据");
            return resultPreRecordList;
        }

        Set<Long> inventoryIdSet = preeRecordList.stream().map(JdhInventoryPreemptionRecord::getInventoryId).collect(Collectors.toSet());
        JdhInventoryQuery jdhInventoryQuery = new JdhInventoryQuery();
        jdhInventoryQuery.setAngelStationId(modifyInventoryContext.getAngelStationId());
        jdhInventoryQuery.setInventoryIdSet(inventoryIdSet);
        jdhInventoryQuery.setInventoryChannelNo(modifyInventoryContext.getInventoryChannelNo());
        List<JdhInventory> jdhInventoryList = jdhInventoryRepository.findList(jdhInventoryQuery);
        if (CollectionUtils.isEmpty(jdhInventoryList)) {
            log.info("AngelStationInventoryDomainServiceImpl -> modifyInventory,没有查询到库存");
            throw new BusinessException(AngelErrorCode.ANGEL_INVENTORY_CHECK_OP_PARAM_ERROR_MESSAGE.formatDescription("没有查询到库存数量!!!"));
        }

        //处理库存修改,修改老数据的数量
        JdhInventory jdhInventory = jdhInventoryList.get(0);
        jdhInventory.setPreeNum(jdhInventory.getPreeNum() - modifyInventoryContext.getModifyReemptionNum());
        jdhInventoryRepository.updatePreeNum(jdhInventory);

        //释放redis库存
        InventoryOpCmd inventoryOpCmd = InventoryOpCmd.builder()
                .storeId(jdhInventory.getAngelStationId() + "")
                .inventoryChannelNo(modifyInventoryContext.getAfterInventoryChannelNo())
                .inventoryCountOpType(InventoryCountOpType.ADD)
                .opCount(modifyInventoryContext.getModifyReemptionNum())
                .day(jdhInventory.getScheduleDay())
                .timeSlot(jdhInventory.getScheduleTime())
                .cacheExpirationDay((int) ChronoUnit.DAYS.between(LocalDate.parse(jdhInventory.getScheduleDay(), DateTimeFormatter.ofPattern(CommonConstant.YMD)), LocalDate.now()) + 2)
                .build();
        angelStationInventoryHelper.inventoryOp(inventoryOpCmd);

        return resultPreRecordList;
    }

    /**
     * 释放-服务站库存
     * @param releaseInventoryContext
     * @return
     */
    @Override
    @LogAndAlarm
    public Boolean releaseInventory(ReleaseInventoryContext releaseInventoryContext) {
        ReleaseInventoryRepositoryCmd releaseInventoryRepositoryCmd = AngelStationInventoryConverter.INS.convertToReleaseInventoryRepositoryCmd(releaseInventoryContext);
        //查询库存单记录信息-唯一
        JdhInventoryPreemptionRecord result;
        if(releaseInventoryRepositoryCmd.getInventoryId()!=null){
            result = jdhInventoryPreemptionRecordRepository.findByPreeRecordId(releaseInventoryRepositoryCmd.getInventoryId());
        }else{
            result = jdhInventoryPreemptionRecordRepository.findByBusinessId(releaseInventoryRepositoryCmd.getBusinessId(),
                    releaseInventoryRepositoryCmd.getBusinessType(),
                    Collections.singletonList(AngelStationInventoryStatusEnum.RELEASE.getType()),
                    releaseInventoryContext.getInventoryChannelNo());
        }
        if(result==null){
            log.info("AngelStationInventoryQueryDomainServiceImpl.releaseInventory result为空!!!");
            return true;
        }

        //校验服务站服务资源类型必须包含骑手
//        JdhStation jdhStation = jdhStationRepository.find(JdhStationIdentifier.builder().angelStationId(result.getAngelStationId()).build());
//        AssertUtils.nonNull(jdhStation,"服务站不存在!!!");
//        if(jdhStation.getAngelType()==null){
//            //旧数据放行
//            return true;
//        }
//        //服务站类型不包含骑手,直接放行
//        if(!AngelTypeEnum.DELIVERY.getType().equals(jdhStation.getAngelType()&AngelTypeEnum.DELIVERY.getType())){
//            return true;
//        }

        releaseInventoryRepositoryCmd.setInventoryId(result.getPreeRecordId());
        //修改库存预占记录状态
        int num = jdhInventoryPreemptionRecordRepository.releaseInventory(releaseInventoryRepositoryCmd);
        if(num<1){
            log.info("AngelStationInventoryQueryDomainServiceImpl.releaseInventory 释放库存失败1!!!");
            throw new BusinessException(AngelErrorCode.ANGEL_INVENTORY_CHECK_OP_PARAM_ERROR_MESSAGE.formatDescription("释放库存失败1!!!"));
        }

        //不限制库存
        if(result.getCurInventoryNum().equals(CommonConstant.SUPPER_INVENTORY_NUM)) {
            return true;
        }

        //库存数量表释放库存
        JdhInventory jdhInventory = jdhInventoryRepository.find(JdhInventoryIdentifier.builder().inventoryId(result.getInventoryId()).build());
        jdhInventory.setPreeNum(result.getPreemptionNum());
        jdhInventory.setUpdateUser(releaseInventoryContext.getPin());
        jdhInventoryRepository.subtractPreeNum(jdhInventory);
        //释放redis库存
        InventoryOpCmd inventoryOpCmd = InventoryOpCmd.builder()
                .storeId(jdhInventory.getAngelStationId()+"")
                .inventoryChannelNo(result.getInventoryChannelNo())
                .inventoryCountOpType(InventoryCountOpType.ADD)
                .opCount(result.getPreemptionNum())
                .day(jdhInventory.getScheduleDay())
                .timeSlot(jdhInventory.getScheduleTime())
                .cacheExpirationDay((int) ChronoUnit.DAYS.between(LocalDate.parse(jdhInventory.getScheduleDay(), DateTimeFormatter.ofPattern(CommonConstant.YMD)),LocalDate.now())+2)
                .build();
        Boolean opResult = angelStationInventoryHelper.inventoryOp(inventoryOpCmd);
        if(!opResult){
            log.info("AngelStationInventoryQueryDomainServiceImpl.releaseInventory 释放库存失败2!!!");
            throw new BusinessException(AngelErrorCode.ANGEL_INVENTORY_CHECK_OP_PARAM_ERROR_MESSAGE.formatDescription("释放库存失败2!!!"));
        }
        return true;
    }

    /**
     * 扣减-服务站库存-维护状态和时间
     * @param reduceInventoryContext
     * @return
     */
    @Override
    @LogAndAlarm
    public Boolean reduceInventory(ReduceInventoryContext reduceInventoryContext) {
        ReduceInventoryRepositoryCmd reduceInventoryRepositoryCmd = AngelStationInventoryConverter.INS.convertToReduceInventoryRepositoryCmd(reduceInventoryContext);
        boolean result = false;

        //走新逻辑
        result = jdhInventoryPreemptionRecordRepository.reduceInventory(reduceInventoryRepositoryCmd)>0;

        if(!result){
            log.info("AngelStationInventoryQueryDomainServiceImpl.reduceInventory 扣减库存失败!!!");
            throw new BusinessException(AngelErrorCode.ANGEL_INVENTORY_CHECK_OP_PARAM_ERROR_MESSAGE.formatDescription("扣减库存失败!!!"));
        }
        return true;
    }

    /**
     * 更新站点库存基础数量
     *
     * @param jdhInventorySaveContext
     * @param jdhStation
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateAngelStationBaseNum(JdhInventorySaveContext jdhInventorySaveContext, JdhStation jdhStation) {
        //当前日期
        LocalDate localDate = LocalDate.now();
        String targetDay = TimeUtils.dateTimeToStr(new Date(), TimeFormat.SHORT_PATTERN_LINE);

        JdhInventoryQuery jdhInventoryQuery = new JdhInventoryQuery();
        jdhInventoryQuery.setAngelStationId(jdhInventorySaveContext.getAngelStationId());
        jdhInventoryQuery.setInventoryChannelNo(jdhInventorySaveContext.getInventoryChannelNo());
        jdhInventoryQuery.setActiveScheduleDay(targetDay);
        List<JdhInventory> jdhInventoryList = jdhInventoryRepository.findList(jdhInventoryQuery);
        if(CollectionUtils.isEmpty(jdhInventoryList)) {
            log.error("[AngelStationInventoryDomainServiceImpl -> updateAngelStationBaseNum],无有效的库存数据!");
            jdhInventoryList = Lists.newArrayList();
        }
        InventoryInitConfig scheduleTimeSlice = duccConfig.getScheduleTimeSlice();
        Integer initDays = scheduleTimeSlice.getInitDays();
        Set<String> scheduleDaySet = jdhInventoryList.stream().map(JdhInventory::getScheduleDay).collect(Collectors.toSet());
        do {
            log.info("AngelStationInventoryDomainServiceImpl -> updateAngelStationBaseNum,localDate={},scheduleDaySet={}", TimeUtils.localDateToStr(localDate), JSON.toJSONString(scheduleDaySet));
            boolean contains = scheduleDaySet.contains(TimeUtils.localDateToStr(localDate));
            log.info("AngelStationInventoryDomainServiceImpl -> updateAngelStationBaseNum,contains={}", contains);
            //初始化库存
            if(!contains) {
                rollJdhStationInventory(Lists.newArrayList(jdhStation), localDate, false, jdhInventorySaveContext.getInventoryChannelNo());
            }
            localDate = localDate.plusDays(CommonConstant.ONE);
        }while (--initDays > 0);

        //更新缓存
        if(CollectionUtils.isEmpty(jdhInventoryList)) {
            return true;
        }

        //更改库存
        JdhInventoryModifyCmd jdhInventoryModifyCmd = new JdhInventoryModifyCmd();
        jdhInventoryModifyCmd.setOperator(jdhInventorySaveContext.getOperator());
        jdhInventoryModifyCmd.setBaseNum(jdhInventorySaveContext.getBaseNum());
        jdhInventoryModifyCmd.setAngelStationId(jdhInventorySaveContext.getAngelStationId());
        jdhInventoryModifyCmd.setInventoryChannelNo(jdhInventorySaveContext.getInventoryChannelNo());
        jdhInventoryModifyCmd.setValidScheduleDay(targetDay);
        jdhInventoryRepository.updateInventory(jdhInventoryModifyCmd);

        JdhInventory jdhInventory = jdhInventoryList.get(0);
        int addNum = jdhInventorySaveContext.getBaseNum() - jdhInventory.getBaseNum();
        log.info("[AngelStationInventoryDomainServiceImpl -> updateAngelStationBaseNum],targetDay={}",targetDay);
        List<JdhInventory> collect = jdhInventoryList.stream().filter(
                item -> LocalDate.parse(item.getScheduleDay(), DateTimeFormatter.ofPattern("yyyy-MM-dd")).isEqual(LocalDate.now())
                        || LocalDate.parse(item.getScheduleDay(), DateTimeFormatter.ofPattern("yyyy-MM-dd")).isAfter(LocalDate.now())
                ).collect(Collectors.toList());
        log.info("[AngelStationInventoryDomainServiceImpl -> updateAngelStationBaseNum],jdhInventoryStream={}",JSON.toJSONString(collect));
        collect.forEach(inventory -> {
            log.info("[AngelStationInventoryDomainServiceImpl -> updateAngelStationBaseNum],inventory={}",JSON.toJSONString(inventory));
            InventoryOpCmd inventoryOpCmd = InventoryOpCmd.builder()
                    .inventoryCountOpType(InventoryCountOpType.B_ADD)
                    .opCount(addNum)
                    .day(inventory.getScheduleDay())
                    .timeSlot(inventory.getScheduleTime())
                    .storeId(String.valueOf(inventory.getAngelStationId()))
                    .inventoryChannelNo(jdhInventorySaveContext.getInventoryChannelNo())
                    .build();
            angelStationInventoryHelper.inventoryOp(inventoryOpCmd);
        });

        return true;
    }

    /**
     * 更新站点异常调仓数量和时间都发生变化
     *
     * @param jdhInventoryReadjustContextList
     * @param removeReadjustIdList
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveAngelStationTimeNumReadjust(List<JdhInventoryReadjustContext> jdhInventoryReadjustContextList, List<Long> timeAndNum, List<Long> removeReadjustIdList) {
        log.info("[AngelStationInventoryDomainServiceImpl -> saveAngelStationTimeNumReadjust]!jdhInventoryReadjustContextList={},timeAndNum={},removeReadjustIdList={}",JSON.toJSONString(jdhInventoryReadjustContextList),JSON.toJSONString(timeAndNum),JSON.toJSONString(removeReadjustIdList));
        if(CollectionUtils.isEmpty(timeAndNum)) {
            log.info("[AngelStationInventoryDomainServiceImpl -> saveAngelStationTimeNumReadjust],没有修改的服务站!");
            return true;
        }
        Map<Long, JdhInventoryReadjustContext> readjustContextMap = jdhInventoryReadjustContextList.stream()
                .filter(item -> timeAndNum.contains(item.getReadjustRecordId()))
                .collect(Collectors.toMap(JdhInventoryReadjustContext::getReadjustRecordId, Function.identity(), (k1, k2) -> k1));
        log.info("[AngelStationInventoryDomainServiceImpl -> saveAngelStationTimeNumReadjust],没有修改的服务站!readjustContextMap={}", JSON.toJSONString(readjustContextMap));
        JdhInventoryReadjustQuery query = JdhInventoryReadjustQuery.builder().inventoryReadjustIdSet(Sets.newHashSet(timeAndNum)).build();
        List<JdhInventoryReadjustRecord> readjustRecordList = jdhInventoryReadjustRecordRepository.findList(query);
        Map<Long, JdhInventoryReadjustRecord> readjustRecordMap = readjustRecordList.stream().collect(Collectors.toMap(JdhInventoryReadjustRecord::getReadjustRecordId, Function.identity(), (k1, k2) -> k1));

        List<InventoryNumProcessBo> collectionRecordList = Lists.newArrayList();

        try {
            Iterator<Long> iterator = timeAndNum.iterator();
            while(iterator.hasNext()){
                Long item = iterator.next();
                JdhInventoryReadjustContext readjustContext = readjustContextMap.get(item);
                JdhInventoryReadjustRecord readjustRecord = readjustRecordMap.get(item);
                //取出时间交集
                LocalDateTime startDate = TimeUtils.dateToLocalDateTime(readjustContext.getStartDate());
                LocalDateTime endDate = TimeUtils.dateToLocalDateTime(readjustContext.getEndDate());
                TimeIntervalIntersection.DateTimeInterval contextDateTimeInterval = new TimeIntervalIntersection.DateTimeInterval(startDate, endDate);

                LocalDateTime readjustStartTime = TimeUtils.dateToLocalDateTime(readjustRecord.getReadjustStartTime());
                LocalDateTime readjustEndTime = TimeUtils.dateToLocalDateTime(readjustRecord.getReadjustEndTime());
                TimeIntervalIntersection.DateTimeInterval recordDateTimeInterval = new TimeIntervalIntersection.DateTimeInterval(readjustStartTime, readjustEndTime);

                Optional<TimeIntervalIntersection.DateTimeInterval> dateTimeInterval = TimeIntervalIntersection.dateTimeIntersection(Lists.newArrayList(contextDateTimeInterval, recordDateTimeInterval));
                //没有交集等于是删除了原来的新增了一个新的
                if(dateTimeInterval.isPresent()){
                    TimeIntervalIntersection.DateTimeInterval processTimeInterval = dateTimeInterval.get();
                    if(startDate.isBefore(processTimeInterval.getStartDateTime())) {
                        InventoryNumProcessBo inventoryNumProcessBo = new InventoryNumProcessBo();
                        inventoryNumProcessBo.setProcessStart(TimeUtils.localDateTimeToDate(startDate));
                        inventoryNumProcessBo.setProcessEnd(TimeUtils.localDateTimeToDate(processTimeInterval.getStartDateTime()));
                        inventoryNumProcessBo.setAngelStationId(readjustRecord.getAngelStationId());
                        inventoryNumProcessBo.setBaseNum(readjustContext.getBaseNum());
                        inventoryNumProcessBo.setReadjustNum(readjustContext.getReadjustNum());
                        inventoryNumProcessBo.setChangeNum(readjustContext.getReadjustNum() - readjustContext.getBaseNum());
                        inventoryNumProcessBo.setOperator(readjustContext.getOperator());
                        collectionRecordList.add(inventoryNumProcessBo);
                    }

                    if(readjustStartTime.isBefore(processTimeInterval.getStartDateTime())) {
                        InventoryNumProcessBo inventoryNumProcessBo = new InventoryNumProcessBo();
                        inventoryNumProcessBo.setProcessStart(TimeUtils.localDateTimeToDate(readjustStartTime));
                        inventoryNumProcessBo.setProcessEnd(TimeUtils.localDateTimeToDate(processTimeInterval.getStartDateTime()));
                        inventoryNumProcessBo.setAngelStationId(readjustRecord.getAngelStationId());
                        inventoryNumProcessBo.setBaseNum(readjustContext.getBaseNum());
                        inventoryNumProcessBo.setReadjustNum(readjustContext.getReadjustNum());
                        inventoryNumProcessBo.setChangeNum(readjustRecord.getBaseNum() - readjustRecord.getReadjustNum());
                        inventoryNumProcessBo.setOperator(readjustContext.getOperator());
                        collectionRecordList.add(inventoryNumProcessBo);
                    }

                    if(endDate.isAfter(processTimeInterval.getEndDateTime())) {
                        InventoryNumProcessBo inventoryNumProcessBo = new InventoryNumProcessBo();
                        inventoryNumProcessBo.setProcessStart(TimeUtils.localDateTimeToDate(processTimeInterval.getEndDateTime()));
                        inventoryNumProcessBo.setProcessEnd(TimeUtils.localDateTimeToDate(endDate));
                        inventoryNumProcessBo.setAngelStationId(readjustRecord.getAngelStationId());
                        inventoryNumProcessBo.setBaseNum(readjustContext.getBaseNum());
                        inventoryNumProcessBo.setReadjustNum(readjustContext.getReadjustNum());
                        inventoryNumProcessBo.setChangeNum(readjustContext.getReadjustNum() - readjustContext.getBaseNum());
                        inventoryNumProcessBo.setOperator(readjustContext.getOperator());
                        collectionRecordList.add(inventoryNumProcessBo);
                    }

                    if(readjustEndTime.isAfter(processTimeInterval.getEndDateTime())) {
                        InventoryNumProcessBo inventoryNumProcessBo = new InventoryNumProcessBo();
                        inventoryNumProcessBo.setProcessStart(TimeUtils.localDateTimeToDate(processTimeInterval.getEndDateTime()));
                        inventoryNumProcessBo.setProcessEnd(TimeUtils.localDateTimeToDate(readjustEndTime));
                        inventoryNumProcessBo.setAngelStationId(readjustRecord.getAngelStationId());
                        inventoryNumProcessBo.setBaseNum(readjustContext.getBaseNum());
                        inventoryNumProcessBo.setReadjustNum(readjustContext.getReadjustNum());
                        inventoryNumProcessBo.setChangeNum(readjustRecord.getBaseNum() - readjustRecord.getReadjustNum());
                        inventoryNumProcessBo.setOperator(readjustContext.getOperator());
                        collectionRecordList.add(inventoryNumProcessBo);
                    }
                    InventoryNumProcessBo inventoryNumProcessBo = new InventoryNumProcessBo();
                    inventoryNumProcessBo.setProcessStart(TimeUtils.localDateTimeToDate(processTimeInterval.getStartDateTime()));
                    inventoryNumProcessBo.setProcessEnd(TimeUtils.localDateTimeToDate(processTimeInterval.getEndDateTime()));
                    inventoryNumProcessBo.setAngelStationId(readjustRecord.getAngelStationId());
                    inventoryNumProcessBo.setBaseNum(readjustContext.getBaseNum());
                    inventoryNumProcessBo.setReadjustNum(readjustContext.getReadjustNum());
                    inventoryNumProcessBo.setChangeNum(readjustContext.getReadjustNum() - readjustContext.getBaseNum() - readjustRecord.getReadjustNum() + readjustRecord.getBaseNum() );
                    inventoryNumProcessBo.setOperator(readjustContext.getOperator());
                    collectionRecordList.add(inventoryNumProcessBo);
                }else {
                    InventoryNumProcessBo inventoryNumProcessBo = new InventoryNumProcessBo();
                    inventoryNumProcessBo.setProcessStart(TimeUtils.localDateTimeToDate(readjustStartTime));
                    inventoryNumProcessBo.setProcessEnd(TimeUtils.localDateTimeToDate(readjustEndTime));
                    inventoryNumProcessBo.setAngelStationId(readjustRecord.getAngelStationId());
                    inventoryNumProcessBo.setBaseNum(readjustContext.getBaseNum());
                    inventoryNumProcessBo.setReadjustNum(readjustContext.getReadjustNum());
                    inventoryNumProcessBo.setChangeNum(readjustRecord.getBaseNum() - readjustRecord.getReadjustNum());
                    inventoryNumProcessBo.setOperator(readjustContext.getOperator());
                    collectionRecordList.add(inventoryNumProcessBo);

                    InventoryNumProcessBo inventoryNumProcessNewBo = new InventoryNumProcessBo();
                    inventoryNumProcessNewBo.setProcessStart(TimeUtils.localDateTimeToDate(startDate));
                    inventoryNumProcessNewBo.setProcessEnd(TimeUtils.localDateTimeToDate(endDate));
                    inventoryNumProcessNewBo.setAngelStationId(readjustRecord.getAngelStationId());
                    inventoryNumProcessNewBo.setBaseNum(readjustContext.getBaseNum());
                    inventoryNumProcessNewBo.setReadjustNum(readjustContext.getReadjustNum());
                    inventoryNumProcessNewBo.setChangeNum(readjustContext.getReadjustNum() - readjustContext.getBaseNum());
                    inventoryNumProcessNewBo.setOperator(readjustContext.getOperator());
                    collectionRecordList.add(inventoryNumProcessNewBo);
                }
            }
        }catch (Exception ex) {
            log.error("[AngelStationInventoryDomainServiceImpl -> saveAngelStationReadjust],处理更新了时间和库存异常调仓数据异常!", ex);
            throw new BusinessException(AngelErrorCode.INVENTORY_READJUST_HANDLE_ERROR);
        }
        if(CollectionUtils.isEmpty(collectionRecordList)) {
            log.info("[AngelStationInventoryDomainServiceImpl -> saveAngelStationReadjust],没有需要处理的异常调仓数据!");
            return true;
        }

        List<JdhInventoryReadjustContext> readjustContextList = readjustContextMap.values().stream().collect(Collectors.toList());
        List<JdhInventoryReadjustRecord> readjustRecordNewList = JdhInventoryReadjustFactory.createReadjustRecordList(readjustContextList);
        jdhInventoryReadjustRecordRepository.batchInsert(readjustRecordNewList);
//        jdhInventoryReadjustRecordRepository.batchRemove(timeAndNum);

        saveInventoryAndCache(collectionRecordList, InventoryChannelEnum.RIDER_CHANNEL.getInventoryChannel());
        return true;
    }

    /**
     * 站点异常调仓时间发生变化
     *
     * @param jdhInventoryReadjustContextList
     * @param timeModifyReadjustIdList
     * @param removeReadjustIdList
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveAngelStationTimeReadjust(List<JdhInventoryReadjustContext> jdhInventoryReadjustContextList, List<Long> timeModifyReadjustIdList, List<Long> removeReadjustIdList) {
        log.info("[AngelStationInventoryDomainServiceImpl -> saveAngelStationTimeReadjust]!jdhInventoryReadjustContextList={},timeModifyReadjustIdList={},removeReadjustIdList={}",JSON.toJSONString(jdhInventoryReadjustContextList),JSON.toJSONString(timeModifyReadjustIdList),JSON.toJSONString(removeReadjustIdList));
        if(CollectionUtils.isEmpty(timeModifyReadjustIdList)){
            return true;
        }
        Map<Long, JdhInventoryReadjustContext> readjustContextMap = jdhInventoryReadjustContextList.stream()
                .filter(item -> timeModifyReadjustIdList.contains(item.getReadjustRecordId()))
                .collect(Collectors.toMap(JdhInventoryReadjustContext::getReadjustRecordId, Function.identity(), (k1, k2) -> k1));
        log.info("[AngelStationInventoryDomainServiceImpl -> saveAngelStationTimeReadjust],服务站新增异常库存调仓!readjustContextMap={}",JSON.toJSONString(readjustContextMap));
        JdhInventoryReadjustQuery query = JdhInventoryReadjustQuery.builder().inventoryReadjustIdSet(Sets.newHashSet(timeModifyReadjustIdList)).build();
        List<JdhInventoryReadjustRecord> readjustRecordList = jdhInventoryReadjustRecordRepository.findList(query);
        Map<Long, JdhInventoryReadjustRecord> readjustRecordMap = readjustRecordList.stream().collect(Collectors.toMap(JdhInventoryReadjustRecord::getReadjustRecordId, Function.identity(), (k1, k2) -> k1));

        List<InventoryNumProcessBo> collectionRecordList = Lists.newArrayList();

        //处理时间和数量都发生变化的异常库存
        try {
            Iterator<Long> iterator = timeModifyReadjustIdList.iterator();
            while(iterator.hasNext()){
                Long item = iterator.next();
                JdhInventoryReadjustContext readjustContext = readjustContextMap.get(item);
                JdhInventoryReadjustRecord readjustRecord = readjustRecordMap.get(item);
                //取出时间交集
                LocalDateTime startDate = TimeUtils.dateToLocalDateTime(readjustContext.getStartDate());
                LocalDateTime endDate = TimeUtils.dateToLocalDateTime(readjustContext.getEndDate());;
                TimeIntervalIntersection.DateTimeInterval contextDateTimeInterval = new TimeIntervalIntersection.DateTimeInterval(startDate, endDate);

                LocalDateTime readjustStartTime = TimeUtils.dateToLocalDateTime(readjustRecord.getReadjustStartTime());
                LocalDateTime readjustEndTime = TimeUtils.dateToLocalDateTime(readjustRecord.getReadjustEndTime());
                TimeIntervalIntersection.DateTimeInterval recordDateTimeInterval = new TimeIntervalIntersection.DateTimeInterval(readjustStartTime, readjustEndTime);

                Optional<TimeIntervalIntersection.DateTimeInterval> dateTimeInterval = TimeIntervalIntersection.dateTimeIntersection(Lists.newArrayList(contextDateTimeInterval, recordDateTimeInterval));

                //没有交集等于是删除了原来的新增了一个新的
                if(dateTimeInterval.isPresent()){
                    TimeIntervalIntersection.DateTimeInterval processTimeInterval = dateTimeInterval.get();
                    if(startDate.isBefore(processTimeInterval.getStartDateTime())) {
                        InventoryNumProcessBo inventoryNumProcessBo = new InventoryNumProcessBo();
                        inventoryNumProcessBo.setProcessStart(TimeUtils.localDateTimeToDate(startDate));
                        inventoryNumProcessBo.setProcessEnd(TimeUtils.localDateTimeToDate(processTimeInterval.getStartDateTime()));
                        inventoryNumProcessBo.setAngelStationId(readjustRecord.getAngelStationId());
                        inventoryNumProcessBo.setBaseNum(readjustContext.getBaseNum());
                        inventoryNumProcessBo.setReadjustNum(readjustContext.getReadjustNum());
                        inventoryNumProcessBo.setChangeNum(readjustContext.getReadjustNum() - readjustContext.getBaseNum());
                        inventoryNumProcessBo.setOperator(readjustContext.getOperator());
                        collectionRecordList.add(inventoryNumProcessBo);
                    }

                    if(readjustStartTime.isBefore(processTimeInterval.getStartDateTime())) {
                        InventoryNumProcessBo inventoryNumProcessBo = new InventoryNumProcessBo();
                        inventoryNumProcessBo.setProcessStart(TimeUtils.localDateTimeToDate(readjustStartTime));
                        inventoryNumProcessBo.setProcessEnd(TimeUtils.localDateTimeToDate(processTimeInterval.getStartDateTime()));
                        inventoryNumProcessBo.setAngelStationId(readjustRecord.getAngelStationId());
                        inventoryNumProcessBo.setBaseNum(readjustContext.getBaseNum());
                        inventoryNumProcessBo.setReadjustNum(readjustContext.getReadjustNum());
                        inventoryNumProcessBo.setChangeNum(readjustRecord.getBaseNum() - readjustRecord.getReadjustNum());
                        inventoryNumProcessBo.setOperator(readjustContext.getOperator());
                        collectionRecordList.add(inventoryNumProcessBo);
                    }

                    if(endDate.isAfter(processTimeInterval.getEndDateTime())) {
                        InventoryNumProcessBo inventoryNumProcessBo = new InventoryNumProcessBo();
                        inventoryNumProcessBo.setProcessStart(TimeUtils.localDateTimeToDate(processTimeInterval.getEndDateTime()));
                        inventoryNumProcessBo.setProcessEnd(TimeUtils.localDateTimeToDate(endDate));
                        inventoryNumProcessBo.setAngelStationId(readjustRecord.getAngelStationId());
                        inventoryNumProcessBo.setBaseNum(readjustContext.getBaseNum());
                        inventoryNumProcessBo.setReadjustNum(readjustContext.getReadjustNum());
                        inventoryNumProcessBo.setChangeNum(readjustContext.getReadjustNum() - readjustContext.getBaseNum());
                        inventoryNumProcessBo.setOperator(readjustContext.getOperator());
                        collectionRecordList.add(inventoryNumProcessBo);
                    }

                    if(readjustEndTime.isAfter(processTimeInterval.getEndDateTime())) {
                        InventoryNumProcessBo inventoryNumProcessBo = new InventoryNumProcessBo();
                        inventoryNumProcessBo.setProcessStart(TimeUtils.localDateTimeToDate(processTimeInterval.getEndDateTime()));
                        inventoryNumProcessBo.setProcessEnd(TimeUtils.localDateTimeToDate(readjustEndTime));
                        inventoryNumProcessBo.setAngelStationId(readjustRecord.getAngelStationId());
                        inventoryNumProcessBo.setBaseNum(readjustContext.getBaseNum());
                        inventoryNumProcessBo.setReadjustNum(readjustContext.getReadjustNum());
                        inventoryNumProcessBo.setChangeNum(readjustRecord.getBaseNum() - readjustRecord.getReadjustNum());
                        inventoryNumProcessBo.setOperator(readjustContext.getOperator());
                        collectionRecordList.add(inventoryNumProcessBo);
                    }
                }else {
                    InventoryNumProcessBo inventoryNumProcessBo = new InventoryNumProcessBo();
                    inventoryNumProcessBo.setProcessStart(TimeUtils.localDateTimeToDate(readjustStartTime));
                    inventoryNumProcessBo.setProcessEnd(TimeUtils.localDateTimeToDate(readjustEndTime));
                    inventoryNumProcessBo.setAngelStationId(readjustRecord.getAngelStationId());
                    inventoryNumProcessBo.setBaseNum(readjustContext.getBaseNum());
                    inventoryNumProcessBo.setReadjustNum(readjustContext.getReadjustNum());
                    inventoryNumProcessBo.setChangeNum(readjustRecord.getBaseNum() - readjustRecord.getReadjustNum());
                    inventoryNumProcessBo.setOperator(readjustContext.getOperator());
                    collectionRecordList.add(inventoryNumProcessBo);

                    InventoryNumProcessBo inventoryNumProcessNewBo = new InventoryNumProcessBo();
                    inventoryNumProcessNewBo.setProcessStart(TimeUtils.localDateTimeToDate(startDate));
                    inventoryNumProcessNewBo.setProcessEnd(TimeUtils.localDateTimeToDate(endDate));
                    inventoryNumProcessNewBo.setAngelStationId(readjustRecord.getAngelStationId());
                    inventoryNumProcessNewBo.setBaseNum(readjustContext.getBaseNum());
                    inventoryNumProcessNewBo.setReadjustNum(readjustContext.getReadjustNum());
                    inventoryNumProcessNewBo.setChangeNum(readjustContext.getReadjustNum() - readjustContext.getBaseNum());
                    inventoryNumProcessNewBo.setOperator(readjustContext.getOperator());
                    collectionRecordList.add(inventoryNumProcessNewBo);
                }
            }
        }catch (Exception ex) {
            log.error("[AngelStationInventoryDomainServiceImpl -> saveAngelStationTimeReadjust],处理更新了时间和库存异常调仓数据异常!", ex);
            throw new BusinessException(AngelErrorCode.INVENTORY_READJUST_HANDLE_ERROR);
        }

        if(CollectionUtils.isEmpty(collectionRecordList)) {
            log.info("[AngelStationInventoryDomainServiceImpl -> saveAngelStationTimeReadjust],没有需要处理的异常调仓数据!");
            return true;
        }

        List<JdhInventoryReadjustContext> readjustContextList = readjustContextMap.values().stream().collect(Collectors.toList());
        List<JdhInventoryReadjustRecord> readjustRecordNewList = JdhInventoryReadjustFactory.createReadjustRecordList(readjustContextList);
        jdhInventoryReadjustRecordRepository.batchInsert(readjustRecordNewList);
//        jdhInventoryReadjustRecordRepository.batchRemove(timeModifyReadjustIdList);

        saveInventoryAndCache(collectionRecordList, InventoryChannelEnum.RIDER_CHANNEL.getInventoryChannel());
        return true;
    }

    /**
     * 站点异常调仓数量发生变化
     *
     * @param jdhInventoryReadjustContextList
     * @param numModifyReadjustIdList
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveAngelStationNumReadjust(List<JdhInventoryReadjustContext> jdhInventoryReadjustContextList, List<Long> numModifyReadjustIdList) {
        log.info("[AngelStationInventoryDomainServiceImpl -> saveAngelStationNumReadjust]!jdhInventoryReadjustContextList={},numModifyReadjustIdList={}",JSON.toJSONString(jdhInventoryReadjustContextList),JSON.toJSONString(numModifyReadjustIdList));
        if(CollectionUtils.isEmpty(numModifyReadjustIdList)){
            return true;
        }
        Map<Long, JdhInventoryReadjustContext> readjustContextMap = jdhInventoryReadjustContextList.stream()
                .filter(item -> numModifyReadjustIdList.contains(item.getReadjustRecordId()))
                .collect(Collectors.toMap(JdhInventoryReadjustContext::getReadjustRecordId, Function.identity(), (k1, k2) -> k1));
        log.info("[AngelStationInventoryDomainServiceImpl -> saveAngelStationNumReadjust],服务站新增异常库存调仓!readjustContextMap={}",JSON.toJSONString(readjustContextMap));
        JdhInventoryReadjustQuery query = JdhInventoryReadjustQuery.builder().inventoryReadjustIdSet(Sets.newHashSet(numModifyReadjustIdList)).build();
        List<JdhInventoryReadjustRecord> readjustRecordList = jdhInventoryReadjustRecordRepository.findList(query);
        Map<Long, JdhInventoryReadjustRecord> readjustRecordMap = readjustRecordList.stream().collect(Collectors.toMap(JdhInventoryReadjustRecord::getReadjustRecordId, Function.identity(), (k1, k2) -> k1));

        List<InventoryNumProcessBo> collectionRecordList = Lists.newArrayList();

        //处理数量发生变化的异常库存
        try {
            Iterator<Long> iterator = numModifyReadjustIdList.iterator();
            while(iterator.hasNext()){
                Long item = iterator.next();
                JdhInventoryReadjustContext readjustContext = readjustContextMap.get(item);
                JdhInventoryReadjustRecord readjustRecord = readjustRecordMap.get(item);

                InventoryNumProcessBo inventoryNumProcessBo = new InventoryNumProcessBo();
                inventoryNumProcessBo.setProcessStart(readjustContext.getStartDate());
                inventoryNumProcessBo.setProcessEnd(readjustContext.getEndDate());
                inventoryNumProcessBo.setAngelStationId(readjustRecord.getAngelStationId());
                inventoryNumProcessBo.setBaseNum(readjustContext.getBaseNum());
                inventoryNumProcessBo.setReadjustNum(readjustContext.getReadjustNum());
                inventoryNumProcessBo.setChangeNum(readjustContext.getReadjustNum() - readjustContext.getBaseNum() - readjustRecord.getReadjustNum() + readjustRecord.getBaseNum() );
                inventoryNumProcessBo.setOperator(readjustContext.getOperator());
                collectionRecordList.add(inventoryNumProcessBo);
            }
        }catch (Exception ex) {
            log.error("[AngelStationInventoryDomainServiceImpl -> saveAngelStationTimeReadjust],处理更新了时间和库存异常调仓数据异常!", ex);
            throw new BusinessException(AngelErrorCode.INVENTORY_READJUST_HANDLE_ERROR);
        }

        if(CollectionUtils.isEmpty(collectionRecordList)) {
            log.info("[AngelStationInventoryDomainServiceImpl -> saveAngelStationTimeReadjust],没有需要处理的异常调仓数据!");
            return true;
        }

        List<JdhInventoryReadjustContext> readjustContextList = readjustContextMap.values().stream().collect(Collectors.toList());
        List<JdhInventoryReadjustRecord> readjustRecordNewList = JdhInventoryReadjustFactory.createReadjustRecordList(readjustContextList);
        jdhInventoryReadjustRecordRepository.batchInsert(readjustRecordNewList);
//        jdhInventoryReadjustRecordRepository.batchRemove(numModifyReadjustIdList);

        saveInventoryAndCache(collectionRecordList, InventoryChannelEnum.RIDER_CHANNEL.getInventoryChannel());
        return true;
    }

    /**
     * 新增站点异常调仓
     *
     * @param jdhInventoryReadjustContextList
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveAngelStationReadjust(List<JdhInventoryReadjustContext> jdhInventoryReadjustContextList) {
        log.info("[AngelStationInventoryDomainServiceImpl -> saveAngelStationReadjust]!jdhInventoryReadjustContextList={}",JSON.toJSONString(jdhInventoryReadjustContextList));
        List<JdhInventoryReadjustContext> readjustContextList = jdhInventoryReadjustContextList.stream()
                .filter(item -> Objects.isNull(item.getReadjustRecordId()))
                .collect(Collectors.toList());

        if(CollectionUtils.isEmpty(readjustContextList)){
            log.info("[AngelStationInventoryDomainServiceImpl -> saveAngelStationReadjust],服务站新增异常库存调仓!readjustContextList={}",JSON.toJSONString(readjustContextList));
            return true;
        }

        List<InventoryNumProcessBo> collectionRecordList = Lists.newArrayList();
        try {
            Iterator<JdhInventoryReadjustContext> iterator = readjustContextList.iterator();
            while(iterator.hasNext()){
                JdhInventoryReadjustContext readjustContext = iterator.next();

                InventoryNumProcessBo inventoryNumProcessBo = new InventoryNumProcessBo();
                inventoryNumProcessBo.setProcessStart(readjustContext.getStartDate());
                inventoryNumProcessBo.setProcessEnd(readjustContext.getEndDate());
                inventoryNumProcessBo.setAngelStationId(readjustContext.getAngelStationId());
                inventoryNumProcessBo.setBaseNum(readjustContext.getBaseNum());
                inventoryNumProcessBo.setReadjustNum(readjustContext.getReadjustNum() - readjustContext.getBaseNum());
                inventoryNumProcessBo.setChangeNum(readjustContext.getReadjustNum() - readjustContext.getBaseNum());
                inventoryNumProcessBo.setOperator(readjustContext.getOperator());
                collectionRecordList.add(inventoryNumProcessBo);
            }
        }catch (Exception ex) {
            log.error("[AngelStationInventoryDomainServiceImpl -> saveAngelStationReadjust],处理更新了时间和库存异常调仓数据异常!", ex);
            throw new BusinessException(AngelErrorCode.INVENTORY_READJUST_HANDLE_ERROR);
        }
        log.info("[AngelStationInventoryDomainServiceImpl -> saveAngelStationReadjust],开始新增异常调仓!collectionRecordList={}", JSON.toJSONString(collectionRecordList));
        if(CollectionUtils.isEmpty(collectionRecordList)) {
            log.info("[AngelStationInventoryDomainServiceImpl -> saveAngelStationReadjust],没有需要处理的异常调仓数据!");
            return true;
        }

        List<JdhInventoryReadjustRecord> readjustRecordNewList = JdhInventoryReadjustFactory.createReadjustRecordList(readjustContextList);
        jdhInventoryReadjustRecordRepository.batchInsert(readjustRecordNewList);

        saveInventoryAndCache(collectionRecordList, InventoryChannelEnum.RIDER_CHANNEL.getInventoryChannel());
        return true;
    }

    /**
     * 删除站点异常调仓数量
     *
     * @param jdhInventoryReadjustContextList
     * @param removeReadjustIdList
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean removeAngelStationReadjust(List<JdhInventoryReadjustContext> jdhInventoryReadjustContextList, List<Long> removeReadjustIdList) {
        log.info("[AngelStationInventoryDomainServiceImpl -> removeAngelStationReadjust]!jdhInventoryReadjustContextList={},removeReadjustIdList={}",JSON.toJSONString(jdhInventoryReadjustContextList), JSON.toJSONString(removeReadjustIdList));
        if(CollectionUtils.isEmpty(removeReadjustIdList)){
            return true;
        }
        JdhInventoryReadjustQuery query = JdhInventoryReadjustQuery.builder().inventoryReadjustIdSet(Sets.newHashSet(removeReadjustIdList)).build();
        List<JdhInventoryReadjustRecord> readjustRecordList = jdhInventoryReadjustRecordRepository.findList(query);
        List<InventoryNumProcessBo> collectionRecordList = Lists.newArrayList();
        try {
            Iterator<JdhInventoryReadjustRecord> iterator = readjustRecordList.iterator();
            while(iterator.hasNext()){
                JdhInventoryReadjustRecord item = iterator.next();

                InventoryNumProcessBo inventoryNumProcessBo = new InventoryNumProcessBo();
                inventoryNumProcessBo.setProcessStart(item.getReadjustStartTime());
                inventoryNumProcessBo.setProcessEnd(item.getReadjustEndTime());
                inventoryNumProcessBo.setAngelStationId(item.getAngelStationId());
                inventoryNumProcessBo.setBaseNum(item.getBaseNum());
                inventoryNumProcessBo.setReadjustNum(CommonConstant.ZERO);
                inventoryNumProcessBo.setChangeNum(item.getBaseNum() - item.getReadjustNum());
                inventoryNumProcessBo.setOperator(item.getUpdateUser());
                collectionRecordList.add(inventoryNumProcessBo);
            }
        }catch (Exception ex) {
            log.error("[AngelStationInventoryDomainServiceImpl -> removeAngelStationReadjust],处理更新了时间和库存异常调仓数据异常!", ex);
            throw new BusinessException(AngelErrorCode.INVENTORY_READJUST_HANDLE_ERROR);
        }

        if(CollectionUtils.isEmpty(collectionRecordList)) {
            log.info("[AngelStationInventoryDomainServiceImpl -> removeAngelStationReadjust],没有需要处理的异常调仓数据!");
            return true;
        }

        jdhInventoryReadjustRecordRepository.batchRemove(removeReadjustIdList);

        saveInventoryAndCache(collectionRecordList, InventoryChannelEnum.RIDER_CHANNEL.getInventoryChannel());
        return true;
    }

    /**
     * 库存打标
     *
     * @param angelStationInventoryFlagContext
     * @return
     */
    @Override
    public void handleInventoryFlag(AngelStationInventoryFlagContext angelStationInventoryFlagContext) {
        try{
            List<JdhAngelStationInfoB> jdhAngelStationInfoBs = angelStationInventoryFlagContext.getJdhAngelStationInfoBs();
            if(CollectionUtils.isEmpty(jdhAngelStationInfoBs)) {
                log.info("AngelStationInventoryDomainServiceImpl -> handleInventoryFlag, 服务站信息为空");
                return;
            }
            Iterator<JdhAngelStationInfoB> iterator = jdhAngelStationInfoBs.iterator();
            while(iterator.hasNext()) {
                JdhAngelStationInfoB jdhAngelStationInfoB = iterator.next();
                if(Objects.isNull(jdhAngelStationInfoB.getAngelStationId())) {
                    log.error("AngelStationInventoryDomainServiceImpl -> handleInventoryFlag, 服务站ID为空");
                    iterator.remove();
                    continue;
                }
                if(StringUtils.isBlank(jdhAngelStationInfoB.getStationName())) {
                    JdhStation jdhStation = jdhStationRepository.find(new JdhStationIdentifier(jdhAngelStationInfoB.getAngelStationId()));
                    jdhAngelStationInfoB.setAngelNum(jdhStation.getAngelNum());
                    jdhAngelStationInfoB.setNurseNum(jdhAngelStationInfoB.getNurseNum());
                    jdhAngelStationInfoB.setStationName(jdhStation.getStationName());
                    jdhAngelStationInfoB.setStationId(jdhStation.getStationId());
                    jdhAngelStationInfoB.setCloseHour(jdhStation.getCloseHour());
                    jdhAngelStationInfoB.setOpenHour(jdhStation.getOpenHour());
                }
            }
            GroovyScript<AngelStationInventoryFlagContext, Boolean> handler = groovyModuleExecutor.getInstance("angelStation_InventoryFlagHandler");
            boolean invoke = handler.invoke(angelStationInventoryFlagContext);
            log.info("AngelStationInventoryDomainServiceImpl -> handleInventoryFlag, 执行结果.invoke={}", invoke);
        }catch (Exception ex) {
            log.error("AngelStationInventoryDomainServiceImpl -> handleInventoryFlag, 库存打标失败，兜底限制库存", ex);
            angelStationInventoryFlagContext.getJdhAngelStationInfoBs().forEach(item -> {
                item.setInventoryFlag(InventoryFlagEnum.LIMIT_INVENTORY.getFlag());
            });
        }
    }

    /**
     * 更新缓存和数据库库存
     *
     * @param collectionRecordList
     * @param inventoryChannel
     */
    private void saveInventoryAndCache(List<InventoryNumProcessBo> collectionRecordList, Integer inventoryChannel) {
        for (InventoryNumProcessBo bo : collectionRecordList) {
            TimeIntervalIntersection.DateTimeInterval dateTimeInterval = new TimeIntervalIntersection.DateTimeInterval(TimeUtils.dateToLocalDateTime(bo.getProcessStart()), TimeUtils.dateToLocalDateTime(bo.getProcessEnd()), duccConfig.getScheduleTimeSlice().getIntervalMinutes());
            List<TimeIntervalIntersection.DateTimeInterval> dateTimeIntervalList = dateTimeInterval.split();
            log.info("[AngelStationInventoryDomainServiceImpl -> saveInventoryAndCache],分割时间结果!dateTimeIntervalList={}", JSON.toJSONString(dateTimeIntervalList));
            for (int i = 0; i < dateTimeIntervalList.size(); i++) {
                LocalDateTime startDateTime = dateTimeIntervalList.get(i).getStartDateTime();
                LocalDateTime endDateTime = dateTimeIntervalList.get(i).getEndDateTime();
                String start = startDateTime.format(DateTimeFormatter.ofPattern("HH:mm"));
                String end = endDateTime.format(DateTimeFormatter.ofPattern("HH:mm"));
                String timeSlot = MessageFormat.format("{0}-{1}", start, end);
                String day = dateTimeIntervalList.get(i).getStartDateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                JdhInventoryModifyCmd jdhInventoryModifyCmd = new JdhInventoryModifyCmd();
                jdhInventoryModifyCmd.setAngelStationId(bo.getAngelStationId());
                jdhInventoryModifyCmd.setScheduleDay(day);
                jdhInventoryModifyCmd.setScheduleTime(timeSlot);
                jdhInventoryModifyCmd.setOperator(bo.getOperator());
                jdhInventoryModifyCmd.setReadjustChangeNum(bo.getChangeNum());
                jdhInventoryModifyCmd.setInventoryChannelNo(inventoryChannel);

                JdhInventoryQuery jdhInventoryQuery = new JdhInventoryQuery();
                jdhInventoryQuery.setAngelStationId(bo.getAngelStationId());
                jdhInventoryQuery.setInventoryChannelNo(inventoryChannel);
                jdhInventoryQuery.setScheduleDaySet(Sets.newLinkedHashSet(day));
                jdhInventoryQuery.setScheduleTimeSet(Sets.newLinkedHashSet(timeSlot));
                List<JdhInventory> jdhInventoryList = jdhInventoryRepository.findList(jdhInventoryQuery);

                log.info("[AngelStationInventoryDomainServiceImpl -> saveInventoryAndCache],handle inventory!jdhInventoryModifyCmd={}", JSON.toJSONString(jdhInventoryModifyCmd));

                //不存在则新增
                if(CollectionUtils.isEmpty(jdhInventoryList)){
                    if(endDateTime.toLocalDate().isBefore(TimeUtils.getCurrentLocalDate())) {
                        log.info("[AngelStationInventoryDomainServiceImpl -> saveInventoryAndCache],小于当前时间处理endDateTime={}", JSON.toJSONString(endDateTime));
                        continue;
                    }
                    if(startDateTime.toLocalDate().isAfter(TimeUtils.getCurrentLocalDate().plusDays(duccConfig.getScheduleTimeSlice().getInitDays()))) {
                        log.info("[AngelStationInventoryDomainServiceImpl -> saveInventoryAndCache],大于当前时间处理endDateTime={}", JSON.toJSONString(endDateTime));
                        continue;
                    }
                    JdhInventory jdhInventory = JdhInventoryFactory.createInventoryByBo(bo, jdhInventoryModifyCmd);
                    jdhInventoryRepository.save(jdhInventory);

                    InventoryOpCmd inventoryOpCmd = InventoryOpCmd.builder()
                            .inventoryCountOpType(InventoryCountOpType.INIT)
                            .opCount(jdhInventory.getBaseNum() - jdhInventory.getPreeNum() + jdhInventory.getReadjustNum())
                            .day(day)
                            .timeSlot(timeSlot)
                            .storeId(String.valueOf(bo.getAngelStationId()))
                            .inventoryChannelNo(jdhInventoryModifyCmd.getInventoryChannelNo())
                            .build();
                    angelStationInventoryHelper.inventoryOp(inventoryOpCmd);
                    continue;
                }

                //存在则修改
                int updateSize = jdhInventoryRepository.updateInventory(jdhInventoryModifyCmd);
                if(updateSize <= CommonConstant.ZERO) {
                    log.error("[AngelStationInventoryDomainServiceImpl -> saveAngelStationTimeReadjust],更新数据库失败!jdhInventoryModifyCmd={}, updateSize={}", JSON.toJSONString(jdhInventoryModifyCmd), updateSize);
                    continue;
                }
                InventoryOpCmd inventoryOpCmd = InventoryOpCmd.builder()
                        .inventoryCountOpType(InventoryCountOpType.ADD)
                        .opCount(bo.getChangeNum())
                        .day(day)
                        .timeSlot(timeSlot)
                        .storeId(String.valueOf(bo.getAngelStationId()))
                        .inventoryChannelNo(jdhInventoryModifyCmd.getInventoryChannelNo())
                        .build();
                angelStationInventoryHelper.inventoryOp(inventoryOpCmd);
            }
        }
    }

    /**
     * 查询服务站实时库存
     * @param angelStationId
     * @param scheduleDay
     * @return
     */
    @LogAndAlarm
    private List<TimeSlotInventory> getTimeSlotInventory(Long angelStationId, Integer inventoryChannelNo, String scheduleDay, List<QueryBatchSkuTimeSlotRangeResult> slotRangeList, List<TimeIntervalIntersection.TimeInterval> timeIntervals){
        log.info("AngelStationInventoryQueryDomainServiceImpl.getTimeSlotInventory angelStationId={},scheduleDay={},slotRangeList={}",angelStationId,scheduleDay,JSON.toJSONString(slotRangeList));
        if (CollectionUtils.isEmpty(slotRangeList)) {
            return Collections.emptyList();
        }
        List<TimeSlotInventory> timeSlotInventories = new ArrayList<>();
        for (QueryBatchSkuTimeSlotRangeResult queryBatchSkuTimeSlotRangeResult : slotRangeList) {
            if (StringUtils.isBlank(queryBatchSkuTimeSlotRangeResult.getBookStartTime()) || StringUtils.isBlank(queryBatchSkuTimeSlotRangeResult.getBookEndTime())) {
                continue;
            }
            List<TimeSlotInventory> timeSlotInventory = getTimeSlotInventory(angelStationId, inventoryChannelNo, scheduleDay, queryBatchSkuTimeSlotRangeResult.getBookStartTime(), queryBatchSkuTimeSlotRangeResult.getBookEndTime(), timeIntervals);
            if (CollectionUtils.isNotEmpty(timeSlotInventory)) {
                timeSlotInventories.addAll(timeSlotInventory);
            }
        }
        log.info("AngelStationInventoryQueryDomainServiceImpl.getTimeSlotInventory all timeSlotInventories={}",JSON.toJSONString(timeSlotInventories));
        return timeSlotInventories;
    }

    /**
     * 查询服务站实时库存
     * @param angelStationId
     * @param scheduleDay
     * @return
     */
    @LogAndAlarm
    private List<TimeSlotInventory> getTimeSlotInventory(Long angelStationId, Integer inventoryChannelNo,String scheduleDay,String bookStartTime,String bookEndTime, List<TimeIntervalIntersection.TimeInterval> timeIntervals){
        log.info("AngelStationInventoryQueryDomainServiceImpl.getTimeSlotInventory angelStationId={},scheduleDay={},bookStartTime={},bookEndTime={}",angelStationId,scheduleDay,bookStartTime,bookEndTime);
        List<TimeSlotInventory> timeSlotInventories = new ArrayList<>();
        ///查询服务站实时库存
        InventoryQuery inventoryQuery = InventoryQuery.builder()
                .storeId(angelStationId+"")
                .inventoryChannelNo(inventoryChannelNo)
                .day(scheduleDay).build();
        ////key:时间槽 08:00-09:00
        Map<String, InventoryResult> inventoryResultMap = angelStationInventoryHelper.inventoryQuery(inventoryQuery);
        log.info("AngelStationInventoryQueryDomainServiceImpl.queryBatchSkuInventory inventoryResultMap={}", JSON.toJSONString(inventoryResultMap));
        //预定开始时间
        LocalTime startTime = LocalTime.parse(bookStartTime, DateTimeFormatter.ofPattern("HH:mm"));
        //预定结束时间
        LocalTime endTime = LocalTime.parse(bookEndTime, DateTimeFormatter.ofPattern("HH:mm"));
        TimeIntervalIntersection.TimeInterval commonInterval = new TimeIntervalIntersection.TimeInterval(startTime,endTime);
        inventoryResultMap.forEach((k,v)->{

            LocalTime startTime1 = LocalTime.parse(k.split("-")[0], DateTimeFormatter.ofPattern(CommonConstant.HM));
            LocalTime endTime1 = LocalTime.parse(k.split("-")[1], DateTimeFormatter.ofPattern(CommonConstant.HM));
            TimeIntervalIntersection.TimeInterval interval = new TimeIntervalIntersection.TimeInterval(startTime1,endTime1);

            if(startTime.toString().equals(endTime1.toString())){
                return;
            }
            if(endTime.toString().equals(startTime1.toString())){
                return;
            }
            //计算两个时段的交集
            if(!commonInterval.intersection(interval).isPresent()){
                return;
            }
            //需查询的库存时间段 与实验室营业时间段可扣减库存时间段取交集，包含才会返回库存，否则默认无库存（实验室非营业时间段无库存）
            if(!TimeIntervalIntersection.groupCalculateIntersection(interval, timeIntervals).isPresent()){
                TimeSlotInventory timeSlotInventory = new TimeSlotInventory();
                timeSlotInventory.setTimeSpan(k);
                timeSlotInventory.setInventoryNum(0);
                timeSlotInventories.add(timeSlotInventory);
                return;
            }

            TimeSlotInventory timeSlotInventory = new TimeSlotInventory();
            timeSlotInventory.setTimeSpan(k);
            timeSlotInventory.setInventoryNum(v.getRemainingCount());
            timeSlotInventories.add(timeSlotInventory);
        });
        log.info("AngelStationInventoryQueryDomainServiceImpl.getTimeSlotInventory timeSlotInventories={}",JSON.toJSONString(timeSlotInventories));
        return timeSlotInventories;
    }

    public static void main(String[] args) throws ParseException {
        List<Integer> list = Lists.newArrayList(1,2,3,4);

        Iterator<Integer> iterator = list.iterator();
        while(iterator.hasNext()) {
            Integer next = iterator.next();
            System.out.println(next);
            if(next == 2) {
                iterator.remove();
            }
        }
        log.info("list={}",JSON.toJSONString(list));
    }
}
