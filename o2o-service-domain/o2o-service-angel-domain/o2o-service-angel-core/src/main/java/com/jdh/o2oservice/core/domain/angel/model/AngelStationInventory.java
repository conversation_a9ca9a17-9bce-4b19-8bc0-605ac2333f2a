package com.jdh.o2oservice.core.domain.angel.model;

import lombok.Data;

import java.util.List;

/**
 * @ClassName SkuInventory
 * @Description
 * <AUTHOR>
 * @Date 2024/6/11 5:49 PM
 * @Version 1.0
 **/
@Data
public class AngelStationInventory {

    /**
     * 服务站id
     */
    private Long angelStationId;

    /**
     * 实验室id
     */
    private String stationId;

    /**
     * 实验室名称
     */
    private String stationName;

    /**
     * 可预约天数
     */
    private List<AngelStationDayInventory> angelStationDayInventories;

    /**
     * 该服务站是否被预占
     */
    private Boolean havePreemption;

    /**
     * 接驳点id
     */
    private Long jdTransferStationId;

}
