package com.jdh.o2oservice.core.domain.angel.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jd.jim.cli.Cluster;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.enums.RedisKeyEnum;
import com.jdh.o2oservice.base.enums.ThreadPoolConfigEnum;
import com.jdh.o2oservice.base.enums.UmpKeyEnum;
import com.jdh.o2oservice.base.event.Event;
import com.jdh.o2oservice.base.event.EventCoordinator;
import com.jdh.o2oservice.base.event.factory.EventFactory;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode;
import com.jdh.o2oservice.base.exception.errorcode.DynamicErrorCode;
import com.jdh.o2oservice.base.exception.errorcode.SystemErrorCode;
import com.jdh.o2oservice.base.factory.ExecutorPoolFactory;
import com.jdh.o2oservice.base.factory.GenerateIdFactory;
import com.jdh.o2oservice.base.util.TimeIntervalIntersection;
import com.jdh.o2oservice.base.util.TimeUtils;
import com.jdh.o2oservice.base.util.UmpUtil;
import com.jdh.o2oservice.common.enums.AngelDetailTypeEnum;
import com.jdh.o2oservice.common.enums.AngelTypeEnum;
import com.jdh.o2oservice.common.enums.SupplierTypeEnum;
import com.jdh.o2oservice.common.ext.ExtResponse;
import com.jdh.o2oservice.common.ext.O2oBusinessIdentifier;
import com.jdh.o2oservice.core.domain.angel.context.*;
import com.jdh.o2oservice.core.domain.angel.converter.MapConverter;
import com.jdh.o2oservice.core.domain.angel.enums.*;
import com.jdh.o2oservice.core.domain.angel.event.AngelStationEventBody;
import com.jdh.o2oservice.core.domain.angel.factory.JdhAngelStationFactory;
import com.jdh.o2oservice.core.domain.angel.factory.JdhLayerFactory;
import com.jdh.o2oservice.core.domain.angel.factory.JdhMapFactory;
import com.jdh.o2oservice.core.domain.angel.model.*;
import com.jdh.o2oservice.core.domain.angel.repository.db.*;
import com.jdh.o2oservice.core.domain.angel.repository.query.JdhInventoryReadjustQuery;
import com.jdh.o2oservice.core.domain.angel.repository.query.JdhLayerDbQuery;
import com.jdh.o2oservice.core.domain.angel.repository.query.StationDbQuery;
import com.jdh.o2oservice.core.domain.angel.repository.query.StationSkuRelDbQuery;
import com.jdh.o2oservice.core.domain.angel.service.JdhMapDomainService;
import com.jdh.o2oservice.core.domain.angel.service.ability.CreateThirdStoreHandleAbility;
import com.jdh.o2oservice.core.domain.angel.service.ability.QueryThirdStoreHandleAbility;
import com.jdh.o2oservice.core.domain.angel.service.ability.UpdateThirdStoreHandleAbility;
import com.jdh.o2oservice.core.domain.support.basic.rpc.AddressRpc;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.BaseAddressBo;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.GisPointBo;
import com.jdh.o2oservice.core.domain.support.gismap.AoiMapServiceRpc;
import com.jdh.o2oservice.core.domain.support.gismap.bo.*;
import com.jdh.o2oservice.core.domain.support.gismap.response.*;
import com.jdh.o2oservice.domain.angel.core.ext.dto.ThirdStoreInfoDto;
import com.jdh.o2oservice.domain.angel.core.ext.param.CreateThirdStoreParam;
import com.jdh.o2oservice.domain.angel.core.ext.param.QueryThirdStoreParam;
import com.jdh.o2oservice.domain.angel.core.ext.param.UpdateThirdStoreParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @ClassName:JdhMapDomainServiceImpl
 * @Description:
 * @Author: yaoqinghai
 * @Date: 2024/4/26 19:04
 * @Vserion: 1.0
 **/
@Service
@Slf4j
public class JdhMapDomainServiceImpl implements JdhMapDomainService {

    @Resource
    private AoiMapServiceRpc aoiMapServiceRpc;

    @Resource
    private JdhMapRepository jdhMapRepository;

    @Resource
    private JdhLayerRepository jdhLayerRepository;

    @Resource
    private JdhStationRepository jdhStationRepository;

    @Resource
    private JdhStationSkuRelRepository jdhStationSkuRelRepository;

    @Resource
    private JdhInventoryReadjustRecordRepository jdhInventoryReadjustRecordRepository;

    @Resource
    private AngelRepository angelRepository;

    @Resource
    private AddressRpc addressRpc;

    @Resource
    private EventCoordinator eventCoordinator;

    @Resource
    private GenerateIdFactory generateIdFactory;

    @Resource
    private Cluster jimClient;

    @Resource
    private DuccConfig duccConfig;

    @Resource
    private CreateThirdStoreHandleAbility createThirdStoreHandleAbility;

    @Resource
    private UpdateThirdStoreHandleAbility updateThirdStoreHandleAbility;


    @Resource
    private QueryThirdStoreHandleAbility queryThirdStoreHandleAbility;

    @Resource
    private ExecutorPoolFactory executorPoolFactory;

    /**
     * 创建地图
     *
     * @param mapSaveContext
     * @return
     */
    @Override
    public Boolean createMap(MapSaveContext mapSaveContext) {
        JdhMap jdhMap;
        Long mapId = mapSaveContext.getMapId();
        if(Objects.nonNull(mapId)){
            jdhMap = jdhMapRepository.find(JdhMapIdentifier.builder().mapId(mapId).build());
            jdhMap.setMapName(mapSaveContext.getMapName());
        }else {
            CreateMapBo createMapBo = CreateMapBo.builder().mapName(mapSaveContext.getMapName()).build();
            CreateMapResponse map = aoiMapServiceRpc.createMap(createMapBo);
            mapSaveContext.setThirdMapId(map.getMapId());
            jdhMap = JdhMapFactory.createMap(mapSaveContext);
        }
        return jdhMapRepository.save(jdhMap) > 1 ? true : false;
    }

    /**
     * 创建图层
     *
     * @param layerSaveContext
     */
    @Override
    public Boolean saveLayer(LayerSaveContext layerSaveContext) {
        //检查地图
        JdhMap jdhMap = jdhMapRepository.find(JdhMapIdentifier.builder().mapId(layerSaveContext.getMapId()).build());
        if(Objects.isNull(jdhMap)){
            log.error("[JdhMapDomainServiceImpl.saveLayer],创建图层地图信息不存在!layerSaveContext={}", JSON.toJSONString(layerSaveContext));
            throw new BusinessException(AngelErrorCode.MAP_NOT_EXIST);
        }
        layerSaveContext.setThirdMapId(jdhMap.getThirdMapId());

        JdhLayer jdhLayer;
        if(Objects.nonNull(layerSaveContext.getLayerId())){
            jdhLayer = jdhLayerRepository.find(JdhLayerIdentifier.builder().layerId(layerSaveContext.getLayerId()).build());
            jdhLayer.setLayerName(layerSaveContext.getLayerName());
            jdhLayer.setMapId(layerSaveContext.getMapId());
            jdhLayer.setModeType(layerSaveContext.getModeType());
            jdhLayer.setUpdateUser(layerSaveContext.getOperator());
        }else {
            CreateLayerBo createLayerBo = MapConverter.ins.convertToCreateLayerBo(layerSaveContext);
            CreateLayerResponse layer = aoiMapServiceRpc.createLayer(createLayerBo);
            layerSaveContext.setThirdLayerId(layer.getLayerId());
            jdhLayer = JdhLayerFactory.createJdhLayer(layerSaveContext);
        }
        return jdhLayerRepository.save(jdhLayer) > 0 ? true : false;
    }

    /**
     * 保存服务站
     *
     * @param angelStationSaveContext
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveStationElement(AngelStationSaveContext angelStationSaveContext) {
        //检查时间
        checkReadjustTime(angelStationSaveContext);

        JdhStation jdhStation;
        //检查地图和图层信息
        if(Objects.isNull(angelStationSaveContext.getMapId()) || Objects.isNull(angelStationSaveContext.getLayerId())){
            log.error("[JdhMapDomainServiceImpl.saveStationElement],地图参数异常!");
            throw new BusinessException(BusinessErrorCode.ILLEGAL_ARG_ERROR);
        }
        JdhMap jdhMap = jdhMapRepository.find(JdhMapIdentifier.builder().mapId(angelStationSaveContext.getMapId()).build());
        if(Objects.isNull(jdhMap)){
            log.error("[JdhMapDomainServiceImpl.saveStationElement],地图信息不存在!");
            throw new BusinessException(AngelErrorCode.MAP_NOT_EXIST);
        }
        angelStationSaveContext.setThirdMapId(jdhMap.getThirdMapId());

        JdhLayer jdhLayer = jdhLayerRepository.find(JdhLayerIdentifier.builder().layerId(angelStationSaveContext.getLayerId()).build());
        if(Objects.isNull(jdhLayer)){
            log.error("[JdhMapDomainServiceImpl.saveStationElement],图层信息不存在!");
            throw new BusinessException(AngelErrorCode.LAYER_NOT_EXIST);
        }
        angelStationSaveContext.setThirdLayerId(jdhLayer.getThirdLayerId());
        angelStationSaveContext.setStationModeType(jdhLayer.getModeType());

        //初始化服务站更新信息
        angelStationSaveContext.initReadjust();
        if (Objects.nonNull(angelStationSaveContext.getAngelStationId())) {
            jdhStation = jdhStationRepository.find(JdhStationIdentifier.builder().angelStationId(angelStationSaveContext.getAngelStationId()).build());
            //判断骑手供应商是否发生了变更 22:00-23:59才允许修改
            if(angelStationSaveContext.getDeliverySuppliers()!=null
                    &&jdhStation.getDeliverySupplier()!=null
                    &&!jdhStation.getDeliverySupplier().equals(jdhStation.toIntFromIndex(angelStationSaveContext.getDeliverySuppliers()))){
                String deliveryUpdateTime = duccConfig.getDeliveryUpdateTime();
                if(!TimeUtils.dateBetweenTime(new Date(),deliveryUpdateTime,"-")){
                    throw new BusinessException(new DynamicErrorCode("-1",deliveryUpdateTime+"之外禁止修改服务站"));
                }
            }

            if( angelStationSaveContext.getJdTransferStationId() != null) {
                jdhStation.setJdTransferStationId(angelStationSaveContext.getJdTransferStationId());
            }

            jdhStation.setJdhInventoryReadjustRecordList(jdhInventoryReadjustRecordRepository.findList(JdhInventoryReadjustQuery.builder().angelStationId(angelStationSaveContext.getAngelStationId()).build()));

            //修改补位
            jdhStation.fillChangeBitMap(jdhStation.CHANGE_TYPE);
            //补充修改项
            jdhStation.diff(angelStationSaveContext);
            //异常库存检查和修改项处理
            jdhStation.diffReadjust(angelStationSaveContext);
            //护士异常库存检查和修改项处理
            jdhStation.nurseDiffReadjust(angelStationSaveContext);
            //参数校验
            jdhStation.checkParams();
            //检查是否调整了地理位置
            if(angelStationSaveContext.getFenceShapeType()==null|| FenceShapeTypeEnum.CIRCLE.getType().equals(angelStationSaveContext.getFenceShapeType())){
                //圆形,属于老逻辑
                if(jdhStation.checkChangeOrBitMap(JdhStation.CENTER_INDEX, JdhStation.RADIUS_INDEX)){
                    jdhStation.generateFenceBoundary(Double.valueOf(angelStationSaveContext.getFenceRangeCenterLat()),
                            Double.valueOf(angelStationSaveContext.getFenceRangeCenterLng()),
                            Double.valueOf(angelStationSaveContext.getFenceRangeRadius()),
                            72);
                    ModifyElementDataBo modifyElementDataBo = ModifyElementDataBo.builder()
                            .elementId(jdhStation.getFenceId())
                            .mapId(jdhMap.getThirdMapId())
                            .elementGeometry(jdhStation.getFenceBoundaryList())
                            .build();
                    aoiMapServiceRpc.modifyAngelStation(modifyElementDataBo);
                }
            }else{
                //多边形
                if(jdhStation.getFenceBoundaryList().hashCode()!=JSON.toJSONString(angelStationSaveContext.generateElementGeometry()).hashCode()){
                    log.info("JdhMapDomainServiceImpl.saveStationElement 修改了多边形围栏坐标,需要同步与图");
                    //维护多边形经纬度集合,写入库中
                    jdhStation.setFenceBoundaryList(angelStationSaveContext.generateElementGeometry());
                    ModifyElementDataBo modifyElementDataBo = ModifyElementDataBo.builder()
                            .elementId(jdhStation.getFenceId())
                            .mapId(jdhMap.getThirdMapId())
                            .elementGeometry(angelStationSaveContext.generateElementGeometry())
                            .build();
                    aoiMapServiceRpc.modifyAngelStation(modifyElementDataBo);
                }
                jdhStation.setFenceRangeRadius(null);
            }

            //检查是否修改了围栏名称
            if(jdhStation.checkChangeAndBitMap(JdhStation.NAME_INDEX)){
                ModifyElementBaseBo modifyElementBaseBo = ModifyElementBaseBo.builder()
                        .mapId(jdhMap.getThirdMapId())
                        .elementId(jdhStation.getFenceId())
                        .title(jdhStation.getAngelStationName())
                        .build();
                aoiMapServiceRpc.modifyAngelStationBase(modifyElementBaseBo);
            }
        }else {
            jdhStation = JdhAngelStationFactory.create(angelStationSaveContext);
            //参数校验
            jdhStation.checkParams();
            //异常库存检查和修改项处理
            jdhStation.diffReadjust(angelStationSaveContext);
            //护士异常库存检查和修改项处理
            jdhStation.nurseDiffReadjust(angelStationSaveContext);

            //与图创建围栏
            CreateElementBo createElementBo = MapConverter.ins.convertToCreateElementBo(angelStationSaveContext);

            //生成围栏的边界点
            if(StringUtils.isBlank(jdhStation.getFenceBoundaryList())){
                //fenceBoundaryList为空则为老逻辑->生成圆
                jdhStation.generateFenceBoundary(Double.valueOf(angelStationSaveContext.getFenceRangeCenterLat()),
                        Double.valueOf(angelStationSaveContext.getFenceRangeCenterLng()),
                        Double.valueOf(angelStationSaveContext.getFenceRangeRadius()),
                        100);
                createElementBo.setElementGeometry(jdhStation.getFenceBoundaryList());
            }else{
                //多边形
                createElementBo.setElementGeometry(angelStationSaveContext.generateElementGeometry());
            }

            createElementBo.setParentId("0");
            CreateElementResponse angelStation = aoiMapServiceRpc.createAngelStation(createElementBo);
            jdhStation.setFenceId(angelStation.getElementId());
        }

        //获取实验室行政区编码
        fillAngelStationRegion(angelStationSaveContext, jdhStation);
        //检查
        jdhStation.checkHour();

        // 如果是兼职请求将服务站所有者设置成服务者本人
        if(BooleanUtils.isTrue(angelStationSaveContext.getPartStationFlag())){
            jdhStation.setStationMaster(angelStationSaveContext.getStationMaster());
            jdhStation.setStationModeType(AngelStationModeTypeEnum.PART_TIME.getCode());
        }

        boolean success = jdhStationRepository.save(jdhStation) > 0;
        jdhStation.fillChangeBitMap(success ? jdhStation.EXECUTE_RESULT : null);
        //发送事件
        this.sendEventForStation(jdhStation, angelStationSaveContext);
        return success;
    }

    /**
     * 启停服务站
     *
     * @param angelStationStartStopContext
     * @return
     */
    @Override
    public String startOrStopElement(AngelStationStartStopContext angelStationStartStopContext) {
        //查询地图信息
        JdhMap jdhMap = jdhMapRepository.find(JdhMapIdentifier.builder().mapId(angelStationStartStopContext.getMapId()).build());
        if(Objects.isNull(jdhMap)){
            log.error("[JdhMapDomainServiceImpl.startOrStopElement],服务站停启用查询地图信息不存在!");
            throw new BusinessException(AngelErrorCode.MAP_NOT_EXIST);
        }
        //查询图层信息
        JdhLayer jdhLayer = jdhLayerRepository.find(JdhLayerIdentifier.builder().layerId(angelStationStartStopContext.getLayerId()).build());
        if(Objects.isNull(jdhLayer)){
            log.error("[JdhMapDomainServiceImpl.startOrStopElement],服务站停启用查询图层信息不存在!");
            throw new BusinessException(AngelErrorCode.LAYER_NOT_EXIST);
        }

        //查询围栏信息
        QueryElementBo queryElementBo = QueryElementBo.builder().elementId(angelStationStartStopContext.getFenceId()).build();
        QueryElementResponse queryElementResponse = aoiMapServiceRpc.queryElement(queryElementBo);

//        if(Objects.equals(angelStationStartStopContext.getAngelStationStatus(), AngelStationStatusEnum.NOT_ALIVE.getCode())){
//            if(Objects.isNull(queryElementResponse) || StringUtils.isBlank(queryElementResponse.getElementId())){
//                return null;
//            }
//            RemoveElementBaseBo removeElementBaseBo = RemoveElementBaseBo.builder()
//                    .elementId(angelStationStartStopContext.getFenceId())
//                    .mapId(jdhMap.getThirdMapId())
//                    .build();
//            aoiMapServiceRpc.removeElement(removeElementBaseBo);
//            return angelStationStartStopContext.getFenceId();
//        }
        log.info("[JdhMapDomainServiceImpl.startOrStopElement],queryElementResponse={}", JSON.toJSONString(queryElementResponse));
        if(Objects.isNull(queryElementResponse) || StringUtils.isBlank(queryElementResponse.getElementId())) {
            CreateElementBo createLayerBo = CreateElementBo.builder()
                    .layerId(jdhLayer.getThirdLayerId())
                    .mapId(jdhMap.getThirdMapId())
                    .title(angelStationStartStopContext.getAngelStationName())
                    .elementGeometry(angelStationStartStopContext.getElementGeometry())
                    .build();
            CreateElementResponse angelStation = aoiMapServiceRpc.createAngelStation(createLayerBo);
            return angelStation.getElementId();
        }
        return angelStationStartStopContext.getFenceId();
    }

    /**
     * 查询地址围栏列表
     *
     * @param angelStationAddressQueryContext
     * @return
     */
    @Override
    @LogAndAlarm
    public Map<String, List<JdhStation>> queryAddressStationList(AngelStationAddressQueryContext angelStationAddressQueryContext) {
        log.info("[JdhMapDomainServiceImpl.queryAddressStationList] queryAddressStationList start");
        Map<String, List<JdhStation>> retMap = Maps.newConcurrentMap();

        List<AngelStationAddressDetail> addressList = angelStationAddressQueryContext.getAddressList();
        if(CollectionUtils.isEmpty(addressList)){
            log.error("[JdhMapDomainServiceImpl.queryAddressStationList],没有地址信息!angelStationAddressQueryContext={}", JSON.toJSONString(angelStationAddressQueryContext));
            return Maps.newHashMap();
        }

        //截取指定的数量的地址进行查询
        if(addressList.size() > duccConfig.getAddressMaxSize()){
            addressList = addressList.subList(CommonConstant.ZERO, duccConfig.getAddressMaxSize());
        }

        //查询图层信息
        JdhLayerDbQuery jdhLayerDbQuery = JdhLayerDbQuery.builder()
                .modeType(angelStationAddressQueryContext.getModeType())
                .build();
        List<JdhLayer> layerList = jdhLayerRepository.findList(jdhLayerDbQuery);
        if(CollectionUtils.isEmpty(layerList)){
            throw new BusinessException(AngelErrorCode.LAYER_NOT_EXIST);
        }
        Map<Long, JdhLayer> layerMap = layerList.stream().collect(Collectors.toMap(JdhLayer::getLayerId, Function.identity(), (k1, k2) -> k1));

        List<CompletableFuture> futureList = Lists.newArrayList();
        for (AngelStationAddressDetail angelStationAddressDetail : addressList) {
            CompletableFuture<Void> cf = CompletableFuture.runAsync(() -> {
                log.info("[JdhMapDomainServiceImpl.queryAddressStationList] queryAddressStationList CompletableFuture.runAsync start addressId={}", angelStationAddressDetail.getAddressId());
                List<JdhStation> retList = Lists.newArrayList();
                List<JdhStation> stationList;
                //查询地址服务站
                String cacheList = jimClient.get(RedisKeyEnum.getRedisKey(RedisKeyEnum.GEO_ADDRESS_CACHE_PREFIX,
                        angelStationAddressDetail.getAddressId(),
                        angelStationAddressDetail.getFullAddress().hashCode(),
                        angelStationAddressQueryContext.getModeType(),
                        Joiner.on(",").useForNull("null").join(AngelTypeEnum.containAngelType(angelStationAddressQueryContext.getAngelType()))
                ));
                if (StringUtils.isNotBlank(cacheList)) {
                    stationList = JSON.parseArray(cacheList, JdhStation.class);
                    log.info("[JdhMapDomainServiceImpl.queryAngelStationList],cacheListSize={}", stationList.size());
                }else {
                    BiFunction<AngelStationAddressDetail, List, List> biFunction = this::hitLocalFence;
                    //该queryAddressStationList方法被其他好多地方引用,其他地方之前是不区分angelType的,所以queryAngelStationList中的angelType参数不能传值;
                    stationList = queryAngelStationList(angelStationAddressDetail, layerList, biFunction,angelStationAddressQueryContext.getAngelType());
                    jimClient.setEx(RedisKeyEnum.getRedisKey(RedisKeyEnum.GEO_ADDRESS_CACHE_PREFIX,
                                    angelStationAddressDetail.getAddressId(),
                                    angelStationAddressDetail.getFullAddress().hashCode(),
                                    angelStationAddressQueryContext.getModeType(),
                                    Joiner.on(",").useForNull("null").join(AngelTypeEnum.containAngelType(angelStationAddressQueryContext.getAngelType()))),
                            JSON.toJSONString(CollectionUtils.isEmpty(stationList) ? Lists.newArrayList() : stationList),
                            RedisKeyEnum.GEO_ADDRESS_CACHE_PREFIX.getExpireTime(),
                            RedisKeyEnum.GEO_ADDRESS_CACHE_PREFIX.getExpireTimeUnit());
                }

                if(CollectionUtils.isEmpty(stationList)){
                    log.info("JdhMapDomainServiceImpl.queryAddressStationList stationList1为空");
                    return;
                }
                //商品的关联关系
                Set<Long> skuNos = angelStationAddressQueryContext.getSkuNos();
                Map<Long, List<JdhStationSkuRel>> angelIdGroup = getStationSkuGroup(stationList, skuNos);
                if (angelIdGroup == null) {
                    return;
                }

                //组装结果
                swapResult(layerMap, retList, stationList, skuNos, angelIdGroup);
                retMap.put(angelStationAddressDetail.getAddressId(), retList);

                log.info("[JdhMapDomainServiceImpl.queryAddressStationList] queryAddressStationList CompletableFuture.runAsync end addressId={}", angelStationAddressDetail.getAddressId());

            }, executorPoolFactory.get(ThreadPoolConfigEnum.DEFAULT));
            futureList.add(cf);
        }
        CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0])).join();
        return retMap;
    }

    /**
     * 查询地址围栏列表
     *
     * @param querySkuAndStationRefContext
     * @return
     */
    @Override
    public Map<Long, List<JdhStationSkuRel>> querySkuAndStationRef(QuerySkuAndStationRefContext querySkuAndStationRefContext) {
        log.info("[JdhMapDomainServiceImpl.querySkuAndStationRef] queryAddressStationList start");
        Map<Long, List<JdhStationSkuRel>> retMap = new HashMap<>();

        //查询图层信息
        JdhLayerDbQuery jdhLayerDbQuery = JdhLayerDbQuery.builder()
                .build();
        List<JdhLayer> layerList = jdhLayerRepository.findList(jdhLayerDbQuery);
        if(CollectionUtils.isEmpty(layerList)){
            throw new BusinessException(AngelErrorCode.LAYER_NOT_EXIST);
        }


        log.info("[JdhMapDomainServiceImpl.querySkuAndStationRef] queryAddressStationList CompletableFuture.runAsync start addressId={}", querySkuAndStationRefContext.getAddressDetail().getAddressId());
        List<JdhStation> stationList;
        //查询地址服务站
        String cacheList = jimClient.get(RedisKeyEnum.getRedisKey(RedisKeyEnum.GEO_ADDRESS_CACHE_PREFIX,
                querySkuAndStationRefContext.getAddressDetail().getAddressId(),
                querySkuAndStationRefContext.getAddressDetail().getFullAddress().hashCode(),
                CommonConstant.ALL
        ));
        if (StringUtils.isNotBlank(cacheList)) {
            stationList = JSON.parseArray(cacheList, JdhStation.class);
            log.info("[JdhMapDomainServiceImpl.querySkuAndStationRef],cacheListSize={}", stationList.size());
        }else {
            BiFunction<AngelStationAddressDetail, List, List> biFunction = this::hitLocalFence;
            stationList = queryAngelStationList(querySkuAndStationRefContext.getAddressDetail(), layerList, biFunction,null);
            jimClient.setEx(RedisKeyEnum.getRedisKey(RedisKeyEnum.GEO_ADDRESS_CACHE_PREFIX,
                            querySkuAndStationRefContext.getAddressDetail().getAddressId(),
                            querySkuAndStationRefContext.getAddressDetail().getFullAddress().hashCode(),
                            CommonConstant.ALL),
                    JSON.toJSONString(CollectionUtils.isEmpty(stationList) ? Lists.newArrayList() : stationList),
                    RedisKeyEnum.GEO_ADDRESS_CACHE_PREFIX.getExpireTime(),
                    RedisKeyEnum.GEO_ADDRESS_CACHE_PREFIX.getExpireTimeUnit());
        }

        if(CollectionUtils.isEmpty(stationList)){
            log.info("JdhMapDomainServiceImpl.querySkuAndStationRef stationList1为空");
            return retMap;
        }
        log.info("JdhMapDomainServiceImpl.querySkuAndStationRef 命中的服务站列表={}", JSON.toJSONString(stationList.stream().map(JdhStation::getAngelStationId).collect(Collectors.toList())));

        //商品的关联关系
        List<Long> skuNos = querySkuAndStationRefContext.getSkuNos();
        Map<Long, List<JdhStationSkuRel>> angelIdGroup = getStationSkuGroup(stationList, new TreeSet<>(skuNos));
        log.info("[JdhMapDomainServiceImpl.querySkuAndStationRef] queryAddressStationList CompletableFuture.runAsync end addressId={}", querySkuAndStationRefContext.getAddressDetail().getAddressId());
        return angelIdGroup;
    }


    /**
     * 创建第三方门店->达达,顺丰等
     * @param createThirdStoreContext
     * @return
     */
    @Override
    public Boolean createThirdStore(CreateThirdStoreContext createThirdStoreContext) {
        log.info("JdhMapDomainServiceImpl#createThirdStore 创建第三方门店");
        //接入扩展点
        CreateThirdStoreParam createThirdStoreParam = new CreateThirdStoreParam();
        BeanUtils.copyProperties(createThirdStoreContext,createThirdStoreParam);
        //deliverySupplier->AngelDetailTypeEnum
        //暂时只有一个
        O2oBusinessIdentifier o2OBusinessIdentifier = O2oBusinessIdentifier.builder()
                .angelType(AngelTypeEnum.DELIVERY.getType())
                .angelDetailType(AngelDetailTypeEnum.getTypeByDelivery(SupplierTypeEnum.getDelivery(createThirdStoreContext.getDeliverySupplier())))
                .businessMode("createThirdStore")
                .build();
        log.info("JdhMapDomainServiceImpl#createThirdStore o2OBusinessIdentifier={}", o2OBusinessIdentifier);
        ExtResponse<Boolean> extResponse = createThirdStoreHandleAbility.createThirdStore(o2OBusinessIdentifier,createThirdStoreParam);
        if(extResponse==null){
            return false;
        }
        return extResponse.getData();
    }

    @Override
    public Boolean updateThirdStore(UpdateThirdStoreContext updateThirdStoreContext) {
        log.info("JdhMapDomainServiceImpl#createThirdStore 更新第三方门店");
        //接入扩展点
        UpdateThirdStoreParam updateThirdStoreParam = new UpdateThirdStoreParam();
        BeanUtils.copyProperties(updateThirdStoreContext,updateThirdStoreParam);

        JdhStation jdhStation = jdhStationRepository.find(JdhStationIdentifier.builder().angelStationId(updateThirdStoreContext.getAngelStationId()).build());
        if (Objects.isNull(jdhStation) || Objects.isNull(jdhStation.getDeliverySupplier())){
            return null;
        }
        updateThirdStoreParam.setOutShopId(jdhStation.getOutShopId());
        //暂时只有一个
        O2oBusinessIdentifier o2OBusinessIdentifier = O2oBusinessIdentifier
                .builder()
                .angelType(AngelTypeEnum.DELIVERY.getType())
                .angelDetailType(AngelDetailTypeEnum.getTypeByDelivery(SupplierTypeEnum.getDelivery(jdhStation.getDeliverySupplier())))
                .businessMode("updateThirdStore")
                .build();

        ExtResponse<Boolean> extResponse = updateThirdStoreHandleAbility.updateThirdStore(o2OBusinessIdentifier,updateThirdStoreParam);
        if(extResponse==null){
            return false;
        }
        return extResponse.getData();
    }

    @Override
    public ThirdStoreInfoDto queryThirdStore(QueryThirdStoreContext queryThirdStoreContext) {
        //接入扩展点
        QueryThirdStoreParam queryThirdStoreParam = new QueryThirdStoreParam();
        BeanUtils.copyProperties(queryThirdStoreContext,queryThirdStoreParam);

        JdhStation jdhStation = jdhStationRepository.find(JdhStationIdentifier.builder().angelStationId(queryThirdStoreContext.getAngelStationId()).build());
        if (Objects.isNull(jdhStation) || Objects.isNull(jdhStation.getDeliverySupplier())){
            return null;
        }
        queryThirdStoreContext.setDeliverySupplier(SupplierTypeEnum.getDelivery(jdhStation.getDeliverySupplier()));
        O2oBusinessIdentifier o2OBusinessIdentifier = O2oBusinessIdentifier
                .builder()
                .angelType(AngelTypeEnum.DELIVERY.getType())
                .angelDetailType(AngelDetailTypeEnum.getTypeByDelivery(queryThirdStoreContext.getDeliverySupplier()))
                .businessMode("queryThirdStore")
                .build();
        queryThirdStoreParam.setOutShopId(jdhStation.getOutShopId());

        ExtResponse<ThirdStoreInfoDto> extResponse = queryThirdStoreHandleAbility.queryThirdStore(o2OBusinessIdentifier,queryThirdStoreParam);
        if(extResponse==null){
            return null;
        }
        return extResponse.getData();
    }

    /**
     * 分单接口异常兜底逻辑
     *
     * @param addressDetail
     * @param layerList
     * @return
     */
    private List<JdhStation> hitLocalFence(AngelStationAddressDetail addressDetail, List<JdhLayer> layerList) {
        log.error("[JdhMapDomainServiceImpl.hitLocalFence],进入兜底逻辑!addressDetail={}, layerList={}",JSON.toJSONString(addressDetail), JSON.toJSONString(layerList));
        List<JdhStation> rstStationList = Lists.newArrayList();
        if(CollectionUtils.isEmpty(layerList)) {
            log.error("[JdhMapDomainServiceImpl.hitLocalFence],图层信息为空!");
            return Lists.newArrayList();
        }
        BaseAddressBo jdAddressFromAddress = addressRpc.getJDAddressFromAddress(addressDetail.getFullAddress());
        Set<Long> layerIdSet = layerList.stream().map(JdhLayer::getLayerId).collect(Collectors.toSet());
        StationDbQuery stationDbQuery = StationDbQuery.builder()
                .layerIds(layerIdSet)
                .provinceCode(jdAddressFromAddress.getProvinceCode())
                .angelStationStatus(AngelStationStatusEnum.ALIVE.getCode()).build();

        List<JdhStation> stationList = jdhStationRepository.findList(stationDbQuery);
        if(CollectionUtils.isEmpty(stationList)){
            log.error("[JdhMapDomainServiceImpl.hitLocalFence],服务站信息为空!");
            return Lists.newArrayList();
        }
        GisPointBo lngLatByAddress = addressRpc.getLngLatByAddress(addressDetail.getFullAddress());
        stationList.forEach(station -> {
            if(station.getGeoCoordinate(lngLatByAddress.getLatitude().doubleValue(), lngLatByAddress.getLongitude().doubleValue())){
                rstStationList.add(station);
            }
        });
        return rstStationList;
    }

    /**
     * 组装结果
     * @param layerMap
     * @param retList
     * @param stationList
     * @param skuNos
     * @param angelIdGroup
     */
    private static void swapResult(Map<Long, JdhLayer> layerMap, List<JdhStation> retList, List<JdhStation> stationList, Set<Long> skuNos, Map<Long, List<JdhStationSkuRel>> angelIdGroup) {
        Map<Long, JdhStation> angelMap = stationList.stream()
                .peek(station -> station.setStationModeType(Objects.nonNull(layerMap.get(station.getLayerId())) ? layerMap.get(station.getLayerId()).getModeType() : null))
                .collect(Collectors.toMap(JdhStation::getAngelStationId, Function.identity(), (k1, k2) -> k1));
        Map<Long, List<JdhStationSkuRel>> finalAngelIdGroup = angelIdGroup;
        angelMap.forEach((k, v) -> {
            if (CollectionUtils.isNotEmpty(skuNos)) {
                if (CollectionUtils.isNotEmpty(finalAngelIdGroup.get(k)) && skuNos.size() == finalAngelIdGroup.get(k).stream().map(JdhStationSkuRel::getSkuId).collect(Collectors.toSet()).size()) {
                    retList.add(
                            JdhStation.builder()
                                    .angelStationId(v.getAngelStationId())
                                    .angelStationName(v.getAngelStationName())
                                    .openHour(v.getOpenHour())
                                    .closeHour(v.getCloseHour())
                                    .stationName(v.getStationName())
                                    .stationId(v.getStationId())
                                    .angelNum(v.getAngelNum())
                                    .nurseNum(v.getNurseNum())
                                    .jdTransferStationId(v.getJdTransferStationId())
                                    .build());
                }
            } else {
                retList.add(JdhStation.builder()
                        .angelStationId(v.getAngelStationId())
                        .angelStationName(v.getAngelStationName())
                        .stationModeType(v.getStationModeType())
                        .openHour(v.getOpenHour())
                        .closeHour(v.getCloseHour())
                        .stationName(v.getStationName())
                        .stationId(v.getStationId())
                        .angelNum(v.getAngelNum())
                        .nurseNum(v.getNurseNum())
                        .jdTransferStationId(v.getJdTransferStationId())
                        .build());
            }
        });
    }

    /**
     * 获取服务站商品资源的分组结果
     *
     * @param stationList
     * @param skuNos
     * @return
     */
    private Map<Long, List<JdhStationSkuRel>> getStationSkuGroup(List<JdhStation> stationList, Set<Long> skuNos) {
        Map<Long, List<JdhStationSkuRel>> angelIdGroup = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(skuNos)) {
            Set<Long> angelStationIds = stationList.stream().map(JdhStation::getAngelStationId).collect(Collectors.toSet());
            StationSkuRelDbQuery stationSkuRelDbQuery = StationSkuRelDbQuery.builder()
                    .angelStationIds(angelStationIds)
                    .skuIds(skuNos)
                    .build();
            List<JdhStationSkuRel> list = jdhStationSkuRelRepository.findList(stationSkuRelDbQuery);
            if (CollectionUtils.isEmpty(list)) {
                return null;
            }
            angelIdGroup = list.stream().collect(Collectors.groupingBy(JdhStationSkuRel::getStationId));
        }
        return angelIdGroup;
    }

    /**
     * 查询地址对应的服务站
     *
     * @param angelStationAddressDetail
     * @param layerList
     * @return
     */
    private List<JdhStation> queryAngelStationList(AngelStationAddressDetail angelStationAddressDetail, List<JdhLayer> layerList, BiFunction<AngelStationAddressDetail, List, List> function,Integer angelType) {
        log.info("[JdhMapDomainServiceImpl.queryAngelStationList],angelStationAddressDetail={}", JSON.toJSONString(angelStationAddressDetail));
        List<JdhStation> stationList = Lists.newArrayList();
        try {
            //与图分单接口查询命中的围栏列表
            QueryFenceBo queryFenceBo = QueryFenceBo.builder()
                    .orderId(generateIdFactory.getIdStr())
                    .layerIdList(layerList.stream().map(JdhLayer::getThirdLayerId).collect(Collectors.toList()))
                    .fullAddress(angelStationAddressDetail.getFullAddress())
                    .build();
            List<QueryFenceResponse> queryFenceResponses;
            try{
                if(BooleanUtils.isTrue(duccConfig.getRequestConfig().getForLocal())){
                    log.info("[JdhMapDomainServiceImpl.queryAngelStationList],本地执行!");
                    return function.apply(angelStationAddressDetail, layerList);
                }else {
                    log.info("[JdhMapDomainServiceImpl.queryAngelStationList],调用与图!");
                    queryFenceResponses = aoiMapServiceRpc.queryElementList(queryFenceBo);
                }
            }catch (Exception ex) {
                log.error("[JdhMapDomainServiceImpl.queryAngelStationList],与图接口调用报错!", ex);
                UmpUtil.showWarnMsg(UmpKeyEnum.GEO_FENCE_QUERY_ERROR, "与图分单接口异常了,请尽快跟进处理");
                //执行兜底逻辑
                List<JdhStation> angelStationList = function.apply(angelStationAddressDetail, layerList);
                return angelStationList.stream().filter(item -> AngelTypeEnum.matchContainType(item.getAngelType(), angelType)).collect(Collectors.toList());
            }

            log.info("[JdhMapDomainServiceImpl.queryAngelStationList],queryFenceResponses={}", JSON.toJSONString(queryFenceResponses));
            if (CollectionUtils.isEmpty(queryFenceResponses)) {
                log.info("[JdhMapDomainServiceImpl.queryAngelStationList],没有命中围栏");
                return Lists.newArrayList();
            }
            Set<String> fenceIds = queryFenceResponses.stream()
                    .filter(item -> StringUtils.isNotBlank(item.getElementId()))
                    .map(QueryFenceResponse::getElementId)
                    .collect(Collectors.toSet());

            if(CollectionUtils.isEmpty(fenceIds)){
                log.error("[JdhMapDomainServiceImpl.queryAngelStationList],没有查询到围栏信息!");
                return Lists.newArrayList();
            }

            //查询服务站信息
            StationDbQuery stationDbQuery = StationDbQuery.builder()
                    .fenceIds(fenceIds)
                    .angelStationStatus(AngelStationStatusEnum.ALIVE.getCode())
                    .angelTypes(AngelTypeEnum.containAngelType(angelType))
                    .build();
            stationList = jdhStationRepository.findList(stationDbQuery);
            if (CollectionUtils.isEmpty(stationList)) {
                return Lists.newArrayList();
            }
        } catch (Exception ex) {
            log.error("[StationApplicationImpl.queryAddressStationList],查询可用地址列表失败!", ex);
        }
        return stationList;
    }

    private void fillAngelStationRegion(AngelStationSaveContext angelStationSaveContext, JdhStation jdhStation) {
        // 如果是全职服务站的储存，需要用全地址计算出 各级地址信息
        if(!BooleanUtils.isTrue(angelStationSaveContext.getPartStationFlag())){
            BaseAddressBo jdAddressFromAddress = addressRpc.getJDAddressFromAddress(angelStationSaveContext.getFullAddress());
            if(Objects.isNull(jdAddressFromAddress)){
                log.error("[JdhMapDomainServiceImpl.saveStationElement],地址解析异常!angelStationSaveContext={}", JSON.toJSONString(angelStationSaveContext));
                throw new BusinessException(AngelErrorCode.ADDRESS_DETAIL_TO_REGION_ERROR);
            }
            jdhStation.setProvinceCode(Objects.nonNull(jdAddressFromAddress.getProvinceCode())?String.valueOf(jdAddressFromAddress.getProvinceCode()):null);
            jdhStation.setProvinceName(jdAddressFromAddress.getProvinceName());
            jdhStation.setCityCode(Objects.nonNull(jdAddressFromAddress.getCityCode())?String.valueOf(jdAddressFromAddress.getCityCode()):null);
            jdhStation.setCityName(jdAddressFromAddress.getCityName());
            jdhStation.setDistrictCode(Objects.nonNull(jdAddressFromAddress.getDistrictCode())?String.valueOf(jdAddressFromAddress.getDistrictCode()):null);
            jdhStation.setDistrictName(jdAddressFromAddress.getDistrictName());
            jdhStation.setCountyCode(Objects.nonNull(jdAddressFromAddress.getTownCode())?String.valueOf(jdAddressFromAddress.getTownCode()):null);
            jdhStation.setCountyName(jdAddressFromAddress.getTownName());
            jdhStation.setAddressDetail(jdAddressFromAddress.getDetailAddress());
        }
        // 如果是兼职服务站多级地址的信息 直接从angelStationSaveContext 透传
        else{
            jdhStation.setProvinceCode(angelStationSaveContext.getProvinceCode());
            jdhStation.setProvinceName(angelStationSaveContext.getProvinceName());

            jdhStation.setCityCode(angelStationSaveContext.getCityCode());
            jdhStation.setCityName(angelStationSaveContext.getCityName());

            jdhStation.setDistrictCode(angelStationSaveContext.getDistrictCode());
            jdhStation.setDistrictName(angelStationSaveContext.getDistrictName());

            jdhStation.setCountyCode(angelStationSaveContext.getCountyCode());
            jdhStation.setCountyName(angelStationSaveContext.getCountyName());

            jdhStation.setAddressDetail(angelStationSaveContext.getAddressDetail());
        }

        jdhStation.setFullAddress(angelStationSaveContext.getFullAddress());
    }

    private void checkReadjustTime(AngelStationSaveContext angelStationSaveContext) {
        //检查异常库存时间是否正确
        List<JdhInventoryReadjustContext> jdhInventoryReadjustContextList = angelStationSaveContext.getJdhInventoryReadjustContextList();
        if(CollectionUtils.isEmpty(jdhInventoryReadjustContextList)) {
            log.info("[JdhStation -> checkReadjustTime],更新后没有库存调整项!");
            jdhInventoryReadjustContextList = Lists.newArrayList();
        }
        List<TimeIntervalIntersection.DateTimeInterval> dateTimeIntervalList = jdhInventoryReadjustContextList.stream()
                .map(item -> new TimeIntervalIntersection.DateTimeInterval(TimeUtils.dateToLocalDateTime(item.getStartDate()), TimeUtils.dateToLocalDateTime(item.getEndDate())))
                .collect(Collectors.toList());

        if(CollectionUtils.isNotEmpty(dateTimeIntervalList)) {
            if(dateTimeIntervalList.size() > CommonConstant.NUMBER_THIRTY) {
                throw new BusinessException(AngelErrorCode.INVENTORY_READJUST_SIZE_ERROR);
            }
        }

        List<JdhInventoryReadjustContext> nurseInventoryReadjustContextList = angelStationSaveContext.getJdhNurseInventoryReadjustContextList();
        if(CollectionUtils.isEmpty(nurseInventoryReadjustContextList)) {
            log.info("[JdhStation -> checkReadjustTime],更新后没有库存调整项!");
            nurseInventoryReadjustContextList = Lists.newArrayList();
        }
        List<TimeIntervalIntersection.DateTimeInterval> nurseDateTimeIntervalList = nurseInventoryReadjustContextList.stream()
                .map(item -> new TimeIntervalIntersection.DateTimeInterval(TimeUtils.dateToLocalDateTime(item.getStartDate()), TimeUtils.dateToLocalDateTime(item.getEndDate())))
                .collect(Collectors.toList());

        if(CollectionUtils.isNotEmpty(nurseDateTimeIntervalList)) {
            if(nurseDateTimeIntervalList.size() > CommonConstant.NUMBER_THIRTY) {
                throw new BusinessException(AngelErrorCode.NURSE_INVENTORY_READJUST_SIZE_ERROR);
            }
        }
        String timeIntervalErrMsg = TimeIntervalIntersection.checkIntersection(dateTimeIntervalList, "", "【骑手】");
        timeIntervalErrMsg = TimeIntervalIntersection.checkIntersection(nurseDateTimeIntervalList, timeIntervalErrMsg, "【护士】");
        log.info("JdhStation -> checkReadjustTime,异常信息,timeIntervalErrMsg={}", timeIntervalErrMsg);
        if(StringUtils.isNotBlank(timeIntervalErrMsg)){
            log.error("[JdhStation -> checkReadjustTime],时间不正确!dateTimeIntervalList={}", JSON.toJSONString(dateTimeIntervalList));
            throw new BusinessException(AngelErrorCode.ANGEL_STATION_READJUST_TIME_ERROR.formatDescription(timeIntervalErrMsg));
        }
    }


    /**
     * 新增/编辑服务站-发送事件
     * @param jdhStation
     * @param angelStationSaveContext
     */
    public void sendEventForStation(JdhStation jdhStation, AngelStationSaveContext angelStationSaveContext){
        AngelStationEventBody angelStationEventBody = new AngelStationEventBody();
        angelStationEventBody.setStationId(jdhStation.getAngelStationId());
        angelStationEventBody.setPosition(jdhStation.getChangeBitMap());
        angelStationEventBody.setJdhInventoryReadjustContextList(angelStationSaveContext.getJdhInventoryReadjustContextList());
        angelStationEventBody.setTimeAndNumModifyReadjustIdList(angelStationSaveContext.getTimeAndNumModifyReadjustIdList());
        angelStationEventBody.setTimeModifyReadjustIdList(angelStationSaveContext.getTimeModifyReadjustIdList());
        angelStationEventBody.setNumModifyReadjustIdList(angelStationSaveContext.getNumModifyReadjustIdList());
        angelStationEventBody.setRemoveReadjustIdList(angelStationSaveContext.getRemoveReadjustIdList());

        angelStationEventBody.setJdhNurseInventoryReadjustContextList(angelStationSaveContext.getJdhNurseInventoryReadjustContextList());
        angelStationEventBody.setNurseTimeAndNumModifyReadjustIdList(angelStationSaveContext.getNurseTimeAndNumModifyReadjustIdList());
        angelStationEventBody.setNurseTimeModifyReadjustIdList(angelStationSaveContext.getNurseTimeModifyReadjustIdList());
        angelStationEventBody.setNurseNumModifyReadjustIdList(angelStationSaveContext.getNurseNumModifyReadjustIdList());
        angelStationEventBody.setNurseRemoveReadjustIdList(angelStationSaveContext.getNurseRemoveReadjustIdList());

        angelStationEventBody.setOperator(angelStationSaveContext.getOperator());
        angelStationEventBody.setStationModeType(jdhStation.getStationModeType());

        //新增/编辑服务站,发送事件
        angelStationEventBody.setDeliverySupplier(jdhStation.getDeliverySupplier());
        angelStationEventBody.setAngelType(jdhStation.getAngelType());
        angelStationEventBody.setLat(jdhStation.getFenceRangeCenterLat());
        angelStationEventBody.setLng(jdhStation.getFenceRangeCenterLng());
        angelStationEventBody.setCityName(jdhStation.getCityName());
        angelStationEventBody.setStoreName(jdhStation.getAngelStationName());
        angelStationEventBody.setStoreAddr(jdhStation.getFullAddress());
        angelStationEventBody.setContactName(angelStationSaveContext.getContactName());
        angelStationEventBody.setContactPhone(angelStationSaveContext.getContactPhone());
        angelStationEventBody.setOutShopId(angelStationSaveContext.getOutShopId());

        if(jdhStation.checkChangeAndBitMap(jdhStation.EXECUTE_RESULT) && !jdhStation.checkChangeAndBitMap(jdhStation.CHANGE_TYPE)){
            Event event = EventFactory.newDefaultEvent(jdhStation, AngelStationEventTypeEnum.ANGEL_STATION_ADD, angelStationEventBody);
            eventCoordinator.publish(event);
        }
        if(jdhStation.checkChangeAndBitMap(jdhStation.EXECUTE_RESULT, jdhStation.CHANGE_TYPE)){
            //发送事件更新服务站资源和服务站商品服务站信息
            Event event = EventFactory.newDefaultEvent(jdhStation, AngelStationEventTypeEnum.ANGEL_STATION_MODIFY, angelStationEventBody);
            eventCoordinator.publish(event);
        }
        // 兼职服务站创建成功后绑定到服务者信息上
        if(BooleanUtils.isTrue(angelStationSaveContext.getPartStationFlag())) {
            JdhAngelBindStationEntity jdhAngelBindStationEntity = JdhAngelBindStationEntity.builder()
                    .operator(jdhStation.getStationMaster())
                    .stationId(jdhStation.getAngelStationId())
                    .angelIdList(Collections.singletonList(angelStationSaveContext.getAngelId()))
                    .build();
            if(angelRepository.batchBindStation(jdhAngelBindStationEntity)>0){
                JdhAngel jdhAngel = new JdhAngel();
                jdhAngel.setAngelId(angelStationSaveContext.getAngelId());
                //发送修改护士信息事件
                eventCoordinator.publish(EventFactory.newDefaultEvent(jdhAngel, AngelEventTypeEnum.ANGEL_MAIN_DATA_MODIFY, null));
            }
        }
    }


    /**
     * 将数字转成[1,2]
     * @param type
     * @return
     */
    private List<Integer> toIndexFromInt(Integer type){
        if(type==null){
            return null;
        }
        List<Integer> lists = new ArrayList<>();
        String b = Integer.toBinaryString(type);
        byte[] bytes = b.getBytes();
        for (int i = bytes.length; i>0; i--) {
            if(bytes[i-1]!=48){
                lists.add(bytes.length-i+1);
            }
        }
        return lists;
    }

    public static void main(String[] args) {
        String ss = "北京丰台区南苑街道北京丰台区南苑街道西红门路与槐房西路交叉口东南250米公园懿府10号楼2单元1401";
        System.out.println(ss.hashCode());
    }
}