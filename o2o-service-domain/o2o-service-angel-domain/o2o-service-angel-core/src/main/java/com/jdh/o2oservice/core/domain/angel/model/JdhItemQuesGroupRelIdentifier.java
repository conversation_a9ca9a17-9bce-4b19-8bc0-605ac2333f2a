package com.jdh.o2oservice.core.domain.angel.model;

import com.jdh.o2oservice.base.model.Identifier;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/5/23
 * @description 题库
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class JdhItemQuesGroupRelIdentifier implements Identifier {

    private Long id;//主键id

    @Override
    public String serialize() {
        return id+"";
    }
}
