package com.jdh.o2oservice.core.domain.angel.model;

import lombok.Builder;
import lombok.Data;

/**
 * @ClassName JdhStaionInfo
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/6/13 6:12 PM
 * @Version 1.0
 **/

@Data
@Builder
public class JdhAngelStationInfo {

    /**
     * 服务站ID
     */
    private Long angelStationId;

    /**
     * 服务资源数量
     */
    private Integer angelNum;

    /**
     * 护士资源数量
     */
    private Integer nurseNum;

    /**
     * 服务站营业开始时间
     */
    private String openHour;

    /**
     * 服务站营业结束时间
     */
    private String closeHour;

    /**
     * 接驳点id
     */
    private Long jdTransferStationId;
}
