package com.jdh.o2oservice.core.domain.angel.factory;

import com.alibaba.fastjson.JSON;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.enums.YnStatusEnum;
import com.jdh.o2oservice.base.factory.GenerateIdFactory;
import com.jdh.o2oservice.base.util.SpringUtil;
import com.jdh.o2oservice.core.domain.angel.context.AngelStationSaveContext;
import com.jdh.o2oservice.core.domain.angel.context.JdhInventoryReadjustContext;
import com.jdh.o2oservice.core.domain.angel.converter.MapConverter;
import com.jdh.o2oservice.core.domain.angel.enums.AngelStationStatusEnum;
import com.jdh.o2oservice.core.domain.angel.enums.FenceRangeTypeEnum;
import com.jdh.o2oservice.core.domain.angel.model.JdhStation;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * @ClassName:JdhAngelStationFactory
 * @Description:
 * @Author: yaoqinghai
 * @Date: 2024/4/27 15:15
 * @Vserion: 1.0
 **/
public class JdhAngelStationFactory {

    public static JdhStation create(AngelStationSaveContext angelStationSaveContext){
        JdhStation jdhStation = MapConverter.ins.convertToAngelStation(angelStationSaveContext);
        Long angelStationId = SpringUtil.getBean(GenerateIdFactory.class).getId();
        jdhStation.setAngelStationId(angelStationId);
        jdhStation.setFenceRangeType(FenceRangeTypeEnum.RANGE_TYPE_KILOMETRE.getType());
        jdhStation.setVersion(CommonConstant.ONE);
        jdhStation.setYn(YnStatusEnum.YES.getCode());
        jdhStation.setCreateTime(new Date());
        jdhStation.setCreateUser(angelStationSaveContext.getOperator());
        jdhStation.setUpdateUser(angelStationSaveContext.getOperator());
        jdhStation.setUpdateTime(new Date());
        jdhStation.setFenceBoundaryList(angelStationSaveContext.generateElementGeometry());
        jdhStation.setFenceShapeType(angelStationSaveContext.getFenceShapeType());
        jdhStation.setAngelType(jdhStation.toIntFromIndex(angelStationSaveContext.getAngelTypes()));
        jdhStation.setDeliverySupplier(jdhStation.toIntFromIndex(angelStationSaveContext.getDeliverySuppliers()));

        if(Objects.isNull(angelStationSaveContext.getStationStatus())){
            jdhStation.setStationStatus(AngelStationStatusEnum.ALIVE.getCode());
        }
        List<JdhInventoryReadjustContext> jdhInventoryReadjustContextList = angelStationSaveContext.getJdhInventoryReadjustContextList();
        List<JdhInventoryReadjustContext> jdhNurseInventoryReadjustContextList = angelStationSaveContext.getJdhNurseInventoryReadjustContextList();
        if(CollectionUtils.isNotEmpty(jdhInventoryReadjustContextList)){
            jdhInventoryReadjustContextList.forEach(item -> {
                item.setAngelStationId(angelStationId);
            });
            jdhNurseInventoryReadjustContextList.forEach(item -> {
                item.setAngelStationId(angelStationId);
            });
        }
        jdhStation.setJdTransferStationId(angelStationSaveContext.getJdTransferStationId());
        return jdhStation;
    }

}
