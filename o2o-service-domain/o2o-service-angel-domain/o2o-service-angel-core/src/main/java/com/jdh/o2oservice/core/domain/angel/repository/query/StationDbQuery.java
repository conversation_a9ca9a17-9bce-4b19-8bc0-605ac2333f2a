package com.jdh.o2oservice.core.domain.angel.repository.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Set;

/**
 * @ClassName:StationDbQuery
 * @Description:
 * @Author: yaoqinghai
 * @Date: 2024/4/23 00:04
 * @Vserion: 1.0
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class StationDbQuery {

    /**
     * 服务站id集合
     */
    private Set<Long> stationIds;

    /**
     * 围栏id
     */
    private Set<String> fenceIds;

    /**
     * 图层id集合
     */
    private Set<Long> layerIds;

    /**
     * 服务站名称
     */
    private String stationName;

    /**
     * 站长erp
     */
    private Set<String> stationMasters;

    /**
     * 服务站状态
     */
    private Integer angelStationStatus;

    /**
     * 不包含的服务站id
     */
    private Set<Long> notInStationIds;

    /**
     * 省编码
     */
    private Integer provinceCode;

    /**
     * 市编码
     */
    private Integer cityCode;

    /**
     * 服务资源类型
     */
    private List<Integer> angelTypes;

    /**
     * 实验室id集合
     */
    private List<String> stationIdList;

    /**
     * 三方店铺id
     */
    private Long jdTransferStationId;

    /**
     * 三方店铺id
     */
    private List<Long> jdTransferStationIds;
}
