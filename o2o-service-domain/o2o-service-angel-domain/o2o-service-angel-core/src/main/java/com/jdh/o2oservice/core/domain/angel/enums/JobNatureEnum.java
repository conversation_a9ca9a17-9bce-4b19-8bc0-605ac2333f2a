package com.jdh.o2oservice.core.domain.angel.enums;

/**
 * @Description: 服务者标签枚举
 * @Author: <PERSON><PERSON>xiaojie<PERSON>
 * @Date: 2024/5/22
**/
public enum JobNatureEnum {
    /**
     * 服务者标签枚举
     */
    PART_TIME(0, "兼职", 1),
    FULL_TIME(1, "全职", 2),
    NULL(2, "空", 0);

    private final int value;
    private final String label;
    private final Integer hyType;

    JobNatureEnum(int value, String label, Integer hyType) {
        this.value = value;
        this.label = label;
        this.hyType = hyType;
    }

    public int getValue() {
        return value;
    }

    public String getLabel() {
        return label;
    }

    public Integer getHyType() {
        return hyType;
    }

    /**
     * 根据数字获取对应的人员标签枚举。
     *
     * @param value 数字（0或1）
     * @return 对应的JobNatureEnum实例，如果没有找到匹配的值，则返回null。
     */
    public static JobNatureEnum fromValue(int value) {
        for (JobNatureEnum nature : JobNatureEnum.values()) {
            if (nature.getValue() == value) {
                return nature;
            }
        }
        return null; // 或者抛出一个异常，如果传入的值不是有效的枚举值
    }

    public static String getLabelByValue(int value) {
        for (JobNatureEnum nature : JobNatureEnum.values()) {
            if (nature.getValue() == value) {
                return nature.getLabel();
            }
        }
        return null; // 或者抛出一个异常，如果传入的值不是有效的枚举值
    }

    public static Integer getValueByLabel(String label) {
        for (JobNatureEnum nature : JobNatureEnum.values()) {
            if (nature.getLabel() == label) {
                return nature.getValue();
            }
        }
        return null; // 或者抛出一个异常，如果传入的值不是有效的枚举值
    }
}
