package com.jdh.o2oservice.core.domain.settlement.model;

import com.jdh.o2oservice.base.model.Aggregate;
import com.jdh.o2oservice.base.model.AggregateCode;
import com.jdh.o2oservice.base.model.DomainCode;
import com.jdh.o2oservice.base.model.DomainEnum;
import lombok.Data;

import java.util.Date;

/**
 * @ClassName:JdhSettlementAreaFeeConfig
 * @Description:
 * @Author: lwm
 * @Date 2025/4/18
 * @Vserion: 1.0
 **/
@Data
public class JdhSettlementAreaFeeConfig implements Aggregate<JdhSettlementAreaFeeConfigIdentifier> {

    /**
     * 主键
     */
    private Long id;
    /**
     * '费项配置文件id'
     */
    private Long feeConfigId;

    /**
     * <pre>
     * 结算主体类型：护士、康复师
     * </pre>
     */
    private String settlementSubjectType;

    /**
     * 结算主体子类型：全职、兼职
     */
    private String settlementSubjectSubType;

    /**
     * 省编码
     */
    private String provinceCode;
    /**
     * 市编码
     */
    private String cityCode;

    /**
     * 县地区code
     */
    private String countyCode;
    /**
     * 乡镇code
     */
    private String townCode;

    /**
     * 省名称
     */
    private String provinceName;

    /**
     * 市名称
     */
    private String cityName;
    /**
     * 区名称
     */
    private String countyName;
    /**
     * 乡镇
     */
    private String townName;
    /**
     * 目标地址code
     */
    private String destCode;

    /**
     * 上门费
     */
    private String onSiteFee;
    /**
     * 即时加价
     */
    private String immediatelyFee;
    /**
     * 节假日加价
     */
    private String holidayFee;
    /**
     * 夜间加价
     */
    private String nightDoorFee;
    /**
     * 动态调整费
     */
    private String dynamicAdjustFee;

    /**
     * 数据来源分支
     */
    private String branch;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 是否有效 1-有效 0-无效
     */
    private Integer yn;
    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;
    /**
     * 创建人
     */
    private String createUser;
    /**
     * 修改人
     */
    private String updateUser;

    @Override
    public DomainCode getDomainCode() {
        return DomainEnum.PRODUCT;
    }

    @Override
    public AggregateCode getAggregateCode() {
        return null;
    }

    @Override
    public Integer version() {
        return version;
    }

    @Override
    public void versionIncrease() {
        version++;
    }

    @Override
    public JdhSettlementAreaFeeConfigIdentifier getIdentifier() {
        return JdhSettlementAreaFeeConfigIdentifier.builder().areaFeeConfigId(this.getFeeConfigId()).build();
    }
}
