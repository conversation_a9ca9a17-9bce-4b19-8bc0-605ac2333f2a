package com.jdh.o2oservice.core.domain.settlement.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @ClassName SettlementFeeDetailSaveBo
 * @Description
 * <AUTHOR>
 * @Date 2025/4/27 10:50
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SettlementFeeDetailSaveBo {

    /**
     * 主键
     */
    private Long id;

    /**
     *
     */
    private Long feeConfigDetailId;

    /**
     * 费项类型
     * @see com.jdh.o2oservice.core.domain.trade.enums.JdOrderFeeTypeEnum
     */
    private Integer feeType;

    /**
     * 费项金额
     */
    private BigDecimal feeAmount;

    /**
     * 扩展信息
     */
    private String extend;

    /**
     * 版本号
     */
    private Integer version;
}