package com.jdh.o2oservice.core.domain.settlement.repository.db;

import com.jdh.o2oservice.base.model.Repository;
import com.jdh.o2oservice.core.domain.settlement.model.JdhSettlementFeeDetailConfig;
import com.jdh.o2oservice.core.domain.settlement.model.JdhSettlementFeeDetailConfigIdentifier;
import com.jdh.o2oservice.core.domain.settlement.repository.query.JdhAngelSettleAreaFeeQuery;

import java.util.List;

/**
 * 地区费项配置
 *
 * <AUTHOR>
 * @date 2025/04/17
 */
public interface JdhSettlementFeeDetailConfigRepository extends Repository<JdhSettlementFeeDetailConfig, JdhSettlementFeeDetailConfigIdentifier> {

    /**
     * 保存地区费项配置
     *
     * @param jdhSettlementFeeDetailConfig
     * @return count
     */
    int save(JdhSettlementFeeDetailConfig jdhSettlementFeeDetailConfig);
    /**
     * 批量保存地区费项配置
     *
     */
    int batchSaveJdhAreaFeeConfig(List<JdhSettlementFeeDetailConfig> jdhSettlementFeeDetailConfigList);
    /**
     * 更新地区费项配置
     *
     * @param jdhSettlementFeeDetailConfig
     * @return count
     */
    int update(JdhSettlementFeeDetailConfig jdhSettlementFeeDetailConfig);
    /**
     * 删除地区费项配置
     *
     * @param queryContext
     * @return count
     */
    int batchDelete(JdhAngelSettleAreaFeeQuery queryContext);

    /**
     *
     * @param jdhSettlementFeeDetailConfig
     * @return
     */
    int updateByCode(JdhSettlementFeeDetailConfig jdhSettlementFeeDetailConfig);
    /**
     * 查询费项配置明细列表
     *
     */
    List<JdhSettlementFeeDetailConfig> queryJdhAreaFeeConfigList(JdhAngelSettleAreaFeeQuery jdhAreaFeeConfigQuery);

    /**
     * 查询费项配置明细列表
     * @param feeConfigIdList
     * @return
     */
    List<JdhSettlementFeeDetailConfig> queryJdhAreaFeeConfigListByFeeIdList(List<Long> feeConfigIdList);
}
