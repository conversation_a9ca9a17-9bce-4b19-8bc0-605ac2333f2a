package com.jdh.o2oservice.core.domain.settlement.context;

import com.jdh.o2oservice.core.domain.settlement.bo.ExternalDomainFeeConfigSaveBo;
import com.jdh.o2oservice.core.domain.support.vertical.context.BusinessContext;
import lombok.Data;

import java.util.List;

/**
 * @ClassName SettlementConfigContext
 * @Description
 * <AUTHOR>
 * @Date 2025/4/25 15:35
 **/
@Data
public class SettlementConfigContext extends BusinessContext {

    /**
     *
     */
    List<ExternalDomainFeeConfigSaveBo> externalDomainFeeConfigList;
}