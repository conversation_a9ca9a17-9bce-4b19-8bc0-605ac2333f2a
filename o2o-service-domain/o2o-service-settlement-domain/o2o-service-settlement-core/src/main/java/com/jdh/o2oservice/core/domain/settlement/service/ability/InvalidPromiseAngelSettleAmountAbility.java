package com.jdh.o2oservice.core.domain.settlement.service.ability;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.jd.medicine.base.common.util.StringUtil;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.ducc.model.RefundStatusRatioMapping;
import com.jdh.o2oservice.core.domain.settlement.bo.JdOrderDetailBo;
import com.jdh.o2oservice.core.domain.settlement.bo.JdhAngelInfoBo;
import com.jdh.o2oservice.core.domain.settlement.bo.OrderAngelSettleDetailBo;
import com.jdh.o2oservice.core.domain.settlement.bo.ServerSettleAmountBo;
import com.jdh.o2oservice.core.domain.settlement.context.AngelServiceFinishSettlementContext;
import com.jdh.o2oservice.core.domain.settlement.rpc.SettleOrderInfoRpc;
import com.jdh.o2oservice.core.domain.support.basic.enums.FeeAggregateTypeEnum;
import com.jdh.o2oservice.core.domain.support.basic.enums.JdOrderFeeTypeEnum;
import com.jdh.o2oservice.core.domain.support.basic.enums.PricingServiceFactObjectEnum;
import com.jdh.o2oservice.core.domain.support.basic.enums.PricingServiceSceneEnum;
import com.jdh.o2oservice.export.support.command.PricingServiceCalculateCmd;
import com.jdh.o2oservice.export.support.dto.PricingServiceCalculateResultDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;


/**
 * 退款结算-护士服务费
 *
 * @author: liwenming
 * @date: 2024/5/17 9:54 上午
 * @version: 1.0
 */
@Component
@Slf4j
public class InvalidPromiseAngelSettleAmountAbility implements SettleAbility {

    /**
     * settleOrderInfoRpc
     */
    @Resource
    private SettleOrderInfoRpc settleOrderInfoRpc;
    /**
     * duccConfig
     */
    @Resource
    private DuccConfig duccConfig;
    /**
     * 当前能力点匹配的能力编码
     *
     * @return
     */
    @Override
    public SettleAbilityCode getAbilityCode() {
        return SettleAbilityCode.INVALID_ANGEL_SERVICE_AMOUNT;
    }

    /**
     * 处理
     *
     * @param context
     */
    @Override
    public void execute(AngelServiceFinishSettlementContext context) {
        log.info("[InvalidPromiseAngelSettleAmountAbility.execute] ,context={}",context);
        // 订单信息
        String serviceFeeSnapshot = settleOrderInfoRpc.getOrderSettleSnapshotAmount(context.getAngelId(),null,context.getPromiseId());
        if(StringUtil.isBlank(serviceFeeSnapshot)){
            log.error("[InvalidPromiseAngelSettleAmountAbility.execute] serviceFeeSnapshot is null,promiseId={}",context.getPromiseId());
            return;
        }
        ServerSettleAmountBo serverSettleAmountBo = JSONObject.parseObject(serviceFeeSnapshot,ServerSettleAmountBo.class);
//        Map<Long,BigDecimal> angelSkuServiceAmountMap = serverSettleAmountBo.getAngelSkuServiceAmountMap();
//        if(CollUtil.isEmpty(angelSkuServiceAmountMap)){
//            log.info("[InvalidPromiseAngelSettleAmountAbility.execute] angelSkuServiceAmountMap is null,orderId={}",context.getOrderId());
//            return;
//        }

        context.setOrderLastService(Boolean.FALSE);
        context.setServiceInComeFlag(Boolean.FALSE);
        context.setFeeInComeFlag(Boolean.FALSE);
        context.setVoucherLastService(Boolean.TRUE);
        context.setOrderId(Long.valueOf(context.getSourceVoucherId()));
        JdOrderDetailBo jdOrderDetailBo = new JdOrderDetailBo();
        jdOrderDetailBo.setOrderId(Long.valueOf(context.getSourceVoucherId()));
        jdOrderDetailBo.setPaymentTime(context.getPaymentTime());
        context.setJdOrderDetailBo(jdOrderDetailBo);

        Map<String, BigDecimal> feeAmountMap = serverSettleAmountBo.getFeeAmountMap();
        if(CollUtil.isNotEmpty(feeAmountMap)){
            dealNewSettleRefundAmount(context,serverSettleAmountBo);
        }else{
            dealOldSettleRefundAmount(context,serverSettleAmountBo);
        }

        context.setAngelId(serverSettleAmountBo.getAngelId());
        context.setJdhAngelInfoBo(getJdhAngelDetail(context));
        log.info("[InvalidPromiseAngelSettleAmountAbility.execute] context={}",context);
    }

    /**
     *
     * @param context
     * @param serverSettleAmountBo
     */
    private void dealOldSettleRefundAmount(AngelServiceFinishSettlementContext context,ServerSettleAmountBo serverSettleAmountBo){
        OrderAngelSettleDetailBo orderAngelSettleDetailBo = context.getOrderAngelSettleDetailBo();
        Map<Long,BigDecimal> angelSkuServiceAmountMap = serverSettleAmountBo.getAngelSkuServiceAmountMap();
        RefundStatusRatioMapping refundStatusRatioMapping = calcOrderRefundSettleFeeAmount(context.getFreezeStatus());
        if(Objects.isNull(refundStatusRatioMapping)){
            orderAngelSettleDetailBo.setFeeOutComeAmount(BigDecimal.ZERO);
            orderAngelSettleDetailBo.getItemOutComeAmountList().add(BigDecimal.ZERO);
        }else{
            BigDecimal settleFeeRatio = new BigDecimal(refundStatusRatioMapping.getSettleFeeRatio());
            if(Objects.nonNull(settleFeeRatio) && settleFeeRatio.compareTo(BigDecimal.ZERO) > 0){
                BigDecimal angelFeeAmount = serverSettleAmountBo.getAngelFeeAmount();
                angelFeeAmount = angelFeeAmount.multiply(settleFeeRatio).setScale(2, RoundingMode.HALF_UP);
                orderAngelSettleDetailBo.setFeeOutComeAmount(angelFeeAmount);
                orderAngelSettleDetailBo.setFee4EbsOutComeAmount(angelFeeAmount);
            }
            BigDecimal settleServiceRatio = new BigDecimal(refundStatusRatioMapping.getSettleServiceRatio());
            if(Objects.nonNull(settleServiceRatio) && settleServiceRatio.compareTo(BigDecimal.ZERO) > 0){
                BigDecimal angelSkuServiceAmount = angelSkuServiceAmountMap.get(Long.parseLong(context.getServiceId()));
                orderAngelSettleDetailBo.getItemOutComeAmountList().add(angelSkuServiceAmount);
            }
        }
    }

    /**
     * 计算整单退款数据
     * @param freezeStatus
     */
    private RefundStatusRatioMapping calcOrderRefundSettleFeeAmount(Integer freezeStatus){
        // 退款比例
        Map<String, RefundStatusRatioMapping> refundStatusAmoutRatioMap = duccConfig.getRefundStatusAmoutRatio();
        RefundStatusRatioMapping refundStatusRatioMapping = JSONUtil.toBean(
                JSONUtil.toJsonStr(refundStatusAmoutRatioMap.get(String.valueOf(freezeStatus))), RefundStatusRatioMapping.class);
        return refundStatusRatioMapping;
    }

    /**
     *
     * @param context
     * @param serverSettleAmountBo
     */
    private void dealNewSettleRefundAmount(AngelServiceFinishSettlementContext context,ServerSettleAmountBo serverSettleAmountBo){
        Map<String,String> refundStatusRatioMapping = calcOrderRefundFeeAmount(context.getFreezeStatus());
        PricingServiceCalculateCmd cmd = new PricingServiceCalculateCmd();
        cmd.setVerticalCode(context.getVerticalCode());
        cmd.setScene(PricingServiceSceneEnum.ANGEL_SETTLEMENT_PRICE_CALCULATE.getScene());
        cmd.setOrderId(context.getOrderId());
        cmd.setPromiseId(context.getPromiseId());
        cmd.setAngelId(context.getAngelId());
        OrderAngelSettleDetailBo orderAngelSettleDetailBo = context.getOrderAngelSettleDetailBo();

        Map<String, Object> factObjectMap = new HashMap<>();
        factObjectMap.putAll(serverSettleAmountBo.getFeeAmountMap());
        factObjectMap.put(PricingServiceFactObjectEnum.SKU_SERVICE_AMOUNT_MAP.getCode(), serverSettleAmountBo.getAngelSkuServiceAmountMap());
        factObjectMap.put(PricingServiceFactObjectEnum.MATERIAL_FEE_CONFIG.getCode(), serverSettleAmountBo.getMaterialFeeConfig());
        // 需要结算的检测单列表
        factObjectMap.put(PricingServiceFactObjectEnum.MEDICAL_PROMISE_LIST.getCode(), context.getMedicalPromises());
        // 是否是最后一个服务
        factObjectMap.put(PricingServiceFactObjectEnum.IS_LAST_SERVICE.getCode(), context.getVoucherLastService());
        // 当前节点的费项结算比例 ducc配置
        factObjectMap.put(PricingServiceFactObjectEnum.ANGEL_SETTLEMENT_FEE_RATIO_CONFIG.getCode(), refundStatusRatioMapping);
        cmd.setFactObjectMap(factObjectMap);
        PricingServiceCalculateResultDto calculateResultDto = settleOrderInfoRpc.calculatePriceForDetail(cmd);
        if(Objects.nonNull(calculateResultDto)){
            Map<String, BigDecimal> feeAmountMap2 = calculateResultDto.getFeeAmountMap();
            orderAngelSettleDetailBo.setFeeAmountMap(feeAmountMap2);
            BigDecimal skuServiceAmount = feeAmountMap2.getOrDefault(JdOrderFeeTypeEnum.ANGEL_SERVICE_FEE.name(), BigDecimal.ZERO);
            this.getFeeAmount(feeAmountMap2,orderAngelSettleDetailBo);
            orderAngelSettleDetailBo.getItemOutComeAmountList().add(skuServiceAmount);
        }else{
            orderAngelSettleDetailBo.setFeeOutComeAmount(BigDecimal.ZERO);
            orderAngelSettleDetailBo.getItemOutComeAmountList().add(BigDecimal.ZERO);
        }
    }

    /**
     *
     * @param feeAmountMap
     * @return
     */
    private BigDecimal getFeeAmount(Map<String,BigDecimal> feeAmountMap,OrderAngelSettleDetailBo orderAngelSettleDetailBo){
        BigDecimal feeAmount = BigDecimal.ZERO;
        BigDecimal platformServiceInComeAmount = BigDecimal.ZERO;
        BigDecimal fee4EbsOutComeAmount = BigDecimal.ZERO;
        for (Map.Entry<String, BigDecimal> entry : feeAmountMap.entrySet()){
            JdOrderFeeTypeEnum feeTypeEnum = JdOrderFeeTypeEnum.getByName(entry.getKey());
            if (Objects.isNull(feeTypeEnum)) {
                continue;
            }
            if (!Objects.equals(feeTypeEnum.getAggregateTypeEnum(), FeeAggregateTypeEnum.SERVICE_FEE)) {
                if (Objects.equals(feeTypeEnum.getAggregateTypeEnum(), FeeAggregateTypeEnum.PLATFORM_SERVICE_FEE)) {
                    platformServiceInComeAmount = entry.getValue().abs();
                }else{
                    fee4EbsOutComeAmount = fee4EbsOutComeAmount.add(entry.getValue());
                }
                feeAmount = feeAmount.add(entry.getValue());
            }
        }
        orderAngelSettleDetailBo.setFeeOutComeAmount(feeAmount);
        orderAngelSettleDetailBo.setPlatformServiceInComeAmount(platformServiceInComeAmount);
        orderAngelSettleDetailBo.setFee4EbsOutComeAmount(fee4EbsOutComeAmount);
        return feeAmount;
    }

    /**
     * 计算整单退款数据
     * @param freezeStatus
     */
    private Map<String,String> calcOrderRefundFeeAmount(Integer freezeStatus){
        // 退款比例
        Map<String, Map<String,String>> refundStatusFeeAmoutRatio = duccConfig.getRefundStatusFeeAmoutRatio();
        Map<String,String> refundStatusRatioMapping = JSONUtil.toBean(
                JSONUtil.toJsonStr(refundStatusFeeAmoutRatio.get(String.valueOf(freezeStatus))), Map.class);
        return refundStatusRatioMapping;
    }

    /**
     * 查询护士
     * @param context
     * @return
     */
    private JdhAngelInfoBo getJdhAngelDetail(AngelServiceFinishSettlementContext context){
        JdhAngelInfoBo jdhAngelInfoBo = context.getJdhAngelInfoBo();
        if(Objects.isNull(jdhAngelInfoBo)){
            jdhAngelInfoBo = settleOrderInfoRpc.queryJdhAngelInfo(context.getAngelId());
        }
        return jdhAngelInfoBo;
    }

}
