package com.jdh.o2oservice.core.domain.settlement.model;

import com.jdh.o2oservice.common.enums.BusinessModeEnum;
import com.jdh.o2oservice.common.enums.EbsSettleMainBodyTypeEnum;
import com.jdh.o2oservice.common.enums.EbsSettleSplitTypeEnum;
import com.jdh.o2oservice.common.enums.EbsSettleTypeEnum;
import com.jdh.o2oservice.core.domain.settlement.bo.JdOrderDetailBo;
import com.jdh.o2oservice.core.domain.settlement.bo.JdhAngelInfoBo;
import com.jdh.o2oservice.core.domain.settlement.bo.OrderAngelSettleDetailBo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;


/**
 * <p>
 * 护士结算
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-28 23:53:18
 */
@Data
public class AngelSettlementAndEbsDetail{

    ///////////////////////////////////////////////////////////////////////////////////////
    /**
     * 业务模式（医护上门、骑手上门）必填
     */
    private BusinessModeEnum businessModeEnum;
    /**
     * 垂直身份Code
     */
    private String verticalCode;
    /**
     * 服务类型，serviceType和serviceNo至少传一个，优先传serviceType
     */
    private String serviceType;

    /**
     * 结算id
     */
    private Long settleId;

    /**
     * 服务者id
     */
    private Long angelId;
    /**
     * 互医医生id
     */
    private Long nethpDocId;
    /**
     * 人员标签 0兼职 1全职
     */
    private Integer jobNature;
    /**
     * 结算业务id
     */
    private String settlementBusinessId;

    /**
     * 订单id
     */
    private Long orderId;
    /**
     * 父订单ID
     */
    private Long parentId;

    /**
     * promiseId
     */
    private Long promiseId;
    /**
     * 预约单唯一
     */
    private Long promisePatientId;
    /**
     * 预计结算时间
     */
    private Date expectSettleTime;

    /**
     * 结算时间
     */
    private Date settleTime;

    /**
     * serviceId
     */
    private String serviceId;

    /**
     * 订单收入与支出：护士结算
     */
    private OrderAngelSettleDetailBo orderAngelSettleDetailBo;

    /**
     * 护士信息
     */
    private JdhAngelInfoBo jdhAngelInfoBo;

    /**
     * 订单信息
     */
    private JdOrderDetailBo jdOrderDetailBo;
    /**
     * 订单最后一笔
     */
    private Boolean orderLastService = false;

    /**
     * 工单最后一笔
     */
    private Boolean voucherLastService = false;
    /**
     * 收入类型 1上门检测服务 2上门护理服务 3调整项 4 激励
     */
    private Integer itemType;
    /**
     * 护士结算标识
     */
    private Boolean angelSettleFlag = Boolean.TRUE;
    /**
     * 是否发送ebs
     */
    private Boolean sendEbsData = Boolean.TRUE;
    /**
     * 计算费用
     */
    private BigDecimal settleAmout;

    /**
     * 记服务费收入
     */
    private Boolean serviceInComeFlag = Boolean.TRUE;
    /**
     * 记fee收入
     */
    private Boolean feeInComeFlag = Boolean.TRUE;

    /**
     * 是否冲收入
     */
    private Boolean angelSettleBack = Boolean.FALSE;
    /**
     * 费项金额明细
     * {"MATERIAL_FEE":50.00,"HOME_VISIT":5.00,"TIME_PERIOD_APPOINTMENT_TYPE_IMMEDIATELY":20.00}
     * @see com.jdh.o2oservice.core.domain.support.basic.enums.JdOrderFeeTypeEnum
     */
    private Map<String, BigDecimal> feeAmountMap;
}
