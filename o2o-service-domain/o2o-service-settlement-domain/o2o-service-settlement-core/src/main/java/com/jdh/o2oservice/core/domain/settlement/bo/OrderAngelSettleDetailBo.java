package com.jdh.o2oservice.core.domain.settlement.bo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 订单收入与支出：护士结算
 * @Description
 * <AUTHOR>
 * @Date 2024/4/18 21:31
 * @Doc
 **/
@Data
public class OrderAngelSettleDetailBo implements Serializable {
    /**
     * 记收入：sku 金额
     */
    private List<OrderAngelSettleFeeBo> itemInComeAmountList = new ArrayList<>();
    /**
     * 预约单：ext 收入金额
     */
    private List<OrderAngelSettleFeeBo> feeInComeAmountList = new ArrayList<>();
    /**
     * 记支出：护士服务费金额
     */
    private List<BigDecimal> itemOutComeAmountList = new ArrayList<>();
    /**
     * 记收入：ebs-平台服务费
     */
    private BigDecimal platformServiceInComeAmount;
    /**
     * 支出：护士收入-fee费用金额
     */
    private BigDecimal feeOutComeAmount;
    /**
     * 预约单ebs支出：ebs-护士fee费项
     */
    private BigDecimal fee4EbsOutComeAmount;
    /**
     * 费项金额明细
     * {"MATERIAL_FEE":50.00,"HOME_VISIT":5.00,"TIME_PERIOD_APPOINTMENT_TYPE_IMMEDIATELY":20.00}
     * @see com.jdh.o2oservice.core.domain.support.basic.enums.JdOrderFeeTypeEnum
     */
    private Map<String, BigDecimal> feeAmountMap;

    /**
     * 预约单支出：冲收入金额
     */
    private BigDecimal refundBackSkuAmount;
    /**
     * 预约单支出：冲收入金额
     */
    private BigDecimal refundBackFeeAmount;
    /**
     * toB账户信息
     */
    private ThirdOrderEnterpriseBo thirdOrderEnterpriseBo;

}
