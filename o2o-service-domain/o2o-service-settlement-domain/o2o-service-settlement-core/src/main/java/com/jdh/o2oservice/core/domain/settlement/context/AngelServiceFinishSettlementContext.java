package com.jdh.o2oservice.core.domain.settlement.context;

import com.jdh.o2oservice.core.domain.settlement.bo.*;
import com.jdh.o2oservice.core.domain.support.vertical.context.AbstractAbilityContext;
import com.jdh.o2oservice.export.medicalpromise.dto.MedicalPromiseDTO;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @Author: liwenming
 * @Date: 2024/5/17 9:30 上午
 * @Description:
 */
@Data
public class AngelServiceFinishSettlementContext extends AbstractAbilityContext implements Serializable {

    /**
     * 订单号
     */
    private Long orderId;

    /**
     * 父订单ID
     */
    private Long parentId;
    /**
     * promiseId
     */
    private Long promiseId;
    /**
     * 服务单ID
     */
    private Long voucherId;
    /**
     * 服务者id
     */
    private Long angelId;
    /**
     * 互医医生id
     */
    private Long nethpDocId;
    /**
     * 人员标签 0兼职 1全职
     */
    private Integer jobNature;
    /**
     * 服务兑换来源id
     */
    private String sourceVoucherId;
    /**
     * 服务ID，快检为skuNo
     */
    private String serviceId;
    /**
     * 履约单生成的患者id
     */
    private Long patientId;
    /**
     * 结算业务id
     */
    private String settlementBusinessId;
    /**
     * 预约单ID，人+sku纬度 虚拟出来的，用来做幂等
     */
    private Long promisePatientId;
    /**
     * 订单最后一笔
     */
    private Boolean orderLastService = false;

    /**
     * 工单最后一笔
     */
    private Boolean voucherLastService = false;

    /**
     * 订单收入与支出：护士结算
     */
    private OrderAngelSettleDetailBo orderAngelSettleDetailBo;
    /**
     * 订单信息
     */
    private JdOrderDetailBo jdOrderDetailBo;
    /**
     * 护士信息
     */
    private JdhAngelInfoBo jdhAngelInfoBo;
    /**
     * 预计结算时间
     */
    private Date expectSettleTime;
    /**
     * 结算时间
     */
    private Date settleTime;
    /**
     * 收入类型 1上门检测服务 2上门护理服务 3调整项 4 激励
     */
    private Integer itemType;
    /**
     * 商品退款件数
     */
    private Integer skuRefundNum;

    /**
     * 护士是否结算:服务费
     */
    private Boolean angelSettleService = Boolean.FALSE;
    /**
     * 护士是否结算:费用细项
     */
    private Boolean angelSettleFee = Boolean.FALSE;
    /**
     * 护士结算标识
     */
    private Boolean angelSettleFlag = Boolean.TRUE;

    /**
     * 记服务费收入
     */
    private Boolean serviceInComeFlag = Boolean.TRUE;
    /**
     * 记fee收入
     */
    private Boolean feeInComeFlag = Boolean.TRUE;

    /**
     * 订单退款最后一笔
     */
    private Boolean orderRefundLastService = false;
    /**
     *
     */
    private BigDecimal angelSkuServiceAmount = BigDecimal.ZERO;
    /**
     * 预约单支出：护士fee费用金额
     */
    private BigDecimal angelFeeServiceAmount = BigDecimal.ZERO;
    /**
     * 是否冲收入
     */
    private Boolean angelSettleBack = Boolean.FALSE;
    /**
     * 冻结前状态
     */
    private Integer freezeStatus;
    /**
     * 支付时间
     */
    private Date paymentTime;
    /**
     * 检测单特殊标记（0.普通订单，1.加项订单）
     */
    private String flag;
    /**
     * 检测单ID
     */
    private Long medicalPromiseId;
    /** 是否处理完成状态 */
    private Boolean finishState = true;
    /**
     * 检测单List
     */
    private List<MedicalPromiseDTO> medicalPromises;

    /**
     * 服务者工单状态：1=待接单，2=待服务，3=已出门，4=服务中，5=送检中，6=服务完成，7=已退款，8=已取消
     */
    private Integer workStatus;
}
