package com.jdh.o2oservice.core.domain.settlement.repository.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName FeeDetailConfigQuery
 * @Description
 * <AUTHOR>
 * @Date 2025/4/27 15:16
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FeeDetailConfigQuery {

    /**
     * 费项明细id
     */
    private Long feeConfigDetailId;

    /**
     * '费项配置id'
     */
    private Long feeConfigId;

    /**
     * '费项配置id'
     */
    private List<Long> feeConfigIdList;

    /**
     * 费用类型：上门费、即时加价费、夜间加价费、节假日加价费
     */
    private String feeType;
}