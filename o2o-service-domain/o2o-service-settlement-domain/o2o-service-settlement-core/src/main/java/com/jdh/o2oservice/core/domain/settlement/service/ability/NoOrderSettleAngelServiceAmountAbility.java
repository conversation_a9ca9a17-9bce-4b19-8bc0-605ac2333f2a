package com.jdh.o2oservice.core.domain.settlement.service.ability;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.jd.medicine.base.common.util.StringUtil;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.core.domain.settlement.bo.*;
import com.jdh.o2oservice.core.domain.settlement.context.AngelServiceFinishSettlementContext;
import com.jdh.o2oservice.core.domain.settlement.rpc.SettleOrderInfoRpc;
import com.jdh.o2oservice.core.domain.support.basic.enums.FeeAggregateTypeEnum;
import com.jdh.o2oservice.core.domain.support.basic.enums.JdOrderFeeTypeEnum;
import com.jdh.o2oservice.core.domain.support.basic.enums.PricingServiceFactObjectEnum;
import com.jdh.o2oservice.core.domain.support.basic.enums.PricingServiceSceneEnum;
import com.jdh.o2oservice.export.support.command.PricingServiceCalculateCmd;
import com.jdh.o2oservice.export.support.dto.PricingServiceCalculateResultDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;


/**
 * 结算-护士服务费
 *
 * @author: liwenming
 * @date: 2024/5/17 9:54 上午
 * @version: 1.0
 */
@Component
@Slf4j
public class NoOrderSettleAngelServiceAmountAbility implements SettleAbility {

    /**
     * settleOrderInfoRpc
     */
    @Resource
    private SettleOrderInfoRpc settleOrderInfoRpc;

    /**
     * duccConfig
     */
    @Resource
    private DuccConfig duccConfig;


    /**
     * 当前能力点匹配的能力编码
     *
     * @return
     */
    @Override
    public SettleAbilityCode getAbilityCode() {
        return SettleAbilityCode.NO_ORDER_ANGEL_SERVICE_AMOUNT;
    }

    /**
     * 处理
     *
     * @param context
     */
    @Override
    public void execute(AngelServiceFinishSettlementContext context) {
        log.info("[NoOrderSettleAngelServiceAmountAbility.execute] ,context={}",context);
        if(context.getAngelSettleBack()){
            log.info("[NoOrderNoOrderSettleAngelServiceAmountAbility.execute] 冲收入,settlementBusinessId={}",context.getSettlementBusinessId());
            return;
        }
        // 订单信息
        String serviceFeeSnapshot = settleOrderInfoRpc.getOrderSettleSnapshotAmount(context.getAngelId(),null,context.getPromiseId());
        if(StringUtil.isBlank(serviceFeeSnapshot)){
            log.error("[NoOrderNoOrderSettleAngelServiceAmountAbility.execute] serviceFeeSnapshot is null,promiseId={}",context.getPromiseId());
            return;
        }
        ServerSettleAmountBo serverSettleAmountBo = JSONObject.parseObject(serviceFeeSnapshot,ServerSettleAmountBo.class);

        context.setOrderLastService(Boolean.FALSE);
//        context.setVoucherLastService(Boolean.FALSE);
        context.setServiceInComeFlag(Boolean.FALSE);
        context.setFeeInComeFlag(Boolean.FALSE);
        context.setOrderId(Long.valueOf(context.getSourceVoucherId()));
        JdOrderDetailBo jdOrderDetailBo = new JdOrderDetailBo();
        jdOrderDetailBo.setOrderId(Long.valueOf(context.getSourceVoucherId()));
        jdOrderDetailBo.setPaymentTime(context.getPaymentTime());
        context.setJdOrderDetailBo(jdOrderDetailBo);
        OrderAngelSettleDetailBo orderAngelSettleDetailBo = Objects.nonNull(context.getOrderAngelSettleDetailBo()) ?
                context.getOrderAngelSettleDetailBo() : new OrderAngelSettleDetailBo();
        context.setOrderAngelSettleDetailBo(orderAngelSettleDetailBo);
//        orderAngelSettleDetailBo.setFeeOutComeAmount(serverSettleAmountBo.getAngelFeeAmount());

        Map<String, BigDecimal> feeAmountMap = serverSettleAmountBo.getFeeAmountMap();
        //结算优化新逻辑
        if(CollUtil.isNotEmpty(feeAmountMap)){
            //如果快照有feeAmountMap则说明走了结算优化新逻辑，使用新逻辑计算护士结算金额
            this.dealNewSettleAmount(context,serverSettleAmountBo);
        }else{
            //历史逻辑
            Map<Long,BigDecimal> angelSkuServiceAmountMap = serverSettleAmountBo.getAngelSkuServiceAmountMap();
            if(CollUtil.isNotEmpty(angelSkuServiceAmountMap)){
                BigDecimal angelSkuServiceAmount = angelSkuServiceAmountMap.get(Long.parseLong(context.getServiceId()));
                orderAngelSettleDetailBo.getItemOutComeAmountList().add(angelSkuServiceAmount);
            }
            orderAngelSettleDetailBo.setFeeOutComeAmount(serverSettleAmountBo.getAngelFeeAmount());
            orderAngelSettleDetailBo.setFee4EbsOutComeAmount(serverSettleAmountBo.getAngelFeeAmount());
        }

        context.setAngelId(serverSettleAmountBo.getAngelId());
        context.setJdhAngelInfoBo(getJdhAngelDetail(context));
    }

    /**
     *
     * @param context
     * @param serverSettleAmountBo
     */
    private void dealNewSettleAmount(AngelServiceFinishSettlementContext context, ServerSettleAmountBo serverSettleAmountBo){
        Map<String,String> refundStatusRatioMapping = matchSettleRatio(context.getWorkStatus());
        PricingServiceCalculateCmd cmd = new PricingServiceCalculateCmd();
        cmd.setVerticalCode(context.getVerticalCode());
        cmd.setScene(PricingServiceSceneEnum.ANGEL_SETTLEMENT_PRICE_CALCULATE.getScene());
        cmd.setOrderId(context.getOrderId());
        cmd.setPromiseId(context.getPromiseId());
        cmd.setAngelId(context.getAngelId());
        OrderAngelSettleDetailBo orderAngelSettleDetailBo = context.getOrderAngelSettleDetailBo();

        Map<String, Object> factObjectMap = new HashMap<>();
        factObjectMap.putAll(serverSettleAmountBo.getFeeAmountMap());
        factObjectMap.put(PricingServiceFactObjectEnum.SKU_SERVICE_AMOUNT_MAP.getCode(), serverSettleAmountBo.getAngelSkuServiceAmountMap());
        factObjectMap.put(PricingServiceFactObjectEnum.MATERIAL_FEE_CONFIG.getCode(), serverSettleAmountBo.getMaterialFeeConfig());
        // 需要结算的检测单列表
        factObjectMap.put(PricingServiceFactObjectEnum.MEDICAL_PROMISE_LIST.getCode(), context.getMedicalPromises());
        // 是否是最后一个服务
        factObjectMap.put(PricingServiceFactObjectEnum.IS_LAST_SERVICE.getCode(), context.getVoucherLastService());
        // 当前节点的费项结算比例 ducc配置
        factObjectMap.put(PricingServiceFactObjectEnum.ANGEL_SETTLEMENT_FEE_RATIO_CONFIG.getCode(), refundStatusRatioMapping);
        cmd.setFactObjectMap(factObjectMap);
        PricingServiceCalculateResultDto calculateResultDto = settleOrderInfoRpc.calculatePriceForDetail(cmd);
        if(Objects.nonNull(calculateResultDto)){
            Map<String, BigDecimal> feeAmountMap2 = calculateResultDto.getFeeAmountMap();
            orderAngelSettleDetailBo.setFeeAmountMap(feeAmountMap2);
            //获取本次应结算的服务费
            BigDecimal skuServiceAmount = feeAmountMap2.getOrDefault(JdOrderFeeTypeEnum.ANGEL_SERVICE_FEE.name(), BigDecimal.ZERO);
            orderAngelSettleDetailBo.getItemOutComeAmountList().add(skuServiceAmount);
            this.getFeeAmount(feeAmountMap2,orderAngelSettleDetailBo);
        }else{
            orderAngelSettleDetailBo.setFeeOutComeAmount(BigDecimal.ZERO);
            orderAngelSettleDetailBo.getItemOutComeAmountList().add(BigDecimal.ZERO);
        }
    }

    /**
     * 计算整单退款数据
     * @param freezeStatus
     */
    private Map<String,String> matchSettleRatio(Integer freezeStatus){
        // 退款比例
        Map<String, Map<String,String>> refundStatusFeeAmoutRatio = duccConfig.getRefundStatusFeeAmoutRatio();
        Map<String,String> refundStatusRatioMapping = JSONUtil.toBean(
                JSONUtil.toJsonStr(refundStatusFeeAmoutRatio.get(String.valueOf(freezeStatus))), new TypeReference<Map<String, String>>() {}, true);
        return refundStatusRatioMapping;
    }

    /**
     *
     * @param feeAmountMap
     * @return
     */
    private BigDecimal getFeeAmount(Map<String,BigDecimal> feeAmountMap,OrderAngelSettleDetailBo orderAngelSettleDetailBo){
        BigDecimal feeAmount = BigDecimal.ZERO;
        BigDecimal platformServiceInComeAmount = BigDecimal.ZERO;
        BigDecimal fee4EbsOutComeAmount = BigDecimal.ZERO;
        for (Map.Entry<String, BigDecimal> entry : feeAmountMap.entrySet()){
            JdOrderFeeTypeEnum feeTypeEnum = JdOrderFeeTypeEnum.getByName(entry.getKey());
            if (Objects.isNull(feeTypeEnum)) {
                continue;
            }
            if (!Objects.equals(feeTypeEnum.getAggregateTypeEnum(), FeeAggregateTypeEnum.SERVICE_FEE)) {
                if (Objects.equals(feeTypeEnum.getAggregateTypeEnum(), FeeAggregateTypeEnum.PLATFORM_SERVICE_FEE)) {
                    platformServiceInComeAmount = entry.getValue().abs();
                }else{
                    fee4EbsOutComeAmount = fee4EbsOutComeAmount.add(entry.getValue());
                }
                feeAmount = feeAmount.add(entry.getValue());
            }
        }
        orderAngelSettleDetailBo.setFeeOutComeAmount(feeAmount);
        orderAngelSettleDetailBo.setPlatformServiceInComeAmount(platformServiceInComeAmount);
        orderAngelSettleDetailBo.setFee4EbsOutComeAmount(fee4EbsOutComeAmount);
        return feeAmount;
    }

    /**
     * 查询护士
     * @param context
     * @return
     */
    private JdhAngelInfoBo getJdhAngelDetail(AngelServiceFinishSettlementContext context){
        JdhAngelInfoBo jdhAngelInfoBo = context.getJdhAngelInfoBo();
        if(Objects.isNull(jdhAngelInfoBo)){
            jdhAngelInfoBo = settleOrderInfoRpc.queryJdhAngelInfo(context.getAngelId());
        }
        return jdhAngelInfoBo;
    }

}
