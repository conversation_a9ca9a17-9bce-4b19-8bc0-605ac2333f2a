package com.jdh.o2oservice.core.domain.settlement.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * @ClassName SettlementSubjectTypeEnum
 * @Description
 * <AUTHOR>
 * @Date 2025/4/25 15:41
 **/
@Getter
@AllArgsConstructor
public enum SettlementSubjectTypeEnum {
    /** 护士 */
    NURSE("nurse", "护士"),
    /** 康复师 */
    THERAPIST("therapist", "康复师"),
    /** 护工 */
    CAREGIVER("caregiver", "护工"),
    /** 实验室 */
    LABORATORY("laboratory", "实验室"),
    ;

    /**
     *
     */
    private String type;
    /**
     *
     */
    private String desc;

    /**
     *
     * @param type
     * @return
     */
    public static String getEnumDescByType(String type) {
        if (Objects.isNull(type)) {
            return "";
        }
        for (SettlementSubjectTypeEnum statusEnum : SettlementSubjectTypeEnum.values()) {
            if (Objects.equals(statusEnum.getType(), type)) {
                return statusEnum.getDesc();
            }
        }
        return "";
    }
    /**
     * @param type
     * @return
     */
    public static SettlementSubjectTypeEnum getEnumByType(String type) {
        if (Objects.isNull(type)) {
            return null;
        }
        for (SettlementSubjectTypeEnum statusEnum : SettlementSubjectTypeEnum.values()) {
            if (Objects.equals(statusEnum.getType(), type)) {
                return statusEnum;
            }
        }
        return null;
    }
}