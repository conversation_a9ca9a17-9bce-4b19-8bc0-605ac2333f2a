package com.jdh.o2oservice.core.domain.settlement.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * @Description: 服务者标签枚举
 * @Author:
 * @see com.jdh.o2oservice.core.domain.settlement.enums.SettlementSubjectTypeEnum 父类型参考
 * @Date: 2024/5/22
**/
@Getter
@AllArgsConstructor
public enum SettlementSubjectSubTypeEnum {
    /**
     * 服务者标签枚举
     */
    PART_TIME("partTime", "兼职"),
    FULL_TIME("fullTime", "全职"),
    POCT("POCT", "即时检验"),
    BLOOD_TEST("bloodTest", "血检"),
    ;

    private final String type;
    private final String desc;

    /**
     *
     * @param desc
     * @return
     */
    public static Boolean validSubjectSubTypeEnumByDesc(String desc) {
        for (SettlementSubjectSubTypeEnum nature : SettlementSubjectSubTypeEnum.values()) {
            if (nature.getDesc().equals(desc)) {
                return Boolean.TRUE;
            }
        }
        return false;
    }

    /**
     *
     * @param type
     * @return
     */
    public static String getEnumDescByType(String type) {
        if (Objects.isNull(type)) {
            return "";
        }
        for (SettlementSubjectSubTypeEnum statusEnum : SettlementSubjectSubTypeEnum.values()) {
            if (Objects.equals(statusEnum.getType(), type)) {
                return statusEnum.getDesc();
            }
        }
        return "";
    }

}
