package com.jdh.o2oservice.core.domain.settlement.repository.query;

import com.jdh.o2oservice.common.result.request.AbstractPageQuery;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <pre>
 *  套餐（服务）之间关系
 * </pre>
 *
 * <AUTHOR>
 * @date 2024-07-15 21:57
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class JdhAngelSettleAreaFeeQuery extends AbstractPageQuery {
    /**
     * '地区费项配置id'
     */
    private Long areaFeeConfigId;

    /**
     * '费项配置文件id'
     */
    private String feeConfigId;

    /**
     * 省编码
     */
    private String provinceCode;
    /**
     * 市编码
     */
    private String cityCode;

    /**
     * '县地区code'
     */
    private String countyCode;
    /**
     * '乡镇code'
     */
    private String townCode;
    /**
     * 主体类型：骑手、护士、康复师'
     * JobNatureEnum
     */
    private String subjectType;
    /**
     * 护士类型 0：兼职 1：全职
     * JobNatureEnum
     */
    private String angelType;
    /**
     * 操作人
     */
    private String operator;
    /**
     * 不拆分查询
     */
    private Boolean noCountyTownQuery = Boolean.TRUE;
    /**
     * '费项配置id List'
     */
    private List<Long> feeConfigIdList;

    /**
     * 目标地址code
     */
    private String destCode;

    /**
     * 目标地址code列表
     */
    private List<String> destCodeList;
}