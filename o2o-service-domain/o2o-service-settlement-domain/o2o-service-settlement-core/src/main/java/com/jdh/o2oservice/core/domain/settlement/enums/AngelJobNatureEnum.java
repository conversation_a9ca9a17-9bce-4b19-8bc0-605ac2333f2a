package com.jdh.o2oservice.core.domain.settlement.enums;

import com.jd.medicine.base.common.util.StringUtil;

/**
 * @Description: 服务者标签枚举
 * @Author: zhangxiaojie17
 * @Date: 2024/5/22
**/
public enum AngelJobNatureEnum {
    /**
     * 服务者标签枚举
     */
    PART_TIME("0", "兼职"),
    FULL_TIME("1", "全职")
    ;

    private final String value;
    private final String label;

    AngelJobNatureEnum(String value, String label) {
        this.value = value;
        this.label = label;
    }

    public String getValue() {
        return value;
    }

    public String getLabel() {
        return label;
    }

    /**
     * 根据数字获取对应的人员标签枚举
     * @param label
     * @return
     */
    public static Boolean validJobNature(String label) {
        for (AngelJobNatureEnum nature : AngelJobNatureEnum.values()) {
            if (nature.getLabel().equals(label)) {
                return Boolean.TRUE;
            }
        }
        return Boolean.FALSE;
    }
    /**
     *
     * @param label
     * @return
     */
    public static String getLabelByLabel(String label) {
        for (AngelJobNatureEnum nature : AngelJobNatureEnum.values()) {
            if (nature.getLabel().equals(label)) {
                return nature.getValue();
            }
        }
        return null;
    }

    /**
     *
     * @param value
     * @return
     */
    public static String getLabelByValue(String value) {
        if(StringUtil.isBlank(value)){
            return "";
        }
        for (AngelJobNatureEnum nature : AngelJobNatureEnum.values()) {
            if (nature.getValue().equals(value)) {
                return nature.getLabel();
            }
        }
        return null;
    }
}
