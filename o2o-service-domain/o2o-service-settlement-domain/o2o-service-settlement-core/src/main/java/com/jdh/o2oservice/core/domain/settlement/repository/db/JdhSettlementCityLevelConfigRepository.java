package com.jdh.o2oservice.core.domain.settlement.repository.db;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jdh.o2oservice.base.model.Repository;
import com.jdh.o2oservice.core.domain.settlement.model.JdhSettlementCityLevelConfig;
import com.jdh.o2oservice.core.domain.settlement.model.JdhSettlementCityLevelConfigIdentifier;
import com.jdh.o2oservice.core.domain.settlement.repository.query.CityAngelSettlementPageQuery;


import java.util.List;

/**
 * 地区等级配置
 *
 * <AUTHOR>
 * @date 2025/04/17
 */
public interface JdhSettlementCityLevelConfigRepository extends Repository<JdhSettlementCityLevelConfig, JdhSettlementCityLevelConfigIdentifier> {


    /**
     * 保存地区费项配置
     *
     * @param jdhSettlementCityLevelConfig
     * @return count
     */
    int save(JdhSettlementCityLevelConfig jdhSettlementCityLevelConfig);
    /**
     * 批量保存地区费项配置
     *
     */
    int batchSaveJdhCityLevelConfig(List<JdhSettlementCityLevelConfig> jdhSettlementCityLevelConfigList);
    /**
     * 更新地区费项配置
     *
     * @param jdhSettlementCityLevelConfig
     * @return count
     */
    int update(JdhSettlementCityLevelConfig jdhSettlementCityLevelConfig);
    /**
     * 分页查询城市级别服务费配置
     *
     * @param cityAngelSettlementPageQuery
     * @return count
     */
    Page<JdhSettlementCityLevelConfig> queryPage(CityAngelSettlementPageQuery cityAngelSettlementPageQuery);

    /**
     * 查询城市级别服务费配置
     * @param cityAngelSettlementPageQuery
     * @return
     */
    JdhSettlementCityLevelConfig queryJdhCityLevelConfig(CityAngelSettlementPageQuery cityAngelSettlementPageQuery);

    /**
     *
     * @param queryContext
     * @return
     */
    Integer queryCityConfigCount(CityAngelSettlementPageQuery queryContext);
}
