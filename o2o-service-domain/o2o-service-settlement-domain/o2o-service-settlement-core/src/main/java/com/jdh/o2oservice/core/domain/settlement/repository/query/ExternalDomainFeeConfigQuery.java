package com.jdh.o2oservice.core.domain.settlement.repository.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName ExternalDomainFeeConfigRespQuery
 * @Description
 * <AUTHOR>
 * @Date 2025/4/25 11:18
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExternalDomainFeeConfigQuery {

    /**
     * '费项配置文件id'
     */
    private Long feeConfigId;

    /**
     * 领域编码
     */
    private String domainCode;

    /**
     * 聚合编码
     */
    private String aggregateCode;

    /**
     * 聚合ID
     */
    private String aggregateId;

    /**
     * 聚合ID列表
     */
    private List<String> aggregateIdList;

    /**
     * 结算主体类型：护士、康复师等
     */
    private String settlementSubjectType;

    /**
     * 结算主体子类型：全职、兼职等
     */
    private String settlementSubjectSubType;
}