package com.jdh.o2oservice.core.domain.settlement.service.impl;

import com.google.common.collect.HashBasedTable;
import com.jd.fastjson.JSON;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.cache.RedisUtil;
import com.jdh.o2oservice.base.enums.RedisKeyEnum;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.core.domain.settlement.bo.ExternalDomainFeeConfigSaveBo;
import com.jdh.o2oservice.core.domain.settlement.bo.SettlementFeeDetailSaveBo;
import com.jdh.o2oservice.core.domain.settlement.bo.query.SettlementConfigDomainQuery;
import com.jdh.o2oservice.core.domain.settlement.context.SettlementConfigContext;
import com.jdh.o2oservice.core.domain.settlement.convert.SettlementDomainConvert;
import com.jdh.o2oservice.core.domain.settlement.enums.SettleErrorCode;
import com.jdh.o2oservice.core.domain.settlement.model.ExternalDomainFeeConfig;
import com.jdh.o2oservice.core.domain.settlement.model.JdhSettlementFeeDetailConfig;
import com.jdh.o2oservice.core.domain.settlement.repository.db.ExternalDomainFeeConfigRepository;
import com.jdh.o2oservice.core.domain.settlement.repository.query.ExternalDomainFeeConfigQuery;
import com.jdh.o2oservice.core.domain.settlement.service.SettlementConfigDomainService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName SettlementConfigDomainServiceImpl
 * @Description
 * <AUTHOR>
 * @Date 2025/4/25 15:35
 **/
@Slf4j
@Service("settlementConfigDomainService")
public class SettlementConfigDomainServiceImpl implements SettlementConfigDomainService {

    /**
     * externalDomainFeeConfigRepository
     */
    @Resource
    ExternalDomainFeeConfigRepository externalDomainFeeConfigRepository;

    /**
     * redisUtil
     */
    @Resource
    RedisUtil redisUtil;

    /**
     * 批量保存外部领域实体结算价配置
     * @param context
     * @return
     */
    @Override
    @LogAndAlarm
    public Boolean batchSaveExternalDomainFeeConfig(SettlementConfigContext context) {
        if (Objects.isNull(context) || CollectionUtils.isEmpty(context.getExternalDomainFeeConfigList())) {
            return Boolean.FALSE;
        }
        //批量限制最多50个
        if (context.getExternalDomainFeeConfigList().size() > 50) {
            return Boolean.FALSE;
        }
        //查询是否已存在配置，设置配置ID
        List<ExternalDomainFeeConfigSaveBo> externalDomainFeeConfigList = context.getExternalDomainFeeConfigList();
        Map<String, List<ExternalDomainFeeConfigSaveBo>> aggregateId2Map = externalDomainFeeConfigList.stream().collect(Collectors.groupingBy(ExternalDomainFeeConfigSaveBo::getAggregateId));

        for (Map.Entry<String, List<ExternalDomainFeeConfigSaveBo>> entry : aggregateId2Map.entrySet()) {
            batchAttachFeeConfigIdByAggregateId(entry.getKey(), entry.getValue());
        }
        log.info("SettlementConfigDomainServiceImpl -> batchSaveExternalDomainFeeConfig context={}", JSON.toJSONString(context));
        List<ExternalDomainFeeConfig> externalDomainFeeConfigs = SettlementDomainConvert.instance.domainConfigBo2Entity(context.getExternalDomainFeeConfigList());
        int count = externalDomainFeeConfigRepository.batchSave(externalDomainFeeConfigs);
        return count > 0;
    }

    /**
     *
     * @param aggregateId
     * @param list
     */
    private void batchAttachFeeConfigIdByAggregateId(String aggregateId, List<ExternalDomainFeeConfigSaveBo> list) {
        String lockRedisKey = RedisKeyEnum.getRedisKey(RedisKeyEnum.SETTLEMENT_EXTERNAL_DOMAIN_FEE_LOCK_KEY, aggregateId);
        try {
            boolean lock = redisUtil.tryLock(lockRedisKey, RedisKeyEnum.SETTLEMENT_EXTERNAL_DOMAIN_FEE_LOCK_KEY.getExpireTime(), RedisKeyEnum.SETTLEMENT_EXTERNAL_DOMAIN_FEE_LOCK_KEY.getExpireTimeUnit());
            if (!lock) {
                throw new BusinessException(SettleErrorCode.SETTLEMENT_CONFIG_SAVE_BUSY);
            }
            //查询已经存在的配置数据
            List<ExternalDomainFeeConfig> externalDomainFeeConfigs = queryExternalDomainFeeConfig(SettlementConfigDomainQuery.builder().aggregateId(aggregateId).build());
            log.info("SettlementConfigDomainServiceImpl -> batchAttachFeeConfigIdByAggregateId, externalDomainFeeConfigs={}", JSON.toJSONString(externalDomainFeeConfigs));
            HashBasedTable<String, String, List<ExternalDomainFeeConfig>> hashBasedTable = HashBasedTable.create();
            externalDomainFeeConfigs.forEach(externalDomainFeeConfig -> {
                List<ExternalDomainFeeConfig> configList = hashBasedTable.get(externalDomainFeeConfig.getExternalAggregateCode(), externalDomainFeeConfig.getSettlementSubjectType());
                if (CollectionUtils.isEmpty(configList)) {
                    configList = new ArrayList<>();
                }
                configList.add(externalDomainFeeConfig);
                hashBasedTable.put(externalDomainFeeConfig.getExternalAggregateCode(), externalDomainFeeConfig.getSettlementSubjectType(), configList);
            });
            //如果配置的数据已存在，设置费项ID、费项明细ID
            for (ExternalDomainFeeConfigSaveBo externalDomainFeeConfigSaveBo : list) {
                List<ExternalDomainFeeConfig> feeConfigList = hashBasedTable.get(externalDomainFeeConfigSaveBo.getAggregateCode().getCode(), externalDomainFeeConfigSaveBo.getSettlementSubjectType().getType());
                if (CollectionUtils.isNotEmpty(feeConfigList)) {
                    Optional<ExternalDomainFeeConfig> first = feeConfigList.stream().filter(feeConfig -> Objects.equals(feeConfig.getSettlementSubjectSubType(), Objects.isNull(externalDomainFeeConfigSaveBo.getSettlementSubjectSubType()) ? "" : externalDomainFeeConfigSaveBo.getSettlementSubjectSubType().getType())).findFirst();
                    if (first.isPresent()) {
                        ExternalDomainFeeConfig externalDomainFeeConfig = first.get();
                        externalDomainFeeConfigSaveBo.setId(externalDomainFeeConfig.getId());
                        externalDomainFeeConfigSaveBo.setFeeConfigId(externalDomainFeeConfig.getFeeConfigId());
                        externalDomainFeeConfigSaveBo.setVersion(externalDomainFeeConfig.getVersion());

                        for (SettlementFeeDetailSaveBo settlementFeeDetailSaveBo : externalDomainFeeConfigSaveBo.getDetailConfigList()) {
                            JdhSettlementFeeDetailConfig config = externalDomainFeeConfig.getDetailConfigListByFeeType(String.valueOf(settlementFeeDetailSaveBo.getFeeType()));
                            settlementFeeDetailSaveBo.setFeeConfigDetailId(Objects.isNull(config) ? null : config.getFeeConfigDetailId());
                            settlementFeeDetailSaveBo.setId(Objects.isNull(config) ? null : config.getId());
                            settlementFeeDetailSaveBo.setVersion(Objects.isNull(config) ? 1 : config.getVersion());
                        }
                    }

                }
            }
        } catch (BusinessException e){
            log.info("SettlementConfigDomainServiceImpl -> batchSaveExternalDomainFeeConfig business exception", e);
            throw e;
        } finally {
            redisUtil.unLock(lockRedisKey);
        }
    }

    /**
     * 查询外部领域实体结算价配置
     * @param query
     * @return
     */
    @Override
    @LogAndAlarm
    public List<ExternalDomainFeeConfig> queryExternalDomainFeeConfig(SettlementConfigDomainQuery query) {
        if (Objects.isNull(query)) {
            return Collections.emptyList();
        }
        ExternalDomainFeeConfigQuery feeConfigQuery = SettlementDomainConvert.instance.request2Query(query);
        return externalDomainFeeConfigRepository.findList(feeConfigQuery);
    }
}