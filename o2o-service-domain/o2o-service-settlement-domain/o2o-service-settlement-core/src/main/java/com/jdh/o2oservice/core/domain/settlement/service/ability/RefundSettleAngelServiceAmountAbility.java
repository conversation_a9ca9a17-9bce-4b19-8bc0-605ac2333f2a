package com.jdh.o2oservice.core.domain.settlement.service.ability;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.jd.medicine.base.common.util.StringUtil;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.ducc.model.RefundStatusRatioMapping;
import com.jdh.o2oservice.core.domain.settlement.bo.JdOrderDetailBo;
import com.jdh.o2oservice.core.domain.settlement.bo.JdhAngelInfoBo;
import com.jdh.o2oservice.core.domain.settlement.bo.OrderAngelSettleDetailBo;
import com.jdh.o2oservice.core.domain.settlement.bo.ServerSettleAmountBo;
import com.jdh.o2oservice.core.domain.settlement.context.AngelServiceFinishSettlementContext;
import com.jdh.o2oservice.core.domain.settlement.rpc.SettleOrderInfoRpc;
import com.jdh.o2oservice.core.domain.support.basic.enums.FeeAggregateTypeEnum;
import com.jdh.o2oservice.core.domain.support.basic.enums.JdOrderFeeTypeEnum;
import com.jdh.o2oservice.core.domain.support.basic.enums.PricingServiceFactObjectEnum;
import com.jdh.o2oservice.core.domain.support.basic.enums.PricingServiceSceneEnum;
import com.jdh.o2oservice.export.support.command.PricingServiceCalculateCmd;
import com.jdh.o2oservice.export.support.dto.PricingServiceCalculateResultDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;


/**
 * 退款结算-护士服务费
 *
 * @author: liwenming
 * @date: 2024/5/17 9:54 上午
 * @version: 1.0
 */
@Component
@Slf4j
public class RefundSettleAngelServiceAmountAbility implements SettleAbility {

    /**
     * settleOrderInfoRpc
     */
    @Resource
    private SettleOrderInfoRpc settleOrderInfoRpc;
    /**
     * duccConfig
     */
    @Resource
    private DuccConfig duccConfig;
    /**
     * 当前能力点匹配的能力编码
     *
     * @return
     */
    @Override
    public SettleAbilityCode getAbilityCode() {
        return SettleAbilityCode.REFUND_ANGEL_SERVICE_AMOUNT;
    }

    /**
     * 处理
     *
     * @param context
     */
    @Override
    public void execute(AngelServiceFinishSettlementContext context) {
        log.info("[RefundSettleAngelServiceAmountAbility.execute] ,context={}",context);
        // 订单信息
        JdOrderDetailBo jdOrderDetailBo = getJdOrderDetailBo(context);
        Long orderId = context.getOrderId();
        if(Objects.nonNull(jdOrderDetailBo.getParentId()) && jdOrderDetailBo.getParentId() > 0){
            orderId = jdOrderDetailBo.getParentId();
        }
        String serviceFeeSnapshot = settleOrderInfoRpc.getOrderSettleSnapshotAmount(context.getAngelId(),orderId,context.getPromiseId());
        if(StringUtil.isBlank(serviceFeeSnapshot)){
            log.error("[RefundSettleAngelServiceAmountAbility.execute] serviceFeeSnapshot is null,orderId={}",orderId);
            return;
        }
        ServerSettleAmountBo serverSettleAmountBo = JSONObject.parseObject(serviceFeeSnapshot,ServerSettleAmountBo.class);
        if(Objects.isNull(serverSettleAmountBo)){
            log.info("[RefundSettleAngelServiceAmountAbility.execute] serverSettleAmountBo is null,orderId={}",context.getOrderId());
            return;
        }
//        Map<Long,BigDecimal> angelSkuServiceAmountMap = serverSettleAmountBo.getAngelSkuServiceAmountMap();
//        if(CollUtil.isEmpty(angelSkuServiceAmountMap)){
//            log.info("[RefundSettleAngelServiceAmountAbility.execute] angelSkuServiceAmountMap is null,orderId={}",context.getOrderId());
//            return;
//        }

        Map<String, BigDecimal> feeAmountMap = serverSettleAmountBo.getFeeAmountMap();
        if(CollUtil.isNotEmpty(feeAmountMap)){
            dealNewSettleRefundAmount(context,serverSettleAmountBo);
        }else{
            dealOldSettleRefundAmount(context,serverSettleAmountBo);
        }

        context.setAngelId(serverSettleAmountBo.getAngelId());
        context.setJdhAngelInfoBo(getJdhAngelDetail(context));
        log.info("[RefundSettleAngelServiceAmountAbility.execute] context={}",context);
    }

    /**
     *
     * @param context
     * @param serverSettleAmountBo
     */
    private void dealOldSettleRefundAmount(AngelServiceFinishSettlementContext context,ServerSettleAmountBo serverSettleAmountBo){
        Map<Long,BigDecimal> angelSkuServiceAmountMap = serverSettleAmountBo.getAngelSkuServiceAmountMap();
        BigDecimal angelSkuServiceAmount = angelSkuServiceAmountMap.get(Long.parseLong(context.getServiceId()));
        OrderAngelSettleDetailBo orderAngelSettleDetailBo = context.getOrderAngelSettleDetailBo();
        if(!context.getAngelSettleService()){
            log.info("[RefundSettleAngelServiceAmountAbility.execute] 护士不结算:服务费,orderId={}",context.getOrderId());
            context.setAngelSkuServiceAmount(Optional.ofNullable(angelSkuServiceAmount).orElse(BigDecimal.ZERO));
            context.setAngelFeeServiceAmount(serverSettleAmountBo.getAngelFeeAmount());
        }else{
            // 护士服务费
            if(Objects.nonNull(angelSkuServiceAmount)){
                orderAngelSettleDetailBo.getItemOutComeAmountList().add(angelSkuServiceAmount);
            }
        }
        // 护士ext费用
        if(context.getAngelSettleFee() && context.getVoucherLastService()){
            BigDecimal angelFeeAmount = serverSettleAmountBo.getAngelFeeAmount();
            BigDecimal settleFeeRatio = calcOrderRefundSettleFeeAmount(context.getFreezeStatus());
            angelFeeAmount = angelFeeAmount.multiply(settleFeeRatio).setScale(2, RoundingMode.HALF_UP);
            orderAngelSettleDetailBo.setFeeOutComeAmount(angelFeeAmount);
            orderAngelSettleDetailBo.setFee4EbsOutComeAmount(angelFeeAmount);
        }
    }

    /**
     * 计算整单退款数据
     * @param freezeStatus
     */
    private BigDecimal calcOrderRefundSettleFeeAmount(Integer freezeStatus){
        if(Objects.isNull(freezeStatus)){
            return BigDecimal.ONE;
        }
        // 退款比例
        Map<String, RefundStatusRatioMapping> refundStatusAmoutRatioMap = duccConfig.getRefundStatusAmoutRatio();
        RefundStatusRatioMapping refundStatusRatioMapping = JSONUtil.toBean(
                JSONUtil.toJsonStr(refundStatusAmoutRatioMap.get(String.valueOf(freezeStatus))), RefundStatusRatioMapping.class);
        if(Objects.isNull(refundStatusRatioMapping)){
            log.error("[RefundSettleAngelServiceAmountAbility.execute],calcOrderRefundSettleFeeAmount is null");
            return BigDecimal.ZERO;
        }else{
            BigDecimal settleFeeRatio = new BigDecimal(refundStatusRatioMapping.getSettleFeeRatio());
            if(Objects.nonNull(settleFeeRatio) && settleFeeRatio.compareTo(BigDecimal.ZERO) > 0){
                return settleFeeRatio;
            }
        }
        return BigDecimal.ZERO;
    }


    /**
     *
     * @param context
     * @return
     */
    private JdOrderDetailBo getJdOrderDetailBo(AngelServiceFinishSettlementContext context){
        JdOrderDetailBo jdOrderDetailBo = context.getJdOrderDetailBo();
        if(Objects.isNull(jdOrderDetailBo)){
            jdOrderDetailBo = settleOrderInfoRpc.getSplitOrderSettleDetail(context.getOrderId(),context.getServiceId(),context.getPromiseId());
        }
        return jdOrderDetailBo;
    }


    /**
     * 查询护士
     * @param context
     * @return
     */
    private JdhAngelInfoBo getJdhAngelDetail(AngelServiceFinishSettlementContext context){
        JdhAngelInfoBo jdhAngelInfoBo = context.getJdhAngelInfoBo();
        if(Objects.isNull(jdhAngelInfoBo)){
            jdhAngelInfoBo = settleOrderInfoRpc.queryJdhAngelInfo(context.getAngelId());
        }
        return jdhAngelInfoBo;
    }


    /**
     *
     * @param context
     * @param serverSettleAmountBo
     */
    private void dealNewSettleRefundAmount(AngelServiceFinishSettlementContext context,ServerSettleAmountBo serverSettleAmountBo){
        Map<String,String> refundStatusRatioMapping = calcOrderRefundFeeAmount(context.getFreezeStatus());
        PricingServiceCalculateCmd cmd = new PricingServiceCalculateCmd();
        cmd.setVerticalCode(context.getVerticalCode());
        cmd.setScene(PricingServiceSceneEnum.ANGEL_SETTLEMENT_PRICE_CALCULATE.getScene());
        cmd.setOrderId(context.getOrderId());
        cmd.setPromiseId(context.getPromiseId());
        cmd.setAngelId(context.getAngelId());
        OrderAngelSettleDetailBo orderAngelSettleDetailBo = context.getOrderAngelSettleDetailBo();

        Map<String, Object> factObjectMap = new HashMap<>();
        factObjectMap.putAll(serverSettleAmountBo.getFeeAmountMap());
        factObjectMap.put(PricingServiceFactObjectEnum.SKU_SERVICE_AMOUNT_MAP.getCode(), serverSettleAmountBo.getAngelSkuServiceAmountMap());
        factObjectMap.put(PricingServiceFactObjectEnum.MATERIAL_FEE_CONFIG.getCode(), serverSettleAmountBo.getMaterialFeeConfig());
        // 需要结算的检测单列表
        factObjectMap.put(PricingServiceFactObjectEnum.MEDICAL_PROMISE_LIST.getCode(), context.getMedicalPromises());
        // 是否是最后一个服务
        factObjectMap.put(PricingServiceFactObjectEnum.IS_LAST_SERVICE.getCode(), context.getVoucherLastService());
        // 当前节点的费项结算比例 ducc配置
        factObjectMap.put(PricingServiceFactObjectEnum.ANGEL_SETTLEMENT_FEE_RATIO_CONFIG.getCode(), refundStatusRatioMapping);
        cmd.setFactObjectMap(factObjectMap);
        PricingServiceCalculateResultDto calculateResultDto = settleOrderInfoRpc.calculatePriceForDetail(cmd);
        if(Objects.nonNull(calculateResultDto)){
            Map<String, BigDecimal> feeAmountMap2 = calculateResultDto.getFeeAmountMap();
            orderAngelSettleDetailBo.setFeeAmountMap(feeAmountMap2);
            BigDecimal skuServiceAmount = feeAmountMap2.getOrDefault(JdOrderFeeTypeEnum.ANGEL_SERVICE_FEE.name(), BigDecimal.ZERO);
            BigDecimal angelFeeAmount = getFeeAmount(feeAmountMap2,orderAngelSettleDetailBo);
            if(!context.getAngelSettleService()){
                log.info("[RefundSettleAngelServiceAmountAbility.dealNewSettleRefundAmount] 护士不结算:服务费,orderId={}",context.getOrderId());
                context.setAngelSkuServiceAmount(skuServiceAmount);
                context.setAngelFeeServiceAmount(angelFeeAmount);
            }
            //获取本次应结算的服务费
            orderAngelSettleDetailBo.getItemOutComeAmountList().add(skuServiceAmount);
        }else{
            orderAngelSettleDetailBo.setFeeOutComeAmount(BigDecimal.ZERO);
            orderAngelSettleDetailBo.getItemOutComeAmountList().add(BigDecimal.ZERO);
        }
    }

    /**
     *
     * @param feeAmountMap
     * @return
     */
    private BigDecimal getFeeAmount(Map<String,BigDecimal> feeAmountMap,OrderAngelSettleDetailBo orderAngelSettleDetailBo){
        BigDecimal feeAmount = BigDecimal.ZERO;
        BigDecimal platformServiceInComeAmount = BigDecimal.ZERO;
        BigDecimal fee4EbsOutComeAmount = BigDecimal.ZERO;
        for (Map.Entry<String, BigDecimal> entry : feeAmountMap.entrySet()){
            JdOrderFeeTypeEnum feeTypeEnum = JdOrderFeeTypeEnum.getByName(entry.getKey());
            if (Objects.isNull(feeTypeEnum)) {
                continue;
            }
            if (!Objects.equals(feeTypeEnum.getAggregateTypeEnum(), FeeAggregateTypeEnum.SERVICE_FEE)) {
                if (Objects.equals(feeTypeEnum.getAggregateTypeEnum(), FeeAggregateTypeEnum.PLATFORM_SERVICE_FEE)) {
                    platformServiceInComeAmount = entry.getValue().abs();
                }else{
                    fee4EbsOutComeAmount = fee4EbsOutComeAmount.add(entry.getValue());
                }
                feeAmount = feeAmount.add(entry.getValue());
            }
        }
        if(Objects.nonNull(orderAngelSettleDetailBo)){
            orderAngelSettleDetailBo.setFeeOutComeAmount(feeAmount);
            orderAngelSettleDetailBo.setPlatformServiceInComeAmount(platformServiceInComeAmount);
            orderAngelSettleDetailBo.setFee4EbsOutComeAmount(fee4EbsOutComeAmount);
        }
        return feeAmount;
    }

    /**
     * 计算整单退款数据
     * @param freezeStatus
     */
    private Map<String,String> calcOrderRefundFeeAmount(Integer freezeStatus){
        // 退款比例
        Map<String, Map<String,String>> refundStatusFeeAmoutRatio = duccConfig.getRefundStatusFeeAmoutRatio();
        Map<String,String> refundStatusRatioMapping = JSONUtil.toBean(
                JSONUtil.toJsonStr(refundStatusFeeAmoutRatio.get(String.valueOf(freezeStatus))), Map.class);
        return refundStatusRatioMapping;
    }
}
