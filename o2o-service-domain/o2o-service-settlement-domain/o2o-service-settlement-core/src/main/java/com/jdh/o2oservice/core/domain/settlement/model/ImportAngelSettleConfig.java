package com.jdh.o2oservice.core.domain.settlement.model;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 护士导入
 *
 * <AUTHOR>
 * @date 2024-08-22 11:46
 */
@Data
public class ImportAngelSettleConfig {

    /**
     * 省编码
     */
    @ExcelIgnore
    private String provinceCode;
    /**
     * 省名称
     */
    @ExcelProperty(value = "省名称")
    private String provinceName;
    /**
     * 市编码
     */
    @ExcelIgnore
    private String cityCode;
    /**
     * 市名称
     */
    @ExcelProperty(value = "市名称")
    private String cityName;
    /**
     * 区编码
     */
    @ExcelIgnore
    private String countyCode;
    /**
     * 区名称
     */
    @ExcelProperty(value = "区名称")
    private String countyName;
    /**
     * 街道编码
     */
    @ExcelIgnore
    private String townCode;
    /**
     * 街道名称
     */
    @ExcelProperty(value = "街道名称")
    private String townName;
    /**
     * 护士类型
     */
    @ExcelIgnore
    private String angelType;
    /**
     * 护士类型
     */
    @ExcelProperty(value = "护士类型")
    private String angelTypeDesc;
    /**
     * 上门费
     */
    @ExcelProperty(value = "上门费")
    private String onSiteFee;
    /**
     * 即时加价
     */
    @ExcelProperty(value = "即时加价")
    private String immediatelyFee;
    /**
     * 节假日加价
     */
    @ExcelProperty(value = "节假日加价")
    private String holidayFee;
    /**
     * 夜间加价
     */
    @ExcelProperty(value = "夜间加价")
    private String nightDoorFee;
    /**
     * 动态调整费
     */
    @ExcelIgnore
    private String dynamicAdjustFee;
    /**
     * 失败原因
     */
    @ExcelProperty(value = "失败原因")
    private String failReason;
    /**
     * '地区配置id'
     */
    @ExcelIgnore
    private Long cityConfigId;

}
