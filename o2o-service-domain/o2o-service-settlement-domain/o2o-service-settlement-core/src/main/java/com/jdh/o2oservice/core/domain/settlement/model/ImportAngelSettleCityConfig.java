package com.jdh.o2oservice.core.domain.settlement.model;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 护士调账导入
 *
 * <AUTHOR>
 * @date 2024-08-22 11:46
 */
@Data
public class ImportAngelSettleCityConfig {

    /**
     * 省编码
     */
    @ExcelProperty(value = "京标省ID")
    private String provinceCode;

    /**
     * 省名称
     */
    @ExcelProperty(value = "省名称")
    private String provinceName;

    /**
     * 市编码
     */
    @ExcelProperty(value = "京标市ID")
    private String cityCode;

    /**
     * 市名称
     */
    @ExcelProperty(value = "市名称")
    private String cityName;

    /**
     * 城市等级
     */
    @ExcelProperty(value = "城市等级")
    private String levelDesc;
    /**
     * 城市等级
     */
    @ExcelIgnore
    private String level;
    /**
     * 失败原因
     */
    @ExcelProperty(value = "失败原因")
    private String failReason;
    /**
     * '地区配置id'
     */
    @ExcelIgnore
    private Long cityConfigId;
}
