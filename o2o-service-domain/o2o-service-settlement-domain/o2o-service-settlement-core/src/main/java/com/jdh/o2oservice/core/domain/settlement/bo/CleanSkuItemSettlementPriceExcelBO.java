package com.jdh.o2oservice.core.domain.settlement.bo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * @ClassName CleanSkuItemSettlementPriceExcelBO
 * @Description
 * <AUTHOR>
 * @Date 2025/6/13 14:38
 **/
@Data
public class CleanSkuItemSettlementPriceExcelBO {

    /**
     * 类型
     */
    @ExcelProperty(value = "类型", index = 0)
    private String type;

    /**
     * 编号
     */
    @ExcelProperty(value = "编号", index = 1)
    private String code;

    /**
     * 结算金额
     */
    @ExcelProperty(value = "结算金额", index = 2)
    private String angelSettlementPrice;
}