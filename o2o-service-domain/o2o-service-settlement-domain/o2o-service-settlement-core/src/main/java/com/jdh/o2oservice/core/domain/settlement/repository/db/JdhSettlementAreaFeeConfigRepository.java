package com.jdh.o2oservice.core.domain.settlement.repository.db;



import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jdh.o2oservice.base.model.Repository;
import com.jdh.o2oservice.core.domain.settlement.model.JdhSettlementAreaFeeConfig;
import com.jdh.o2oservice.core.domain.settlement.model.JdhSettlementAreaFeeConfigIdentifier;
import com.jdh.o2oservice.core.domain.settlement.repository.query.JdhAngelSettleAreaFeeQuery;

import java.util.List;

/**
 * 地区费项配置
 *
 * <AUTHOR>
 * @date 2025/04/17
 */
public interface JdhSettlementAreaFeeConfigRepository extends Repository<JdhSettlementAreaFeeConfig, JdhSettlementAreaFeeConfigIdentifier> {

    /**
     * 保存地区费项配置
     *
     * @param jdhSettlementAreaFeeConfig
     * @return count
     */
    int save(JdhSettlementAreaFeeConfig jdhSettlementAreaFeeConfig);
    /**
     * 批量保存地区费项配置
     *
     */
    int batchSaveJdhAreaFeeConfig(List<JdhSettlementAreaFeeConfig> jdhSettlementAreaFeeConfigList);
    /**
     * 更新地区费项配置
     *
     * @param jdhSettlementAreaFeeConfig
     * @return count
     */
    int update(JdhSettlementAreaFeeConfig jdhSettlementAreaFeeConfig);
    /**
     * 更新地区费项配置
     *
     * @param queryContext
     * @return count
     */
    int delete(JdhAngelSettleAreaFeeQuery queryContext);
    /**
     *
     * @param jdhSettlementAreaFeeConfig
     * @return
     */
    int updateByCode(JdhSettlementAreaFeeConfig jdhSettlementAreaFeeConfig);
    /**
     * 查询地区费项配置列表
     *
     */
    List<JdhSettlementAreaFeeConfig> queryJdhAreaFeeConfigList(JdhAngelSettleAreaFeeQuery queryContext);
    /**
     * 查询地区费项配置列表
     *
     */
    JdhSettlementAreaFeeConfig queryJdhAreaFeeConfig(JdhSettlementAreaFeeConfig jdhAngelSettlementConfig);

    /**
     * 分页查询护士结算配置
     *
     * @param queryContext
     * @return count
     */
    Page<JdhSettlementAreaFeeConfig> queryPage(JdhAngelSettleAreaFeeQuery queryContext);


    /**
     *
     * @param queryContext
     * @return
     */
    Integer queryCityConfigCount(JdhAngelSettleAreaFeeQuery queryContext);
}
