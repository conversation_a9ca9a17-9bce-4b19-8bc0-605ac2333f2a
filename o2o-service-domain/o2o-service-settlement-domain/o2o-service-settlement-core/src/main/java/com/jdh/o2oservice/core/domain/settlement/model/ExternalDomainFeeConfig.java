package com.jdh.o2oservice.core.domain.settlement.model;

import com.jdh.o2oservice.base.model.Aggregate;
import com.jdh.o2oservice.base.model.AggregateCode;
import com.jdh.o2oservice.base.model.DomainCode;
import com.jdh.o2oservice.base.model.DomainEnum;
import com.jdh.o2oservice.core.domain.settlement.enums.SettleAggregateEnum;
import lombok.Data;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Stream;

/**
 * @ClassName ExternalDomainFeeConfig
 * @Description
 * <AUTHOR>
 * @Date 2025/4/25 10:17
 **/
@Data
public class ExternalDomainFeeConfig implements Aggregate<ExternalDomainFeeConfigIdentifier> {

    /**
     * 主键
     */
    private Long id;
    /**
     * '费项配置文件id'
     */
    private Long feeConfigId;

    /**
     * 领域编码
     */
    private String externalDomainCode;

    /**
     * 聚合编码
     */
    private String externalAggregateCode;

    /**
     * 聚合ID
     */
    private String aggregateId;

    /**
     * 结算主体类型：护士、康复师等
     */
    private String settlementSubjectType;

    /**
     * 结算主体子类型：全职、兼职等
     */
    private String settlementSubjectSubType;

    /**
     * 数据来源分支
     */
    private String branch;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 是否有效 1-有效 0-无效
     */
    private Integer yn;
    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;
    /**
     * 创建人
     */
    private String createUser;
    /**
     * 修改人
     */
    private String updateUser;

    /**
     * 费项明细配置
     */
    private List<JdhSettlementFeeDetailConfig> detailConfigList;

    @Override
    public DomainCode getDomainCode() {
        return DomainEnum.SETTLE_MENT;
    }

    @Override
    public AggregateCode getAggregateCode() {
        return SettleAggregateEnum.SETTLE_FEE_EXTERNAL_DOMAIN_CONFIG;
    }

    @Override
    public Integer version() {
        return version;
    }

    @Override
    public void versionIncrease() {
        version++;
    }

    @Override
    public ExternalDomainFeeConfigIdentifier getIdentifier() {
        return ExternalDomainFeeConfigIdentifier.builder().feeConfigId(this.getFeeConfigId()).build();
    }

    /**
     * 根据费项类型获取对应配置list
     * @see com.jdh.o2oservice.core.domain.trade.enums.JdOrderFeeTypeEnum
     * @param feeType
     */
    public JdhSettlementFeeDetailConfig getDetailConfigListByFeeType(String feeType) {
        return Optional.ofNullable(detailConfigList).map(List::stream).orElseGet(Stream::empty)
                .filter(detailConfig -> Objects.equals(detailConfig.getFeeType(), feeType))
                .findFirst()
                .orElse(null);
    }

    /**
     * 根据费项类型获取对应配置金额
     * @see com.jdh.o2oservice.core.domain.trade.enums.JdOrderFeeTypeEnum
     * @param feeType
     */
    public BigDecimal getDetailConfigPriceByFeeType(String feeType) {
        JdhSettlementFeeDetailConfig config = getDetailConfigListByFeeType(feeType);
        return Objects.nonNull(config) ? config.getFeeAmount() : null;

    }
}