package com.jdh.o2oservice.core.domain.settlement.service;

import com.jdh.o2oservice.core.domain.settlement.bo.query.SettlementConfigDomainQuery;
import com.jdh.o2oservice.core.domain.settlement.context.SettlementConfigContext;
import com.jdh.o2oservice.core.domain.settlement.model.ExternalDomainFeeConfig;

import java.util.List;

/**
 * @ClassName SettlementConfigDomainService
 * @Description
 * <AUTHOR>
 * @Date 2025/4/25 15:33
 **/
public interface SettlementConfigDomainService {

    /**
     * 批量保存外部领域实体结算价配置
     * @param context
     * @return
     */
    Boolean batchSaveExternalDomainFeeConfig(SettlementConfigContext context);

    /**
     * 查询外部领域实体结算价配置
     * @param query
     * @return
     */
    List<ExternalDomainFeeConfig> queryExternalDomainFeeConfig(SettlementConfigDomainQuery query);
}