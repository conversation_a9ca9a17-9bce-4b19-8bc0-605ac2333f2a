package com.jdh.o2oservice.core.domain.settlement.model;

import com.jdh.o2oservice.base.model.Identifier;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName ExternalDomainFeeConfigIdentifier
 * @Description
 * <AUTHOR>
 * @Date 2025/4/25 10:17
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ExternalDomainFeeConfigIdentifier implements Identifier {

    /**
     * feeConfigId
     */
    private Long feeConfigId;

    @Override
    public String serialize() {
        return String.valueOf(feeConfigId);
    }


}