package com.jdh.o2oservice.core.domain.settlement.model;

import com.jdh.o2oservice.base.model.Identifier;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description: JdhSettlementAreaFeeConfigIdentifier
 * <AUTHOR>
 * @Date 2025/4/18
 * @Version V1.0
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class JdhSettlementAreaFeeConfigIdentifier implements Identifier {

    /**
     * areaFeeConfigId
     */
    private Long areaFeeConfigId;

    @Override
    public String serialize() {
        return String.valueOf(areaFeeConfigId);
    }


}
