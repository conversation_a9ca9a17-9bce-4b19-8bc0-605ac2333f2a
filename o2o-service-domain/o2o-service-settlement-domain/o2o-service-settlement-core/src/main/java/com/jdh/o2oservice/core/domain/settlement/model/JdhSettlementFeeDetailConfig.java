package com.jdh.o2oservice.core.domain.settlement.model;

import com.jdh.o2oservice.base.model.Aggregate;
import com.jdh.o2oservice.base.model.AggregateCode;
import com.jdh.o2oservice.base.model.DomainCode;
import com.jdh.o2oservice.base.model.DomainEnum;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName:JdhSettlementFeeDetailConfig
 * @Description:
 * @Author: lwm
 * @Date 2025/4/18
 * @Vserion: 1.0
 **/
@Data
public class JdhSettlementFeeDetailConfig implements Aggregate<JdhSettlementFeeDetailConfigIdentifier> {

    /**
     * 主键
     */
    private Long id;

    /**
     * '费项配置文件id'
     */
    private Long feeConfigDetailId;
    /**
     * '费项配置文件id'
     */
    private Long feeConfigId;
    /**
     * 费用类型：上门费、即时加价费、夜间加价费、节假日加价费
     */
    private String feeType;
    /**
     * 费项金额 单位元
     */
    private BigDecimal feeAmount;
    /**
     * 扩展信息
     */
    private String extend;
    /**
     * 数据来源分支
     */
    private String branch;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 是否有效 1-有效 0-无效
     */
    private Integer yn;
    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    @Override
    public DomainCode getDomainCode() {
        return DomainEnum.PRODUCT;
    }

    @Override
    public AggregateCode getAggregateCode() {
        return null;
    }

    @Override
    public Integer version() {
        return version;
    }

    @Override
    public void versionIncrease() {
        version++;
    }

    @Override
    public JdhSettlementFeeDetailConfigIdentifier getIdentifier() {
        return JdhSettlementFeeDetailConfigIdentifier.builder().feeConfigDetailId(this.getFeeConfigDetailId()).build();
    }
}
