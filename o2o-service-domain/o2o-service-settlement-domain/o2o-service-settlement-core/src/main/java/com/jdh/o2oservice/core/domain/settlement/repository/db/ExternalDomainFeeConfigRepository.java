package com.jdh.o2oservice.core.domain.settlement.repository.db;

import com.jdh.o2oservice.base.model.Repository;
import com.jdh.o2oservice.core.domain.settlement.model.ExternalDomainFeeConfig;
import com.jdh.o2oservice.core.domain.settlement.model.ExternalDomainFeeConfigIdentifier;
import com.jdh.o2oservice.core.domain.settlement.repository.query.ExternalDomainFeeConfigQuery;

import java.util.List;

/**
 * @ClassName JdhSettlementExternalDomainFeeConfig
 * @Description 外部领域实体结算价配置
 * <AUTHOR>
 * @Date 2025/4/25 10:16
 **/
public interface ExternalDomainFeeConfigRepository extends Repository<ExternalDomainFeeConfig, ExternalDomainFeeConfigIdentifier> {

    /**
     * 查询列表
     * @param query
     * @return
     */
    List<ExternalDomainFeeConfig> findList(ExternalDomainFeeConfigQuery query);

    /**
     * 批量保存
     * @param externalDomainFeeConfigs
     * @return
     */
    int batchSave(List<ExternalDomainFeeConfig> externalDomainFeeConfigs);
}