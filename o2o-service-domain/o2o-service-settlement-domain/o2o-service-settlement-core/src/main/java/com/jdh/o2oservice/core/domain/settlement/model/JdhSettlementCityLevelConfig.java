package com.jdh.o2oservice.core.domain.settlement.model;

import com.jdh.o2oservice.base.model.Aggregate;
import com.jdh.o2oservice.base.model.AggregateCode;
import com.jdh.o2oservice.base.model.DomainCode;
import com.jdh.o2oservice.base.model.DomainEnum;
import lombok.Data;

import java.util.Date;

/**
 * @ClassName:JdhSettlementCityLevelConfig
 * @Description:
 * @Author: lwm
 * @Date 2025/4/18
 * @Vserion: 1.0
 **/
@Data
public class JdhSettlementCityLevelConfig implements Aggregate<JdhSettlementCityLevelConfigIdentifier> {

    /**
     * 主键
     */
    private Long id;
    /**
     * '地区配置id'
     */
    private Long cityConfigId;

    /**
     * 省编码
     */
    private String provinceCode;
    /**
     * 省名称
     */
    private String provinceName;
    /**
     * 市编码
     */
    private String cityCode;
    /**
     * 市名称
     */
    private String cityName;
    /**
     * 目标地址code
     */
    private String destCode;
    /**
     * 城市等级
     */
    private String level;
    /**
     * 数据来源分支
     */
    private String branch;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 是否有效 1-有效 0-无效
     */
    private Integer yn;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 创建人
     */
    private String createUser;
    /**
     * 修改时间
     */
    private Date updateTime;
    /**
     * 修改人
     */
    private String updateUser;

    @Override
    public DomainCode getDomainCode() {
        return DomainEnum.PRODUCT;
    }

    @Override
    public AggregateCode getAggregateCode() {
        return null;
    }

    @Override
    public Integer version() {
        return version;
    }

    @Override
    public void versionIncrease() {
        version++;
    }

    @Override
    public JdhSettlementCityLevelConfigIdentifier getIdentifier() {
        return JdhSettlementCityLevelConfigIdentifier.builder().cityConfigId(this.getCityConfigId()).build();
    }
}
