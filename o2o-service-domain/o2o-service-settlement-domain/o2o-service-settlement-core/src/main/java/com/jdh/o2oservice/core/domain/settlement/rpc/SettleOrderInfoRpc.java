package com.jdh.o2oservice.core.domain.settlement.rpc;

import com.jdh.o2oservice.core.domain.settlement.bo.*;
import com.jdh.o2oservice.core.domain.settlement.context.AngelSettlementQueryContext;
import com.jdh.o2oservice.core.domain.settlement.vo.AngelCashOutVo;
import com.jdh.o2oservice.core.domain.settlement.vo.BankCardDetailVo;
import com.jdh.o2oservice.core.domain.settlement.vo.WithdrawAccountVo;
import com.jdh.o2oservice.export.support.command.PricingServiceCalculateCmd;
import com.jdh.o2oservice.export.support.dto.PricingServiceCalculateResultDto;

import java.util.List;

/**
 * 结算订单信息
 * @author: liwenming
 * @date: 2024/5/17 9:54 上午
 * @version: 1.0
 */
public interface SettleOrderInfoRpc {

    /**
     *
     *
     * @param orderItemId
     * @return
     */
    Long queryJdOrderItemByOrderItemId(Long orderItemId);

    /**
     * 查询护士信息
     * @param angelId
     * @return
     */
    JdhAngelInfoBo queryJdhAngelInfo(Long angelId);

    /**
     * 查询订单详情
     */
    JdOrderDetailBo queryOrderFullDetail(Long orderId);

    /**
     * 查询订单结算数据详情
     * @param orderId
     * @param serviceId
     * @return
     */
    JdOrderDetailBo queryOrderSettleDetail(Long orderId,String serviceId);

    /**
     *
     * @param orderId
     * @param serviceId
     * @param promiseId
     * @return
     */
    JdOrderDetailBo getSplitOrderSettleDetail(Long orderId,String serviceId,Long promiseId);

    /**
     *
     * @param orderId
     * @return
     */
    String findJdOrderExtContext(Long orderId);
    /**
     *
     * @param skuId
     * @return
     */
    JdhSkuBo queryJdhSkuInfoBySkuId(Long skuId);

    /**
     *
     * @param serviceItemIdList
     * @return
     */
    List<ServiceItemBo> queryServiceItemList(List<Long> serviceItemIdList);

    /**
     * 护士费用细项
     * @param addressInfo
     * @param dispatchAppointmentTimeBo
     * @return
     */
    List<ServiceFeeDetailBo> calcNoFreeOrderServiceFee(AddressInfoBo addressInfo,DispatchAppointmentTimeBo dispatchAppointmentTimeBo);

    /**
     * 派单预估结算价
     * @param angelId
     * @param orderId
     * @param promiseId
     * @return
     */
    String getOrderSettleSnapshotAmount(Long angelId,Long orderId,Long promiseId);
    /**
     *
     * @param orderId
     * @return
     */
    List<JdOrderMoneyBo> getJdOrderMoneyList(Long orderId);

    /**
     *
     * @param voucherId
     * @return
     */
    VoucherBo getVoucherDto(Long voucherId);

    /**
     *
     * @return
     */
    PricingServiceCalculateResultDto calculatePriceForDetail(PricingServiceCalculateCmd cmd);

}
