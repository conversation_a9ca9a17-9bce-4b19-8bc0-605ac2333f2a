package com.jdh.o2oservice.core.domain.settlement.model;

import com.jdh.o2oservice.base.model.Identifier;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description: JdhSettlementCityLevelConfigIdentifier
 * <AUTHOR>
 * @Date 2025/4/18
 * @Version V1.0
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class JdhSettlementCityLevelConfigIdentifier implements Identifier {


    /**
     * '地区配置id'
     */
    private Long cityConfigId;

    @Override
    public String serialize() {
        return String.valueOf(cityConfigId);
    }


}
