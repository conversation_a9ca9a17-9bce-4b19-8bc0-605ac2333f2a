package com.jdh.o2oservice.core.domain.settlement.repository.query;

import com.jdh.o2oservice.common.result.request.AbstractPageQuery;
import lombok.Data;

/**
 * @ClassName:CityAngelSettlementPageQuery
 * @Description:
 * @Author: lwm
 * @Date: 2025/4/21 13:06
 * @Vserion: 1.0
 **/
@Data
public class CityAngelSettlementPageQuery extends AbstractPageQuery {

    /**
     * 省编码
     */
    private String provinceCode;

    /**
     * 市编码
     */
    private String cityCode;

    /**
     * 城市等级
     */
    private String cityLevel;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 根据provinceCode、cityCode强制过滤，如果字段值为空，则带入null过滤
     */
    private Boolean forceAddressFilter;

    /**
     * 目标地址code
     */
    private String destCode;
}
