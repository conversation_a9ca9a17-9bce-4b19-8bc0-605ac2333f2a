package com.jdh.o2oservice.core.domain.settlement.bo.query;

import com.jdh.o2oservice.base.model.AggregateCode;
import com.jdh.o2oservice.base.model.DomainEnum;
import com.jdh.o2oservice.core.domain.settlement.enums.SettlementSubjectSubTypeEnum;
import com.jdh.o2oservice.core.domain.settlement.enums.SettlementSubjectTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName SettlementConfigDomainQuery
 * @Description
 * <AUTHOR>
 * @Date 2025/4/27 13:40
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SettlementConfigDomainQuery {

    /**
     * '费项配置文件id'
     */
    private Long feeConfigId;

    /**
     * 领域编码
     */
    private DomainEnum domainCode;

    /**
     * 聚合编码
     */
    private AggregateCode aggregateCode;

    /**
     * 聚合ID
     */
    private String aggregateId;

    /**
     * 聚合ID列表
     */
    private List<String> aggregateIdList;

    /**
     * 结算主体类型：护士、康复师等
     */
    private SettlementSubjectTypeEnum settlementSubjectType;

    /**
     * 结算主体子类型：全职、兼职等
     */
    private SettlementSubjectSubTypeEnum settlementSubjectSubType;
}