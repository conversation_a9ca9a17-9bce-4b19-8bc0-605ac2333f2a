package com.jdh.o2oservice.core.domain.settlement.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * @Author: duanqiaona1
 * @Date: 2024/5/14 3:35 下午
 * @Description:
 */
@Getter
@AllArgsConstructor
public enum SettleFeeTypeEnum {

    ONSITE_FEE("1", "上门费"),
    IMMEDIATELY_FF("2", "即时加价"),
    HOLIDAY_FF("3", "节假日加价"),
    NIGHT_DOOR_FEE("4", "夜间加价"),
    DYNAMIC_ADJUST_FEE("5", "动态调整费"),
    ;

    /**
     *
     */
    private String type;

    /**
     *
     */
    private String desc;

}
