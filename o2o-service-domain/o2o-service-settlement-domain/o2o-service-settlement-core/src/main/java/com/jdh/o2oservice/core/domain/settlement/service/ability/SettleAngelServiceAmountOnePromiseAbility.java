package com.jdh.o2oservice.core.domain.settlement.service.ability;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jd.medicine.base.common.util.StringUtil;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.core.domain.settlement.bo.*;
import com.jdh.o2oservice.core.domain.settlement.context.AngelServiceFinishSettlementContext;
import com.jdh.o2oservice.core.domain.settlement.rpc.SettleOrderInfoRpc;
import com.jdh.o2oservice.core.domain.support.basic.enums.*;
import com.jdh.o2oservice.export.support.command.PricingServiceCalculateCmd;
import com.jdh.o2oservice.export.support.dto.PricingServiceCalculateResultDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;


/**
 * 结算-护士服务费
 *
 * @author: liwenming
 * @date: 2024/5/17 9:54 上午
 * @version: 1.0
 */
@Component
@Slf4j
public class SettleAngelServiceAmountOnePromiseAbility implements SettleAbility {

    /**
     * settleOrderInfoRpc
     */
    @Resource
    private SettleOrderInfoRpc settleOrderInfoRpc;

    /**
     * duccConfig
     */
    @Resource
    private DuccConfig duccConfig;


    /**
     * 当前能力点匹配的能力编码
     *
     * @return
     */
    @Override
    public SettleAbilityCode getAbilityCode() {
        return SettleAbilityCode.PROMISE_ANGEL_SERVICE_AMOUNT;
    }

    /**
     * 处理
     *
     * @param context
     */
    @Override
    public void execute(AngelServiceFinishSettlementContext context) {
        log.info("[SettleAngelServiceAmountAbility.execute] ,context={}", JSON.toJSONString(context));
        if(context.getAngelSettleBack()){
            log.info("[SettleAngelServiceAmountAbility.execute] 冲收入,settlementBusinessId={}",context.getSettlementBusinessId());
            return;
        }
        // 订单信息
        JdOrderDetailBo jdOrderDetailBo = getJdOrderDetailBo(context);
        String serviceFeeSnapshot = jdOrderDetailBo.getServiceFeeSnapshot();
        Long orderId = context.getOrderId();
        if(StringUtil.isBlank(serviceFeeSnapshot)){
            log.error("[SettleAngelServiceAmountAbility.execute] serviceFeeSnapshot is null,orderId={}",orderId);
            if(Objects.nonNull(jdOrderDetailBo.getParentId()) && jdOrderDetailBo.getParentId() > 0){
                orderId = jdOrderDetailBo.getParentId();
                context.setParentId(orderId);
            }
            serviceFeeSnapshot = settleOrderInfoRpc.getOrderSettleSnapshotAmount(context.getAngelId(),orderId,context.getPromiseId());
        }
        ServerSettleAmountBo serverSettleAmountBo = JSONObject.parseObject(serviceFeeSnapshot,ServerSettleAmountBo.class);

        context.setVoucherLastService(Boolean.TRUE);
        //计算此笔服务的收入（订单支付金额）
        OrderAngelSettleDetailBo orderAngelSettleDetailBo = bulidOrderAngelSettleDetail(context,jdOrderDetailBo);
        //最后一个服务获取订单支付的费项金额推送ebs
        if(context.getVoucherLastService()){
            List<JdOrderMoneyBo> jdOrderMoneyList = settleOrderInfoRpc.getJdOrderMoneyList(orderId);
            orderAngelSettleDetailBo.setFeeInComeAmountList(bulidOrderAngelSettleFeeBoList(jdOrderMoneyList));
        }
        Map<String, BigDecimal> feeAmountMap = serverSettleAmountBo.getFeeAmountMap();
        //结算优化新逻辑
        if(CollUtil.isNotEmpty(feeAmountMap)){
            //如果快照有feeAmountMap则说明走了结算优化新逻辑，使用新逻辑计算护士结算金额
            this.dealNewSettleAmount(context,serverSettleAmountBo);
        }else{
            //历史逻辑
            Map<Long,BigDecimal> angelSkuServiceAmountMap = serverSettleAmountBo.getAngelSkuServiceAmountMap();
            if(CollUtil.isNotEmpty(angelSkuServiceAmountMap)){
                BigDecimal angelSkuServiceAmount = angelSkuServiceAmountMap.get(Long.parseLong(context.getServiceId()));
                orderAngelSettleDetailBo.getItemOutComeAmountList().add(angelSkuServiceAmount);
            }
            //历史逻辑，如果是最后一个服务使用快照中记录的费项金额进行结算
            if(context.getVoucherLastService()){
                orderAngelSettleDetailBo.setFeeOutComeAmount(serverSettleAmountBo.getAngelFeeAmount());
                orderAngelSettleDetailBo.setFee4EbsOutComeAmount(serverSettleAmountBo.getAngelFeeAmount());
            }
        }

        context.setOrderId(jdOrderDetailBo.getOrderId());
        context.setOrderAngelSettleDetailBo(orderAngelSettleDetailBo);
        context.setAngelId(serverSettleAmountBo.getAngelId());
        context.setJdhAngelInfoBo(getJdhAngelDetail(context));
        log.info("SettleAngelServiceAmountOnePromiseAbility -> execute context={}",JSONUtil.toJsonStr(context));
    }

    /**
     *
     * @param context
     * @param jdOrderDetailBo
     * @return
     */
    private OrderAngelSettleDetailBo bulidOrderAngelSettleDetail(AngelServiceFinishSettlementContext context,JdOrderDetailBo jdOrderDetailBo){
        OrderAngelSettleDetailBo orderAngelSettleDetailBo = Objects.nonNull(context.getOrderAngelSettleDetailBo()) ?
                context.getOrderAngelSettleDetailBo() : new OrderAngelSettleDetailBo();
        VoucherBo voucherBo = settleOrderInfoRpc.getVoucherDto(context.getVoucherId());
        if(!(Objects.nonNull(voucherBo) && JdhVoucherStatusEnum.EXPIRED.getStatus().equals(voucherBo.getStatus()))){
            List<JdOrderItemBo> jdOrderItemList = jdOrderDetailBo.getJdOrderItemList();
            if(CollUtil.isNotEmpty(jdOrderItemList)){
                JdOrderItemBo jdOrderItemBo = jdOrderItemList.get(0);
                BigDecimal itemInComeAmount = this.calcSkuRefundAmount(context.getVoucherLastService(),jdOrderDetailBo,jdOrderItemBo);
                OrderAngelSettleFeeBo orderAngelSettleFeeBo = new OrderAngelSettleFeeBo();
                orderAngelSettleFeeBo.setOrderId(jdOrderItemBo.getOrderId());
                orderAngelSettleFeeBo.setServiceId(String.valueOf(jdOrderItemBo.getSkuId()));
                orderAngelSettleFeeBo.setItemInComeAmount(itemInComeAmount);
                List<OrderAngelSettleFeeBo> itemInComeAmountList = new ArrayList<>();
                itemInComeAmountList.add(orderAngelSettleFeeBo);
                orderAngelSettleDetailBo.setItemInComeAmountList(itemInComeAmountList);
            }
        }
        context.setOrderAngelSettleDetailBo(orderAngelSettleDetailBo);
        return orderAngelSettleDetailBo;
    }

    /**
     *
     * @param context
     * @param serverSettleAmountBo
     */
    private void dealNewSettleAmount(AngelServiceFinishSettlementContext context, ServerSettleAmountBo serverSettleAmountBo){
        Map<String,String> refundStatusRatioMapping = matchSettleRatio(context.getWorkStatus());
        PricingServiceCalculateCmd cmd = new PricingServiceCalculateCmd();
        cmd.setVerticalCode(context.getVerticalCode());
        cmd.setScene(PricingServiceSceneEnum.ANGEL_SETTLEMENT_PRICE_CALCULATE.getScene());
        cmd.setOrderId(context.getOrderId());
        cmd.setPromiseId(context.getPromiseId());
        cmd.setAngelId(context.getAngelId());
        OrderAngelSettleDetailBo orderAngelSettleDetailBo = context.getOrderAngelSettleDetailBo();

        Map<String, Object> factObjectMap = new HashMap<>();
        factObjectMap.putAll(serverSettleAmountBo.getFeeAmountMap());
        factObjectMap.put(PricingServiceFactObjectEnum.SKU_SERVICE_AMOUNT_MAP.getCode(), serverSettleAmountBo.getAngelSkuServiceAmountMap());
        factObjectMap.put(PricingServiceFactObjectEnum.MATERIAL_FEE_CONFIG.getCode(), serverSettleAmountBo.getMaterialFeeConfig());
        // 需要结算的检测单列表
        factObjectMap.put(PricingServiceFactObjectEnum.MEDICAL_PROMISE_LIST.getCode(), context.getMedicalPromises());
        // 是否是最后一个服务
        factObjectMap.put(PricingServiceFactObjectEnum.IS_LAST_SERVICE.getCode(), context.getVoucherLastService());
        // 当前节点的费项结算比例 ducc配置
        factObjectMap.put(PricingServiceFactObjectEnum.ANGEL_SETTLEMENT_FEE_RATIO_CONFIG.getCode(), refundStatusRatioMapping);
        cmd.setFactObjectMap(factObjectMap);
        PricingServiceCalculateResultDto calculateResultDto = settleOrderInfoRpc.calculatePriceForDetail(cmd);
        if(Objects.nonNull(calculateResultDto)){
            Map<String, BigDecimal> feeAmountMap2 = calculateResultDto.getFeeAmountMap();
            orderAngelSettleDetailBo.setFeeAmountMap(feeAmountMap2);
            //获取本次应结算的服务费
            BigDecimal skuServiceAmount = feeAmountMap2.getOrDefault(JdOrderFeeTypeEnum.ANGEL_SERVICE_FEE.name(), BigDecimal.ZERO);
            orderAngelSettleDetailBo.getItemOutComeAmountList().add(skuServiceAmount);
            this.getFeeAmount(feeAmountMap2,orderAngelSettleDetailBo);
        }else{
            orderAngelSettleDetailBo.setFeeOutComeAmount(BigDecimal.ZERO);
            orderAngelSettleDetailBo.getItemOutComeAmountList().add(BigDecimal.ZERO);
        }
    }

    /**
     * 匹配结算比例
     * @param freezeStatus
     */
    private Map<String,String> matchSettleRatio(Integer freezeStatus){
        // 退款比例
        Map<String, Map<String,String>> refundStatusFeeAmoutRatio = duccConfig.getRefundStatusFeeAmoutRatio();
        Map<String,String> refundStatusRatioMapping = JSONUtil.toBean(
                JSONUtil.toJsonStr(refundStatusFeeAmoutRatio.get(String.valueOf(freezeStatus))), new TypeReference<Map<String, String>>() {}, true);
        return refundStatusRatioMapping;
    }

    /**
     *
     * @param feeAmountMap
     * @return
     */
    private BigDecimal getFeeAmount(Map<String,BigDecimal> feeAmountMap,OrderAngelSettleDetailBo orderAngelSettleDetailBo){
        BigDecimal feeAmount = BigDecimal.ZERO;
        BigDecimal platformServiceInComeAmount = BigDecimal.ZERO;
        BigDecimal fee4EbsOutComeAmount = BigDecimal.ZERO;
        for (Map.Entry<String, BigDecimal> entry : feeAmountMap.entrySet()){
            JdOrderFeeTypeEnum feeTypeEnum = JdOrderFeeTypeEnum.getByName(entry.getKey());
            if (Objects.isNull(feeTypeEnum)) {
                continue;
            }
            if (!Objects.equals(feeTypeEnum.getAggregateTypeEnum(), FeeAggregateTypeEnum.SERVICE_FEE)) {
                if (Objects.equals(feeTypeEnum.getAggregateTypeEnum(), FeeAggregateTypeEnum.PLATFORM_SERVICE_FEE)) {
                    platformServiceInComeAmount = entry.getValue().abs();
                }else{
                    fee4EbsOutComeAmount = fee4EbsOutComeAmount.add(entry.getValue());
                }
                feeAmount = feeAmount.add(entry.getValue());
            }
        }
        orderAngelSettleDetailBo.setFeeOutComeAmount(feeAmount);
        orderAngelSettleDetailBo.setPlatformServiceInComeAmount(platformServiceInComeAmount);
        orderAngelSettleDetailBo.setFee4EbsOutComeAmount(fee4EbsOutComeAmount);
        return feeAmount;
    }

    /**
     *
     * @param jdOrderDetailBo
     * @param jdOrderItemBo
     * @return
     */
    private BigDecimal calcSkuRefundAmount(Boolean voucherLastService,JdOrderDetailBo jdOrderDetailBo,JdOrderItemBo jdOrderItemBo){
        // 订单实付款
        BigDecimal orderRefundAmount = jdOrderDetailBo.getOrderAmount();
        if(Objects.isNull(orderRefundAmount) || orderRefundAmount.compareTo(BigDecimal.ZERO) <= 0){
            return BigDecimal.ZERO;
        }
        // ext金额
        BigDecimal feeAmount = bulidOrderAngelSettleFeeAmount(jdOrderDetailBo.getJdOrderMoneyList(),jdOrderDetailBo.getOrderId());
        // sku金额
        BigDecimal orderSkuTotalAmount = orderRefundAmount.subtract(feeAmount);
        // 单个sku金额
        BigDecimal orderSkuAvgAmount = orderSkuTotalAmount.divide(BigDecimal.valueOf(jdOrderItemBo.getSkuNum()),2,BigDecimal.ROUND_DOWN);
        // 最后一笔
        if(voucherLastService){
            BigDecimal lastSkuAmount = orderSkuTotalAmount.subtract(orderSkuAvgAmount.multiply(BigDecimal.valueOf(jdOrderItemBo.getSkuNum() - 1)));
            return lastSkuAmount;
        }
        return orderSkuAvgAmount;
    }

    /**
     *
     * @param context
     * @return
     */
    private JdOrderDetailBo getJdOrderDetailBo(AngelServiceFinishSettlementContext context){
        JdOrderDetailBo jdOrderDetailBo = context.getJdOrderDetailBo();
        if(Objects.isNull(jdOrderDetailBo)){
            jdOrderDetailBo = settleOrderInfoRpc.getSplitOrderSettleDetail(context.getOrderId(),context.getServiceId(),context.getPromiseId());
        }
        return jdOrderDetailBo;
    }

    /**
     *
     * @param jdOrderMoneyList
     * @return
     */
    private List<OrderAngelSettleFeeBo> bulidOrderAngelSettleFeeBoList(List<JdOrderMoneyBo> jdOrderMoneyList){
        Integer feeMoneyType = 507;
        List<OrderAngelSettleFeeBo> orderAngelSettleFeeBoList = new ArrayList<>();
        if(CollectionUtil.isNotEmpty(jdOrderMoneyList)){
            for(JdOrderMoneyBo jdOrderMoney : jdOrderMoneyList){
                if(feeMoneyType.equals(jdOrderMoney.getMoneyType())){
                    OrderAngelSettleFeeBo orderAngelSettleFeeBo = new OrderAngelSettleFeeBo();
                    orderAngelSettleFeeBo.setOrderId(jdOrderMoney.getOrderId());
                    orderAngelSettleFeeBo.setFeeInComeAmount(jdOrderMoney.getAmount());
                    orderAngelSettleFeeBoList.add(orderAngelSettleFeeBo);
                }
            }
        }
        return orderAngelSettleFeeBoList;
    }

    /**
     *
     * @param jdOrderMoneyList
     * @return
     */
    private BigDecimal bulidOrderAngelSettleFeeAmount(List<JdOrderMoneyBo> jdOrderMoneyList,Long orderId){
        Integer feeMoneyType = 507;
        BigDecimal feeInComeAmount = BigDecimal.ZERO;
        if(CollectionUtil.isNotEmpty(jdOrderMoneyList)){
            for(JdOrderMoneyBo jdOrderMoney : jdOrderMoneyList){
                if(orderId.equals(jdOrderMoney.getOrderId())){
                    if(feeMoneyType.equals(jdOrderMoney.getMoneyType())){
                        feeInComeAmount = feeInComeAmount.add(jdOrderMoney.getAmount());
                    }
                }

            }
        }
        return feeInComeAmount;
    }

    /**
     * 查询护士
     * @param context
     * @return
     */
    private JdhAngelInfoBo getJdhAngelDetail(AngelServiceFinishSettlementContext context){
        JdhAngelInfoBo jdhAngelInfoBo = context.getJdhAngelInfoBo();
        if(Objects.isNull(jdhAngelInfoBo)){
            jdhAngelInfoBo = settleOrderInfoRpc.queryJdhAngelInfo(context.getAngelId());
        }
        return jdhAngelInfoBo;
    }

}
