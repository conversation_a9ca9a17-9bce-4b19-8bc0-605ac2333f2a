package com.jdh.o2oservice.core.domain.settlement.enums;

import com.jdh.o2oservice.base.event.EventType;
import com.jdh.o2oservice.base.model.AggregateCode;

/**
 * 结算域事件类型枚举
 * @author: liwenming
 * @date: 2024/05/10 11:13
 * @version: 1.0
 */
public enum SettleEventTypeEnum implements EventType {

    /**
     * 服务者结算事件
     */
    SERVER_SETTLE(SettleAggregateEnum.SETTLE_ANGEL, "serverSettle", "服务者结算事件", null),

    REFUND_SERVER_SETTLE(SettleAggregateEnum.SETTLE_ANGEL, "refundServerSettle", "退款成功服务者结算", null),

    SERVICE_SETTLE_FOR_ANGEL_TO_EBS(SettleAggregateEnum.SETTLE_ANGEL, "serviceSettle4AngelToEbs", "服务完成护士结算和推送ebs", null),

    ANGEL_SETTLE_AMOUNT_FOR_CASH(SettleAggregateEnum.SETTLE_ANGEL, "angelSettleAmount4Cash", "护士结算金额可提现", null),

    ANGEL_SETTLE_BY_WORK_SHIP(SettleAggregateEnum.SETTLE_ANGEL, "angelSettleByWorkShip", "通过工单下运单状态触发上门检测护士结算", null),

    HOME_TEST_ANGEL_SETTLE_BY_SERVICE_COMPLETE(SettleAggregateEnum.SETTLE_ANGEL, "homeTestAngelSettleByServiceComplete", "服务完成触发上门检测护士结算", null),
    ;


    /** 事件所属领域，用于区分 */
    private SettleAggregateEnum aggregate;

    /** 事件名称 */
    private String code;

    /** 事件说明 */
    private String desc;

    private Class<?> bodyClass;

    SettleEventTypeEnum(SettleAggregateEnum aggregate, String code, String desc, Class<?> bodyClass) {
        this.aggregate = aggregate;
        this.code = code;
        this.desc = desc;
        this.bodyClass = bodyClass;
    }

    /**
     * 事件所属的领域
     */
    @Override
    public AggregateCode getAggregateType() {
        return aggregate;
    }

    /**
     * 事件编码
     */
    @Override
    public String getCode() {
        return code;
    }

    /**
     * 事件描述
     */
    @Override
    public String getDesc() {
        return desc;
    }

    @Override
    public Class<?> bodyClass() {
        return bodyClass;
    }

    /**
     * 通过代码获取enum
     *
     * @param eventCode 事件代码
     * @return {@link SettleEventTypeEnum}
     */
    public static SettleEventTypeEnum getEnumByCode(String eventCode){
        for (SettleEventTypeEnum value : SettleEventTypeEnum.values()) {
            if(value.getCode().equals(eventCode)){
                return value;
            }
        }
        return null;
    }
}
