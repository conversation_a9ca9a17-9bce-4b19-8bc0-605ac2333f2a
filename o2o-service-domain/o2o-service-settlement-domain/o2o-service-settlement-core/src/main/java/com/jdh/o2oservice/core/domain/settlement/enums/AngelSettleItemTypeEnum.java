package com.jdh.o2oservice.core.domain.settlement.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * @Author: duanqiaona1
 * @Date: 2024/5/29 11:38 上午
 * @Description:
 */
@Getter
@AllArgsConstructor
public enum AngelSettleItemTypeEnum {
    // 1上门检测服务 2上门护理服务 3调整项 4 激励 5 其他费项
    TESTING(1, "服务费",68),
    NURSING(2, "服务费",68),
    ADJUST(3, "保底工资调整",70),
    INCENTIVE(4, "激励奖金",70),
    FEE(5, "上门费",69),
    OTHER(6, "其他费项",69),
    ACTIVITY(7, "邀请佣金",70),

    ADJUST_TESTING(21, "人工调账-上门检测服务费",70),
    ADJUST_NURSING(22, "人工调账-上门护理服务费",70),
    ADJUST_ADJUST(23, "人工调账-保底工资",70),
    ADJUST_INCENTIVE(24, "人工调账-激励奖金",70),
    ADJUST_FEE(25, "人工调账-费项",70),
    ADJUST_OTHER(26, "人工调账-其他",70),
    ;


    /**
     *
     */
    private Integer type;

    /**
     *
     */
    private String desc;
    /**
     *
     */
    private Integer huYiFeeType;

    /**
     * @param type
     * @return
     */
    public static String getSettleTypeDescByType(Integer type) {
        if (Objects.isNull(type)) {
            return "";
        }
        for (AngelSettleItemTypeEnum settleTypeEnum : AngelSettleItemTypeEnum.values()) {
            if (Objects.equals(settleTypeEnum.getType(), type)) {
                return settleTypeEnum.getDesc();
            }
        }
        return "";
    }
}
