package com.jdh.o2oservice.core.domain.angelpromise.context;

import com.jdh.o2oservice.core.domain.angelpromise.model.AngelWork;
import com.jdh.o2oservice.core.domain.angelpromise.vo.ShipTask;
import com.jdh.o2oservice.core.domain.angelpromise.vo.UavResult;
import com.jdh.o2oservice.core.domain.support.vertical.context.BusinessContext;
import com.jdh.o2oservice.export.angelpromise.dto.AngelWorkCreateShipExtCmd;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * @ClassName:AngelWorkShipCreateContext
 * @Description:
 * @Author: yaoqinghai
 * @Date: 2024/4/21 19:57
 * @Vserion: 1.0
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AngelWorkShipCreateContext extends BusinessContext {

    /**
     * 工单id
     */
    private Long workId;

    /**
     * 门店编号，门店创建后可在门店列表查看，对应的是在达达侧创建的门店id
     */
    private String shopNo;

    /**
     * 骑手配送的实验室id
     */
    private String providerShopNo;

    /**
     * 第三方订单ID
     */
    private String originId;

    /**
     * 订单金额（单位：元）
     */
    private BigDecimal cargoPrice;

    /**
     * 是否需要垫付 1:是 0:否 (垫付订单金额，非运费)
     */
    private Integer isPrepay;

    /**
     * 发货人经度
     */

    private Double supplierLng;

    /**
     * 发货人纬度
     */
    private Double supplierLat;

    /**
     * 发货人地址
     */
    private String supplierAddress;

    /**
     * 发货人手机号
     */
    private String supplierPhone;

    /**
     * 发货人姓名
     */
    private String supplierName;

    /**
     * 收货人姓名
     */
    private String receiverName;

    /**
     * 收货人手机号
     */
    private String receiverPhone;

    /**
     * 收货人座机号（手机号和座机号必填一项）
     */
    private String receiverTel;

    /**
     * 收货人地址
     */
    private String receiverAddress;

    /**
     * 收货人地址纬度
     */
    private Double receiverLat;

    /**
     * 收货人地址经度
     */
    private Double receiverLng;

    /**
     * 回调URL
     */
    private String callback;

    /**
     * 订单重量（单位：Kg）
     */
    private Double cargoWeight;

    /**
     * 小费（单位：元，精确小数点后一位，小费金额不能高于订单金额。）
     */
    private BigDecimal tips;

    /**
     * 订单备注
     */
    private String info;

    /**
     * 支持配送的物品品类
     */
    private Integer cargoType;

    /**
     * 订单商品数量
     */
    private Integer cargoNum;

    /**
     * 发票抬头
     */
    private String invoiceTitle;

    /**
     * 预约发单时间（unix时间戳(10位)，精确到分）
     */
    private Integer delayPublishTime;

    /**
     * 是否根据期望送达时间预约发单（0-否，即时发单；1-是，预约发单）
     */
    private Integer isExpectFinishOrder;

    /**
     * 期望送达时间（单位秒，不早于当前时间）
     */
    private Long expectFinishTimeLimit;

    /**
     * 是否选择直拿直送（0：不需要；1：需要)
     */
    private Integer isDirectDelivery;

    /**
     * 服务者资源类型 1：骑手 2：护士
     * AngelTypeEnum
     */
    private Integer angelType;

    /**
     * 服务者资源明细类型 201：达达  202：顺丰
     * AngelDetailTypeEnum
     */
    private Integer angelDetailType;

    /**
     * 护士id
     */
    private String angelId;

    /**
     * 护士pin
     */
    private String angelPin;

    /**
     * 配送方式：1自配送 2运力配送 3闪送 4顺丰
     */
    private Integer deliveryType;

    /**
     * 被服务人set
     */
    private List<ShipTask> shipTaskList;

    /**
     * 工单
     */
    private AngelWork angelWork;

    /**
     * 级联地址 一级
     */
    private Long firstArea;

    /**
     * 级联地址
     */
    private String firstAreaName;

    /**
     * 级联地址 二级
     */
    private Long secondArea;

    /**
     * 级联地址
     */
    private String secondAreaName;

    /**
     * 级联地址 三级
     */
    private Long thirdArea;

    /**
     * 级联地址
     */
    private String thirdAreaName;

    /**
     * 级联地址 四级
     */
    private Long fourthArea;

    /**
     * 级联地址
     */
    private String fourthAreaName;

    /**
     * 调用重新下单接口，默认true，false走创建接口
     */
    private Boolean invokeRecall = true;

    /**
     * 扩展信息
     */
    private AngelWorkCreateShipExtCmd angelWorkCreateShipExt;

    private UavResult uavResult;//无人机信息

    private String startAirportId;//起飞机场ID

    private String endAirportId;//终点机场ID

    private Long parentShipId;//父运单id

    private String angelStationId;//服务站id

    private List<Long> medicalPromiseIds;//检测单id
}
