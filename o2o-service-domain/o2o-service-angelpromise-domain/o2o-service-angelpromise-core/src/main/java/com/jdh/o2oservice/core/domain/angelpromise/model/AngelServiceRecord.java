package com.jdh.o2oservice.core.domain.angelpromise.model;
import com.jdh.o2oservice.base.model.Aggregate;
import com.jdh.o2oservice.base.model.AggregateCode;
import com.jdh.o2oservice.base.model.DomainCode;
import com.jdh.o2oservice.base.model.DomainEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AngelServiceRecord implements Aggregate<AngelServiceRecordIdentifier> {

    /**
     * 主键
     */
    private Long id;

    /**
     * 垂直业务身份编码
     */
    private String verticalCode;

    /**
     * 服务类型
     */
    private String serviceType;

    /**
     * 服务记录ID
     */
    private Long recordId;

    /**
     * 任务单ID
     */
    private Long taskId;

    /**
     * 服务工单Id
     */
    private Long workId;

    /**
     * 履约单Id
     */
    private Long promiseId;

    /**
     * 服务者Id
     */
    private String angelId;

    /**
     * 服务的项目ID明细
     */
    private String serviceItemIdList;

    /**
     * 初始状态：0、完成:1、 取消：2、评估结果高风险：-1
     */
    private Integer status;

    /**
     * 已完成的最后一个节点
     */
    private String lastFlowNode;

    /**
     * 是否有效 1有效 0无效
     */
    private Integer yn;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 数据来源分支
     */
    private String branch;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人
     */
    private String updateUser;

    /**
     * 修改时间
     */
    private Date updateTime;

    @Override
    public DomainCode getDomainCode() {
        return DomainEnum.ANGEL_PROMISE;
    }

    @Override
    public AggregateCode getAggregateCode() {
        return null;
    }

    @Override
    public Integer version() {
        return version;
    }

    /**
     * 版本增加
     */
    @Override
    public void versionIncrease() {
        this.version++;
    }

    @Override
    public AngelServiceRecordIdentifier getIdentifier() {
        return new AngelServiceRecordIdentifier(this.recordId);
    }
}
