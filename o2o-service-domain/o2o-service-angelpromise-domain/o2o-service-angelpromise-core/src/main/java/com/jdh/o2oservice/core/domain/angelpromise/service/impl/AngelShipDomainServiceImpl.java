package com.jdh.o2oservice.core.domain.angelpromise.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.jd.jim.cli.Cluster;
import com.jd.matrix.core.biz.service.trace.ServiceTraceUtils;
import com.jd.matrix.core.domain.flow.OrderedDomainFlow;
import com.jd.matrix.core.domain.flow.engine.DomainFlowContext;
import com.jd.matrix.core.domain.flow.engine.DomainFlowEngine;
import com.jd.matrix.core.domain.flow.engine.IDomainFlowEngine;
import com.jdh.o2oservice.base.annotation.AutoTestSupport;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.enums.AutoTestMockTypeEnum;
import com.jdh.o2oservice.base.enums.RedisKeyEnum;
import com.jdh.o2oservice.base.event.Event;
import com.jdh.o2oservice.base.event.EventCoordinator;
import com.jdh.o2oservice.base.event.factory.EventFactory;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode;
import com.jdh.o2oservice.base.exception.errorcode.DynamicErrorCode;
import com.jdh.o2oservice.base.util.AssertUtils;
import com.jdh.o2oservice.base.util.GeoDistanceUtil;
import com.jdh.o2oservice.base.util.TimeFormat;
import com.jdh.o2oservice.common.enums.AngelDetailTypeEnum;
import com.jdh.o2oservice.common.enums.AngelTypeEnum;
import com.jdh.o2oservice.common.enums.DeliveryTypeEnum;
import com.jdh.o2oservice.common.ext.DomainBusinessIdentifierCode;
import com.jdh.o2oservice.common.ext.ExtResponse;
import com.jdh.o2oservice.common.ext.O2oBusinessIdentifier;
import com.jdh.o2oservice.core.domain.angelpromise.assembler.JdhAngelWorkAssembler;
import com.jdh.o2oservice.core.domain.angelpromise.bo.AngelRealTrackBo;
import com.jdh.o2oservice.core.domain.angelpromise.bo.UserAndStationPositionBo;
import com.jdh.o2oservice.core.domain.angelpromise.context.*;
import com.jdh.o2oservice.core.domain.angelpromise.enums.*;
import com.jdh.o2oservice.core.domain.angelpromise.event.eventbody.AngelShipEventBody;
import com.jdh.o2oservice.core.domain.angelpromise.factory.JdhAngelShipFactory;
import com.jdh.o2oservice.core.domain.angelpromise.factory.JdhAngelShipHistoryFactory;
import com.jdh.o2oservice.core.domain.angelpromise.model.*;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.*;
import com.jdh.o2oservice.core.domain.angelpromise.repository.query.AngelShipDBQuery;
import com.jdh.o2oservice.core.domain.angelpromise.repository.query.AngelTaskDBQuery;
import com.jdh.o2oservice.core.domain.angelpromise.service.AngelShipDomainService;
import com.jdh.o2oservice.core.domain.angelpromise.service.ability.WorkPromiseLinkAbility;
import com.jdh.o2oservice.core.domain.angelpromise.service.ability.WorkShipCallTransferAbility;
import com.jdh.o2oservice.core.domain.angelpromise.service.flow.angelship.SaveAngelShipService;
import com.jdh.o2oservice.core.domain.angelpromise.service.flow.angelship.ShipStatusCheckService;
import com.jdh.o2oservice.core.domain.angelpromise.service.flow.angelship.VerifySignatureService;
import com.jdh.o2oservice.core.domain.angelpromise.vo.UavResult;
import com.jdh.o2oservice.core.domain.support.basic.rpc.AddressRpc;
import com.jdh.o2oservice.core.domain.support.basic.rpc.DirectionServiceRpc;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.BaseAddressBo;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.DirectionResultBO;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.GisPointBo;
import com.jdh.o2oservice.core.domain.support.basic.rpc.param.DirectionRequestParam;
import com.jdh.o2oservice.core.domain.support.gismap.AoiMapServiceRpc;
import com.jdh.o2oservice.core.domain.support.gismap.bo.GeoCoordinateConversionBo;
import com.jdh.o2oservice.core.domain.support.ship.JdLogisticsRpc;
import com.jdh.o2oservice.core.domain.support.ship.param.*;
import com.jdh.o2oservice.core.domain.support.template.service.TemplateDomainService;
import com.jdh.o2oservice.core.domain.support.vertical.model.JdhVerticalBusiness;
import com.jdh.o2oservice.core.domain.support.vertical.repository.VerticalBusinessRepository;
import com.jdh.o2oservice.ext.ship.param.CancelDadaShipParam;
import com.jdh.o2oservice.ext.ship.param.CreateDadaShipParam;
import com.jdh.o2oservice.ext.ship.param.DeliveryOrderDetailRequest;
import com.jdh.o2oservice.ext.ship.reponse.*;
import com.jdh.o2oservice.ext.work.param.RealTrackParam;
import com.jdh.o2oservice.ext.work.param.TrackParam;
import com.jdh.o2oservice.ext.work.response.DeliveryRealTrackResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName:AngelShipDomainServiceImpl
 * @Description: 服务者运单消息处理
 * @Author: yaoqinghai
 * @Date: 2024/4/21 22:57
 * @Vserion: 1.0
 **/
@Component
@Slf4j
public class AngelShipDomainServiceImpl implements AngelShipDomainService {


    /**
     * 有效运单状态
     */
    private static final Set<Integer> VALID_SHIP_STATUS = Sets.newHashSet(AngelShipStatusEnum.WAITING_RECEIVE_GOODS.getShipStatus(),
            AngelShipStatusEnum.DELIVERING_GOODS.getShipStatus(),
            AngelShipStatusEnum.ORDER_SHIP_FINISH.getShipStatus(),
            AngelShipStatusEnum.KNIGHT_REACH_STORE.getShipStatus());
    @Resource
    private AngelShipRepository angelShipRepository;

    @Resource
    private AngelWorkRepository angelWorkRepository;

    @Resource
    private AngelTaskRepository angelTaskRepository;

    @Resource
    private AngelShipHistoryRepository angelShipHistoryRepository;

    @Resource
    private WorkShipCallTransferAbility workShipCallTransferAbility;

    @Resource
    private WorkPromiseLinkAbility workPromiseLinkAbility;

    /** */
    @Resource
    private EventCoordinator eventCoordinator;

    @Resource
    private AddressRpc addressRpc;

    @Resource
    private DuccConfig duccConfig;

    @Resource
    private VerifySignatureService verifySignatureService;

    @Resource
    private ShipStatusCheckService shipStatusCheckService;

    @Resource
    private SaveAngelShipService saveAngelShipService;

    @Resource
    private JdhAngelShipFeeRepository jdhAngelShipFeeRepository;

    @Resource
    private DirectionServiceRpc directionServiceRpc;

    @Resource
    private VerticalBusinessRepository verticalBusinessRepository;

    @Resource
    private TemplateDomainService templateDomainService;

    @Value("${shansong.config.clientId}")
    private String clientId;

    @Value("${shansong.config.shopId}")
    private String shopId;

    @Value("${shansong.config.accessToken}")
    private String accessToken;

    @Value("${shansong.config.url}")
    private String url;

    @Autowired
    private PlatformTransactionManager platformTransactionManager;

    @Autowired
    private JdLogisticsRpc jdLogisticsRpc;

    @Autowired
    private AoiMapServiceRpc aoiMapServiceRpc;

    @Resource
    private Cluster jimClient;


    /**
     * 接收服务者运单消息
     *
     * @param callBackContext
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "AngelShipDomainServiceImpl.receiveAngelShipCallback")
    public boolean receiveAngelShipCallback(AngelShipCallBackContext callBackContext) {
        //检查运单
        AngelShipDBQuery angelShipDBQuery = new AngelShipDBQuery();
        if(DeliveryTypeEnum.JD_LOGISTICS_DELIVERY.getType().equals(callBackContext.getDeliveryType())||DeliveryTypeEnum.UAV.getType().equals(callBackContext.getDeliveryType())){
            angelShipDBQuery.setOutShipId(callBackContext.getClientId());
        }else{
            angelShipDBQuery.setShipIds(Lists.newArrayList(callBackContext.getOrderId()));
        }
        if(StringUtils.isEmpty(callBackContext.getClientId())&&callBackContext.getOrderId()==null){
            throw new BusinessException(AngelPromiseBizErrorCode.ANGEL_WORK_EXECUTE_ARGUMENT_ILLEGAL);
        }
        if(DeliveryTypeEnum.UAV.getType().equals(callBackContext.getDeliveryType())){
            //如果是无人机,需要传入type=6
            angelShipDBQuery.setTypes(Lists.newArrayList(DeliveryTypeEnum.UAV.getType()));
        }
        List<AngelShip> list = angelShipRepository.findList(angelShipDBQuery);
        if(CollectionUtils.isEmpty(list)){
            log.error("[AngelShipDomainServiceImpl.receiveAngelShipCallback],没有查询到运单信息!callBackContext={}", JSON.toJSONString(callBackContext));
            return false;
        }
        AngelShip firstAngelShip = list.get(0);
        //检查工单
        AngelWork angelWork = angelWorkRepository.find(new AngelWorkIdentifier(firstAngelShip.getWorkId()));
        if(Objects.isNull(angelWork)){
            log.error("[AngelShipDomainServiceImpl.receiveAngelShipCallback],工单信息不存在!callBackContext={}", JSON.toJSONString(callBackContext));
            throw new BusinessException(AngelPromiseBizErrorCode.ANGEL_WORK_NOT_EXIST);
        }
        callBackContext.setVerticalCode(angelWork.getVerticalCode());
        callBackContext.setServiceType(angelWork.getServiceType());
        callBackContext.initVertical();

        for (AngelShip angelShip:list) {
            O2oBusinessIdentifier businessIdentifier = O2oBusinessIdentifier.builder()
                    .verticalCode(angelWork.getVerticalCode())
                    .serviceType(angelWork.getServiceType())
                    .businessMode(callBackContext.getVerticalBusiness().getBusinessModeCode())
                    .angelType(AngelTypeEnum.DELIVERY.getType())
                    .angelDetailType(DeliveryTypeEnum.fetchAngelDetailType(angelShip.getType()))
                    .build();

            IDomainFlowEngine engine = new DomainFlowEngine.Builder()
                    .code("deliveryStatusBack")
                    .name("运单状态回传")
                    .domainCode(DomainBusinessIdentifierCode.DOMAIN_ANGEL_PROMISE_CODE).build();

            //运力状态回传
            OrderedDomainFlow conditionTransfer = new OrderedDomainFlow.Builder()
                    .code("deliveryStatusBack")
                    .name("骑手状态回传")
                    .then(verifySignatureService)
                    .then(shipStatusCheckService)
                    .then(saveAngelShipService)
                    .build();

            Map<String, Object> header = Maps.newHashMap();
            header.put("angelShip", angelShip);
            header.put("angelWork", angelWork);
            header.put("businessIdentifier", businessIdentifier);
            DomainFlowContext context = new DomainFlowContext(businessIdentifier, callBackContext, header);
            try {
                engine.start(conditionTransfer, context);
                ServiceTraceUtils.echo();
            } catch (Exception e) {
                log.error("[AngelShipDomainServiceImpl.receiveAngelShipCallback],运单状态回传处理异常!", e);
                throw new BusinessException(AngelPromiseBizErrorCode.CREATE_ANGEL_WORK_ERROR);
            }

            //4、发送事件
            AngelShipEventTypeEnum angelShipEventTypeEnum = AngelShipStatusEnum.matchAngelShipEventTypeEnum(angelShip.getShipStatus());
            if(Objects.isNull(angelShipEventTypeEnum)){
                log.error("[AngelShipDomainServiceImpl.receiveAngelShipCallback],没有匹配到运单事件,不发送事件消息!angelShip={}", JSON.toJSONString(angelShip));
                return Boolean.TRUE;
            }
            if(Objects.isNull(callBackContext.getSendEvent()) || BooleanUtils.isFalse(callBackContext.getSendEvent())){
                log.info("[AngelShipDomainServiceImpl.receiveAngelShipCallback],不需要发送事件!callBackContext={}", JSON.toJSONString(callBackContext));
                return Boolean.TRUE;
            }
            log.info("[AngelShipDomainServiceImpl.receiveAngelShipCallback],发送运单修改事件!callBackContext={}", JSON.toJSONString(callBackContext));
            AngelShipEventBody angelShipEventBody = new AngelShipEventBody();
            angelShipEventBody.setOperatorFrom(Objects.nonNull(callBackContext.getCancelFrom()) ? String.valueOf(callBackContext.getCancelFrom()) : null);
            angelShipEventBody.setReason(callBackContext.getCancelReason());
            angelShipEventBody.setAngelName(angelWork.getAngelName());
            angelShipEventBody.setWorkId(angelWork.getWorkId());
            angelShipEventBody.setCancelFrom(callBackContext.getCancelFrom());
            //传递标准取消code码
            angelShipEventBody.setStandCancelCode(angelShip.getStandCancelCode());
            Event publishEvent = EventFactory.newDefaultEvent(angelShip, angelShipEventTypeEnum, angelShipEventBody);
            eventCoordinator.publish(publishEvent);
        }
        return Boolean.TRUE;
    }

    /**
     * 创建运单
     *
     * @param shipCreateContext
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "AngelShipDomainServiceImpl.createAngelShip")
    @AutoTestSupport(whiteKey = "angelWork.promiseId", type = AutoTestMockTypeEnum.ENHANCE)
    public AngelShip createAngelShip(AngelWorkShipCreateContext shipCreateContext) {
        //手动创建事务
        TransactionStatus status = platformTransactionManager.getTransaction(new DefaultTransactionDefinition());

        //运单费用信息
        JdhAngelShipFee shipFee = null;

        //是否呼叫运力
        Boolean invokeCallTransfer = false;
        //呼叫运力所需参数
        CreateDadaShipParam createDadaShipParam=null;
        //呼叫运力所需参数
        O2oBusinessIdentifier businessIdentifier=null;

        //创建服务者运单
        AngelShip angelShip = null;

        //运单预计发单时间
        Date delayPublishTime = null;

        try{
            //init业务身份
            shipCreateContext.initVertical();

            //检查工单下实验室是否已经存在运单
            AngelShipDBQuery angelShipDBQuery = new AngelShipDBQuery();
            angelShipDBQuery.setWorkId(shipCreateContext.getWorkId());
            angelShipDBQuery.setReceiverId(shipCreateContext.getProviderShopNo());
            angelShipDBQuery.setTypes(Collections.singletonList(shipCreateContext.getAngelType()));
            List<AngelShip> list = angelShipRepository.findList(angelShipDBQuery);
            if(CollectionUtils.isNotEmpty(list) && CollectionUtils.isNotEmpty(
                    list.stream().filter(item -> AngelShipStatusEnum.checkForwardStatus(item.getShipStatus())).collect(Collectors.toList()))) {
                log.error("[AngelShipDomainServiceImpl.createAngelShip],该实验室已经存在运单不能重复创建!");
                return list.stream().filter(item -> AngelShipStatusEnum.checkForwardStatus(item.getShipStatus())).collect(Collectors.toList()).get(0);
            }

            //补充上下文的信息
            fillContext(shipCreateContext);
            log.info("[AngelShipDomainServiceImpl.createAngelShip], shipCreateContext={}", JSON.toJSONString(shipCreateContext));
            angelShip = JdhAngelShipFactory.create(shipCreateContext);
            //运单预计发单时间
            delayPublishTime = shipCreateContext.getAngelWork().getPlanCallTime();


            if(Objects.equals(AngelWorkTypeEnum.RIDER.getType(), shipCreateContext.getAngelWork().getWorkType())){
                angelShip.setSenderName(shipCreateContext.getSupplierName());
                angelShip.setSenderNameIndex(shipCreateContext.getSupplierName());
                angelShip.setSenderPhone(shipCreateContext.getSupplierPhone());
                angelShip.setSenderPhoneIndex(shipCreateContext.getSupplierPhone());
            }else {
                angelShip.setSenderName(shipCreateContext.getAngelWork().getAngelName());
                angelShip.setSenderNameIndex(shipCreateContext.getAngelWork().getAngelName());
                angelShip.setSenderPhone(shipCreateContext.getAngelWork().getAngelPhone());
                angelShip.setSenderPhoneIndex(shipCreateContext.getAngelWork().getAngelPhone());
                delayPublishTime = new Date();
            }
            if(Objects.equals(DeliveryTypeEnum.SELF_DELIVERY.getType(), shipCreateContext.getDeliveryType()) ||
                    Objects.equals(DeliveryTypeEnum.THIRD_DELIVERY.getType(), shipCreateContext.getDeliveryType())
            ){
                angelShip.setTransferId(shipCreateContext.getAngelWork().getAngelId());
                angelShip.setTransferName(shipCreateContext.getAngelWork().getAngelName());
                angelShip.setTransferNameIndex(shipCreateContext.getAngelWork().getAngelName());
                angelShip.setTransferPhone(shipCreateContext.getAngelWork().getAngelPhone());
                angelShip.setTransferPhoneIndex(shipCreateContext.getAngelWork().getAngelPhone());
                angelShip.setShipStatus(AngelShipStatusEnum.DELIVERING_GOODS.getShipStatus());
                AngelShip finalAngelShip = angelShip;
                angelShip.getShipHistoryList().stream().forEach(item -> item.setAfterStatus(finalAngelShip.getShipStatus()));
            }else {
                angelShip.setTransferName(null);
                angelShip.setTransferPhone(null);
                //创建运单
                createDadaShipParam = JdhAngelWorkAssembler.ins.convertToCreateDadaShipBo(shipCreateContext);
                AngelWork angelWork = shipCreateContext.getAngelWork();
                Long businessOrderNo = Objects.nonNull(angelWork.getJdOrderId()) ? angelWork.getJdOrderId() : angelWork.getPromiseId();
                createDadaShipParam.setBusinessOrderNo(String.valueOf(businessOrderNo));

                businessIdentifier = O2oBusinessIdentifier.builder()
                        .verticalCode(shipCreateContext.getVerticalCode())
                        .serviceType(shipCreateContext.getServiceType())
                        .businessMode(shipCreateContext.getVerticalBusiness().getBusinessModeCode())
                        .angelType(shipCreateContext.getAngelType())
                        .angelDetailType(shipCreateContext.getAngelDetailType())
                        .build();

                if(Boolean.TRUE.equals(shipCreateContext.getInvokeRecall()) && CollectionUtils.isNotEmpty(list)
                        && list.get(0).getShipStatus().equals(AngelShipStatusEnum.ORDER_SHIP_CANCEL.getShipStatus())
                        && DeliveryTypeEnum.RIDER_DELIVERY.getType().equals(list.get(0).getType())){
                    AngelShip currAngelShip = list.get(0);
                    angelShip.setId(currAngelShip.getId());
                    angelShip.setShipId(currAngelShip.getShipId());
                    createDadaShipParam.setOriginId(String.valueOf(currAngelShip.getShipId()));

                    AngelShipHistory shipHistory = angelShip.getShipHistoryList().get(0);
                    shipHistory.setBeforeStatus(currAngelShip.getShipStatus());
                    shipHistory.setShipId(currAngelShip.getShipId());

                    workShipCallTransferAbility.reCallTransfer(businessIdentifier, createDadaShipParam);
                }else {
                    invokeCallTransfer = true;
                }
            }
            log.info("[AngelShipDomainServiceImpl.createAngelShip], angelShip={}", JSON.toJSONString(angelShip));
            angelShipRepository.save(angelShip);
            angelShipHistoryRepository.save(angelShip.getShipHistoryList().get(0));
        }catch (Exception ex){
            log.error("createAngelShip 事务回滚",ex);
            platformTransactionManager.rollback(status);
            throw ex;
        }finally{
            try{
                if(!status.isCompleted()){
                    log.info("createAngelShip 事务关闭");
                    //手动提交事务
                    platformTransactionManager.commit(status);
                }
            }catch (Exception ex){
                log.info("createAngelShip 事务关闭异常",ex);
            }
        }

        if(invokeCallTransfer){
            try{
                createDadaShipParam.setOriginId(String.valueOf(angelShip.getShipId()));
                createDadaShipParam.setMedicalPromiseIds(shipCreateContext.getMedicalPromiseIds());
                ExtResponse<CreateShipExtResponse> extResponse = workShipCallTransferAbility.callTransfer(businessIdentifier, createDadaShipParam, delayPublishTime, shipCreateContext.getProviderShopNo());
                if(extResponse.isSuccess() && Objects.nonNull(extResponse.getData())) {
                    //配送距离，寄件地址到收件地址
                    angelShip.setTotalDistance(extResponse.getData().getTotalDistance());
                    angelShip.setPlanCallTime(extResponse.getData().getEstimateCallTime());
                    angelShip.setEstimateGrabTime(extResponse.getData().getEstimateGrabTime());
                    angelShip.setEstimateReceiveTime(extResponse.getData().getEstimateReceiveTime());
                    angelShip.setEstimatePickUpTime(extResponse.getData().getEstimatePickUpTime());
                    angelShip.setOutShipId(extResponse.getData().getOutOrderNo());

                    if(extResponse.getData().getExpectTakeoffTime()!=null){
                        //维护无人机起飞时间
                        UavResult uavResult = new UavResult();
                        uavResult.setFlightTime(new Date(extResponse.getData().getExpectTakeoffTime()*1000));
                        angelShip.getJdhAngelShipExtVo().setUavResult(uavResult);
                    }
                    if(StringUtils.isBlank(angelShip.getShopNo())) {
                        angelShip.setShopNo(extResponse.getData().getShopNo());
                    }
                    //拼装运单费用信息
                    CreateShipExtFeeResponse extFeeResponse = extResponse.getData().getExtFeeResponse();
                    shipFee = JdhAngelShipFactory.createShipFee(angelShip, extFeeResponse);

                    //走更新逻辑
                    angelShipRepository.updateByShipId(angelShip);
                }
                log.info("[AngelShipDomainServiceImpl.createAngelShip], shipFee={}", JSON.toJSONString(shipFee));
                jdhAngelShipFeeRepository.save(shipFee);
            }catch (Exception ex){
                log.error("[AngelShipDomainServiceImpl.createAngelShip], 异常:", ex);
                angelShip.setShipStatus(AngelShipStatusEnum.ORDER_SHIP_CANCEL.getShipStatus());
                angelShipRepository.updateByShipId(angelShip);
            }
        }
        return angelShip;
    }

    /**
     * 自配送完成
     *
     * @param deliverContext
     * @return
     */
    @Override
    @LogAndAlarm
    public boolean selfDeliveryFinish(DeliverContext deliverContext) {
        //检查护士配送完成的距离是否在规则范围内容
        Double maxGeoDistance = duccConfig.getMaxGeoDistance();
        if(Objects.isNull(maxGeoDistance)){
            maxGeoDistance = CommonConstant.FIFTY;
        }
        double distance = GeoDistanceUtil.calculateDistance(deliverContext.getAngelLatitude(), deliverContext.getAngelLongitude(), deliverContext.getLaboratoryLatitude(), deliverContext.getLaboratoryLongitude());
        if(distance > maxGeoDistance){
            log.error("[[AngelShipDomainServiceImpl.selfDeliveryFinish],点击运单完成未满足指定的距离限制.maxGeoDistance={},distance={}]", maxGeoDistance, distance);
            throw new BusinessException(AngelPromiseBizErrorCode.DISTANCE_NOT_LESS_THAN_CONFIG);
        }

        //查询运单信息
        AngelShipDBQuery angelShipDBQuery = new AngelShipDBQuery();
        angelShipDBQuery.setWorkId(Long.valueOf(deliverContext.getWorkId()));
        angelShipDBQuery.setShipIds(Lists.newArrayList(Long.valueOf(deliverContext.getShipId())));
        angelShipDBQuery.setReceiverId(deliverContext.getLaboratoryId());
        List<AngelShip> list = angelShipRepository.findList(angelShipDBQuery);

        if(CollectionUtils.isEmpty(list)){
            log.error("[AngelShipDomainServiceImpl.selfDeliveryFinish],运单信息不存在.deliverContext={}", JSON.toJSONString(deliverContext));
            throw new BusinessException(AngelPromiseBizErrorCode.SHIP_TASK_NOT_EXIST);
        }
        AngelShip angelShip = list.get(0);

        //检查工单的业务状态是否可以执行完成
        if(Objects.isNull(angelShip.getJdhAngelShipExtVo()) || CollectionUtils.isEmpty(angelShip.getJdhAngelShipExtVo().getShipTaskList())){
            log.error("[AngelShipDomainServiceImpl.selfDeliveryFinish],运单信息不存在.deliverContext={}", JSON.toJSONString(deliverContext));
            throw new BusinessException(AngelPromiseBizErrorCode.ANGEL_WORK_INFO_DELETION_ERROR);
        }
//        List<Long> taskIds = angelShip.getJdhAngelShipExtVo().getShipTaskList().stream().map(ShipTask::getTaskId).collect(Collectors.toList());
//        AngelTaskDBQuery angelTaskDBQuery = new AngelTaskDBQuery();
//        angelTaskDBQuery.setTaskIds(taskIds);
//        angelTaskDBQuery.setWorkId(Long.valueOf(deliverContext.getWorkId()));
//        List<AngelTask> angelTaskList = angelTaskRepository.findList(angelTaskDBQuery);
//        Set<Integer> bizStatusSet = angelTaskList.stream().filter(task -> task.checkValidStatus(true, true)).map(AngelTask::getBizExtStatus).collect(Collectors.toSet());
//        if(CollectionUtils.isEmpty(bizStatusSet)){
//            log.error("[AngelShipDomainServiceImpl.selfDeliveryFinish],当前状态不允许送检完成.deliverContext={}", JSON.toJSONString(deliverContext));
//            throw new BusinessException(AngelPromiseBizErrorCode.WORK_STATUS_INVALID_ERROR);
//        }
//
//        if(!bizStatusSet.stream().allMatch(item -> AngelBizExtStatusEnum.SAMPLE_DELIVERING.getType().equals(item))){
//            log.error("[AngelShipDomainServiceImpl.selfDeliveryFinish],当前状态不允许送检完成.deliverContext={}", JSON.toJSONString(deliverContext));
//            throw new BusinessException(AngelPromiseBizErrorCode.ANGEL_WORK_STATUS_FINISH_ERROR);
//        }

        if (!AngelWorkStatusEnum.deliveryBlankStatus().contains(deliverContext.getAngelWork().getWorkStatus())){
            log.error("[AngelShipDomainServiceImpl.selfDeliveryFinish],当前工单状态不允许送检完成.deliverContext={}", JSON.toJSONString(deliverContext));
            throw new BusinessException(AngelPromiseBizErrorCode.WORK_STATUS_INVALID_ERROR);
        }

        //记录历史
        Integer preStatus = angelShip.getShipStatus();
        angelShip.setShipStatus(AngelShipStatusEnum.ORDER_SHIP_FINISH.getShipStatus());
        angelShip.setUpdateTime(new Date());
        AngelShipHistory history = JdhAngelShipHistoryFactory.create(preStatus, angelShip);
        angelShipRepository.save(angelShip);
        angelShipHistoryRepository.save(history);

        //4、发送事件
        AngelShipEventTypeEnum angelShipEventTypeEnum = AngelShipStatusEnum.matchAngelShipEventTypeEnum(angelShip.getShipStatus());
        if(Objects.isNull(angelShipEventTypeEnum)){
            log.error("[AngelShipDomainServiceImpl.receiveAngelShipCallback],没有匹配到运单事件,不发送事件消息!angelShip={}", JSON.toJSONString(angelShip));
            return Boolean.FALSE;
        }
        Event publishEvent = EventFactory.newDefaultEvent(angelShip, angelShipEventTypeEnum, null);
        eventCoordinator.publish(publishEvent);
        return true;
    }

    /**
     * 取消运单
     *
     * @param cancelShipContext
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @LogAndAlarm
    public boolean cancelShip(AngelWorkShipCancelContext cancelShipContext) {
        AssertUtils.nonNull(cancelShipContext.getWorkId(), "工单id不能为空");
        AssertUtils.nonNull(cancelShipContext.getShipId(), "运单id不能为空");

        AngelWork angelWork = angelWorkRepository.find(AngelWorkIdentifier.builder().workId(Long.valueOf(cancelShipContext.getWorkId())).build());
        if(Objects.isNull(angelWork)){
            log.error("[AngelShipDomainServiceImpl.cancelShip],工单信息不存在!cancelShipContext={}", JSON.toJSONString(cancelShipContext));
            throw new BusinessException(AngelPromiseBizErrorCode.ANGEL_WORK_NOT_EXIST);
        }
        cancelShipContext.setVerticalCode(angelWork.getVerticalCode());
        cancelShipContext.setServiceType(angelWork.getServiceType());
        cancelShipContext.initVertical();

        //默认取消原因
        if (cancelShipContext.getCancelReasonId() != null) {
            cancelShipContext.setCancelReasonId(cancelShipContext.getCancelReasonId());
        } else {
            cancelShipContext.setCancelReasonId(CommonConstant.FOUR);
        }
        if (StringUtils.isNotBlank(cancelShipContext.getCancelReason())) {
            cancelShipContext.setCancelReason(cancelShipContext.getCancelReason());
        } else {
            cancelShipContext.setCancelReason("顾客取消订单");
        }

        AngelShipDBQuery angelShipDBQuery = new AngelShipDBQuery();
        angelShipDBQuery.setShipIds(Lists.newArrayList(cancelShipContext.getShipId()));
        angelShipDBQuery.setWorkId(cancelShipContext.getWorkId());
        angelShipDBQuery.setStatus(Sets.newHashSet(AngelShipStatusEnum.canRefundShipStatusList));
        List<AngelShip> list = angelShipRepository.findList(angelShipDBQuery);
        if(CollectionUtils.isEmpty(list)){
            log.error("[AngelShipDomainServiceImpl.cancelShip],可取消的运单信息不存在!cancelShipContext={}", JSON.toJSONString(cancelShipContext));
            throw new BusinessException(AngelPromiseBizErrorCode.SHIP_TASK_NOT_EXIST);
        }
        AngelShip angelShip = list.get(0);
        if (!AngelShipStatusEnum.matchCanCancelStatus(angelShip.getShipStatus())) {
            log.error("[AngelShipDomainServiceImpl.cancelShip],运单状态不允许取消!cancelShipContext={}", JSON.toJSONString(cancelShipContext));
            throw new BusinessException(AngelPromiseBizErrorCode.SHIP_STATUS_CAN_NOT_CANCEL);
        }
        Integer preStatus = angelShip.getShipStatus();
        angelShip.setShipStatus(AngelShipStatusEnum.ORDER_SHIP_CANCEL.getShipStatus());
        angelShip.setUpdater(cancelShipContext.getOperator());
        angelShip.setUpdateTime(new Date());

        AngelShipHistory shipHistory = JdhAngelShipHistoryFactory.create(preStatus, angelShip);
        //标记取消原因,根据取消原因做是否退款逻辑
        angelShip.setStandCancelCode(cancelShipContext.getStandCancelCode());
        angelShipRepository.save(angelShip);
        angelShipHistoryRepository.save(shipHistory);

        //取消运单
        CancelDadaShipParam cancelDadaShipParam = CancelDadaShipParam.builder()
                .orderId(String.valueOf(angelShip.getShipId()))
                .providerOrderId(angelShip.getOutShipId())
                .cancelReason(cancelShipContext.getCancelReason())
                .cancelReasonId(cancelShipContext.getCancelReasonId())
                .build();
        O2oBusinessIdentifier businessIdentifier = O2oBusinessIdentifier.builder()
                .verticalCode(cancelShipContext.getVerticalCode())
                .serviceType(cancelShipContext.getServiceType())
                .businessMode(cancelShipContext.getVerticalBusiness().getBusinessModeCode())
                .angelDetailType(DeliveryTypeEnum.fetchAngelDetailType(angelShip.getType()))
                .build();

        //自配送方式,不用掉三方供应商取消运力接口
        if(!DeliveryTypeEnum.SELF_DELIVERY.getType().equals(angelShip.getType())){
            ExtResponse<Boolean> booleanExtResponse = workShipCallTransferAbility.cancelTransfer(businessIdentifier, cancelDadaShipParam);
            if(Objects.isNull(booleanExtResponse) || !booleanExtResponse.getData()){
                log.error("[AngelShipDomainServiceImpl.cancelShip],取消运单失败!cancelDadaShipParam={}, booleanExtResponse={}",
                        JSON.toJSONString(cancelDadaShipParam), JSON.toJSONString(booleanExtResponse));
                throw new BusinessException(AngelPromiseBizErrorCode.CANCEL_SHIP_ERROR);
            }
        }
        return true;
    }

    /**
     * 查询骑手轨迹
     *
     * @param trackContext 骑手轨迹上下文
     */
    @Override
    @LogAndAlarm
    public AngelShipTrack getTransferTrack(AngelWorkTransferTrackContext trackContext) {
        AssertUtils.nonNull(trackContext, "查询起手参数入参为空");
        AssertUtils.nonNull(trackContext.getAngelWork(), "工单信息不能为空");
        if(Objects.isNull(trackContext.getShipId()) && Objects.isNull(trackContext.getPromiseId())){
            log.error("[AngelShipDomainServiceImpl.getTransferTrack],业务参数异常!trackContext={}", JSON.toJSONString(trackContext));
            throw new BusinessException(BusinessErrorCode.ILLEGAL_ARG_ERROR);
        }
        trackContext.initVertical();

        TrackParam trackParam = new TrackParam();
        trackParam.setOrderId(trackContext.getOutShipId());
        trackParam.setPromiseId(trackContext.getPromiseId());
        trackParam.setWorkStatus(trackContext.getAngelWork().getWorkStatus());
        trackParam.setShipStatus(trackContext.getShipStatus());
        trackParam.setShipId(trackContext.getShipId());
        trackParam.setAngelType(trackContext.getAngelType());
        trackParam.setAngelId(trackContext.getAngelId());

        O2oBusinessIdentifier businessIdentifier = O2oBusinessIdentifier.builder()
                .verticalCode(trackContext.getAngelWork().getVerticalCode())
                .serviceType(trackContext.getAngelWork().getServiceType())
                .businessMode(trackContext.getVerticalBusiness().getBusinessModeCode())
                .angelType(trackContext.getAngelType())
                .angelDetailType(DeliveryTypeEnum.fetchAngelDetailType(trackContext.getDeliveryType()))
                .build();
        try{
            ExtResponse<DeliveryTrackResponse> transferTrackResp = workPromiseLinkAbility.getTransferTrack(businessIdentifier, trackParam);
            log.info("[AngelShipDomainServiceImpl.getTransferTrack],transferTrackResp={}", JSON.toJSONString(transferTrackResp));
            if(Objects.isNull(transferTrackResp) || Objects.isNull(transferTrackResp.getData()) || !transferTrackResp.isSuccess()){
                return null;
            }
            return AngelShipTrack.builder().trackUrl(transferTrackResp.getData().getTrackUrl()).build();
        }catch(Exception ex){
            log.error("[AngelShipDomainServiceImpl.getTransferTrack],查询骑手轨迹异常!", ex);
            return null;
        }
    }

    /**
     * 转换运力供应商状态回传参数
     *
     * @param callbackMap
     * @param providerType
     * @return
     */
    @Override
    @LogAndAlarm
    public AngelShipCallBackContext parseToShipCallbackContext(Map<String, Object> callbackMap, AngelDetailTypeEnum providerType, AngelShip angelShip) {
        if(Objects.isNull(angelShip)) {
            log.error("[AngelShipDomainServiceImpl.parseToShipCallbackContext],运单信息不存在!callbackMap={}", JSON.toJSONString(callbackMap));
            throw new BusinessException(AngelPromiseBizErrorCode.SHIP_TASK_NOT_EXIST);
        }
        AngelWork angelWork = angelWorkRepository.find(new AngelWorkIdentifier(angelShip.getWorkId()));

        JdhVerticalBusiness jdhVerticalBusiness = verticalBusinessRepository.find(angelWork.getVerticalCode());
        O2oBusinessIdentifier businessIdentifier = O2oBusinessIdentifier.builder()
                .verticalCode(angelWork.getVerticalCode())
                .serviceType(angelWork.getServiceType())
                .businessMode(jdhVerticalBusiness.getBusinessModeCode())
                .angelType(AngelTypeEnum.DELIVERY.getType())
                .angelDetailType(providerType.getType())
                .build();

        ExtResponse<ShipCallbackParamResponse> responseExtResponse = workShipCallTransferAbility.parseToShipCallbackContext(businessIdentifier, callbackMap);
        if(Objects.isNull(responseExtResponse.getData()) || !responseExtResponse.isSuccess()){
            return null;
        }
        return JdhAngelWorkAssembler.ins.convertToShipCallBackContext(responseExtResponse.getData());
    }

    /**
     * 查询骑手实时轨迹
     *
     * @param angelShipTrackContext
     * @return
     */
    @Override
    @LogAndAlarm
    public AngelRealTrackBo getTransferRealTrack(AngelShipTrackContext angelShipTrackContext) {
        angelShipTrackContext.initVertical();

        if(Objects.equals(AngelTypeEnum.DELIVERY.getType(), angelShipTrackContext.getAngelType())) {
            AngelShip angelShip = angelShipRepository.find(new AngelShipIdentifier(angelShipTrackContext.getShipId()));
            if(Objects.isNull(angelShip)) {
                log.error("[AngelShipDomainServiceImpl -> getTransferRealTrack],运单信息不存在!");
                throw new BusinessException(AngelPromiseBizErrorCode.SHIP_TASK_NOT_EXIST);
            }
            angelShipTrackContext.setDeliveryType(angelShip.getType());
        }

        O2oBusinessIdentifier businessIdentifier = O2oBusinessIdentifier.builder()
                .businessMode(angelShipTrackContext.getVerticalBusiness().getBusinessModeCode())
                .verticalCode(angelShipTrackContext.getVerticalCode())
                .serviceType(angelShipTrackContext.getServiceType())
                .angelType(angelShipTrackContext.getAngelType())
                .angelDetailType(DeliveryTypeEnum.fetchAngelDetailType(angelShipTrackContext.getDeliveryType()))
                .build();

        RealTrackParam realTrackParam = JdhAngelWorkAssembler.ins.convertToAngelRealTrackParam(angelShipTrackContext);
        ExtResponse<DeliveryRealTrackResponse> transferRealTrack = workPromiseLinkAbility.getTransferRealTrack(businessIdentifier, realTrackParam);
        if(Objects.isNull(transferRealTrack.getData()) || !transferRealTrack.isSuccess()){
            return null;
        }
        return JdhAngelWorkAssembler.ins.convertToAngelRealTrackBo(transferRealTrack.getData());
    }

    /**
     * 获取用户和实验室的经纬度
     *
     * @param angelShipTrackContext
     */
    @Override
    @LogAndAlarm
    public UserAndStationPositionBo getUserAndStationPosition(AngelWork angelWork, AngelShipTrackContext angelShipTrackContext, AngelRealTrackBo angelRealTrackBo) {
        UserAndStationPositionBo rstPos = new UserAndStationPositionBo();

        DirectionRequestParam directionRequestParam = new DirectionRequestParam();
        directionRequestParam.setTravelMode(DirectionServiceRpc.TravelMode.BICYCLING);
        //服务者位置
        String angelPos = Joiner.on(",").join(angelRealTrackBo.getLat(), angelRealTrackBo.getLng());
        //查询骑手类型下用户和实验室的位置信息
        if(AngelTypeEnum.DELIVERY.getType().equals(angelShipTrackContext.getAngelType())) {
            if(Objects.isNull(angelShipTrackContext.getShipId())){
                log.error("[AngelShipDomainServiceImpl -> getUserAndStationPosition],运单id不能为空!angelShipTrackContext={}", JSON.toJSONString(angelShipTrackContext));
                throw new BusinessException(AngelPromiseBizErrorCode.SHIP_TASK_NOT_EXIST);
            }
            AngelShip angelShip = angelShipRepository.find(new AngelShipIdentifier(angelShipTrackContext.getShipId()));
            if(Objects.isNull(angelShip)) {
                log.error("[AngelShipDomainServiceImpl -> getUserAndStationPosition],运单信息不存在!angelShipTrackContext={}", JSON.toJSONString(angelShipTrackContext));
                throw new BusinessException(AngelPromiseBizErrorCode.SHIP_TASK_NOT_EXIST);
            }

            GisPointBo sendPosition = addressRpc.getLngLatByAddress(angelShip.getSenderFullAddress());
            GisPointBo receivePosition = addressRpc.getLngLatByAddress(angelShip.getReceiverFullAddress());
            rstPos.setUserPos(Joiner.on(",").join(sendPosition.getLatitude(), sendPosition.getLongitude()));
            rstPos.setStationPos(Joiner.on(",").join(receivePosition.getLatitude(), receivePosition.getLongitude()));

            if(Objects.nonNull(angelRealTrackBo.getRemainDuration())) {
                rstPos.setDuration(angelRealTrackBo.getRemainDuration());
            }

            if (AngelShipStatusEnum.SHIP_ORDER_INIT.getShipStatus().equals(angelShip.getShipStatus())
                    || AngelShipStatusEnum.WAITING_RECEIVE_ORDER.getShipStatus().equals(angelShip.getShipStatus())) {
                directionRequestParam.setFromLocation(angelPos);
                directionRequestParam.setToLocation(rstPos.getUserPos());
                if(StringUtils.isNotBlank(angelShipTrackContext.getEstimateGrabTime())){
                    LocalDateTime localDateTime = LocalDateTime.parse(angelShipTrackContext.getEstimateGrabTime(), TimeFormat.LONG_PATTERN_LINE.formatter);
                    Duration duration = Duration.between(localDateTime, LocalDateTime.now());
                    rstPos.setDuration(Math.toIntExact(duration.toMinutes()));
                }
            }else if (AngelShipStatusEnum.DELIVERING_GOODS.getShipStatus().equals(angelShip.getShipStatus())
                    || AngelShipStatusEnum.KNIGHT_REACH_STORE.getShipStatus().equals(angelShip.getShipStatus())) {
                directionRequestParam.setFromLocation(angelPos);
                directionRequestParam.setToLocation(rstPos.getStationPos());
            }else if (AngelShipStatusEnum.WAITING_RECEIVE_GOODS.getShipStatus().equals(angelShip.getShipStatus())) {
                directionRequestParam.setFromLocation(angelPos);
                directionRequestParam.setToLocation(rstPos.getUserPos());
            }else {
                log.error("[AngelShipDomainServiceImpl -> getUserAndStationPosition],该状态的不展示实时轨迹!angelShip={}", JSON.toJSONString(angelShip));
                throw new BusinessException(AngelPromiseBizErrorCode.WORK_STATUS_CAN_NOT_SHOW_ANGEL_TRACK_ERROR);
            }
        }else {
            if(!AngelWorkStatusEnum.showAngelRealTrackStatus(angelWork.getWorkStatus())) {
                log.error("[AngelShipDomainServiceImpl -> getUserAndStationPosition],该状态的工单不展示护士实时轨迹!angelWork={}", JSON.toJSONString(angelWork));
                throw new BusinessException(AngelPromiseBizErrorCode.WORK_STATUS_CAN_NOT_SHOW_ANGEL_TRACK_ERROR);
            }
            AngelTaskDBQuery angelTaskDBQuery = new AngelTaskDBQuery();
            angelTaskDBQuery.setWorkId(Long.valueOf(angelShipTrackContext.getWorkId()));
            List<AngelTask> taskList = angelTaskRepository.findList(angelTaskDBQuery);
            if(CollectionUtils.isEmpty(taskList)){
                throw new BusinessException(AngelPromiseBizErrorCode.ANGEL_TASK_NOT_EXIST);
            }
            AngelTask angelTask = taskList.stream().findFirst().get();
            rstPos.setUserPos(Joiner.on(",").join(angelTask.getPatientAddressLat(), angelTask.getPatientAddressLng()));

            directionRequestParam.setFromLocation(angelPos);
            directionRequestParam.setToLocation(rstPos.getUserPos());

        }
        DirectionResultBO directionResult = directionServiceRpc.getDirectionResult(directionRequestParam);
        rstPos.setAngelPosition(angelPos);
        rstPos.setMode(directionResult.getMode());
        rstPos.setDistance(directionResult.getDistance());
        if(Objects.isNull(rstPos.getDuration()) || rstPos.getDuration() < 0) {
            rstPos.setDuration(directionResult.getDuration().intValue());
        }
        return rstPos;
    }

    /**
     * 查询运单订单详情
     *
     * @param detailContext
     * @return
     */
    @Override
    @LogAndAlarm
    public DeliveryOrderDetailResponse getShipOrderDetail(AngelShipOrderDetailContext detailContext) {
        DeliveryOrderDetailResponse result = new DeliveryOrderDetailResponse();
        try{
            if(Objects.isNull(detailContext.getShipId())) {
                log.error("[AngelShipDomainServiceImpl -> getShipOrderDetail],参数不正确.detailContext={}", JSON.toJSONString(detailContext));
                return result;
            }
            AngelShipDBQuery angelShipDBQuery = new AngelShipDBQuery();
            angelShipDBQuery.setShipIds(Lists.newArrayList(detailContext.getShipId()));
            List<AngelShip> list = angelShipRepository.findList(angelShipDBQuery);
            if(CollectionUtils.isEmpty(list)) {
                log.error("[AngelShipDomainServiceImpl -> getShipOrderDetail],没有运单.detailContext={}", JSON.toJSONString(detailContext));
                return result;
            }
            AngelWork angelWork = angelWorkRepository.find(new AngelWorkIdentifier(list.get(0).getWorkId()));
            detailContext.setVerticalCode(angelWork.getVerticalCode());
            detailContext.setServiceId(angelWork.getServiceType());
            detailContext.initVertical();

            O2oBusinessIdentifier businessIdentifier = new O2oBusinessIdentifier();
            businessIdentifier.setAngelType(AngelTypeEnum.DELIVERY.getType());
            businessIdentifier.setVerticalCode(angelWork.getVerticalCode());
            businessIdentifier.setServiceType(angelWork.getServiceType());
            businessIdentifier.setBusinessMode(detailContext.getVerticalBusiness().getBusinessModeCode());
            businessIdentifier.setAngelDetailType(DeliveryTypeEnum.fetchAngelDetailType(list.get(0).getType()));

            DeliveryOrderDetailRequest deliveryOrderDetailRequest = new DeliveryOrderDetailRequest();
            deliveryOrderDetailRequest.setOrderId(String.valueOf(detailContext.getShipId()));
            ExtResponse<DeliveryOrderDetailResponse> shipOrderDetail = workShipCallTransferAbility.getShipOrderDetail(businessIdentifier, deliveryOrderDetailRequest);
            if(Objects.isNull(shipOrderDetail) || Objects.isNull(shipOrderDetail.getData())){
                log.info("[AngelShipDomainServiceImpl -> getShipOrderDetail],查询运单详情是空的!");
            }
            result = shipOrderDetail.getData();
        }catch (Exception ex) {
            log.error("[AngelShipDomainServiceImpl -> getShipOrderDetail],查询运单详情失败", ex);
        }
        return result;
    }


    @Override
    public DeliveryOrderDetailResponse getShipOrderDetailByWork(AngelWork angelWork, JdhVerticalBusiness business) {
        AngelShipDBQuery angelShipDBQuery = new AngelShipDBQuery();
        angelShipDBQuery.setWorkId(angelWork.getWorkId());
        // 获取当前服务单有效的运单信息 todo 初始化状态
        angelShipDBQuery.setStatus(VALID_SHIP_STATUS);
        List<AngelShip> list = angelShipRepository.findList(angelShipDBQuery);
        if(CollectionUtils.isEmpty(list)) {
            log.error("[AngelShipDomainServiceImpl -> getShipOrderDetailByWork],没有运单.workId={}", angelWork.getWorkId());
            return null;
        }
        AngelShip validShip = list.get(0);
        O2oBusinessIdentifier businessIdentifier = new O2oBusinessIdentifier();
        businessIdentifier.setAngelType(AngelTypeEnum.DELIVERY.getType());
        businessIdentifier.setVerticalCode(angelWork.getVerticalCode());
        businessIdentifier.setServiceType(angelWork.getServiceType());
        businessIdentifier.setBusinessMode(business.getBusinessModeCode());
        businessIdentifier.setAngelDetailType(DeliveryTypeEnum.fetchAngelDetailType(validShip.getType()));

        DeliveryOrderDetailRequest deliveryOrderDetailRequest = new DeliveryOrderDetailRequest();
        deliveryOrderDetailRequest.setOrderId(String.valueOf(validShip.getShipId()));
        deliveryOrderDetailRequest.setOutOrderId(validShip.getOutShipId());
        ExtResponse<DeliveryOrderDetailResponse> shipOrderDetail = workShipCallTransferAbility.getShipOrderDetail(businessIdentifier, deliveryOrderDetailRequest);
        if(Objects.isNull(shipOrderDetail) || Objects.isNull(shipOrderDetail.getData())){
            shipOrderDetail = new ExtResponse<>();
            shipOrderDetail.setData(new DeliveryOrderDetailResponse());
            log.info("[AngelShipDomainServiceImpl -> getShipOrderDetailByWork],查询运单详情是空的!");
        }
        shipOrderDetail.getData().setShipType(validShip.getType());
        shipOrderDetail.getData().setStatusCode(validShip.getShipStatus());
        shipOrderDetail.getData().setShipId(validShip.getShipId());
        shipOrderDetail.getData().setJdStoreId(validShip.getReceiverId());
        if (validShip.getJdhAngelShipExtVo() != null && validShip.getJdhAngelShipExtVo().getUavResult() != null) {
            DeliveryOrderDetailFailResult deliveryOrderDetailFailResult = new DeliveryOrderDetailFailResult();
            deliveryOrderDetailFailResult.setCode(validShip.getJdhAngelShipExtVo().getUavResult().getErrorCode());
            deliveryOrderDetailFailResult.setMsg(validShip.getJdhAngelShipExtVo().getUavResult().getErrorMsg());
            deliveryOrderDetailFailResult.setStartTime(validShip.getJdhAngelShipExtVo().getUavResult().getFlightTime());
            shipOrderDetail.getData().setDeliveryOrderDetailFailResult(deliveryOrderDetailFailResult);
        }
        return shipOrderDetail.getData();
    }


    /**
     * 补充上下文
     *
     * @param shipCreateContext
     */
    private void fillContext(AngelWorkShipCreateContext shipCreateContext) {
        //查询寄件地址经纬度
        GisPointBo receiverGis = addressRpc.getLngLatByAddress(shipCreateContext.getReceiverAddress());
        if(Objects.isNull(receiverGis)){
            throw new BusinessException(AngelPromiseBizErrorCode.RECEIVER_ADDRESS_INFO_ERROR);
        }
        shipCreateContext.setReceiverLat(receiverGis.getLatitude().doubleValue());
        shipCreateContext.setReceiverLng(receiverGis.getLongitude().doubleValue());

        GisPointBo supplierGis = addressRpc.getLngLatByAddress(shipCreateContext.getSupplierAddress());
        if(Objects.isNull(supplierGis)){
            throw new BusinessException(AngelPromiseBizErrorCode.RECEIVER_ADDRESS_INFO_ERROR);
        }
        shipCreateContext.setSupplierLat(supplierGis.getLatitude().doubleValue());
        shipCreateContext.setSupplierLng(supplierGis.getLongitude().doubleValue());

        shipCreateContext.setCallback(duccConfig.getDadaCallbackUrl());

        //获取京标地址
        BaseAddressBo jdAddressFromAddress = addressRpc.getJDAddressFromAddress(shipCreateContext.getSupplierAddress());
        shipCreateContext.setFirstArea(jdAddressFromAddress.getProvinceCode().longValue());
        shipCreateContext.setFirstAreaName(jdAddressFromAddress.getProvinceName());
        shipCreateContext.setSecondArea(jdAddressFromAddress.getCityCode().longValue());
        shipCreateContext.setSecondAreaName(jdAddressFromAddress.getCityName());
        shipCreateContext.setThirdArea(Objects.nonNull(jdAddressFromAddress.getDistrictCode()) ? jdAddressFromAddress.getDistrictCode().longValue() : null);
        shipCreateContext.setThirdAreaName(jdAddressFromAddress.getDistrictName());
        shipCreateContext.setFourthArea(Objects.nonNull(jdAddressFromAddress.getTownCode()) ? jdAddressFromAddress.getTownCode().longValue() : null);
        shipCreateContext.setFourthAreaName(jdAddressFromAddress.getTownName());
    }

    /**
     * 查询京东物流可约时间
     * @return
     */
    @Override
    @LogAndAlarm
    public CommonCheckPreCreateOrderBo queryAvailableTime(QueryAvailableTimeContext queryAvailableTimeContext){
        CommonCheckPreCreateOrderParam commonCheckPreCreateOrderParam = new CommonCheckPreCreateOrderParam();
        commonCheckPreCreateOrderParam.setCustomerCode(duccConfig.getJdLogisticsConfig().getCustomerCode());

        CommonCargoInfoParam commonCargoInfoParam = new CommonCargoInfoParam();
        commonCargoInfoParam.setWeight(new BigDecimal("0.2"));
        commonCargoInfoParam.setVolume(new BigDecimal("100"));
        commonCargoInfoParam.setName("消费医疗检测盒");
        commonCargoInfoParam.setQuantity(1);
        commonCheckPreCreateOrderParam.setCargoes(Collections.singletonList(commonCargoInfoParam));

        ContactBo sender = new ContactBo();
        sender.setFullAddress(queryAvailableTimeContext.getSenderFullAddress());
        commonCheckPreCreateOrderParam.setSenderContact(sender);

        ContactBo receiver = new ContactBo();
        receiver.setFullAddress(queryAvailableTimeContext.getReceiverFullAddress());
        commonCheckPreCreateOrderParam.setReceiverContact(receiver);

        commonCheckPreCreateOrderParam.setProductsReq(new CommonProductInfoBo());
        Boolean removeFirst = false;
        if(StringUtils.isEmpty(receiver.getFullAddress())){
            //兜底逻辑.如果收件地址为空,则使用寄件地址
            receiver.setFullAddress(sender.getFullAddress());
            removeFirst=true;
        }
        CommonCheckPreCreateOrderBo commonCheckPreCreateOrderBo = jdLogisticsRpc.commonCheckPreCreateOrderV1(commonCheckPreCreateOrderParam);
        if(removeFirst){
            //如果进入到该方法,说明收件地址走了兜底逻辑
            //咱们多做一步，不展示第一个时间，一般第一个时间约的比较满
            if(commonCheckPreCreateOrderBo!=null&&CollectionUtils.isNotEmpty(commonCheckPreCreateOrderBo.getPickupSliceTimes())
                    &&CollectionUtils.isNotEmpty(commonCheckPreCreateOrderBo.getPickupSliceTimes().get(0).getPickupSliceTimes())){
                commonCheckPreCreateOrderBo.getPickupSliceTimes().get(0).getPickupSliceTimes().remove(0);
            }
        }
        return commonCheckPreCreateOrderBo;
    }

    /**
     * 保存三方运力经纬度
     * @param angelShipPositionContext
     * @return
     */
    @Override
    @LogAndAlarm
    public Boolean savePosition(AngelShipPositionContext angelShipPositionContext) {
        AngelShipDBQuery angelShipDBQuery = new AngelShipDBQuery();
        angelShipDBQuery.setOutShipId(angelShipPositionContext.getOutShipId());
        List<AngelShip> angelShipList = angelShipRepository.findList(angelShipDBQuery);
        if(CollectionUtils.isEmpty(angelShipList)){
            log.info("shipPositionCallback 通过outShipId={} 未查询到angelShipList,逻辑终止!!!",angelShipPositionContext.getOutShipId());
            return false;
        }
        if(DeliveryTypeEnum.UAV.getType().equals(angelShipList.get(0).getType())){
            //无人机坐标WGS-84 转成国标
            GeoCoordinateConversionBo geoCoordinateConversionBo = new GeoCoordinateConversionBo();
            geoCoordinateConversionBo.setCoordinateSys(3);
            geoCoordinateConversionBo.setLocations(angelShipPositionContext.getLon()+","+angelShipPositionContext.getLat());
            String locations = aoiMapServiceRpc.geoCoordinateConversion(geoCoordinateConversionBo);
            if(StringUtils.isEmpty(locations)){
                throw new BusinessException(new DynamicErrorCode("-1","无人机坐标转换异常!!!"));
            }
            locations = locations.split(";")[0];
            String lon = locations.split(",")[0];
            String lat = locations.split(",")[1];
            angelShipPositionContext.setLon(Double.parseDouble(lon));
            angelShipPositionContext.setLat(Double.parseDouble(lat));
            String cacheKey = RedisKeyEnum.getRedisKey(RedisKeyEnum.DELIVERY_POSITION,angelShipPositionContext.getOutShipId());
            jimClient.setEx(cacheKey,JSON.toJSONString(angelShipPositionContext),RedisKeyEnum.DELIVERY_POSITION.getExpireTime(),RedisKeyEnum.DELIVERY_POSITION.getExpireTimeUnit());
            return true;
        }
        return true;
    }

    /**
     * 获取三方运力经纬度
     * @param angelShipPositionContext
     * @return
     */
    @Override
    @LogAndAlarm
    public String getPosition(AngelShipPositionContext angelShipPositionContext) {
        String cacheKey = RedisKeyEnum.getRedisKey(RedisKeyEnum.DELIVERY_POSITION,angelShipPositionContext.getOutShipId());
        return jimClient.get(cacheKey);
    }
}
