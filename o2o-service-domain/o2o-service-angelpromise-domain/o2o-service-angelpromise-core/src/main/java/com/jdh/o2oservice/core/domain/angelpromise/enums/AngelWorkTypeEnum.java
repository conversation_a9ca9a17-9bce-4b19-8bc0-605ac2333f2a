package com.jdh.o2oservice.core.domain.angelpromise.enums;

import com.jdh.o2oservice.common.enums.AngelTypeEnum;
import com.jdh.o2oservice.common.enums.BusinessModeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * AngelWorkTypeEnum
 *
 * <AUTHOR>
 * @date 2024-05-13 18:33
 */

@Getter
@AllArgsConstructor
public enum AngelWorkTypeEnum {

    /**
     * 骑手
     */
    RIDER(1, "用户自检测", "送检员", BusinessModeEnum.SELF_TEST, AngelTypeEnum.DELIVERY),
    /**
     * 京东物流
     */
    SELF_TEST_TRANSPORT(1, "用户自检测", "京东物流", BusinessModeEnum.SELF_TEST_TRANSPORT, AngelTypeEnum.DELIVERY),

    /**
     * 护士
     */
    NURSE(2, "上门检测", "护士", BusinessModeEnum.ANGEL_TEST, AngelTypeEnum.NURSE),

    /**
     * 护士
     */
    NURSE_NO_LABORATORY(2, "上门检测", "护士", BusinessModeEnum.ANGEL_TEST_NO_LABORATORY, AngelTypeEnum.NURSE),

    /**
     * 护工
     */
    CARE(3, "上门护理","护士",  BusinessModeEnum.ANGEL_CARE, AngelTypeEnum.NURSE),
    ;

    /**
     * 类型
     */
    private Integer type;

    /**
     * desc
     */
    private String desc;
    /**
     *
     */
    private String angelType;

    /**
     * 业务模式
     */
    private BusinessModeEnum businessModeEnum;

    /**
     * 业务模式
     */
    private AngelTypeEnum angelTypeEnum;

    /**
     * 根据类型查询枚举详情
     * @param type
     * @return
     */
    public static AngelWorkTypeEnum getEnumByCode(Integer type) {
        if (Objects.isNull(type)) {
            return null;
        }
        for (AngelWorkTypeEnum value : values()) {
            if (value.getType().equals(type)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 获取工单上服务者的类型
     * @param workType
     * @return
     */
    public static Integer fetchAngelModel(Integer workType) {
        if(workType == null) {
            return 0;
        }
        if(RIDER.getType().equals(workType)) {
            return 1;
        }else if(NURSE.getType().equals(workType) || CARE.getType().equals(workType)) {
            return 2;
        }else {
            return 0;
        }
    }

    public String getAngelType() {
        return angelType;
    }

    /**
     * 匹配骑手
     * @param workType
     * @return
     */
    public static boolean matchTransfer(Integer workType) {
        return AngelWorkTypeEnum.RIDER.getType().equals(workType);
    }


    /**
     * 匹配到家检测护理服务
     *
     * @param workType
     * @return
     */
    public static boolean matchToHome(Integer workType) {
        return AngelWorkTypeEnum.NURSE.getType().equals(workType)
                || AngelWorkTypeEnum.CARE.getType().equals(workType);
    }

    /**
     * 匹配对应的业务模式枚举
     *
     * @param enumByCode
     * @return
     */
    public static Integer matchType(BusinessModeEnum enumByCode) {
        if (Objects.isNull(enumByCode)) {
            return null;
        }
        for (AngelWorkTypeEnum value : values()) {
            if (Objects.equals(value.getBusinessModeEnum(), enumByCode)) {
                return value.getType();
            }
        }
        return null;
    }


    /**
     * 匹配对应的业务模式枚举
     *
     * @param enumByCode
     * @return
     */
    public static AngelWorkTypeEnum matchType(String enumByCode) {
        if (Objects.isNull(enumByCode)) {
            return null;
        }
        for (AngelWorkTypeEnum value : values()) {
            if (Objects.equals(value.getBusinessModeEnum().getCode(), enumByCode)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 判断是否是订单场景
     *
     * @param workType
     * @return
     */
    public static boolean matchOrderScene(Integer workType) {
        return AngelWorkTypeEnum.NURSE.getType().equals(workType)
                || AngelWorkTypeEnum.CARE.getType().equals(workType)
                || AngelWorkTypeEnum.RIDER.getType().equals(workType);
    }
}
