package com.jdh.o2oservice.core.domain.angelpromise.repository.query;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AngelServiceRecordDBQuery{

    /**
     * 服务记录ID
     */
    private Long recordId;

    /**
     * 任务单ID
     */
    private Long taskId;

    /**
     * 服务工单Id
     */
    private Long workId;

    /**
     * 履约单Id
     */
    private Long promiseId;

    /**
     * 服务者Id
     */
    private String angelId;

    /**
     * 初始状态：0、完成:1、 取消：2、评估结果高风险：-1
     */
    private Integer status;

    /**
     * 已完成的最后一个节点
     */
    private String lastFlowNode;

    /**
     * 护理单状态
     */
    private List<Integer> statusList;

}
