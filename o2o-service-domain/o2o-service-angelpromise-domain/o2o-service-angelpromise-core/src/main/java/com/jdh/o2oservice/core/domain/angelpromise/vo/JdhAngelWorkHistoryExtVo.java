package com.jdh.o2oservice.core.domain.angelpromise.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName:JdhAngelWorkHistoryExtVo
 * @Description: TODO
 * @Author: yaoqing<PERSON>
 * @Date: 2024/4/24 01:36
 * @Vserion: 1.0
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class JdhAngelWorkHistoryExtVo {

    /**
     * 操作原因
     */
    private String reason;

    /**
     * 经度
     */
    private String lon;

    /**
     * 维度
     */
    private String lat;
}
