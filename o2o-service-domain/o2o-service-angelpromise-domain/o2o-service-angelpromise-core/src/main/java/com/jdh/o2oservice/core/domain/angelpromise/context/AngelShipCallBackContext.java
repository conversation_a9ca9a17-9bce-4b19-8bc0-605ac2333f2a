package com.jdh.o2oservice.core.domain.angelpromise.context;

import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelPromiseBizErrorCode;
import com.jdh.o2oservice.core.domain.support.vertical.context.BusinessContext;
import lombok.Builder;
import lombok.Data;
import org.apache.commons.codec.digest.DigestUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * @ClassName:AngelShipDadaCallBackContext
 * @Description:
 * @Author: yaoqinghai
 * @Date: 2024/4/21 22:46
 * @Vserion: 1.0
 **/
@Data
@Builder
public class AngelShipCallBackContext extends BusinessContext {

    /**
     * 外部公司运力订单号，默认为空
     */
    private String clientId;

    /**
     * angelShip 运单id
     */
    private Long orderId;

    /**
     * 订单状态：对应标准运单状态
     */
    private Integer orderStatus;

    /**
     * 供应商订单状态：不同的运力供应商状态码不同
     */
    private Integer providerOrderStatus;

    /**
     * 订单状态描述
     */
    private String providerOrderStatusDesc;

    /**
     * 订单子状态
     */
    private Integer subStatus;

    /**
     * 订单子状态描述
     */
    private String subStatusDesc;

    /**
     * 重复回传状态原因(1-重新分配骑士，2-骑士转单)。重复的状态消息默认不回传，如系统支持可在开发助手-应用信息中开启【运单重抛回调通知】开关
     * 达达回传字段
     */
    private Integer repeatReasonType;

    /**
     * 订单取消原因,其他状态下默认值为空字符串
     */
    private String cancelReason;

    /**
     * 订单取消原因来源
     */
    private Integer cancelFrom;

    /**
     * 更新时间，时间戳除了创建达达运单失败=1000的精确毫秒，其他时间戳精确到秒
     */
    private Long updateTime;

    /**
     * 配送员id
     * 达达：达达配送员id，接单以后会传
     */
    private String dmId;

    /**
     * 配送员名称
     * 达达：配送员姓名，接单以后会传
     */
    private String dmName;

    /**
     * 配送员电话
     * 达达：配送员手机号，接单以后会传
     */
    private String dmMobile;

    /**
     * 骑手头像
     */
    private String dmHeadIcon;

    /**
     * 配送员纬度
     */
    private double latitude;

    /**
     * 配送员经度
     */
    private double longitude;

    /**
     * 配送员接单时间
     */
    private String time;

    /**
     * 预计送达时间文案
     */
    private String estimateDeliveryTimeTip;

    /**
     * 发件码
     */
    private String sendCode;

    /**
     * 收货码
     */
    private String finishCode;

    /**
     * 扣款金额, 单位：元
     */
    private BigDecimal deductAmount;

    /**
     * 取消责任人
     */
    private Integer punishType;

    /**
     * 送回费，单位：元
     */
    private BigDecimal sendBackFee;

    /**
     * 退款金额，单位：元
     */
    private BigDecimal drawback;

    /**
     * 加密串
     * 达达：对client_id, order_id, update_time的值进行字符串升序排列，再连接字符串，取md5值
     */
    private String signature;

    /**
     * 保存运单信息
     */
    private Boolean saveShip;

    /**
     * 发送事件
     */
    private Boolean sendEvent;

    /**
     * 物流动态
     */
    private String logisticsMessage;

    private Integer deliveryType;//对应 DeliveryTypeEnum

    private String errorMsg;//无人机异常状态  UavErrorResultEnum


    public void verifySignature() {
        // 根据参数计算出期望的正确签名
        String expectSign = generateSignature();

        // 期望签名结果和实际签名结果比较
        if (!Objects.equals(expectSign, signature)) {
            throw new BusinessException(AngelPromiseBizErrorCode.DADA_CALL_BACK_HANDLE_ERROR);
        }
    }
    private String generateSignature() {
        // 将签名相关字段加入list
        List<String> list = new ArrayList<>(4);
        list.add(clientId == null ? "" : clientId);
        list.add(orderId == null ? "" : orderId.toString());
        list.add(updateTime == null ? "" : updateTime.toString());

        // 将参与签名的字段的值进行升序排列
        Collections.sort(list);

        // 将排序过后的参数，进行字符串拼接
        String joinedStr = String.join("", list);

        // 对拼接后的字符串进行md5加密
        return DigestUtils.md5Hex(joinedStr);
    }
}
