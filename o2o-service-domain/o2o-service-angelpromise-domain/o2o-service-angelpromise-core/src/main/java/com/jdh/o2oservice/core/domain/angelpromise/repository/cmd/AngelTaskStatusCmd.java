package com.jdh.o2oservice.core.domain.angelpromise.repository.cmd;

import lombok.Data;

/**
 * @ClassName:AngelTaskStatusCmd
 * @Description:
 * @Author: yaoqinghai
 * @Date: 2024/5/16 23:13
 * @Vserion: 1.0
 **/
@Data
public class AngelTaskStatusCmd {

    /**
     * 任务单id
     */
    private Long taskId;

    /**
     * 任务单状态
     */
    private Integer taskStatus;

    /**
     * 任务暂停状态：0正常，1退款暂停, 2取消暂停
     */
    private Integer stopStatus;

    /**
     * 业务任务状态：0初始状态 21验证通过 22采样 23绑码 24送检中 25服务记录上传
     */
    private Integer bizExtStatus;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 工单id
     */
    private Long workId;

}
