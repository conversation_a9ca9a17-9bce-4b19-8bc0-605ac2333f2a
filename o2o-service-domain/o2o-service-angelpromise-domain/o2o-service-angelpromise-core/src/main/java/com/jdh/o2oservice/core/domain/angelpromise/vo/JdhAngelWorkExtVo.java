package com.jdh.o2oservice.core.domain.angelpromise.vo;

import com.jdh.o2oservice.base.model.ImgExifModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @ClassName:JdhAngelWorkExtVo
 * @Description: 扩展值对象
 * @Author: yaoqinghai
 * @Date: 2024/4/18 20:45
 * @Vserion: 1.0
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class JdhAngelWorkExtVo {

    /** work完成任务 待提交 */
    public static final Integer COMPLETE_STATUS_WAITING = 0;
    /** work完成任务 暂存 */
    public static final Integer COMPLETE_STATUS_TEMP = 1;
    /** work完成任务 提交 */
    public static final Integer COMPLETE_STATUS_ENDING = 2;
    /**
     * 订单
     */
    private AngelOrder angelOrder;

    /**
     * 费用
     */
    private AngelCharge angelCharge;

    /**
     * 客户确认信息类型,1-医嘱证明 2-患者签字
     */
    List<Integer> customerConfirm;

    /**
     * 服务记录类型,1-废料处理 2-服务记录 3-上传着装照片
     *
     */
    List<Integer> serviceRecord;

    /**
     * 医生着装图片
     */
    private List<Long> clothingFileIds;

    /**
     * 医疗废物处理图片
     */
    private List<Long> wasteDestroyFileIds;

    /**
     * 废弃物销毁照片文件ID
     */
    private List<Long> serviceRecordFileIds;

    /**
     * 医生着装图片扩展信息
     */
    private Map<String, ImgExifModel> clothingImgExif;

    /**
     * 医疗废物处理图片扩展信息
     */
    private Map<String, ImgExifModel> wasteDestroyImgExif;

    /**
     * 废弃物销毁照片扩展信息
     */
    private Map<String, ImgExifModel> serviceRecordImgExif;

    /**
     * 录音文件按照索引顺序排列
     */
    private List<Long> soundRecordingFileIds;

    /**
     * 服务记录提交状态 0：为提交；1：暂存；2：提交完成
     */
    private Integer complete;
    /**
     * 工单属性，1.上海+自营+护士检测
     */
    private Integer workAttribute;

    /**
     * 操作原因
     */
    private String reason;

    /**
     * 护士接单位置纬度
     */
    private Double angelReceiveLat;

    /**
     * 护士接单位置经度
     */
    private Double angelReceiveLng;

    /**
     * 护士接单位置距上门地址距离
     */
    private Double angelReceiveDistance;

    /**
     * 护士到达时间
     */
    private Date angelArrivedTime;

    /**
     * 修改次数
     */
    private Integer modifyTimes;

    /**
     * 修改日期原因内容
     */
    private Integer modifyReasonType;

    /**
     * 修改日期原因内容
     */
    private String modifyReason;

    /**
     * 修改前预约时间
     */
    private Date beforeAppointmentTime;
}
