package com.jdh.o2oservice.core.domain.angelpromise.repository.db;
import com.jdh.o2oservice.base.model.Repository;
import com.jdh.o2oservice.core.domain.angelpromise.model.*;
import com.jdh.o2oservice.core.domain.angelpromise.repository.query.AngelServiceRecordDBQuery;
import java.util.List;

public interface AngelServiceRecordRepository extends Repository<AngelServiceRecord, AngelServiceRecordIdentifier> {

    /**
     * 查询服务记录列表
     * @param query
     * @return
     */
    List<AngelServiceRecord> findList(AngelServiceRecordDBQuery query);

    /**
     * 保存
     * @param entity
     * @return count
     */
    int save(AngelServiceRecord entity);

    /**
     * 通过Identify 查询
     * @param identifier
     * @return
     */
    AngelServiceRecord find(AngelServiceRecordIdentifier identifier);

    /**
     * 删除
     * @param entity
     * @return
     */
    int remove(AngelServiceRecord entity);

    /**
     * 通过履约ID删除护理单
     * @param entity
     * @return
     */
    int removeByPromiseId(AngelServiceRecord entity);

}
