package com.jdh.o2oservice.core.domain.angelpromise.service.impl;
import cn.hutool.core.util.BooleanUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.event.EventCoordinator;
import com.jdh.o2oservice.base.event.factory.EventFactory;
import com.jdh.o2oservice.base.factory.GenerateIdFactory;
import com.jdh.o2oservice.common.enums.QuestionGroupTypeEnum;
import com.jdh.o2oservice.common.enums.ServiceRecordFlowStatusEnum;
import com.jdh.o2oservice.common.enums.ServiceRecordStatusEnum;
import com.jdh.o2oservice.core.domain.angelpromise.context.AngelServiceRecordContext;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelServiceRecordEventTypeEnum;
import com.jdh.o2oservice.core.domain.angelpromise.event.eventbody.AngelServiceRecordEventBody;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelServiceRecord;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelServiceRecordFlow;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelServiceRecordFlowRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelServiceRecordRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.query.AngelServiceRecordFlowDBQuery;
import com.jdh.o2oservice.core.domain.angelpromise.service.AngelServiceRecordDomainService;
import com.jdh.o2oservice.export.angelpromise.dto.AngelServiceRecordQuestionDto;
import com.jdh.o2oservice.export.angelpromise.dto.AngelServiceRecordQuestionGroupDto;
import com.jdh.o2oservice.export.angelpromise.dto.ServiceRecordAnswerQuestionDTO;
import com.jdh.o2oservice.export.angelpromise.dto.SubmitAngelServiceRecordDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 护理单
 */
@Component
@Slf4j
public class AngelServiceRecordDomainServiceImpl implements AngelServiceRecordDomainService {

    @Resource
    private AngelServiceRecordRepository angelServiceRecordRepository;

    @Resource
    private AngelServiceRecordFlowRepository angelServiceRecordFlowRepository;

    @Resource
    private GenerateIdFactory generateIdFactory;

    @Resource
    private EventCoordinator eventCoordinator;

    @Resource
    private DuccConfig duccConfig;

    /**
     * 提交护理单
     * @param context
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "AngelServiceRecordDomainServiceImpl.submitAngelServiceRecord")
    @Transactional
    public SubmitAngelServiceRecordDto submitAngelServiceRecord(AngelServiceRecordContext context) {
        // 查询当前已存在的护理单节点
        AngelServiceRecordFlow existCurrentServiceRecordFlow = this.queryServiceRecordFlow(context.getRecordId(), context.getFlowCode());
        log.info("AngelServiceRecordDomainServiceImpl submitAngelServiceRecord existCurrentServiceRecordFlow={}", JSON.toJSONString(existCurrentServiceRecordFlow));
        // 移除已存在的护理单节点
        if (Objects.nonNull(existCurrentServiceRecordFlow)){
            angelServiceRecordFlowRepository.remove(existCurrentServiceRecordFlow);
        }

        // 移除的签名节点
        String removeSignNode = this.findRemoveSignNode(context, existCurrentServiceRecordFlow);
        log.info("AngelServiceRecordDomainServiceImpl submitAngelServiceRecord removeSignNode={}", removeSignNode);
        // 护士返回上一步修改了信息，需清空签字
        if (StringUtils.isNotBlank(removeSignNode)){
            AngelServiceRecordFlow removeServiceRecordSignNode = this.queryServiceRecordFlow(context.getRecordId(), removeSignNode);
            log.info("AngelServiceRecordDomainServiceImpl submitAngelServiceRecord removeServiceRecordSignNode={}", JSON.toJSONString(removeServiceRecordSignNode));
            if (Objects.nonNull(removeServiceRecordSignNode)){
                angelServiceRecordFlowRepository.remove(removeServiceRecordSignNode);
            }
        }

        // 护理单节点
        Long recordFlowId = generateIdFactory.getId();
        AngelServiceRecordFlow angelServiceRecordFlowDB = AngelServiceRecordFlow.builder()
                .recordId(context.getRecordId())
                .recordFlowId(recordFlowId)
                .flowCode(context.getFlowCode())
                .flowName(context.getFlowName())
                .sortId(context.getSortId())
                .status(BooleanUtil.isTrue(context.getHighRiskOption())? ServiceRecordFlowStatusEnum.HIGH_RISK.getStatus() : ServiceRecordFlowStatusEnum.FINISH.getStatus())
                .detail(context.getDetail())
                .build();
        log.info("AngelServiceRecordDomainServiceImpl submitAngelServiceRecord angelServiceRecordFlowDB={}", JSON.toJSONString(angelServiceRecordFlowDB));
        angelServiceRecordFlowRepository.save(angelServiceRecordFlowDB);

        List<AngelServiceRecordFlow> angelServiceRecordFlowList = angelServiceRecordFlowRepository.findList(AngelServiceRecordFlowDBQuery.builder().recordId(context.getRecordId()).build());
        log.info("AngelServiceRecordDomainServiceImpl submitAngelServiceRecord angelServiceRecordFlowList={}", JSON.toJSONString(angelServiceRecordFlowList));

        boolean isAllFlowFinish = StringUtils.isNotBlank(removeSignNode) ? false : context.getFlowCode().equals(context.getLastFlowNode());
        log.info("AngelServiceRecordDomainServiceImpl submitAngelServiceRecord isAllFlowFinish={}", JSON.toJSONString(isAllFlowFinish));

        // 护理单
        AngelServiceRecord angelServiceRecordDB = AngelServiceRecord.builder()
                .id(context.getId())
                .recordId(context.getRecordId())
                .lastFlowNode(context.getFlowCode())
                .status(BooleanUtil.isTrue(context.getHighRiskOption()) ? ServiceRecordStatusEnum.HIGH_RISK.getStatus()
                        : (BooleanUtil.isTrue(isAllFlowFinish) ? ServiceRecordStatusEnum.FINISH.getStatus() : ServiceRecordStatusEnum.INIT.getStatus()))
                .build();
        log.info("AngelServiceRecordDomainServiceImpl submitAngelServiceRecord angelServiceRecordDB={}", JSON.toJSONString(angelServiceRecordDB));
        angelServiceRecordRepository.save(angelServiceRecordDB);

        // 高风险选项
        if (BooleanUtil.isTrue(context.getHighRiskOption())){
            String refundReason = "";
            if (QuestionGroupTypeEnum.PRERECEIVEASSESSMENT.getCode().equals(context.getFlowCode())){
                refundReason = "护士接单评估未通过退款";

            }else if (QuestionGroupTypeEnum.PRESERVICEASSESSMENT.getCode().equals(context.getFlowCode())){
                refundReason = "护士服务前评估未通过退款";
            }
            AngelServiceRecordEventBody eventBody = AngelServiceRecordEventBody.builder()
                    .recordId(context.getRecordId())
                    .recordFlowId(recordFlowId)
                    .taskId(context.getTaskId())
                    .promisePatientId(context.getPromisePatientId())
                    .promiseId(context.getPromiseId())
                    .refundReason(refundReason)
                    .serviceName(context.getServiceName())
                    .build();
            // 发布事件，触发给C端客户退款，并结束服务者履约单
            eventCoordinator.publish(EventFactory.newDefaultEvent(angelServiceRecordDB, AngelServiceRecordEventTypeEnum.SERVICE_RECORD_EVALUATE_HIGH_RISK, eventBody));
            log.info("submitAngelServiceRecord highRisk publish");
        }

        // 返回结果
        SubmitAngelServiceRecordDto result = new SubmitAngelServiceRecordDto();
        result.setTaskId(context.getTaskId());
        result.setServiceRecordStatus(angelServiceRecordDB.getStatus());
        return result;
    }

    private AngelServiceRecordFlow queryServiceRecordFlow(Long recordId, String flowCode) {
        AngelServiceRecordFlowDBQuery flowDBQuery = AngelServiceRecordFlowDBQuery.builder()
                .recordId(recordId)
                .flowCode(flowCode)
                .build();
        List<AngelServiceRecordFlow> serviceRecordFlows = angelServiceRecordFlowRepository.findList(flowDBQuery);
        if (CollectionUtils.isNotEmpty(serviceRecordFlows)){
            return serviceRecordFlows.get(0);
        }
        return null;
    }

    private String findRemoveSignNode(AngelServiceRecordContext context, AngelServiceRecordFlow existCurrentServiceRecordFlow){
        if (Objects.isNull(existCurrentServiceRecordFlow)){
            return null;
        }
        JSONObject obj = JSON.parseObject(duccConfig.getAngelServiceRecordConfig());
        // 评估节点
        List<String> evaluateFlowCodeChanges = JSON.parseArray(obj.getString("evaluateFlowCodeChange"), String.class);
        // 非评估节点
        List<String> otherFlowCodeChanges = JSON.parseArray(obj.getString("otherFlowCodeChange"), String.class);
        // 提交的问题答案
        List<ServiceRecordAnswerQuestionDTO> submitAnswerQuestionList = context.getAnswerQuestionDTOList();

        if (evaluateFlowCodeChanges.contains(context.getFlowCode())){// 当前节点在 接单前评估、服务前评估
            return matchRemoveSignNode(existCurrentServiceRecordFlow, submitAnswerQuestionList, QuestionGroupTypeEnum.PRESERVICESIGNATURE.getCode());
        }else if (otherFlowCodeChanges.contains(context.getFlowCode())){// // 当前节点在 耗材确认、服务记录、健康宣教、记录上传
            return matchRemoveSignNode(existCurrentServiceRecordFlow, submitAnswerQuestionList, QuestionGroupTypeEnum.SIGNCONFIRM.getCode());
        }
        return null;
    }

    private String matchRemoveSignNode(AngelServiceRecordFlow existCurrentServiceRecordFlow, List<ServiceRecordAnswerQuestionDTO> submitAnswerQuestionList, String signFlowCode) {
        log.info("matchRemoveSignNode signFlowCode={}", signFlowCode);
        AngelServiceRecordQuestionGroupDto serviceRecordQuestionGroupDB = JSON.parseObject(existCurrentServiceRecordFlow.getDetail(), AngelServiceRecordQuestionGroupDto.class);
        log.info("matchRemoveSignNode serviceRecordQuestionGroupDB={}", JSON.toJSONString(serviceRecordQuestionGroupDB));

        Map<String, AngelServiceRecordQuestionDto> answerQuestionDBMap =  serviceRecordQuestionGroupDB.getQuestionDTOS().stream().collect(Collectors
                .toMap(AngelServiceRecordQuestionDto::getQuesCode, Function.identity(), (key1, key2) -> key2));

        for (ServiceRecordAnswerQuestionDTO submitAnswerQuestion : submitAnswerQuestionList) {
            AngelServiceRecordQuestionDto answerQuestionDB = answerQuestionDBMap.get(submitAnswerQuestion.getQuesCode());
            if (Objects.isNull(answerQuestionDB)){
                log.info("matchRemoveSignNode hit 1");
                return signFlowCode;

            }
            if (StringUtils.isNotBlank(submitAnswerQuestion.getAnswerValue())){
                if (!submitAnswerQuestion.getAnswerValue().equals(answerQuestionDB.getAnswerValue())){
                    log.info("matchRemoveSignNode hit 2");
                    return signFlowCode;
                }
            }
            if (StringUtils.isNotBlank(answerQuestionDB.getAnswerValue())){
                if (!answerQuestionDB.getAnswerValue().equals(submitAnswerQuestion.getAnswerValue())){
                    log.info("matchRemoveSignNode hit 3");
                    return signFlowCode;
                }
            }
        }
        return null;
    }
}
