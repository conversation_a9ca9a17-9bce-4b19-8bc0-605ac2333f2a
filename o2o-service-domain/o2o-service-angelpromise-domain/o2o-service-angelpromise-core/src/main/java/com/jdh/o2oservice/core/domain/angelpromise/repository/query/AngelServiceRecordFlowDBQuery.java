package com.jdh.o2oservice.core.domain.angelpromise.repository.query;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AngelServiceRecordFlowDBQuery {

    /**
     * 服务记录ID
     */
    private Long recordId;

    /**
     * 服务记录节点ID
     */
    private Long recordFlowId;

    /**
     * 流程节点编码
     */
    private String flowCode;

    /**
     * 初始状态：0，提交通过:1，提交未通过评估高风险状态：-1
     */
    private Integer status;

}
