package com.jdh.o2oservice.core.domain.angelpromise.service.flow.angelship;

import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson.JSON;
import com.jd.matrix.core.domain.flow.DomainFlowNode;
import com.jd.matrix.core.domain.flow.InputMessage;
import com.jd.matrix.core.domain.flow.OutputMessage;
import com.jd.matrix.core.domain.flow.Rollbackable;
import com.jdh.o2oservice.base.enums.ShipRepeatReasonEnum;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.common.enums.DeliveryTypeEnum;
import com.jdh.o2oservice.core.domain.angelpromise.context.AngelShipCallBackContext;
import com.jdh.o2oservice.core.domain.angelpromise.enums.*;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelShip;
import com.jdh.o2oservice.ext.ship.param.CheckDeliverStatusParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.Set;

/**
 * @ClassName:ShipStatusCheckService
 * @Description:
 * @Author: yaoqinghai
 * @Date: 2024/5/28 10:32
 * @Vserion: 1.0
 **/
@Service("shipStatusCheckService")
@Slf4j
public class ShipStatusCheckService extends Rollbackable implements DomainFlowNode {

    /**
     * 检查状态并获取运单的nextStatus
     *
     * @param inputMessage
     * @return
     */
    @Override
    public OutputMessage call(InputMessage inputMessage) {
        log.info("[ShipStatusCheckService.call],状态检查 START");
        OutputMessage outputMessage = new OutputMessage();

        AngelShip angelShip = Convert.convert(AngelShip.class, inputMessage.getHeader("angelShip"));
        AngelShipCallBackContext callBackContext = Convert.convert(AngelShipCallBackContext.class, inputMessage.getBody());
        if(Objects.isNull(angelShip)){
            log.error("[ShipStatusCheckService.call],运单信息不存在!inputMessage={}", JSON.toJSONString(inputMessage));
            throw new BusinessException(AngelPromiseBizErrorCode.SHIP_TASK_NOT_EXIST);
        }

        //先补全运单后续的执行逻辑-默认都执行
        callBackContext.setSaveShip(Boolean.TRUE);
        callBackContext.setSendEvent(Boolean.TRUE);


        Integer preStatus = angelShip.getShipStatus();
        CheckDeliverStatusParam checkDeliverStatusParam = new CheckDeliverStatusParam();
        checkDeliverStatusParam.setCurrStatus(angelShip.getShipStatus());
        checkDeliverStatusParam.setTargetStatus(callBackContext.getOrderStatus());

        //如果是商家侧取消的运单，终止状态回传的流程
        if(AngelShipStatusEnum.ORDER_SHIP_CANCEL.getShipStatus().equals(callBackContext.getOrderStatus())) {
            if(angelShip.getStandCancelCode() == null){
                angelShip.setStandCancelCode(AngelShipCancelCodeStatusEnum.DELIVERY_CANCEL.getType());
            }else if(!AngelShipCancelCodeStatusEnum.findCancelStatusEnum(angelShip.getStandCancelCode()).getIsRefund()) {
                //如果取消运单时设置了StandCancelCode,按StandCancelCode来处理是否走后续逻辑
                log.error("[ShipStatusCheckService.call],是商家或系统取消的订单，运单状态终止执行由商家或系统侧执行!inputMessage={}", JSON.toJSONString(inputMessage));
                callBackContext.setSaveShip(Boolean.FALSE);
                callBackContext.setSendEvent(Boolean.FALSE);
                return outputMessage;
            }
        }

        //处理转单和重新派单逻辑
        if(reTransferDispatchShip(callBackContext, angelShip)) {
            angelShip.setShipStatus(checkDeliverStatusParam.getTargetStatus());
            inputMessage.setHeader("preStatus", preStatus);
            inputMessage.setBody(callBackContext);
            log.info("[ShipStatusCheckService.call], target status end");
            return outputMessage;
        }

        //不是转单或者重派继续执行
        log.info("[ShipStatusCheckService.call],运单状态处理!checkDeliverStatusParam={}", JSON.toJSONString(checkDeliverStatusParam));
        AngelShipStatusEnum dadaAngelShipStatusEnum = AngelShipStatusEnum.matchShipStatusEnum(checkDeliverStatusParam.getCurrStatus());
        if (Objects.isNull(dadaAngelShipStatusEnum)) {
            log.error("[ShipStatusCheckService.call],不兼容的状态不做处理!dadaAngelShipStatusEnum={}", JSON.toJSONString(checkDeliverStatusParam));
            throw new BusinessException(AngelPromiseBizErrorCode.DADA_CALL_BACK_HANDLE_ERROR);
        }
        Set<Integer> nextStatusSet = dadaAngelShipStatusEnum.getNextStatusSet();
        Boolean canExecute = nextStatusSet.contains(checkDeliverStatusParam.getTargetStatus());
        if(canExecute) {
            callBackContext.setSaveShip(Boolean.TRUE);
        }else {
            callBackContext.setSaveShip(Boolean.FALSE);
            log.error("[ShipStatusCheckService.call],不兼容的状态不做处理!nextStatusSet={}", JSON.toJSONString(nextStatusSet));
        }
        angelShip.setShipStatus(checkDeliverStatusParam.getTargetStatus());
        inputMessage.setHeader("preStatus", preStatus);
        inputMessage.setBody(callBackContext);
        log.info("[ShipStatusCheckService.call], target status end");
        return outputMessage;
    }

    /**
     * 处理转单和重新派单逻辑
     *
     * @param callBackContext
     * @param angelShip
     */
    private Boolean reTransferDispatchShip(AngelShipCallBackContext callBackContext, AngelShip angelShip) {
        if(Objects.isNull(callBackContext.getRepeatReasonType())) {
            return Boolean.FALSE;
        }
        if(ShipRepeatReasonEnum.RE_DISPATCH.getReasonType().equals(callBackContext.getRepeatReasonType())) {
            angelShip.setRepeatType(ShipRepeatReasonEnum.getNewReasonType(angelShip.getRepeatType(), ShipRepeatReasonEnum.RE_DISPATCH));
        }else if(ShipRepeatReasonEnum.RE_TRANSFER.getReasonType().equals(callBackContext.getRepeatReasonType())) {
            angelShip.setRepeatType(ShipRepeatReasonEnum.getNewReasonType(angelShip.getRepeatType(), ShipRepeatReasonEnum.RE_TRANSFER));
        }
        return Boolean.TRUE;
    }

    @Override
    public String getCode() {
        return AngelWorkFlowEnum.SHIP_STATUS_CHECK_SERVICE.getFlowCode();
    }

    @Override
    public String getName() {
        return AngelWorkFlowEnum.SHIP_STATUS_CHECK_SERVICE.getFlowDesc();
    }

    @Override
    public void rollBack(InputMessage inputMessage) {
        log.info("FLOW ROLLBACK: ======VerifySignatureService rollback biz=======");
    }
}
