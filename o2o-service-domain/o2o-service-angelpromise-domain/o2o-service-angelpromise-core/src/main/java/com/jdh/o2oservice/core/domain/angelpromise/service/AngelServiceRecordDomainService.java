package com.jdh.o2oservice.core.domain.angelpromise.service;
import com.jdh.o2oservice.core.domain.angelpromise.context.AngelServiceRecordContext;
import com.jdh.o2oservice.export.angelpromise.dto.SubmitAngelServiceRecordDto;

/**
 * 护理单
 */
public interface AngelServiceRecordDomainService {

    /**
     * 提交护理单
     * @param context
     * @return
     */
    SubmitAngelServiceRecordDto submitAngelServiceRecord(AngelServiceRecordContext context);
}
