package com.jdh.o2oservice.core.domain.angelpromise.model;

import com.google.common.collect.Lists;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.enums.YnStatusEnum;
import com.jdh.o2oservice.base.model.Aggregate;
import com.jdh.o2oservice.base.model.AggregateCode;
import com.jdh.o2oservice.base.model.DomainCode;
import com.jdh.o2oservice.base.model.DomainEnum;
import com.jdh.o2oservice.base.util.SpringUtil;
import com.jdh.o2oservice.base.util.TimeUtils;
import com.jdh.o2oservice.core.domain.angelpromise.enums.*;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelTaskRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.query.AngelTaskDBQuery;
import com.jdh.o2oservice.core.domain.angelpromise.vo.AngelCharge;
import com.jdh.o2oservice.core.domain.angelpromise.vo.AngelOrder;
import com.jdh.o2oservice.core.domain.angelpromise.vo.JdhAngelWorkExtVo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author:lichen55
 * @date 2024-05-14 14:33
 * @Description: 服务者工单领域对象
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Slf4j
public class AngelWork implements Aggregate<AngelWorkIdentifier> {

    /**
     * 自增主键
     */
    private Long id;

    /**
     * 服务工单Id
     */
    private Long workId;

    /**
     * 垂直业务身份编码
     */
    private String verticalCode;

    /**
     * 服务类型
     */
    private String serviceType;

    /**
     * 京东订单Id
     */
    private Long jdOrderId;

    /**
     * 来源Id，派单id
     */
    private Long sourceId;

    /**
     * 履约单Id
     */
    private Long promiseId;

    /**
     * 服务工单类型：1=用户自检测,2=上门检测,3=上门护理
     */
    private Integer workType;

    /**
     * 服务工单类型：1=骑手,2=护士,3=护工,4=康复师
     * 注意：这个字段要自己去ducc取，work_type_desc
     */
    private String workTypeDesc;

    /**
     * 服务者Id
     */
    private String angelId;

    /**
     * 服务者Cpin
     */
    private String angelPin;

    /**
     * 服务者头像
     */
    private String angelHeadImg;

    /**
     * 服务者姓名
     */
    private String angelName;

    /**
     * 服务者联系方式
     */
    private String angelPhone;

    /**
     * 服务者联系方式加密
     */
    private String angelPhoneIndex;

    /**
     * 服务开始时间
     */
    private Date workStartTime;

    /**
     * 服务结束时间
     */
    private Date workEndTime;

    /**
     * 计划派时间
     */
    private Date planCallTime;

    /**
     * 计划出门时间
     */
    private Date planOutTime;

    /**
     * 计划完成时间
     */
    private Date planFinishTime;

    /**
     * 预计服务费用
     */
    private BigDecimal angelCharge;

    /**
     * 服务者工单状态：1=待接单，2=待服务，3=已出门，4=服务中，5=送检中，6=服务完成，7=已退款，8=已取消
     */
    private Integer workStatus;

    /**
     * 工单暂停状态：0正常，1退款暂停, 2取消暂停
     */
    private Integer stopStatus;

    /**
     * 服务站Id
     */
    private String angelStationId;

    /**
     * 扩展信息：订单信息{订单Id,订单remark,预约人姓名、联系方式}，医生着装图片，医疗废物处理图片
     */
    private JdhAngelWorkExtVo jdhAngelWorkExtVo;

    /**
     * 保单Id
     */
    private String insureId;

    /**
     * 投保状态：1=审核中，2=成功，3=失败，4=失效
     */
    private Integer insureStatus;

    /**
     * 投保状态：1=审核中，2=成功，3=失败，4=失效
     */
    private String insureStatusDesc;

    /**
     * 数据有效性 0：无效 1：有效
     */
    private Integer yn;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人
     */
    private String updater;

    /**
     * 修改时间
     */
    private Date updateTime;


    /**
     * 服务者任务单列表
     */
    private List<AngelTask> angelTasks;

    /**
     * 服务者运单
     */
    private List<AngelShip> angelShips;

    /**
     * 工单历史
     */
    private List<AngelWorkHistory> workHistories;
    /**
     * 京东保险侧保单id
     */
    private String policyId;

    /**
     * 版本号
     */
    private Integer oldVersion;

    /**
     * 是否展示护理单
     */
    private Boolean needNurseSheet;

    /**
     * 是否展示沟通记录
     */
    private boolean needCallRecordSheet;

    /**
     * 初始化基础字段
     *
     */
    public void initNormalField() {
        this.createTime = new Date();
        this.updateTime = new Date();
        this.version = CommonConstant.ONE;
        this.yn = YnStatusEnum.YES.getCode();
        this.stopStatus = AngelWorkStopStatusEnum.INIT.getStatus();
    }


    @Override
    public DomainCode getDomainCode() {
        return DomainEnum.ANGEL_PROMISE;
    }

    @Override
    public AggregateCode getAggregateCode() {
        return AngelWorkAggregateEnum.WORK;
    }

    @Override
    public Integer version() {
        return version;
    }

    @Override
    public void versionIncrease() {
        version++;
    }

    @Override
    public AngelWorkIdentifier getIdentifier() {
        return new AngelWorkIdentifier(this.workId);
    }

    /**
     * 获取预约地址
     * @return
     */
    public String takeUserFullAddress(){
        if(CollectionUtils.isEmpty(angelTasks)){
            return null;
        }

        return angelTasks.get(0).getPatientFullAddress();
    }

    /**
     * 获取预约地址 经度
     * @return
     */
    public BigDecimal takeUserLng(){
        if(CollectionUtils.isEmpty(angelTasks)){
            return null;
        }
        return BigDecimal.valueOf(angelTasks.get(0).getPatientAddressLng());
    }
    /**
     * 获取预约地址 维度
     * @return
     */
    public BigDecimal takeUserLat(){
        if(CollectionUtils.isEmpty(angelTasks)){
            return null;
        }

        return BigDecimal.valueOf(angelTasks.get(0).getPatientAddressLat());
    }
    /**
     * 获取服务开始时间
     * @return
     */
    public Date takeServiceStartTime(){
        return Objects.nonNull(workStartTime) ? workStartTime : angelTasks.get(0).getTaskStartTime();
    }

    /**
     * 根据实验室提取该工单下的运单
     * @return
     */
    public Map<String, AngelShip> takeReciveShipMap(){
        if(CollectionUtils.isEmpty(angelShips)){
            return new HashMap<>();
        }
        return angelShips.stream().filter(AngelShip::checkForwardStatus).collect(Collectors.toMap(AngelShip::getReceiverId, ship -> ship, (v1, v2) -> v2 ));
    }

    /**
     * 根据被服务者Id提取对应task
     * @param patientId
     * @return
     */
    public AngelTask takeTaskByPatientId(String patientId){
        if(CollectionUtils.isEmpty(angelTasks)){
            return null;
        }

        return angelTasks.stream().filter(task -> task.getPatientId().equals(patientId)).findFirst().orElse(null);
    }

    /**
     * 根据被任务Id提取对应task
     * @param taskId
     * @return
     */
    public AngelTask takeTaskByTaskId(Long taskId){
        if(CollectionUtils.isEmpty(angelTasks)){
            return null;
        }

        return angelTasks.stream().filter(task -> task.getTaskId().equals(taskId)).findFirst().orElse(null);
    }

    /**
     * 提取目标用户地址经纬度
     * @return
     */
    public String takeTargetLocation(){
        if(CollectionUtils.isEmpty(angelTasks)){
            return null;
        }
        AngelTask angelTask = angelTasks.get(0);
        return StringUtils.join(angelTask.getPatientAddressLat(), ",", angelTask.getPatientAddressLng());
    }


    /**
     * work完成
     *
     * @param clothingFileIds
     * @param wasteDestroyFileIds
     * @param serviceRecordFileIds
     */
    public void complete(List<Long> clothingFileIds, List<Long> wasteDestroyFileIds, List<Long> serviceRecordFileIds){

        if (Objects.isNull(jdhAngelWorkExtVo)){
            jdhAngelWorkExtVo = new JdhAngelWorkExtVo();
        }
        jdhAngelWorkExtVo.setClothingFileIds(clothingFileIds);
        jdhAngelWorkExtVo.setWasteDestroyFileIds(wasteDestroyFileIds);
        jdhAngelWorkExtVo.setServiceRecordFileIds(serviceRecordFileIds);
        jdhAngelWorkExtVo.setComplete(JdhAngelWorkExtVo.COMPLETE_STATUS_ENDING);
    }

    /**
     * work完成
     * @param fileIds
     */
    public void submitSoundRecording(Set<Long> fileIds){

        if (Objects.isNull(jdhAngelWorkExtVo)){
            jdhAngelWorkExtVo = new JdhAngelWorkExtVo();
        }
        List<Long> existIds = jdhAngelWorkExtVo.getSoundRecordingFileIds();
        if (CollectionUtils.isNotEmpty(existIds)){
            fileIds.addAll(existIds);
        }
        jdhAngelWorkExtVo.setSoundRecordingFileIds(Lists.newArrayList(fileIds));

    }

    public void updateSoundRecording(Set<Long> fileIds){

        if (Objects.isNull(jdhAngelWorkExtVo)){
            jdhAngelWorkExtVo = new JdhAngelWorkExtVo();
        }
        jdhAngelWorkExtVo.setSoundRecordingFileIds(Lists.newArrayList(fileIds));

    }

    /**
     * 获取定时任务的目标时间
     *
     * @param eventTypeEnum
     * @return
     */
    public LocalDateTime getTargetTime(AngelWorkDelayEventTypeEnum eventTypeEnum) {
        switch(eventTypeEnum) {
            case ANGEL_WORK_PLAN_OUT_DELAY:
                return TimeUtils.dateToLocalDateTime(this.planOutTime);

            case ANGEL_WORK_PLAN_FINISH_DELAY:
                return TimeUtils.dateToLocalDateTime(this.planFinishTime);

            case ANGEL_WORK_SERVICE_END_DELAY:
                return TimeUtils.dateToLocalDateTime(this.workEndTime);

            case ANGEL_WORK_SERVICE_START_DELAY:
                return TimeUtils.dateToLocalDateTime(this.workStartTime);

            default :
                return null;
        }
    }

    /**
     * 更显金额信息
     *
     * @param orderSettleAmount
     * @param dispatchMarkupPrice
     */
    public void addAngelChargeDetail(BigDecimal orderSettleAmount, BigDecimal dispatchMarkupPrice){
        if(Objects.isNull(this.jdhAngelWorkExtVo)){
            this.jdhAngelWorkExtVo = new JdhAngelWorkExtVo();
            AngelCharge angelCharge = new AngelCharge();
            angelCharge.setOrderSettleAmount(orderSettleAmount);
            angelCharge.setDispatchMarkupPrice(dispatchMarkupPrice);
            jdhAngelWorkExtVo.setAngelCharge(new AngelCharge());
        }else if(Objects.isNull(this.jdhAngelWorkExtVo.getAngelCharge())){
            AngelCharge angelCharge = new AngelCharge();
            angelCharge.setOrderSettleAmount(orderSettleAmount);
            angelCharge.setDispatchMarkupPrice(dispatchMarkupPrice);
            jdhAngelWorkExtVo.setAngelCharge(new AngelCharge());
        }else {
            jdhAngelWorkExtVo.getAngelCharge().setOrderSettleAmount(
                    Objects.isNull(jdhAngelWorkExtVo.getAngelCharge().getOrderSettleAmount()) ? orderSettleAmount : jdhAngelWorkExtVo.getAngelCharge().getOrderSettleAmount().add(orderSettleAmount));
            jdhAngelWorkExtVo.getAngelCharge().setDispatchMarkupPrice(
                    Objects.isNull(jdhAngelWorkExtVo.getAngelCharge().getDispatchMarkupPrice()) ? dispatchMarkupPrice : jdhAngelWorkExtVo.getAngelCharge().getDispatchMarkupPrice().add(dispatchMarkupPrice));
        }
    }

    /**
     * 检查工单状态是否合法
     *
     * @param stop
     * @param work
     * @return
     */
    public boolean checkValidStatus(Boolean stop, Boolean work){
        boolean stopValid = true;
        boolean workValid = true;
        if(stop){
            stopValid = Objects.isNull(this.stopStatus) ? true : AngelWorkStopStatusEnum.INIT.getStatus().equals(this.stopStatus);
        }
        if(work){
            workValid = Objects.isNull(this.workStatus) ? true : AngelWorkStatusEnum.getValidStatus().contains(this.workStatus);
        }
        return stopValid && workValid;
    }

    /**
     * 是否是上门检测和上门护理
     * @return
     */
    public boolean isGoHomeMode() {
        return AngelWorkTypeEnum.NURSE.getType().equals(this.workType) || AngelWorkTypeEnum.CARE.getType().equals(this.workType);
    }

    /**
     * 是否是上门检测
     * @return
     */
    public boolean isToHomeTest() {
        return AngelWorkTypeEnum.NURSE.getType().equals(this.workType);
    }

    /**
     * 获取下单人信息
     *
     * @return
     */
    public AngelOrder getAngelOrder() {
        if(Objects.isNull(this.jdhAngelWorkExtVo)){
            return new AngelOrder();
        }
        if(Objects.isNull(this.jdhAngelWorkExtVo.getAngelOrder())){
            return new AngelOrder();
        }
        return this.jdhAngelWorkExtVo.getAngelOrder();
    }

    /**
     * 补全服务者头像链接
     */
    public void fulfilAngelHeadImg() {
        String urlPrefix = "https://img12.360buyimg.com/imagetools/{0}";
        String transferDefaultImg = "https://img13.360buyimg.com/imagetools/jfs/t1/227328/2/4966/15113/65646095Fa48ff98c/cb9627262d866760.png";
        if(AngelWorkTypeEnum.RIDER.getType().equals(workType)){
            this.angelHeadImg = transferDefaultImg;
        }else if(StringUtils.isNotBlank(angelHeadImg)){
            this.angelHeadImg = MessageFormat.format(urlPrefix, angelHeadImg);
        }
    }

    /**
     * 获取工单中文件id
     *
     * @param fileTypeCode
     * @return
     */
    public List<Long> getFileId(List<String> fileTypeCode) {
        List<Long> fileResult = Lists.newArrayList();
        Optional.ofNullable(fileTypeCode).orElseGet(Lists::newArrayList).forEach(fileType -> {
                    if(AngelPromiseFileBizTypeEnum.SERVICE_RECORD.getBizType().equals(fileType)){
                        List<Long> serviceRecordFileIds = this.jdhAngelWorkExtVo.getServiceRecordFileIds();
                        if(CollectionUtils.isNotEmpty(serviceRecordFileIds)) {
                            fileResult.addAll(serviceRecordFileIds);
                        }
                    }else if(AngelPromiseFileBizTypeEnum.MEDICAL_WASTE.getBizType().equals(fileType)){
                        List<Long> wasteDestroyFileIds = this.jdhAngelWorkExtVo.getWasteDestroyFileIds();
                        if(CollectionUtils.isNotEmpty(wasteDestroyFileIds)) {
                            fileResult.addAll(wasteDestroyFileIds);
                        }
                    }else if(AngelPromiseFileBizTypeEnum.CLOTHING.getBizType().equals(fileType)) {
                        List<Long> clothingFileIds = this.jdhAngelWorkExtVo.getClothingFileIds();
                        if(CollectionUtils.isNotEmpty(clothingFileIds)) {
                            fileResult.addAll(clothingFileIds);
                        }
                    } else if (AngelPromiseFileBizTypeEnum.MEDICAL_CERTIFICATE.getBizType().equals(fileType)
                            || AngelPromiseFileBizTypeEnum.LETTER_OF_CONSENT.getBizType().equals(fileType)) {
                        AngelTaskRepository angelTaskRepository = SpringUtil.getBean(AngelTaskRepository.class);
                        AngelTaskDBQuery angelTaskDBQuery = new AngelTaskDBQuery();
                        angelTaskDBQuery.setWorkId(workId);
                        List<AngelTask> list = angelTaskRepository.findList(angelTaskDBQuery);
                        if (CollectionUtils.isEmpty(list)) {
                            return;
                        }
                        list.forEach(task -> {
                            List<Long> medicalCertificates = task.getFileId(Lists.newArrayList(fileType));
                            if (CollectionUtils.isNotEmpty(medicalCertificates)) {
                                fileResult.addAll(medicalCertificates);
                            }
                        });
                    }else {
                        log.error("[AngelWork -> getFileId],服务者工单不支持这个类型的文件查询!fileType={}", fileType);
                    }
                });
        return fileResult;
    }
}
