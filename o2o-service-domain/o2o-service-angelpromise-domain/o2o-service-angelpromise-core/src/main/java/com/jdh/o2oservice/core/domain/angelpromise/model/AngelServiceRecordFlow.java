package com.jdh.o2oservice.core.domain.angelpromise.model;
import com.jdh.o2oservice.base.model.Aggregate;
import com.jdh.o2oservice.base.model.AggregateCode;
import com.jdh.o2oservice.base.model.DomainCode;
import com.jdh.o2oservice.base.model.DomainEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AngelServiceRecordFlow implements Aggregate<AngelServiceRecordFlowIdentifier> {

    /**
     * 主键
     */
    private Long id;

    /**
     * 服务记录ID
     */
    private Long recordId;

    /**
     * 服务记录节点ID
     */
    private Long recordFlowId;

    /**
     * 流程节点编码
     */
    private String flowCode;

    /**
     * 流程节点名称
     */
    private String flowName;

    /**
     * 顺序编码
     */
    private Integer sortId;

    /**
     * 初始状态：0，提交通过:1，提交未通过评估高风险状态：-1
     */
    private Integer status;

    /**
     * 节点内容明细
     */
    private String detail;

    /**
     * 是否有效 1有效 0无效
     */
    private Integer yn;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 数据来源分支
     */
    private String branch;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人
     */
    private String updateUser;

    /**
     * 修改时间
     */
    private Date updateTime;

    @Override
    public DomainCode getDomainCode() {
        return DomainEnum.ANGEL_PROMISE;
    }

    @Override
    public AggregateCode getAggregateCode() {
        return null;
    }

    @Override
    public Integer version() {
        return version;
    }

    /**
     * 版本增加
     */
    @Override
    public void versionIncrease() {
        this.version++;
    }

    @Override
    public AngelServiceRecordFlowIdentifier getIdentifier() {
        return new AngelServiceRecordFlowIdentifier(this.recordFlowId);
    }
}
