package com.jdh.o2oservice.core.domain.angelpromise.model;

import com.jdh.o2oservice.base.model.Identifier;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AngelServiceRecordIdentifier implements Identifier {

    /**
     * 服务记录ID
     */
    private Long recordId;

    @Override
    public String serialize() {
        return String.valueOf(recordId);
    }
}
