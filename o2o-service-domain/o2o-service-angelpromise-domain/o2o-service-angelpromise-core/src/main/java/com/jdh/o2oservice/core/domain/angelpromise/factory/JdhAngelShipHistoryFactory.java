package com.jdh.o2oservice.core.domain.angelpromise.factory;

import com.google.common.base.Joiner;
import com.jdh.o2oservice.base.enums.YnStatusEnum;
import com.jdh.o2oservice.core.domain.angelpromise.context.AngelShipCallBackContext;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelShipStatusEnum;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelShip;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelShipHistory;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.Objects;

/**
 * @ClassName:JdhAngelShipHistoryFactory
 * @Description:
 * @Author: yaoqinghai
 * @Date: 2024/4/21 23:29
 * @Vserion: 1.0
 **/
public class JdhAngelShipHistoryFactory {

    /**
     * 创建服务者运单历史
     *
     * @param preStatus
     * @param angelShip
     * @return
     */
    public static AngelShipHistory create(Integer preStatus, AngelShip angelShip) {
        // 将秒级时间戳转换为LocalDateTime
        return AngelShipHistory.builder()
                .shipId(angelShip.getShipId())
                .workId(angelShip.getWorkId())
                .beforeStatus(preStatus)
                .afterStatus(angelShip.getShipStatus())
                .operateTime(angelShip.getUpdateTime())
                .yn(YnStatusEnum.YES.getCode())
                .createTime(new Date())
                .creator(angelShip.getUpdater())
                .build();
    }

    /**
     * 创建服务者运单历史
     *
     * @param preStatus
     * @param angelShip
     * @param callBackContext
     * @return
     */
    public static AngelShipHistory createHistory(Integer preStatus, AngelShip angelShip, AngelShipCallBackContext callBackContext) {
        AngelShipHistory angelShipHistory = create(preStatus, angelShip);
        angelShipHistory.setBeforeTransfer(angelShip.getTransferName());
        angelShipHistory.setAfterTransfer(callBackContext.getDmName());
        angelShipHistory.setRepeatType(callBackContext.getRepeatReasonType());
        Date date;
        if(Objects.nonNull(callBackContext.getUpdateTime())) {
            if(AngelShipStatusEnum.CREATE_ORDER_FAIL.getShipStatus().equals(callBackContext.getOrderStatus())) {
                date = new Date(callBackContext.getUpdateTime());
            }else {
                date = new Date(callBackContext.getUpdateTime() * 1000);
            }
        }else {
            date = new Date();
        }
        angelShipHistory.setOperateTime(date);
        //保存回调过来的扩展信息
        angelShipHistory.setExtend(callBackContext.getErrorMsg());
        return angelShipHistory;
    }
}
