package com.jdh.o2oservice.core.domain.angelpromise.repository.query;

import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.Lists;
import com.jdh.o2oservice.base.util.TimeFormat;
import com.jdh.o2oservice.base.util.TimeUtils;
import com.jdh.o2oservice.common.result.request.AbstractPageQuery;
import com.jdh.o2oservice.core.domain.angelpromise.context.AngelWorkListQueryContext;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * @author:lichen55
 * @createTime: 2024-04-17 14:45
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AngelWorkDBQuery extends AbstractPageQuery {
    /** 日程默认往前查7天 */
    private static final Integer PRE_DAY = -7;
    /**
     * 服务工单Id
     */
    private List<Long> workIds;

    /**
     * 京东订单号
     */
    private Long jdOrderId;

    /**
     * 履约单Id
     */
    private Long promiseId;

    /**
     * 来源Id，派单id
     */
    private Long sourceId;

    /**
     * 服务工单类型：1=骑手,2=护士,3=护工,4=康复师
     */
    private Integer	workType;

    /**
     * 服务者Id
     */
    private List<String> angelIds;

    /**
     * 服务者pin
     */
    private String angelPin;

    /**
     * 服务者工单状态：1=待接单，2=待服务（已接单），3=已出门，4=服务中（上门），5=待送检，6=送检中，7=已送达, 8=已完成，9=退款中，10-已退款，11=已取消
     */
    private List<Integer> statusList;

    /**
     * 服务开始时间
     */
    private Date serviceStartTimeBegin;

    /**
     * 服务开始时间
     */
    private Date serviceStartTimeEnd;

    /**
     * 计划出门任务开始时间
     */
    private Date planOutTimeStart;

    /**
     * 计划出门任务开始时间
     */
    private Date planOutTimeEnd;

    /**
     * 结束上门开始时间
     */
    private Date serviceDoneStart;

    /**
     * 结束上门截止时间
     */
    private Date serviceDoneEnd;

    /**
     * 服务完成开始时间
     */
    private Date serviceFinishStart;

    /**
     * 服务完成截止时间
     */
    private Date serviceFinishEnd;

    /**
     * 创建开始时间
     */
    private Date createStartTime;

    /**
     * 创建截止时间
     */
    private Date createEndTime;
    /**
     * 按照服务时间升序，false为倒序，默认是倒序徘的
     */
    private Boolean serviceTimeOrderByAsc = Boolean.FALSE;

    /**
     * 按照创建时间升序，false为倒序，默认是倒序徘的
     */
    private Boolean createTimeOrderByAsc;
    /**
     * 服务类型
     */
    private Set<String> serviceTypes;

    /**
     *
     */
    private List<Integer> notInWorkStatusList;

    /**
     * 履约单id
     */
    private List<Long> promiseIds;

    /**
     * 分组字段
     */
    private String column;

    /**
     * 业务身份
     */
    private List<String> verticalCodes;

    /**
     * 查询扩展字段 服务记录非空记录
     */
    private Boolean queryExtServiceRecordFileIdsNotEmpty;

    /**
     * 查询扩展字段 服务记录非空记录
     */
    private Boolean queryExtClothingFileIdsNotEmpty;

    /**
     * 查询扩展字段 服务记录非空记录
     */
    private Boolean queryExtWasteDestroyFileIdsNotEmpty;

    /**
     * 服务者工单状态：1待接单，2、已接单，3、待服务，4、服务中，5送检中，6服务完成，7退款中，8已退款，9已取消
     */
    private List<Integer> notInStatusList;


    /**
     *
     * @param context
     */
    public AngelWorkDBQuery(AngelWorkListQueryContext context){
        workIds = context.getWorkIds();
        promiseId = context.getPromiseId();
        workType = context.getWorkType();
        if(Objects.nonNull(context.getAngelId())){
            angelIds = Lists.newArrayList(context.getAngelId());
        }
        statusList = context.getStatusList();
        serviceTypes = context.getServiceTypes();
        if (Objects.nonNull(context.getServiceTimeOrderByAsc())){
            serviceTimeOrderByAsc = context.getServiceTimeOrderByAsc();
        }
        this.setPageNum(context.getPageIndex());
        this.setPageSize(context.getPageSize());

        if (CollUtil.isNotEmpty(context.getPromiseIdList())){
            promiseIds = context.getPromiseIdList();
        }

        if (StringUtils.isNotBlank(context.getStartDay())){
            Date start = TimeUtils.strToDate(context.getStartDay());
            this.serviceStartTimeBegin = TimeUtils.getDateStart(start);
        }


        if (StringUtils.isNotBlank(context.getEndDay())){
            LocalDate localDate = LocalDate.parse(context.getEndDay(), TimeFormat.SHORT_PATTERN_LINE.formatter);
            localDate = localDate.plusDays(1);
            this.serviceStartTimeEnd = TimeUtils.localDateToDate(localDate);
        }

        this.notInStatusList = context.getNotInStatusList();
    }


    /**
     *
     * @param
     */
    public void refresh(LocalDate localDate, Integer size){

        Date start = TimeUtils.localDateToDate(localDate);
        serviceStartTimeBegin = TimeUtils.getDateStart(start);
        LocalDate endDay = localDate.plus(size, ChronoUnit.DAYS);
        Date end = TimeUtils.localDateToDate(endDay);
        serviceStartTimeEnd = TimeUtils.getDateEnding(end);
    }


}
