package com.jdh.o2oservice.core.domain.angelpromise.repository.db;
import com.jdh.o2oservice.base.model.Repository;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelServiceRecordFlow;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelServiceRecordFlowIdentifier;
import com.jdh.o2oservice.core.domain.angelpromise.repository.query.AngelServiceRecordFlowDBQuery;
import java.util.List;

public interface AngelServiceRecordFlowRepository extends Repository<AngelServiceRecordFlow, AngelServiceRecordFlowIdentifier> {

    /**
     * 查询服务记录流程节点列表
     * @param query
     * @return
     */
    List<AngelServiceRecordFlow> findList(AngelServiceRecordFlowDBQuery query);

    /**
     * 保存
     * @param entity
     * @return count
     */
    int save(AngelServiceRecordFlow entity);

    /**
     * 通过Identify 查询
     * @param identifier
     * @return
     */
    AngelServiceRecordFlow find(AngelServiceRecordFlowIdentifier identifier);

    /**
     * 删除
     * @param entity
     * @return
     */
    int remove(AngelServiceRecordFlow entity);
}
