package com.jdh.o2oservice.core.domain.angelpromise.context;
import com.jdh.o2oservice.export.angelpromise.dto.ServiceRecordAnswerQuestionDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.io.Serializable;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AngelServiceRecordContext implements Serializable {

    /**
     * 护理单主键id
     */
    private Long id;

    /**
     * 任务单id
     */
    private Long taskId;

    /**
     * 被服务人Id
     */
    private Long promisePatientId;

    /**
     * 服务记录ID
     */
    private Long recordId;

    /**
     * 是否存在高危选项
     */
    private Boolean highRiskOption;

    /**
     * 流程节点编码
     */
    private String flowCode;

    /**
     * 流程节点名称
     */
    private String flowName;

    /**
     * 顺序编码
     */
    private Integer sortId;

    /**
     * 节点内容明细
     */
    private String detail;

    /**
     * 提交问题答案
     */
    private List<ServiceRecordAnswerQuestionDTO> answerQuestionDTOList;

    /**
     * 配置节点数
     */
    private int configFlowCodeNum;

    /**
     * 履约单Id
     */
    private Long promiseId;

    /**
     * 配置最后的节点code
     */
    private String lastFlowNode;

    /**
     *
     */
    private String serviceName;
}
