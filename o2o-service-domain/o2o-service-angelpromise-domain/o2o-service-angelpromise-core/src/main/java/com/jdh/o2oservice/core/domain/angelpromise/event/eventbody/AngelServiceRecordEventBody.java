package com.jdh.o2oservice.core.domain.angelpromise.event.eventbody;
import com.jdh.o2oservice.base.event.EventBody;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

@Data
@AllArgsConstructor
@Builder
public class AngelServiceRecordEventBody implements EventBody {

    /**
     * 服务记录ID
     */
    private Long recordId;

    /**
     * 服务记录节点ID
     */
    private Long recordFlowId;

    /**
     * 任务单Id
     */
    private Long taskId;

    /**
     * 被服务人Id
     */
    private Long promisePatientId;

    /**
     * 履约单Id
     */
    private Long promiseId;

    /**
     * 退款原因
     */
    private String refundReason;

    /**
     *
     */
    private String serviceName;

}
