package com.jdh.o2oservice.core.domain.angelpromise.model;

import com.google.common.base.Joiner;
import com.jdh.o2oservice.base.model.*;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelShipCancelCodeStatusEnum;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelShipStatusEnum;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelWorkAggregateEnum;
import com.jdh.o2oservice.core.domain.angelpromise.vo.JdhAngelShipExtVo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * @ClassName:JdhAngelShipPo
 * @Description: 运单
 * @Author: yaoqinghai
 * @Date: 2024/4/21 14:23
 * @Vserion: 1.0
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AngelShip implements Aggregate<AngelShipIdentifier> {

    /**
     * 自增主键
     */
    private Long id;

    /**
     * 运单Id
     */
    private Long shipId;

    /**
     * 服务工单Id
     */
    private Long workId;

    /**
     * 外部运单Id
     */
    private String outShipId;

    /**
     * shopNo
     */
    private String shopNo;

    /**
     * 运单类型：1=自行寄送，2=达达
     * DeliveryTypeEnum
     */
    private Integer type;

    /**
     * 接收点Id
     */
    private String receiverId;

    /**
     * 接收点名称
     */
    private String receiverName;

    /**
     * 接收点全地址
     */
    private String receiverFullAddress;

    /**
     * 骑手Id
     */
    private String transferId;

    /**
     * 骑手姓名
     */
    private String transferName;

    /**
     * 骑手姓名加密
     */
    private String transferNameIndex;

    /**
     * 骑手联系方式
     */
    private String transferPhone;

    /**
     * 骑手联系方式加密
     */
    private String transferPhoneIndex;

    /**
     * 骑手头像
     */
    private String transferHeadImg;

    /**
     * 发件人姓名
     */
    private String senderName;

    /**
     * 发件人姓名加密
     */
    private String senderNameIndex;

    /**
     * 发件人联系方式
     */
    private String senderPhone;

    /**
     * 发件人联系方式加密
     */
    private String senderPhoneIndex;

    /**
     * 发件全地址
     */
    private String senderFullAddress;

    /**
     * 运单状态：
     */
    private Integer shipStatus;

    /**
     * 骑手接单纬度
     */
    private double transferStartLat;

    /**
     * 骑手接单经度
     */
    private double transferStartLng;

    /**
     * 配送距离，寄件地址到收件地址
     */
    private BigDecimal totalDistance;

    /**
     * 预计呼叫时间
     */
    private Date planCallTime;

    /**
     * 预计接单时间
     */
    private Date estimateGrabTime;

    /**
     * 预计取件时间
     */
    private Date estimatePickUpTime;

    /**
     * 预计完单时间
     */
    private Date estimateReceiveTime;

    /**
     * 重发类型
     */
    private Integer repeatType;

    /**
     * 发件码加密
     */
    private String sendCode;

    /**
     * 发件码索引
     */
    private String sendCodeIndex;

    /**
     * 收件码加密
     */
    private String finishCode;

    /**
     * 收件码索引
     */
    private String finishCodeIndex;

    /**
     * 扩展信息
     */
    private JdhAngelShipExtVo jdhAngelShipExtVo;

    /**
     * 数据有效性 0：无效 1：有效
     */
    private Integer yn;

    /**
     * 版本号
     */
    private Integer	version;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人
     */
    private String updater;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 骑手轨迹
     */
    private String trackUrl;

    /**
     * 运单历史明细
     */
    private List<AngelShipHistory> shipHistoryList;

    /**
     * @see AngelShipCancelCodeStatusEnum
     */
    private Integer standCancelCode;

    /**
     * 物流动态信息
     */
    private String logisticsMessage;

    private Long parentShipId;//父运单id

    /**
     * 版本
     *
     * @return {@link Integer}
     */
    @Override
    public Integer version() {
        return version;
    }

    /**
     * 版本增加
     */
    @Override
    public void versionIncrease() {
        this.version++;
    }

    /**
     * 获取标识符
     *
     * @return {@link ID}
     */
    @Override
    public AngelShipIdentifier getIdentifier() {
        return new AngelShipIdentifier(this.shipId);
    }

    /**
     * 聚合所属领域编码
     *
     * @return
     */
    @Override
    public DomainCode getDomainCode() {
        return DomainEnum.ANGEL_PROMISE;
    }

    /**
     * 获取聚合编码
     *
     * @return {@link AggregateCode}
     */
    @Override
    public AggregateCode getAggregateCode() {
        return AngelWorkAggregateEnum.SHIP;
    }

    /**
     * 判断当前task是否处于正向状态
     * @return
     */
    public Boolean checkForwardStatus(){
        return AngelShipStatusEnum.checkForwardStatus(shipStatus);
    }

    /**
     * 处理运单备注信息
     *
     * @param info
     */
    public void extendVoRemark(String info) {
        if(Objects.isNull(jdhAngelShipExtVo)){
            jdhAngelShipExtVo = new JdhAngelShipExtVo();
        }
        if(StringUtils.isBlank(jdhAngelShipExtVo.getShipRemark())){
            jdhAngelShipExtVo.setShipRemark(info);
        }else {
            String joinStr = Joiner.on("\r\n").join(jdhAngelShipExtVo.getShipRemark(), info);
            jdhAngelShipExtVo.setShipRemark(joinStr);
        }
    }
}
