package com.jdh.o2oservice.core.domain.trade.enums;

import com.jdh.o2oservice.base.exception.errorcode.AbstractErrorCode;
import com.jdh.o2oservice.base.model.DomainEnum;
import lombok.ToString;

/**
 * 订单域错误码
 * @author: l<PERSON><PERSON><PERSON>
 * @date: 2024/01/23 12:11 下午
 * @version: 1.0
 */
@ToString
public enum TradeErrorCode implements AbstractErrorCode {

    PARAM_NULL(DomainEnum.TRADE, "30000", "参数不能为空"),
    /**
     * 商家域错误码
     */
    ORDER_ID_NULL(DomainEnum.TRADE, "30001", "订单号为空"),
    AMOUNT_ID_NULL(DomainEnum.TRADE, "30002", "支付金额为空"),
    IP_ID_NULL(DomainEnum.TRADE, "30003", "用户IP为空"),
    ORDER_IS_NULL(DomainEnum.TRADE, "30004", "查不到订单"),
    ORDER_STATUS_CHANGE(DomainEnum.TRADE, "30005", "订单状态已变更"),
    ORDER_SEND_PAY_MODIFY_FAIL(DomainEnum.TRADE,"30006", "订单sendpay修改失败"),
    /**
     * 交易 服务费项计算
     */
    ORDER_CALC_SERVICE_FEE_USER_PIN_MISS_ERROR(DomainEnum.TRADE, "31011", "计算服务费项信息，用户pin信息缺失"),
    ORDER_CALC_SERVICE_FEE_SKU_ID_SET_MISS_ERROR(DomainEnum.TRADE, "31012", "计算服务费项信息，商品信息缺失"),

    ORDER_CALC_SERVICE_FEE_USER_ADDRESS_MISS(DomainEnum.TRADE, "31013", "计算服务费项信息，用户地址信息缺失"),
    ORDER_CALC_SERVICE_FEE_USER_ADDRESS_PROVINCE_MISS(DomainEnum.TRADE, "31014", "计算服务费项信息，用户地址所属省信息缺失"),
    ORDER_CALC_SERVICE_FEE_USER_ADDRESS_CITY_MISS(DomainEnum.TRADE, "31015", "计算服务费项信息，用户地址所属市信息缺失"),
    ORDER_CALC_SERVICE_FEE_USER_ADDRESS_COUNTY_MISS(DomainEnum.TRADE, "31016", "计算服务费项信息，用户地址所属区县信息缺失"),

    ORDER_CALC_SERVICE_FEE_USER_APPOINTMENT_TIME_INFO_MISS(DomainEnum.TRADE, "31018", "计算服务费项信息，用户预约信息缺失"),
    ORDER_CALC_SERVICE_FEE_USER_APPOINTMENT_TIME_START_TIME_MISS(DomainEnum.TRADE, "31019", "计算服务费项信息，用户预约信息-预约开始时间缺失"),
    ORDER_CALC_SERVICE_FEE_USER_APPOINTMENT_TIME_TYPE_MISS(DomainEnum.TRADE, "31020", "计算服务费项信息，用户预约方式缺失"),

    /**
     * 订单收银台
     */
    ORDER_RPC_PAY_URL_ERROR(DomainEnum.TRADE,"32000", "获取支付信息失败，请重新支付"),


    /**
     * 退款
     */
    ORDER_REFUND_TYPE_ERROR(DomainEnum.TRADE,"33000", "非法退款类型"),
    ORDER_REFUND_FAIL(DomainEnum.TRADE,"33001", "退款申请提交失败，请稍后"),
    ORDER_PAY_DETAIL_FAIL(DomainEnum.TRADE,"33002", "退款支付明细获取失败，请稍后再试"),
    REFUND_REASONS_FAIL(DomainEnum.TRADE,"33003", "获取退款原因列表失败"),
    REFUND_MQ_FAILED(DomainEnum.TRADE,"33004","退款MQ通知失败"),
    REFUND_ORDER_ITEM_NULL(DomainEnum.TRADE,"33005","订单商品信息为空"),
    REFUND_ORDER_FREEZE_NULL(DomainEnum.TRADE,"33006","退款提交失败，服务单冻结失败"),
    REFUND_ORDER_ITEM_MOST(DomainEnum.TRADE,"33007","不支持多订单商品一起退款"),
    REFUND_AMOUNT_OUT(DomainEnum.TRADE,"33008","退款金额超出订单实际支付金额"),
    QUERY_REFUND_AMOUNT_TYPE_NULL(DomainEnum.TRADE,"33009","查询可退款金额类型为空"),
    QUERY_REFUND_AMOUNT_PROIMSE_NULL(DomainEnum.TRADE,"33010","查询可退款履约单为空"),
    QUERY_REFUND_PAY_TYPE_NULL(DomainEnum.TRADE,"33011","订单支付明细生成中，申请退款请稍后再试"),
    REFUND_AMOUNT_NULL(DomainEnum.TRADE,"33012","退款金额不能为空或负数"),
    REFUND_SUBMIT(DomainEnum.TRADE,"33013","退款操作太快，请稍后"),
    ORDER_REFUND_EXIST(DomainEnum.TRADE,"33014","已有退款存在，请勿重复提交退款"),
    ORDER_REFUND_PROMISEPATIENTID(DomainEnum.TRADE,"33014","指定退款人为空"),
    ORDER_REFUND_NO_CONFIG(DomainEnum.TRADE,"33015","退款申请提交失败,缺少配置,联系客服"),
    PROMISE_ID_PARAM_NULL(DomainEnum.TRADE,"33016","退款申请promiseId为空"),
    PROMISE_ID_MULTI_ERROR(DomainEnum.TRADE,"33017","注意：退款失败，每次只能操作一个服务单退款~"),
    VOUCHER_DEDUCTION_FAIL(DomainEnum.TRADE,"33018","可服务数量扣减失败"),
    REFUND_REASONS_PARAM_NULL(DomainEnum.TRADE,"33019","退款申请refundReason为空"),
    ORDER_CANCEL_REFUND_ORDER_STATUS_ERROR(DomainEnum.TRADE,"33021","订单状态不合法,不能取消订单或退款"),

    /**
     * 互医交互
     */
    ORDER_SHEET_ID_ERROR(DomainEnum.TRADE,"34000", "通过检验单获取信息失败"),

    /**
     * 无地址
     */
    ORDER_NO_ADDRESS_ERROR(DomainEnum.TRADE,"34001", "结算页无地址异常"),

    /**
     * 交易 结算
     */
    CITY_ID_NULL(DomainEnum.TRADE,"35000", "上门地址城市id为空"),
    CITY_INFO_NULL(DomainEnum.TRADE,"35001", "城市未配置，不能估算结算价"),
    ANGEL_ID_NULL(DomainEnum.TRADE,"35002", "护士id为空"),
    ANGEL_INFO_NULL(DomainEnum.TRADE,"35003", "查不到护士信息"),
    ANGEL_PROFESSION_INFO_NULL(DomainEnum.TRADE,"35004", "护士职级为空"),
    ANGEL_PROFESSION_AMOUNT_NULL(DomainEnum.TRADE,"35005", "护士职级对应时薪未维护，请运营维护"),
    ORDER_ITEM_PROFESSION_AMOUNT_NULL(DomainEnum.TRADE,"35006", "护士职级对应时薪未维护，请运营维护"),
    ORDER_STATUS_NO_SAME(DomainEnum.TRADE,"35007", "订单结算，履约单未完成"),
    ORDER_SETTLE_NOT_VERTICAL_CODE(DomainEnum.TRADE,"35008", "结算场景不支持"),
    ANGEL_STATION_NULL(DomainEnum.TRADE,"35009", "护士站不存在"),
    INVENTORY_OUT_OFF(DomainEnum.TRADE,"35010", "当前时段已约满，请选择其他上门时间"),

    RECEIVE_ORDER_IDEMPOTENT(DomainEnum.TRADE,"35011", "当前单号正在收单中，请勿重复提交"),
    RECEIVE_ORDER_EXIST(DomainEnum.TRADE,"35012","当前订单已存在，请勿重复提交"),
    ;


    /**
     * ProviderErrorCode
     *
     * @param domainEnum  域枚举
     * @param code        代码
     * @param description 描述
     */
    TradeErrorCode(DomainEnum domainEnum, String code, String description) {
        this.domainEnum = domainEnum;
        this.code = code;
        this.description = description;
    }

    /** */
    private DomainEnum domainEnum;
    /** */
    private String code;
    /**
     * 展示给客户的错误提示，因为有的场景把系统异常传递给客户展示，体验不好
     */
    private String description;

    /**
     * 获取代码
     *
     * @return {@link String}
     */
    @Override
    public String getCode() {
        return code;
    }

    /**
     * 获取描述
     *
     * @return {@link String}
     */
    @Override
    public String getDescription() {
        return description;
    }

}
