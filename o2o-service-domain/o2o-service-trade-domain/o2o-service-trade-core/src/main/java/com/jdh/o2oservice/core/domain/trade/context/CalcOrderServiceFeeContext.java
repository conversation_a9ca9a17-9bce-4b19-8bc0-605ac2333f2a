package com.jdh.o2oservice.core.domain.trade.context;

import com.jdh.o2oservice.base.util.AssertUtils;
import com.jdh.o2oservice.core.domain.support.basic.enums.JdOrderFeeTypeEnum;
import com.jdh.o2oservice.core.domain.trade.enums.TradeErrorCode;
import com.jdh.o2oservice.core.domain.trade.vo.AddressInfoValueObject;
import com.jdh.o2oservice.core.domain.trade.vo.OrderAppointmentTimeValueObject;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Set;

/**
 * 计算订单费用上下文
 *
 * <AUTHOR>
 * @date 2024/04/17
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CalcOrderServiceFeeContext {

    /**
     * sku集合
     */
    private Set<String> skuIds;

    /**
     * 用户pin
     */
    private String userPin;

    /**
     * 预约时间
     */
    private OrderAppointmentTimeValueObject appointmentTime;

    /**
     * 地址信息
     */
    private AddressInfoValueObject addressInfo;
    /**
     * 垂直业务身份编码
     */
    private String verticalCode;
    /**
     * 销售渠道
     */
    private Long channelId;
     /**
     * 服务类型
     */
    private Integer serviceType;

    /**
     * 查询服务人员升级
     */
    private Boolean searchServiceUpgrade = false;

    /**
     * 主品skuId
     */
    private String mainSkuId;

    /**
     * 费项类型
     * @see JdOrderFeeTypeEnum
     */
    private Integer feeType;

    /**
     * 参数校验
     */
    public void paramCheck(){
        AssertUtils.hasText(userPin, TradeErrorCode.ORDER_CALC_SERVICE_FEE_USER_PIN_MISS_ERROR);

        AssertUtils.isNotEmpty(skuIds, TradeErrorCode.ORDER_CALC_SERVICE_FEE_SKU_ID_SET_MISS_ERROR);

        AssertUtils.nonNull(addressInfo, TradeErrorCode.ORDER_CALC_SERVICE_FEE_USER_ADDRESS_MISS);
        AssertUtils.nonNull(addressInfo.getProvinceId(), TradeErrorCode.ORDER_CALC_SERVICE_FEE_USER_ADDRESS_PROVINCE_MISS);
        AssertUtils.nonNull(addressInfo.getCityId(), TradeErrorCode.ORDER_CALC_SERVICE_FEE_USER_ADDRESS_CITY_MISS);
        AssertUtils.nonNull(addressInfo.getCountyId(), TradeErrorCode.ORDER_CALC_SERVICE_FEE_USER_ADDRESS_COUNTY_MISS);

        //AssertUtils.nonNull(appointmentTime, TradeErrorCode.ORDER_CALC_SERVICE_FEE_USER_APPOINTMENT_TIME_INFO_MISS);
        //AssertUtils.nonNull(appointmentTime.getIsImmediately(), TradeErrorCode.ORDER_CALC_SERVICE_FEE_USER_APPOINTMENT_TIME_TYPE_MISS);
        //AssertUtils.hasText(appointmentTime.getAppointmentStartTime(), TradeErrorCode.ORDER_CALC_SERVICE_FEE_USER_APPOINTMENT_TIME_START_TIME_MISS);

    }

}
