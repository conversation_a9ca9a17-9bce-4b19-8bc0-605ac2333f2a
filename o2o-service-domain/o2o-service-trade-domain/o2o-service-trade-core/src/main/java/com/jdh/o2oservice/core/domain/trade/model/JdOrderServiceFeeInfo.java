package com.jdh.o2oservice.core.domain.trade.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * JD订单费项
 *
 * <AUTHOR>
 * @date 2024/04/17
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class JdOrderServiceFeeInfo {

    /**
     * 总费用
     */
    private BigDecimal serviceFee;

    /**
     * 聚合类型
     */
    private Integer aggregateType;

    /**
     * 聚合子类型
     */
    private Integer aggregateSubType;

    /**
     * 价格是否全部计算完成
     */
    private Boolean isFinish = false;

    /**
     * 收费项目表
     */
    private List<ServiceFeeDetail> serviceFeeDetailList;
}
