package com.jdh.o2oservice.core.domain.dispatch.event;

import com.jdh.o2oservice.core.domain.dispatch.model.JdhDispatch;
import com.jdh.o2oservice.core.domain.dispatch.model.ServiceItem;
import com.jdh.o2oservice.core.domain.support.basic.model.DomainAppointmentTime;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName DispatchReceivedEventBody
 * @Description
 * <AUTHOR>
 * @Date 2024/4/17 22:07
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DispatchCallbackEventBody extends DispatchEventBaseBody {

    /**
     * 派单任务ID
     */
    private Long dispatchId;

    /**
     * 履约单ID
     */
    private Long promiseId;

    /**
     * 服务者ID
     */
    private Long angelId;

    /**
     * 操作人（一般为服务者）
     */
    private String operator;

    /**
     * 派单任务明细ID-指定派单护士数据的detailId
     */
    private Long dispatchDetailId;

    /**
     * 首次接单
     */
    private Boolean isFirstReceived;

    /**
     * 原时间
     */
    private String beforeAppointmentTime;

    /**
     * 新时间
     */
    private String afterAppointmentTime;

    /**
     * 操作角色
     * @see com.jdh.o2oservice.common.enums.OperatorRoleTypeEnum
     */
    private Integer operatorRoleType;

    /**
     * 服务名称
     */
    private String serviceName;

    /**
     *
     */
    private String reason;

    public DispatchCallbackEventBody(JdhDispatch snapshot, JdhDispatch jdhDispatch) {
        this.dispatchId = jdhDispatch.getDispatchId();
        this.promiseId = jdhDispatch.getPromiseId();
        this.beforeStatus = snapshot.getDispatchStatus();
        this.afterStatus = jdhDispatch.getDispatchStatus();
        if (Objects.nonNull(jdhDispatch.getServiceInfo()) && Objects.nonNull(jdhDispatch.getServiceInfo().getBeforeTime())) {
            this.beforeAppointmentTime = jdhDispatch.getServiceInfo().getBeforeTime().formatAppointmentStartTime();
        }
        if (Objects.nonNull(jdhDispatch.getServiceInfo()) && Objects.nonNull(jdhDispatch.getServiceInfo().getAppointmentTime())) {
            this.afterAppointmentTime = jdhDispatch.getServiceInfo().getAppointmentTime().formatAppointmentStartTime();
        }
    }

    public DispatchCallbackEventBody(JdhDispatch snapshot, JdhDispatch jdhDispatch, Boolean isFirstReceived, Integer operatorRoleType, String reason, String serviceBriefInfo) {
        this.dispatchId = jdhDispatch.getDispatchId();
        this.promiseId = jdhDispatch.getPromiseId();
        this.beforeStatus = snapshot.getDispatchStatus();
        this.afterStatus = jdhDispatch.getDispatchStatus();
        this.isFirstReceived = isFirstReceived;
        if (Objects.nonNull(jdhDispatch.getServiceInfo()) && Objects.nonNull(jdhDispatch.getServiceInfo().getBeforeTime())) {
            this.beforeAppointmentTime = jdhDispatch.getServiceInfo().getBeforeTime().formatAppointmentStartTime();
        }
        if (Objects.nonNull(jdhDispatch.getServiceInfo()) && Objects.nonNull(jdhDispatch.getServiceInfo().getAppointmentTime())) {
            this.afterAppointmentTime = jdhDispatch.getServiceInfo().getAppointmentTime().formatAppointmentStartTime();
        }
        this.operatorRoleType = operatorRoleType;
        this.reason = reason;
        if (Objects.nonNull(jdhDispatch.getServiceInfo()) && CollectionUtils.isNotEmpty(jdhDispatch.getServiceInfo().getPatients())) {
            Set<String> itemNames = jdhDispatch.getServiceInfo().getPatients().stream().flatMap(patientDto -> Optional.ofNullable(patientDto.getServiceItems()).orElse(Collections.emptyList()).stream()).map(ServiceItem::getItemName).collect(Collectors.toSet());
            this.serviceName = CollectionUtils.isEmpty(itemNames) ? "" : itemNames.size() > 2 ? itemNames.stream().limit(2).collect(Collectors.joining("、")) + "..."  : String.join("、", itemNames);
        }
    }

    public DispatchCallbackEventBody(JdhDispatch snapshot, JdhDispatch jdhDispatch, Long angelId, Boolean isFirstReceived) {
        this.dispatchId = jdhDispatch.getDispatchId();
        this.promiseId = jdhDispatch.getPromiseId();
        this.beforeStatus = snapshot.getDispatchStatus();
        this.afterStatus = jdhDispatch.getDispatchStatus();
        this.angelId = angelId;
        this.isFirstReceived = isFirstReceived;
        if (Objects.nonNull(jdhDispatch.getServiceInfo()) && Objects.nonNull(jdhDispatch.getServiceInfo().getBeforeTime())) {
            this.beforeAppointmentTime = jdhDispatch.getServiceInfo().getBeforeTime().formatAppointmentStartTime();
        }
        if (Objects.nonNull(jdhDispatch.getServiceInfo()) && Objects.nonNull(jdhDispatch.getServiceInfo().getAppointmentTime())) {
            this.afterAppointmentTime = jdhDispatch.getServiceInfo().getAppointmentTime().formatAppointmentStartTime();
        }
    }

    public DispatchCallbackEventBody(JdhDispatch snapshot, JdhDispatch jdhDispatch, Long angelId, String operator, Long dispatchDetailId, Boolean isFirstReceived) {
        this.dispatchId = jdhDispatch.getDispatchId();
        this.promiseId = jdhDispatch.getPromiseId();
        this.beforeStatus = snapshot.getDispatchStatus();
        this.afterStatus = jdhDispatch.getDispatchStatus();
        this.angelId = angelId;
        this.operator = operator;
        this.dispatchDetailId = dispatchDetailId;
        this.isFirstReceived = isFirstReceived;
        if (Objects.nonNull(jdhDispatch.getServiceInfo()) && Objects.nonNull(jdhDispatch.getServiceInfo().getBeforeTime())) {
            this.beforeAppointmentTime = jdhDispatch.getServiceInfo().getBeforeTime().formatAppointmentStartTime();
        }
        if (Objects.nonNull(jdhDispatch.getServiceInfo()) && Objects.nonNull(jdhDispatch.getServiceInfo().getAppointmentTime())) {
            this.afterAppointmentTime = jdhDispatch.getServiceInfo().getAppointmentTime().formatAppointmentStartTime();
        }
    }
}