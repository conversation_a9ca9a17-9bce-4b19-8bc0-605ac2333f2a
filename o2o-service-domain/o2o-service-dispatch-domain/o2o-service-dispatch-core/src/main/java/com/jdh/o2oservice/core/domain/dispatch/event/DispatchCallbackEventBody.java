package com.jdh.o2oservice.core.domain.dispatch.event;

import com.jdh.o2oservice.core.domain.dispatch.model.JdhDispatch;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName DispatchReceivedEventBody
 * @Description
 * <AUTHOR>
 * @Date 2024/4/17 22:07
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DispatchCallbackEventBody extends DispatchEventBaseBody {

    /**
     * 派单任务ID
     */
    private Long dispatchId;

    /**
     * 履约单ID
     */
    private Long promiseId;

    /**
     * 服务者ID
     */
    private Long angelId;

    /**
     * 操作人（一般为服务者）
     */
    private String operator;

    /**
     * 派单任务明细ID-指定派单护士数据的detailId
     */
    private Long dispatchDetailId;

    /**
     * 首次接单
     */
    private Boolean isFirstReceived;

    public DispatchCallbackEventBody(JdhDispatch snapshot, JdhDispatch jdhDispatch) {
        this.dispatchId = jdhDispatch.getDispatchId();
        this.promiseId = jdhDispatch.getPromiseId();
        this.beforeStatus = snapshot.getDispatchStatus();
        this.afterStatus = jdhDispatch.getDispatchStatus();
    }

    public DispatchCallbackEventBody(JdhDispatch snapshot, JdhDispatch jdhDispatch, Boolean isFirstReceived) {
        this.dispatchId = jdhDispatch.getDispatchId();
        this.promiseId = jdhDispatch.getPromiseId();
        this.beforeStatus = snapshot.getDispatchStatus();
        this.afterStatus = jdhDispatch.getDispatchStatus();
        this.isFirstReceived = isFirstReceived;
    }

    public DispatchCallbackEventBody(JdhDispatch snapshot, JdhDispatch jdhDispatch, Long angelId, Boolean isFirstReceived) {
        this.dispatchId = jdhDispatch.getDispatchId();
        this.promiseId = jdhDispatch.getPromiseId();
        this.beforeStatus = snapshot.getDispatchStatus();
        this.afterStatus = jdhDispatch.getDispatchStatus();
        this.angelId = angelId;
        this.isFirstReceived = isFirstReceived;
    }

    public DispatchCallbackEventBody(JdhDispatch snapshot, JdhDispatch jdhDispatch, Long angelId, String operator, Long dispatchDetailId, Boolean isFirstReceived) {
        this.dispatchId = jdhDispatch.getDispatchId();
        this.promiseId = jdhDispatch.getPromiseId();
        this.beforeStatus = snapshot.getDispatchStatus();
        this.afterStatus = jdhDispatch.getDispatchStatus();
        this.angelId = angelId;
        this.operator = operator;
        this.dispatchDetailId = dispatchDetailId;
        this.isFirstReceived = isFirstReceived;
    }
}