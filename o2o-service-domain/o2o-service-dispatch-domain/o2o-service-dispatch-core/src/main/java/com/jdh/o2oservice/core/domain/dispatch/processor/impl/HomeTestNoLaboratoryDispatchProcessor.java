package com.jdh.o2oservice.core.domain.dispatch.processor.impl;

import com.alibaba.fastjson.JSON;
import com.jdh.o2oservice.base.processor.MapAutowiredKey;
import com.jdh.o2oservice.common.enums.BusinessModeEnum;
import com.jdh.o2oservice.common.enums.ServiceTypeEnum;
import com.jdh.o2oservice.core.domain.dispatch.context.AngelDispatchContext;
import com.jdh.o2oservice.core.domain.dispatch.context.DispatchCallbackContext;
import com.jdh.o2oservice.core.domain.dispatch.context.DispatchCompleteContext;
import com.jdh.o2oservice.core.domain.dispatch.context.TargetDispatchContext;
import com.jdh.o2oservice.core.domain.dispatch.factory.DispatchProcessorFactory;
import com.jdh.o2oservice.core.domain.dispatch.processor.DispatchProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @ClassName HomeTestNoLaboratoryDispatchProcessor
 * @Description
 * <AUTHOR>
 * @Date 2025/7/7 16:30
 **/
@Service
@Slf4j
public class HomeTestNoLaboratoryDispatchProcessor implements DispatchProcessor, MapAutowiredKey {

    /**
     * homeTestDispatchProcessor
     */
    @Resource
    private HomeTestDispatchProcessor homeTestDispatchProcessor;

    /**
     * 发起派单
     * @param context
     * @return
     */
    @Override
    public Boolean angelDispatch(AngelDispatchContext context) {
        log.info("HomeTestNoLaboratoryDispatchProcessor -> angelDispatch forward, 护士上门context={}", JSON.toJSONString(context));
        return homeTestDispatchProcessor.angelDispatch(context);
    }

    /**
     * 定向派单
     *
     * @param context
     * @return
     */
    @Override
    public Boolean targetDispatch(TargetDispatchContext context) {
        log.info("HomeTestNoLaboratoryDispatchProcessor -> targetDispatch forward, 护士上门context={}", JSON.toJSONString(context));
        return homeTestDispatchProcessor.targetDispatch(context);
    }

    /**
     * 派单接单
     *
     * @param context
     * @return
     */
    @Override
    public Boolean doctorReply(DispatchCallbackContext context) {
        log.info("HomeTestNoLaboratoryDispatchProcessor -> doctorReply forward, 护士上门context={}", JSON.toJSONString(context));
        return homeTestDispatchProcessor.doctorReply(context);
    }

    /**
     * 派单完成
     *
     * @param context
     * @return
     */
    @Override
    public Boolean dispatchComplete(DispatchCompleteContext context) {
        log.info("HomeTestNoLaboratoryDispatchProcessor -> dispatchComplete forward, 护士上门context={}", JSON.toJSONString(context));
        return homeTestDispatchProcessor.dispatchComplete(context);
    }

    /**
     * 获取map key
     * @return
     */
    @Override
    public String getMapKey() {
        //业务身份 + 服务类型
        return DispatchProcessorFactory.createRouteKey(BusinessModeEnum.ANGEL_TEST_NO_LABORATORY.getCode(), ServiceTypeEnum.TEST.getServiceType());
    }
}