package com.jdh.o2oservice.core.domain.medpromise.event;

import com.jdh.o2oservice.base.event.EventBody;
import com.jdh.o2oservice.export.medicalpromise.dto.MedicalPromiseDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @Description: 检测单状态body
 * @Author: lvyifan3
 * @Date: 2024/5/1
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MedicalPromiseEventBody implements EventBody {

    /**
     * 检测单ID
     */
    private Long medicalPromiseId;
    /**
     * 履约单ID
     */
    private Long promiseId;

    /**
     * 服务兑换来源id
     */
    private String sourceVoucherId;
    /**
     * 样本条码
     */
    private String specimenCode;
    /**
     * 检测单状态：待检测/检测中/已出报告
     */
    private Integer status;

    /** 垂直业务身份编码 */
    private String verticalCode;

    /** 服务类型 */
    private String serviceType;
    /**
     *
     */
    private String outerId;
    /**
     * 服务ID，快检为skuNo
     */
    private String serviceId;
    /**
     * 受检人
     */
    private Long promisePatientId;
    /**
     * 预约单ID，人+sku纬度 虚拟出来的，用来做幂等
     */
    private String appointmentId;
    /**
     * 检测单更改之前状态：待检测/检测中/已出报告
     */
    private Integer beforeStatus;

    /**
     * 新的条码
     */
    private String oldSpecimenCode;
    /**
     * 旧的条码
     */
    private String freshSpecimenCode;

    /**
     * 实验室收样时间
     */
    private Date stationReceiveTime;

    /**
     * 骑手送达时间
     */
    private Date shipFinishTime;
    /**
     * 检测单特殊标记（0.普通订单，1.加项订单）
     */
    private String flag;

    private String extJson;//扩展字段

    private Integer version;//版本号

    /** 服务地点id */
    private String stationId;

    /** 服务地点id */
    private String beforeStationId;

    /**
     * 待收样超时绝对时间
     */
    private Date waitingTestTimeOutDate;

    /**
     * 是否待收样超时
     * 0 - 否 1 - 是
     */
    private Integer waitingTestTimeOutStatus;

    /**
     * 检测超时绝对时间
     */
    private Date testingTimeOutDate;

    /**
     * 是否检测超时
     * 0 - 否 1 - 是
     */
    private Integer testingTimeOutStatus;

    /** 是否处理完成状态 */
    private Boolean finishState = true;

    /**
     * 检测单List
     */
    private List<MedicalPromiseDTO> medicalPromises;

    /**
     * 服务者工单状态：1=待接单，2=待服务，3=已出门，4=服务中，5=送检中，6=服务完成，7=已退款，8=已取消
     */
    private Integer workStatus;
}
