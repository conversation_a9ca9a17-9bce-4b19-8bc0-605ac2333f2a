package com.jdh.o2oservice.core.domain.medpromise.repository;

import com.google.common.collect.Lists;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.processor.MapAutowiredKey;
import com.jdh.o2oservice.core.domain.medpromise.bo.StoreDispatchBO;
import com.jdh.o2oservice.core.domain.medpromise.context.MedicalPromiseDispatchContext;
import com.jdh.o2oservice.core.domain.medpromise.enums.MedPromiseErrorCode;
import com.jdh.o2oservice.core.domain.medpromise.factory.MedDispatchProcessorFactory;
import com.jdh.o2oservice.core.domain.medpromise.factory.StoreDispatchRuleFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @Description: 骑手上门采样分派实验室
 * @Author: wangpengfei144
 * @Date: 2024/4/15
 */
@Slf4j
@Service
public class KnightHomeDispatchRuleProcessor implements DispatchRuleProcessor, MapAutowiredKey {


    /**
     * 获取地图键
     * @return 使用消息到达工厂创建路由键的结果
     */
    @Override
    public String getMapKey() {
        return MedDispatchProcessorFactory.createRouteKey("KnightHomeDispatchRuleProcessor");
    }


    /**
     * 顺序规则factory
     */
    @Autowired
    private StoreDispatchRuleFactory storeDispatchRuleFactory;

    /**
     * 分派实验室
     */
    @Override
    public List<Map<String, Set<String>>> storeDispatch(MedicalPromiseDispatchContext medicalPromiseDispatchContext) {
        log.info("KnightHomeDispatchRuleProcessor->storeDispatch,start");
        StoreDispatchBO storeDispatchBO = new StoreDispatchBO();
        List<Map<String, Set<String>>> res = Lists.newArrayList();
        storeDispatchBO.setMedicalPromiseGroupList(res);
        storeDispatchBO.setJdhStationServiceItemRelContexts(medicalPromiseDispatchContext.getJdhStationServiceItemRelContexts());
        storeDispatchBO.setMedicalPromises(medicalPromiseDispatchContext.getMedicalPromises());
        storeDispatchBO.setStoreDispatchRules(medicalPromiseDispatchContext.getStoreDispatchRules());
        storeDispatchBO.setPromiseId(medicalPromiseDispatchContext.getPromiseId());
        storeDispatchBO.setOrderId(medicalPromiseDispatchContext.getOrderId());
        storeDispatchBO.setBookTimeSpan(medicalPromiseDispatchContext.getBookTimeSpan());
        storeDispatchBO.setBusinessModeCode(medicalPromiseDispatchContext.getBusinessModeCode());
        //获取过滤和圈选规则,实验室有效性&圈选
        List<StoreDispatchRule> storeSwitchRules = Lists.newArrayList(
                storeDispatchRuleFactory.createDispatchRuleProcessor(CommonConstant.DISPATCH_ROUTE_KEY,CommonConstant.VALID_STR)
                ,storeDispatchRuleFactory.createDispatchRuleProcessor(CommonConstant.DISPATCH_ROUTE_KEY,CommonConstant.SELECT_STR)
        );
        //遍历圈选规则
        for (StoreDispatchRule storeDispatchRule : storeSwitchRules){
            storeDispatchBO = storeDispatchRule.handle(storeDispatchBO);
        }
        log.info("KnightHomeDispatchRuleProcessor->storeDispatch,storeDispatchBO={}", JsonUtil.toJSONString(storeDispatchBO));
        //没有能做完这些项目的实验室组,
        if (CollectionUtils.isEmpty(storeDispatchBO.getMedicalPromiseGroupList())){
            throw new BusinessException(MedPromiseErrorCode.MEDICAL_PROMISE_STATION_GROUP_NULL);
        }
        //骑手上门必须是唯一实验室
        storeDispatchBO.getMedicalPromiseGroupList().removeIf(p->p.size()!=CommonConstant.ONE);
        if (CollectionUtils.isEmpty(storeDispatchBO.getMedicalPromiseGroupList())){
            throw new BusinessException(MedPromiseErrorCode.MEDICAL_PROMISE_STATION_GROUP_NULL);
        }
        //只有一个满足的实验室组，则返回这一个
        if (storeDispatchBO.getMedicalPromiseGroupList().size()==1){
            //返回唯一一个
            return storeDispatchBO.getMedicalPromiseGroupList();
        }
        //排序
        List<StoreDispatchRule> storeDispatchRules = medicalPromiseDispatchContext.getStoreDispatchRules();
        for (StoreDispatchRule storeDispatchRule : storeDispatchRules){
            storeDispatchBO = storeDispatchRule.handle(storeDispatchBO);
            log.info("KnightHomeDispatchRuleProcessor->storeDispatch,sort={}", JsonUtil.toJSONString(storeDispatchBO));
            if (storeDispatchBO.getLast()){
                break;
            }
        }
        return storeDispatchBO.getMedicalPromiseGroupList();
    }
}
