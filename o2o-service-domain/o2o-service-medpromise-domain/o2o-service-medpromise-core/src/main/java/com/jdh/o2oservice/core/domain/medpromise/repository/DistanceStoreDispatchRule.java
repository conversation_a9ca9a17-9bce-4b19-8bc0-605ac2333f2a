package com.jdh.o2oservice.core.domain.medpromise.repository;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jd.medicine.base.common.util.StringUtil;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.processor.MapAutowiredKey;
import com.jdh.o2oservice.common.enums.BusinessModeEnum;
import com.jdh.o2oservice.core.domain.medpromise.bo.StoreDispatchBO;
import com.jdh.o2oservice.core.domain.medpromise.context.JdhStationServiceItemRelContext;
import com.jdh.o2oservice.core.domain.medpromise.context.StationTransferContext;
import com.jdh.o2oservice.core.domain.medpromise.factory.StoreDispatchRuleFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 实验室距离排序
 * @Author: wangpengfei144
 * @Date: 2024/4/15
 */
@Component
@Slf4j
public class DistanceStoreDispatchRule implements StoreDispatchRule , MapAutowiredKey {

    @Autowired
    private DuccConfig duccConfig;

    /**
     * 获取map key
     *
     * @return
     */
    @Override
    public String getMapKey() {
        return StoreDispatchRuleFactory.createRouteKey(CommonConstant.DISPATCH_ROUTE_KEY, CommonConstant.TWO);
    }

    /**
     * 处理
     */
    @Override
    public StoreDispatchBO handle(StoreDispatchBO storeDispatchBO) {
        log.info("StoreValidFilterRule->DISTANCE,Start,storeDispatchBO= {}", JsonUtil.toJSONString(storeDispatchBO));

        //如果可分配实验室组为空，返回
        if (CollectionUtils.isEmpty(storeDispatchBO.getMedicalPromiseGroupList())){
            return storeDispatchBO;
        }
        //如果只有一个实验室组，直接返回
        List<Map<String, Set<String>>> medicalPromiseGroupList = storeDispatchBO.getMedicalPromiseGroupList();
        if (medicalPromiseGroupList.size()==1){
            if (StringUtils.isBlank(storeDispatchBO.getSelectName())){
                storeDispatchBO.setSelectName("距离");
            }
            storeDispatchBO.setLast(Boolean.TRUE);
            return storeDispatchBO;
        }

        if (StringUtils.isBlank(storeDispatchBO.getSelectName())){
            storeDispatchBO.setSelectName("距离");
        }else {
            storeDispatchBO.setSelectName(storeDispatchBO.getSelectName()+"距离");
        }

        //结果
        List<Map<String, Set<String>>> res = Lists.newArrayList();
        //最小距离
        BigDecimal minDistance = new BigDecimal(Integer.MAX_VALUE);
        //key：站点ID，value：距离
        Map<String, BigDecimal> stationToDistance = Maps.newHashMap();



        if (StringUtil.equals(BusinessModeEnum.SELF_TEST.getCode(),storeDispatchBO.getBusinessModeCode()) && duccConfig.getUavStoreDispatchLogic()){
            for (JdhStationServiceItemRelContext jdhStationServiceItemRelContext : storeDispatchBO.getJdhStationServiceItemRelContexts()) {
                StationTransferContext min = Collections.min(jdhStationServiceItemRelContext.getStationTransferContexts(), Comparator.comparing(StationTransferContext::getDistance));
                stationToDistance.put(jdhStationServiceItemRelContext.getStationId(),min.getDistance());
            }
        }else {
            stationToDistance = storeDispatchBO.getJdhStationServiceItemRelContexts().stream().collect(Collectors.toMap(JdhStationServiceItemRelContext::getStationId, JdhStationServiceItemRelContext::getDistance));
        }



//         = storeDispatchBO.getJdhStationServiceItemRelContexts().stream().collect(Collectors.toMap(JdhStationServiceItemRelContext::getStationId, JdhStationServiceItemRelContext::getDistance));
        //遍历可能的实验室组，每种组合计算需要的距离
        for (Map<String, Set<String>> stringListMap : medicalPromiseGroupList) {
            //计算距离
            BigDecimal totalDistance = getDistance(stringListMap,stationToDistance);
            //获取最小的距离
            minDistance = judgeMinDistance(res, minDistance, stringListMap, totalDistance);
        }
        //返回结果
        storeDispatchBO.setMedicalPromiseGroupList(res);
        if (res.size()==1){
            log.info("DistanceStoreDispatchRule->dispatch,根据距离筛选成功,minDistance = {}",minDistance.toString());
            storeDispatchBO.setLast(Boolean.TRUE);
        }

        return storeDispatchBO;
    }

    /**
     * 获取最短的距离
     * @param res
     * @param minDistance
     * @param stringListMap
     * @param totalDistance
     * @return
     */
    private  BigDecimal judgeMinDistance(List<Map<String, Set<String>>> res, BigDecimal minDistance, Map<String, Set<String>> stringListMap, BigDecimal totalDistance) {
        //如果这个组合的距离更近，选最近的
        if (minDistance.compareTo(totalDistance) > 0) {
            minDistance = totalDistance;
            res.clear();
            res.add(stringListMap);
        }else if (minDistance.compareTo(totalDistance) == 0){
            //如果一样，加入，继续下一个判断
            res.add(stringListMap);
        }
        return minDistance;
    }

    /**
     * 计算距离
     * @param stringListMap
     * @return
     */
    private  BigDecimal getDistance(Map<String, Set<String>> stringListMap,Map<String, BigDecimal> stationToDistance) {
        //实验室ID
        Set<String> stationIdSet = stringListMap.keySet();
        //初始化距离
        BigDecimal distance = BigDecimal.ZERO;
       //计算距离
        for (String s : stationIdSet){
            distance= distance.add(stationToDistance.get(s));
        }
        log.info("DistanceStoreDispatchRule->getDistance,stationId={},distance = {}",JsonUtil.toJSONString(stationIdSet),distance);
        return distance;
    }

}
