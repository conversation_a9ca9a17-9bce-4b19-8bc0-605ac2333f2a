package com.jdh.o2oservice.core.domain.medpromise.enums;

import com.jd.common.util.StringUtils;
import com.jdh.o2oservice.common.enums.BusinessModeEnum;
import com.jdh.o2oservice.common.enums.ServiceTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Description: 派发实验室路由key枚举
 * Enum: MedicalPromiseDispatchRuleKeyEnum
 * Author: wangpengfei144
 * Date: 2024/4/25
 */
@Getter
@AllArgsConstructor
public enum MedicalPromiseDispatchRuleKeyEnum {

    /**
     * 自采样
     */
    SELF_TEST(BusinessModeEnum.SELF_TEST.getCode(),"KnightHomeDispatchRuleProcessor"),
    /**
     * 护士采样
     */
    ANGLE_TEST(BusinessModeEnum.ANGEL_TEST.getCode(),"NurseHomeDispatchRuleProcessor"),
    /**
     * 非快检模式-京东物流
     */
    SELF_TEST_TRANSPORT(BusinessModeEnum.SELF_TEST_TRANSPORT.getCode(),"SelfTestTransportDispatchRuleProcessor"),
    /**
     * 护士采样（无实验室履约模式）
     */
    ANGEL_TEST_NO_LABORATORY(BusinessModeEnum.ANGEL_TEST_NO_LABORATORY.getCode(),"NurseHomeDispatchRuleProcessor"),



//
//    /**
//     * 骑手上门用户采样
//     */
//    KNIGHT_HOME_CHECK("xfylHomeSelfTest1Phase", BusinessModeEnum.SELF_TEST.getCode(), ServiceTypeEnum.TEST.getServiceType(), "KnightHomeDispatchRuleProcessor"),
//    /**
//     * 护士上门检测
//     */
//    NURSE_HOME_CHECK("xfylHomeTest",BusinessModeEnum.ANGEL_TEST.getCode(),ServiceTypeEnum.TEST.getServiceType(),"NurseHomeDispatchRuleProcessor"),
//
//    /**
//     * 互医开单情况 骑手上门用户采样
//     */
//    NH_KNIGHT_HOME_CHECK("nhHomeSelfTest1Phase",BusinessModeEnum.SELF_TEST.getCode(),ServiceTypeEnum.TEST.getServiceType(),"KnightHomeDispatchRuleProcessor"),
//
//    /**
//     * 互医开单情况 护士上门检测
//     */
//    NH_NURSE_HOME_CHECK("nhHomeTest",BusinessModeEnum.ANGEL_TEST.getCode(),ServiceTypeEnum.TEST.getServiceType(),"NurseHomeDispatchRuleProcessor"),
//
//    /**
//     * 家医开单情况 骑手上门用户采样
//     */
//    FH_KNIGHT_HOME_CHECK("fhHomeSelfTest1Phase",BusinessModeEnum.SELF_TEST.getCode(),ServiceTypeEnum.TEST.getServiceType(),"KnightHomeDispatchRuleProcessor"),
//
//    /**
//     * 家医开单情况 护士上门检测
//     */
//    FH_NURSE_HOME_CHECK("fhhHomeTest",BusinessModeEnum.ANGEL_TEST.getCode(),ServiceTypeEnum.TEST.getServiceType(),"NurseHomeDispatchRuleProcessor"),

    ;

    /**
     * 业务模式
     */
    private final String businessModeCode;

    /**
     * 路由key
     */
    private final String routeKey;


    /**
     *  根据垂直业务身份编码和服务类型 获取路由key
     * @return
     */
    public static String getRouteKey(String businessModeCode){
        if (StringUtils.isBlank(businessModeCode)){
            return null;
        }
        for (MedicalPromiseDispatchRuleKeyEnum medicalPromiseDispatchRuleKeyEnum : MedicalPromiseDispatchRuleKeyEnum.values()){
            if (StringUtils.equals(medicalPromiseDispatchRuleKeyEnum.getBusinessModeCode(),businessModeCode)){
                return medicalPromiseDispatchRuleKeyEnum.getRouteKey();
            }
        }
        return null;

    }

}
