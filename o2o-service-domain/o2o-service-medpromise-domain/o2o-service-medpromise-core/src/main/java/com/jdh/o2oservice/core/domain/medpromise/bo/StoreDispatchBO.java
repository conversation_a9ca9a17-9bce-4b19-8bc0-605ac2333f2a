package com.jdh.o2oservice.core.domain.medpromise.bo;

import com.jdh.o2oservice.core.domain.medpromise.context.JdhStationServiceItemRelContext;
import com.jdh.o2oservice.core.domain.medpromise.model.MedicalPromise;
import com.jdh.o2oservice.core.domain.medpromise.repository.StoreDispatchRule;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @Description: 实验室派发BO
 * @Author: wangpengfei144
 * @Date: 2024/4/22
 */
@Data
public class StoreDispatchBO {

    /**
     * 是否是最后的结果
     */
    private Boolean last = Boolean.FALSE;

    /**
     * 检测单列表
     */
    private List<MedicalPromise> medicalPromises;

    /**
     * 关联关系
     */
    private List<JdhStationServiceItemRelContext> jdhStationServiceItemRelContexts;

//    /**
//     *
//     */
//    private List<Map<String,List<MedicalPromise>>> medicalPromiseGroupList;

    /**
     * key：实验室ID，value：实验室要做的项目set
     */
    private List<Map<String, Set<String>>> medicalPromiseGroupList;

    /**
     * 派送实验室排序规则
     */
    private List<StoreDispatchRule> storeDispatchRules;

    /**
     * 预约开始时间
     * 格式 yyyy-MM-dd HH:mm
     */
    protected Date appointmentStartTime;

    /**
     * 预约结束时间
     * 格式 yyyy-MM-dd HH:mm
     */
    protected Date appointmentEndTime;

    /**
     * 方案名称，整单_距离_成本
     */
    private String selectName;

    /**
     * key：实验室ID，value：实验室要做的项目set 全部方案快照版
     */
    private List<Map<String, Set<String>>> medicalPromiseGroupListShot;

    /**
     * 履约单ID
     */
    private Long promiseId;

    /**
     * 订单ID
     */
    private Long orderId;
    /**
     * 预约时间 09:00-10:00
     */
    private String bookTimeSpan;

    private String highQualityStoreId;//定向派实验室
    /**
     * 业务模式
     */
    private String businessModeCode;
}
