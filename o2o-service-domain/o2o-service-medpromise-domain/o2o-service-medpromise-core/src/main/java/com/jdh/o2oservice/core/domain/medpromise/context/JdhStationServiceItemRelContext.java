package com.jdh.o2oservice.core.domain.medpromise.context;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Description: 临时定义实验室和项目关系
 * @Author: wangpengfei144
 * @Date: 2024/4/22
 */
@Data
public class JdhStationServiceItemRelContext {
    /**
     * <pre>
     * 京东门店id
     * </pre>
     */
    private String stationId;

    /**
     * 门店所在省份
     */
    private Integer provinceId;

    /**
     * 门店所在城市
     */
    private Integer cityId;

    /**
     * 省份名称
     */
    private String provinceName;

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 渠道编号
     */
    private Long channelNo;

    /**
     * 服务门店状态（是否可用）
     */
    private Integer stationStatus;

    /**
     * 门店地址
     */
    private String stationAddr;

    /**
     * 经度
     */
    private String stationLng;

    /**
     * 纬度
     */
    private String stationLat;

    /**
     * 门店营业时间
     */
    private String stationHours;

    /**
     * 据派送起始地址多远
     */
    private BigDecimal distance;

    /**
     * 实验室名称
     */
    private String stationName;

    /**
     * 实验室联系方式
     */
    private String stationPhone;

    /**
     * 可检测项目信息
     */
    private List<JdhServiceItemContext> jdhServiceItemContexts;

    /**
     * 实验室接驳点
     */
    private List<StationTransferContext> stationTransferContexts;


}
