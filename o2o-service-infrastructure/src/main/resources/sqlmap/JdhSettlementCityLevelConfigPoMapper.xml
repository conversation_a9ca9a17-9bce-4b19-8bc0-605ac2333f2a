<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jdh.o2oservice.infrastructure.repository.db.dao.JdhSettlementCityLevelConfigPoMapper">

    <insert id="batchSaveJdhSettlementCityLevelConfig">
        INSERT INTO jdh_settlement_city_level_config
        (city_config_id, province_code, province_name, city_code, city_name,
         dest_code,level, branch, create_user,update_user)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.cityConfigId}, #{item.provinceCode}, #{item.provinceName}, #{item.cityCode}, #{item.cityName},#{item.destCode},
            #{item.level}, #{item.branch}, #{item.createUser}, #{item.updateUser})
        </foreach>
    </insert>
</mapper>