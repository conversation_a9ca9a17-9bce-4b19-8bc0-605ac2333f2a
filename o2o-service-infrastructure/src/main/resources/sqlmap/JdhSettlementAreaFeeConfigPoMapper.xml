<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jdh.o2oservice.infrastructure.repository.db.dao.JdhSettlementAreaFeeConfigPoMapper">

    <insert id="batchSaveJdhSettlementAreaFeeConfig">
        INSERT INTO jdh_settlement_area_fee_config
        (fee_config_id,settlement_subject_type,settlement_subject_sub_type, province_code, province_name, city_code, city_name,
        county_code,town_code,county_name,town_name,dest_code, branch, create_user,update_user)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.feeConfigId}, #{item.settlementSubjectType}, #{item.settlementSubjectSubType},#{item.provinceCode}, #{item.provinceName},
             #{item.cityCode}, #{item.cityName},#{item.countyCode},#{item.townCode},#{item.countyName},#{item.townName},#{item.destCode},
            #{item.branch}, #{item.createUser}, #{item.updateUser})
        </foreach>
    </insert>
</mapper>
