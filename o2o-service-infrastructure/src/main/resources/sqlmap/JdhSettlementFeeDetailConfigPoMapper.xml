<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jdh.o2oservice.infrastructure.repository.db.dao.JdhSettlementFeeDetailConfigPoMapper">

    <insert id="batchSaveJdhSettlementFeeDetailConfig">
        INSERT INTO jdh_settlement_fee_detail_config
        (fee_config_detail_id, fee_config_id, fee_type, fee_amount, branch)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.feeConfigDetailId}, #{item.feeConfigId}, #{item.feeType}, #{item.feeAmount}, #{item.branch})
        </foreach>
    </insert>
</mapper>
