package com.jdh.o2oservice.infrastructure.repository.db.po;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/5/23
 * @description 节点和题关联关系
 */
@Data
@TableName(value = "jdh_group_ques_rel",autoResultMap = true)
public class JdhGroupQuesRelPo extends JdhBasicPo{

    private Long id;//主键id

    private String quesId;//题id

    private String groupCode;//节点code

    @Deprecated
    private Integer type;//1题库中的题 2ducc配置的题

    private String extJson;//扩展字段 保存 客户签字和护士签字等内容

    private String quesCode;//题code

    private Integer sort;//排序由小到大

    private Long serviceItemId;//服务项目id
}
