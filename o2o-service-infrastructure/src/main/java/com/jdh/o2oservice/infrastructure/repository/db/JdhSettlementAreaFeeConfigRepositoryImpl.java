package com.jdh.o2oservice.infrastructure.repository.db;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.enums.YnStatusEnum;
import com.jdh.o2oservice.core.domain.settlement.model.JdhSettlementAreaFeeConfig;
import com.jdh.o2oservice.core.domain.settlement.model.JdhSettlementAreaFeeConfigIdentifier;
import com.jdh.o2oservice.core.domain.settlement.repository.db.JdhSettlementAreaFeeConfigRepository;
import com.jdh.o2oservice.core.domain.settlement.repository.query.JdhAngelSettleAreaFeeQuery;
import com.jdh.o2oservice.infrastructure.repository.db.convert.JdhAngelSettlementPoConvert;
import com.jdh.o2oservice.infrastructure.repository.db.convert.JdhBasicPoConverter;
import com.jdh.o2oservice.infrastructure.repository.db.dao.JdhSettlementAreaFeeConfigPoMapper;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhAngelPo;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhSettlementAreaFeeConfigPo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * JdhSettlementAreaFeeConfigRepositoryImpl
 * @author:lwm
 * @createTime: 2025-04-18 16:06
 * @Description:
 */
@Component
@Slf4j
public class JdhSettlementAreaFeeConfigRepositoryImpl implements JdhSettlementAreaFeeConfigRepository {


    /**
     * jdhSettlementAreaFeeConfigPoMapper
     */
    @Resource
    private JdhSettlementAreaFeeConfigPoMapper jdhSettlementAreaFeeConfigPoMapper;


    /**
     * 通过ID寻找Aggregate。
     * 找到的Aggregate自动是可追踪的
     *
     * @param jdhSettlementAreaFeeConfigIdentifier
     */
    @Override
    public JdhSettlementAreaFeeConfig find(JdhSettlementAreaFeeConfigIdentifier jdhSettlementAreaFeeConfigIdentifier) {
        return null;
    }

    /**
     * 将一个Aggregate从Repository移除
     * 操作后的aggregate对象自动取消追踪
     *
     * @param entity
     */
    @Override
    public int remove(JdhSettlementAreaFeeConfig entity) {
        return 0;
    }

    /**
     * 保存地区费项配置
     *
     * @param jdhSettlementAreaFeeConfig
     * @return count
     */
    @Override
    public int save(JdhSettlementAreaFeeConfig jdhSettlementAreaFeeConfig) {
        JdhSettlementAreaFeeConfigPo jdhSettlementAreaFeeConfigPo = JdhAngelSettlementPoConvert.INSTANCE.
                convertToJdhSettlementAreaFeeConfigPo(jdhSettlementAreaFeeConfig);
        return jdhSettlementAreaFeeConfigPoMapper.insert(jdhSettlementAreaFeeConfigPo);
    }

    /**
     * 批量保存地区费项配置
     *
     * @param jdhSettlementAreaFeeConfigList
     */
    @Override
    public int batchSaveJdhAreaFeeConfig(List<JdhSettlementAreaFeeConfig> jdhSettlementAreaFeeConfigList) {
        List<JdhSettlementAreaFeeConfigPo> jdhSettlementAreaFeeConfigPos = JdhAngelSettlementPoConvert.INSTANCE.
                convertToJdhSettlementAreaFeeConfigPoList(jdhSettlementAreaFeeConfigList);
        return jdhSettlementAreaFeeConfigPoMapper.batchSaveJdhSettlementAreaFeeConfig(jdhSettlementAreaFeeConfigPos);
    }

    /**
     * 更新地区费项配置
     *
     * @param jdhSettlementAreaFeeConfig
     * @return count
     */
    @Override
    public int update(JdhSettlementAreaFeeConfig jdhSettlementAreaFeeConfig) {
        LambdaUpdateWrapper<JdhSettlementAreaFeeConfigPo> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(JdhSettlementAreaFeeConfigPo::getUpdateUser, jdhSettlementAreaFeeConfig.getUpdateUser())
                .eq(JdhSettlementAreaFeeConfigPo::getFeeConfigId, jdhSettlementAreaFeeConfig.getFeeConfigId())
                .eq(JdhSettlementAreaFeeConfigPo::getYn,  CommonConstant.ONE);
        return jdhSettlementAreaFeeConfigPoMapper.update(null, updateWrapper);
    }

    /**
     * 更新地区费项配置
     *
     * @param queryContext
     * @return count
     */
    @Override
    public int delete(JdhAngelSettleAreaFeeQuery queryContext) {
        LambdaUpdateWrapper<JdhSettlementAreaFeeConfigPo> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(JdhSettlementAreaFeeConfigPo::getYn, CommonConstant.ZERO)
                .set(JdhSettlementAreaFeeConfigPo::getUpdateUser, queryContext.getOperator())
                .in(JdhSettlementAreaFeeConfigPo::getFeeConfigId, queryContext.getFeeConfigIdList());
        return jdhSettlementAreaFeeConfigPoMapper.update(null, updateWrapper);
    }

    /**
     * @param jdhSettlementAreaFeeConfig
     * @return
     */
    @Override
    public int updateByCode(JdhSettlementAreaFeeConfig jdhSettlementAreaFeeConfig) {
        return 0;
    }

    /**
     * 查询地区费项配置列表
     *
     * @param queryContext
     */
    @Override
    public List<JdhSettlementAreaFeeConfig> queryJdhAreaFeeConfigList(JdhAngelSettleAreaFeeQuery queryContext) {
        LambdaQueryWrapper<JdhSettlementAreaFeeConfigPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(CollUtil.isNotEmpty(queryContext.getFeeConfigIdList()),JdhSettlementAreaFeeConfigPo::getFeeConfigId, queryContext.getFeeConfigIdList())
                .eq(StringUtils.isNotBlank(queryContext.getProvinceCode()),JdhSettlementAreaFeeConfigPo::getProvinceCode, queryContext.getProvinceCode())
                .eq(StringUtils.isNotBlank(queryContext.getCityCode()), JdhSettlementAreaFeeConfigPo::getCityCode, queryContext.getCityCode())
                .eq(StringUtils.isNotBlank(queryContext.getDestCode()), JdhSettlementAreaFeeConfigPo::getDestCode, queryContext.getDestCode())
                .in(CollectionUtils.isNotEmpty(queryContext.getDestCodeList()), JdhSettlementAreaFeeConfigPo::getDestCode, queryContext.getDestCodeList());
                if(queryContext.getNoCountyTownQuery()){
                    queryWrapper.eq(StringUtils.isNotBlank(queryContext.getCountyCode()), JdhSettlementAreaFeeConfigPo::getCountyCode, queryContext.getCountyCode())
                            .eq(StringUtils.isNotBlank(queryContext.getTownCode()), JdhSettlementAreaFeeConfigPo::getTownCode, queryContext.getTownCode());
                }
        queryWrapper.eq(StringUtils.isNotBlank(queryContext.getSubjectType()), JdhSettlementAreaFeeConfigPo::getSettlementSubjectType, queryContext.getSubjectType())
                .eq(StringUtils.isNotBlank(queryContext.getAngelType()), JdhSettlementAreaFeeConfigPo::getSettlementSubjectSubType, queryContext.getAngelType())
                .eq(StringUtils.isNotBlank(queryContext.getOperator()), JdhSettlementAreaFeeConfigPo::getUpdateUser, queryContext.getOperator())
                .eq(JdhSettlementAreaFeeConfigPo::getYn, YnStatusEnum.YES.getCode()).orderByDesc(JdhSettlementAreaFeeConfigPo::getCreateTime);
        List<JdhSettlementAreaFeeConfigPo> poList = jdhSettlementAreaFeeConfigPoMapper.selectList(queryWrapper);
        return JdhAngelSettlementPoConvert.INSTANCE.convertToJdhSettlementAreaFeeConfigList(poList);
    }

    /**
     * 查询地区费项配置列表
     *
     * @param jdhAngelSettlementConfig
     */
    @Override
    public JdhSettlementAreaFeeConfig queryJdhAreaFeeConfig(JdhSettlementAreaFeeConfig jdhAngelSettlementConfig) {
        LambdaQueryWrapper<JdhSettlementAreaFeeConfigPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(StringUtils.isNotBlank(jdhAngelSettlementConfig.getDestCode()), JdhSettlementAreaFeeConfigPo::getDestCode, jdhAngelSettlementConfig.getDestCode())
                .eq(StringUtils.isNotBlank(jdhAngelSettlementConfig.getSettlementSubjectType()), JdhSettlementAreaFeeConfigPo::getSettlementSubjectType, jdhAngelSettlementConfig.getSettlementSubjectType())
                .eq(StringUtils.isNotBlank(jdhAngelSettlementConfig.getSettlementSubjectSubType()), JdhSettlementAreaFeeConfigPo::getSettlementSubjectSubType, jdhAngelSettlementConfig.getSettlementSubjectSubType())
                .eq(JdhSettlementAreaFeeConfigPo::getYn, YnStatusEnum.YES.getCode());
        List<JdhSettlementAreaFeeConfigPo> poList = jdhSettlementAreaFeeConfigPoMapper.selectList(queryWrapper);
        if(CollectionUtils.isNotEmpty(poList)){
            return JdhAngelSettlementPoConvert.INSTANCE.convertToJdhAngelSettlementConfig(poList.get(0));
        }
        return null;
    }

    /**
     * 分页查询护士结算配置
     *
     * @param queryContext
     * @return queryContext
     */
    @Override
    @LogAndAlarm
    public Page<JdhSettlementAreaFeeConfig> queryPage(JdhAngelSettleAreaFeeQuery queryContext) {
        Page<JdhSettlementAreaFeeConfigPo> param = new Page<>(queryContext.getPageNum(), queryContext.getPageSize());
        LambdaQueryWrapper<JdhSettlementAreaFeeConfigPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(StringUtils.isNotBlank(queryContext.getProvinceCode()),JdhSettlementAreaFeeConfigPo::getProvinceCode, queryContext.getProvinceCode())
                .eq(StringUtils.isNotBlank(queryContext.getCityCode()), JdhSettlementAreaFeeConfigPo::getCityCode, queryContext.getCityCode())
                .eq(StringUtils.isNotBlank(queryContext.getCountyCode()), JdhSettlementAreaFeeConfigPo::getCountyCode, queryContext.getCountyCode())
                .eq(StringUtils.isNotBlank(queryContext.getTownCode()), JdhSettlementAreaFeeConfigPo::getTownCode, queryContext.getTownCode())
                .eq(StringUtils.isNotBlank(queryContext.getDestCode()), JdhSettlementAreaFeeConfigPo::getDestCode, queryContext.getDestCode())
                .in(CollectionUtils.isNotEmpty(queryContext.getDestCodeList()), JdhSettlementAreaFeeConfigPo::getDestCode, queryContext.getDestCodeList())
                .eq(StringUtils.isNotBlank(queryContext.getAngelType()), JdhSettlementAreaFeeConfigPo::getSettlementSubjectSubType, queryContext.getAngelType())
                .eq(StringUtils.isNotBlank(queryContext.getOperator()), JdhSettlementAreaFeeConfigPo::getUpdateUser, queryContext.getOperator())
                .eq(JdhSettlementAreaFeeConfigPo::getYn, YnStatusEnum.YES.getCode()).orderByDesc(JdhSettlementAreaFeeConfigPo::getCreateTime);
        IPage<JdhSettlementAreaFeeConfigPo> page = jdhSettlementAreaFeeConfigPoMapper.selectPage(param, queryWrapper);
        List<JdhSettlementAreaFeeConfig> list= JdhAngelSettlementPoConvert.INSTANCE.convertToJdhSettlementAreaFeeConfigList(page.getRecords());
        return JdhBasicPoConverter.initPage(page, list);
    }

    /**
     * @param queryContext
     * @return
     */
    @Override
    public Integer queryCityConfigCount(JdhAngelSettleAreaFeeQuery queryContext) {
        LambdaQueryWrapper<JdhSettlementAreaFeeConfigPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(StringUtils.isNotBlank(queryContext.getProvinceCode()),JdhSettlementAreaFeeConfigPo::getProvinceCode, queryContext.getProvinceCode())
                .eq(StringUtils.isNotBlank(queryContext.getCityCode()), JdhSettlementAreaFeeConfigPo::getCityCode, queryContext.getCityCode())
                .eq(StringUtils.isNotBlank(queryContext.getCountyCode()), JdhSettlementAreaFeeConfigPo::getCountyCode, queryContext.getCountyCode())
                .eq(StringUtils.isNotBlank(queryContext.getTownCode()), JdhSettlementAreaFeeConfigPo::getTownCode, queryContext.getTownCode())
                .eq(StringUtils.isNotBlank(queryContext.getAngelType()), JdhSettlementAreaFeeConfigPo::getSettlementSubjectSubType, queryContext.getAngelType())
                //.eq(StringUtils.isNotBlank(queryContext.getOperator()), JdhSettlementAreaFeeConfigPo::getUpdateUser, queryContext.getOperator())
                .eq(JdhSettlementAreaFeeConfigPo::getYn, YnStatusEnum.YES.getCode()).orderByDesc(JdhSettlementAreaFeeConfigPo::getCreateTime);
        return jdhSettlementAreaFeeConfigPoMapper.selectCount(queryWrapper);
    }
}
