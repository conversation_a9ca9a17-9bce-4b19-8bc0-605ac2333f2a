package com.jdh.o2oservice.infrastructure.repository.db.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/5/23
 * @description 标品和节点关系表
 */
@Data
@TableName(value = "jdh_item_ques_group_rel",autoResultMap = true)
public class JdhItemQuesGroupRelPo extends JdhBasicPo{

    private Long id;//主键id

    private Long serviceItemId;//服务项目id

    @TableField("`show`")
    private Integer show;//1展示 0不展示

    private String groupCode;//节点code

    private String groupSnapshot;//节点快照数据

    private String extJson;//扩展字段

    private Integer sort;//排序由小到大
}
