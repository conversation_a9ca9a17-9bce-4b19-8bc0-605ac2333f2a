package com.jdh.o2oservice.infrastructure.repository.db.po;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/5/23
 * @description 题库
 */
@Data
@TableName(value = "jdh_question",autoResultMap = true)
public class JdhQuestionPo extends JdhBasicPo{

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private String name;//题目名称

    private String quesDesc;//题目说明

    private Integer type;//1单选 2多选 3填空 4图片/视频上传 5题库

    private Integer required;//1必填 0非必填

    private Long quesId;//题目唯一单据

    private String quesCode;//题编码

    @TableField(updateStrategy = FieldStrategy.IGNORED)  // 强制更新此字段
    private String unit;//单位

    @TableField(updateStrategy = FieldStrategy.IGNORED)  // 强制更新此字段
    private Integer highRisk;//1高危 0否

    @TableField(updateStrategy = FieldStrategy.IGNORED)  // 强制更新此字段
    private String extJson;//扩展字段 保存多选项

    private String value;//题的值

    private Integer readOnly;//1只读 0非

    private Integer source;//1题库 2ducc

}
