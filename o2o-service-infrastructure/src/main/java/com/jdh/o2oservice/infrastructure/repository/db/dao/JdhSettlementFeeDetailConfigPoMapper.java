package com.jdh.o2oservice.infrastructure.repository.db.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhSettlementFeeDetailConfigPo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @ClassName JdhSettlementFeeDetailConfigPoMapper
 * @Description
 * <AUTHOR>
 * @Date 2025/04/18 22:22
 **/
public interface JdhSettlementFeeDetailConfigPoMapper extends BaseMapper<JdhSettlementFeeDetailConfigPo> {

    /**
     * 批量插入
     *
     * @param jdhSettlementFeeDetailConfigPoList
     * @return {@link Integer}
     */
    Integer batchSaveJdhSettlementFeeDetailConfig(@Param("list") List<JdhSettlementFeeDetailConfigPo> jdhSettlementFeeDetailConfigPoList);
}