package com.jdh.o2oservice.infrastructure.repository.db;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jdh.o2oservice.base.enums.YnStatusEnum;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelServiceRecord;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelServiceRecordIdentifier;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelServiceRecordRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.query.AngelServiceRecordDBQuery;
import com.jdh.o2oservice.infrastructure.repository.db.convert.AngelServiceRecordPoConverter;
import com.jdh.o2oservice.infrastructure.repository.db.convert.JdhBasicPoConverter;
import com.jdh.o2oservice.infrastructure.repository.db.dao.JdhAngelServiceRecordPoMapper;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhAngelServiceRecordPo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Component
@Slf4j
public class AngelServiceRecordRepositoryImpl implements AngelServiceRecordRepository {

    @Resource
    private JdhAngelServiceRecordPoMapper jdhAngelServiceRecordPoMapper;

    @Override
    public int remove(AngelServiceRecord entity) {
        LambdaUpdateWrapper<JdhAngelServiceRecordPo> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.setSql("`version` = `version` + 1").set(JdhAngelServiceRecordPo::getYn, YnStatusEnum.NO.getCode())
                .set(JdhAngelServiceRecordPo::getUpdateTime, new Date())
                .eq(JdhAngelServiceRecordPo::getRecordId, entity.getRecordId());
        return jdhAngelServiceRecordPoMapper.update(null, updateWrapper);
    }

    @Override
    public int removeByPromiseId(AngelServiceRecord entity) {
        LambdaUpdateWrapper<JdhAngelServiceRecordPo> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.setSql("`version` = `version` + 1").set(JdhAngelServiceRecordPo::getYn, YnStatusEnum.NO.getCode())
                .set(JdhAngelServiceRecordPo::getUpdateTime, new Date())
                .eq(JdhAngelServiceRecordPo::getPromiseId, entity.getPromiseId())
                .eq(JdhAngelServiceRecordPo::getYn,YnStatusEnum.YES.getCode());
        return jdhAngelServiceRecordPoMapper.update(null, updateWrapper);
    }

    /**
     * 查询服务记录列表
     * @param query
     * @return
     */
    @Override
    public List<AngelServiceRecord> findList(AngelServiceRecordDBQuery query) {
        LambdaQueryWrapper<JdhAngelServiceRecordPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(Objects.nonNull(query.getRecordId()), JdhAngelServiceRecordPo::getRecordId, query.getRecordId())
                .eq(Objects.nonNull(query.getTaskId()), JdhAngelServiceRecordPo::getTaskId, query.getTaskId())
                .eq(Objects.nonNull(query.getWorkId()), JdhAngelServiceRecordPo::getWorkId, query.getWorkId())
                .eq(Objects.nonNull(query.getPromiseId()), JdhAngelServiceRecordPo::getPromiseId, query.getPromiseId())
                .eq(StringUtils.isNotBlank(query.getAngelId()), JdhAngelServiceRecordPo::getAngelId, query.getAngelId())
                .eq(Objects.nonNull(query.getStatus()), JdhAngelServiceRecordPo::getStatus, query.getStatus())
                .in(CollectionUtils.isNotEmpty(query.getStatusList()), JdhAngelServiceRecordPo::getStatus, query.getStatusList())
                .eq(StringUtils.isNotBlank(query.getLastFlowNode()), JdhAngelServiceRecordPo::getLastFlowNode, query.getLastFlowNode())
                .eq(JdhAngelServiceRecordPo::getYn, YnStatusEnum.YES.getCode())
                .orderByDesc(JdhAngelServiceRecordPo::getCreateTime);

        List<JdhAngelServiceRecordPo> poList = jdhAngelServiceRecordPoMapper.selectList(queryWrapper);
        return AngelServiceRecordPoConverter.INS.convertToAngelServiceRecordList(poList);
    }

    /**
     * 保存/更新
     * @param entity
     * @return
     */
    @Override
    public int save(AngelServiceRecord entity) {
        if (Objects.isNull(entity)){
            return 0;
        }
        JdhAngelServiceRecordPo angelServiceRecordPo = AngelServiceRecordPoConverter.INS.convertToJdhAngelServiceRecordPo(entity);
        log.info("AngelServiceRecordRepositoryImpl -> save angelServiceRecordPo={}", JSON.toJSONString(angelServiceRecordPo));
        if (Objects.isNull(entity.getId())){
            JdhBasicPoConverter.initInsertBasicPo(angelServiceRecordPo);
            return jdhAngelServiceRecordPoMapper.insert(angelServiceRecordPo);
        }else {
            LambdaUpdateWrapper<JdhAngelServiceRecordPo> updateWrapper = Wrappers.lambdaUpdate();
            updateWrapper.eq(JdhAngelServiceRecordPo::getRecordId, entity.getRecordId())
                    .set(Objects.nonNull(entity.getStatus()), JdhAngelServiceRecordPo::getStatus, entity.getStatus())
                    .set(Objects.nonNull(entity.getTaskId()), JdhAngelServiceRecordPo::getTaskId, entity.getTaskId())
                    .set(Objects.nonNull(entity.getAngelId()), JdhAngelServiceRecordPo::getAngelId, entity.getAngelId())
                    .set(StringUtils.isNotBlank(entity.getServiceItemIdList()), JdhAngelServiceRecordPo::getServiceItemIdList, entity.getServiceItemIdList())
                    .set(StringUtils.isNotBlank(entity.getLastFlowNode()), JdhAngelServiceRecordPo::getLastFlowNode, entity.getLastFlowNode())
                    .setSql("`update_time` = now()")
                    .setSql("`version` = version+1");
            return jdhAngelServiceRecordPoMapper.update(null, updateWrapper);
        }
    }

    /**
     * 通过Identify 查询
     * @param identifier
     * @return
     */
    @Override
    public AngelServiceRecord find(AngelServiceRecordIdentifier identifier) {
        LambdaQueryWrapper<JdhAngelServiceRecordPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(JdhAngelServiceRecordPo::getRecordId, identifier.getRecordId())
                .eq(JdhAngelServiceRecordPo::getYn, YnStatusEnum.YES.getCode());
        JdhAngelServiceRecordPo po = jdhAngelServiceRecordPoMapper.selectOne(queryWrapper);
        return AngelServiceRecordPoConverter.INS.convertToAngelServiceRecord(po);
    }
}
