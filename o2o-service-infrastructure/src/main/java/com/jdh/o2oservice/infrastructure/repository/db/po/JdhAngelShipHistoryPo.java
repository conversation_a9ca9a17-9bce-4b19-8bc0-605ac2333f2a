package com.jdh.o2oservice.infrastructure.repository.db.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Builder;
import lombok.Data;

import java.util.Date;

/**
 * @ClassName:JdhAngelShipHistoryPo
 * @Description: 运单流水表
 * @Author: yaoqinghai
 * @Date: 2024/4/21 14:27
 * @Vserion: 1.0
 **/
@Data
@TableName(value = "jdh_angel_ship_history",autoResultMap = true)
public class JdhAngelShipHistoryPo {
    /**
     * 自增主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 运单Id
     */
    private Long shipId;

    /**
     * 服务工单Id
     */
    private Long workId;

    /**
     * 变更前任务单状态：1
     */
    private Integer beforeStatus;

    /**
     * 变更后任务单状态：1
     */
    private Integer afterStatus;

    /**
     * 变更前骑手
     */
    private String beforeTransfer;

    /**
     * 变更后骑手
     */
    private String afterTransfer;

    /**
     * 重发类型：1重派  2转单
     */
    private Integer repeatType;

    /**
     * 操作时间
     */
    private Date operateTime;

    /**
     * 数据有效性 0：无效 1：有效
     */
    private Integer yn;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 扩展信息
     */
    private String extend;
}
