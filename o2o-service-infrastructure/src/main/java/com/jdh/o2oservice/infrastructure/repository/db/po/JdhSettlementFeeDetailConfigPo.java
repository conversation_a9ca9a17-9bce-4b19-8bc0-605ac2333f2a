package com.jdh.o2oservice.infrastructure.repository.db.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName:JdhSettlementFeeDetailConfigPo
 * @Description:
 * @Author: lwm
 * @Date: 2025/4/18 11:14
 * @Vserion: 1.0
 **/
@Data
@TableName(value = "jdh_settlement_fee_detail_config",autoResultMap = true)
public class JdhSettlementFeeDetailConfigPo {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * '费项配置文件id'
     */
    private Long feeConfigDetailId;
    /**
     * '费项配置文件id'
     */
    private Long feeConfigId;
    /**
     * 费用类型：上门费、即时加价费、夜间加价费、节假日加价费
     */
    private String feeType;
    /**
     * 费项金额 单位元
     */
    private BigDecimal feeAmount;
    /**
     * 扩展信息
     */
    private String extend;
    /**
     * 数据来源分支
     */
    private String branch;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 是否有效 1-有效 0-无效
     */
    private Integer yn;
    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;
}
