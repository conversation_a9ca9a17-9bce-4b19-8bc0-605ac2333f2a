package com.jdh.o2oservice.infrastructure.repository.db;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jdh.o2oservice.base.enums.YnStatusEnum;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromiseHistory;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromiseHistoryIdentifier;
import com.jdh.o2oservice.core.domain.promise.repository.db.PromiseHistoryRepository;
import com.jdh.o2oservice.core.domain.promise.repository.query.PromiseHistoryRepQuery;
import com.jdh.o2oservice.infrastructure.repository.db.convert.PromiseHistoryConverter;
import com.jdh.o2oservice.infrastructure.repository.db.dao.JdhPromiseHistoryPoMapper;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhPromiseHistoryPo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * @author: yangxiyu
 * @date: 2023/12/29 3:33 下午
 * @version: 1.0
 */
@Component
@Slf4j
public class PromiseHistoryRepositoryImpl implements PromiseHistoryRepository {

    @Resource
    private JdhPromiseHistoryPoMapper jdhPromiseHistoryPoMapper;
    /**
     * 当前配置
     */
    private static String ACTIVE;
    /**
     * 初始注入maven环境
     */
    @Value("${spring.profiles.active}")
    public void setActive(String value){
        ACTIVE = value;
    }

    @Override
    public JdhPromiseHistory find(JdhPromiseHistoryIdentifier jdhPromiseHistoryIdentifier) {
        return null;
    }

    @Override
    public int remove(JdhPromiseHistory aggregate) {
        return 0;
    }

    @Override
    public int save(JdhPromiseHistory aggregate) {
        JdhPromiseHistoryPo jdhPromisePo = PromiseHistoryConverter.convertor.promiseHistory2Po(aggregate);
        jdhPromisePo.setCreateTime(new Date());
        jdhPromisePo.setUpdateTime(new Date());
        jdhPromisePo.setYn(YnStatusEnum.YES.getCode());
        jdhPromisePo.setBranch(ACTIVE);
        return jdhPromiseHistoryPoMapper.insert(jdhPromisePo);
    }

    @Override
    public JdhPromiseHistory findLastEvent(Long promiseId, Integer promiseStatus) {
        if (Objects.isNull(promiseStatus)){
            log.warn("PromiseHistoryRepositoryImpl->findLastEvent promiseStatus is null promiseId={}", promiseId);
            return null;
        }
        LambdaQueryWrapper<JdhPromiseHistoryPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(JdhPromiseHistoryPo::getPromiseId, promiseId);
        queryWrapper.eq(JdhPromiseHistoryPo::getAfterStatus, promiseStatus);
        queryWrapper.orderByDesc(JdhPromiseHistoryPo::getVersion);
        queryWrapper.last("limit 1");
        JdhPromiseHistoryPo po = jdhPromiseHistoryPoMapper.selectOne(queryWrapper);
        return PromiseHistoryConverter.convertor.po2PromiseHistory(po);
    }

    /**
     * 查找列表
     *
     * @param promiseHistoryRepQuery promiseHistoryRepQuery
     * @return {@link List}<{@link JdhPromiseHistory}>
     */
    @Override
    public List<JdhPromiseHistory> findList(PromiseHistoryRepQuery promiseHistoryRepQuery) {
        LambdaQueryWrapper<JdhPromiseHistoryPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(Objects.nonNull(promiseHistoryRepQuery.getPromiseId()),JdhPromiseHistoryPo::getPromiseId, promiseHistoryRepQuery.getPromiseId());
        queryWrapper.in(CollectionUtils.isNotEmpty(promiseHistoryRepQuery.getPromiseIdList()),JdhPromiseHistoryPo::getPromiseId, promiseHistoryRepQuery.getPromiseIdList());
        queryWrapper.eq(Objects.nonNull(promiseHistoryRepQuery.getAfterStatus()),JdhPromiseHistoryPo::getAfterStatus, promiseHistoryRepQuery.getAfterStatus());
        queryWrapper.eq(StringUtils.isNotBlank(promiseHistoryRepQuery.getEventCode()), JdhPromiseHistoryPo::getEventCode, promiseHistoryRepQuery.getEventCode());
        if (Boolean.TRUE.equals(promiseHistoryRepQuery.getCreateTimeOrderByDesc())) {
            queryWrapper.orderByDesc(JdhPromiseHistoryPo::getCreateTime);
        }
        List<JdhPromiseHistoryPo> promiseHistoryPos = jdhPromiseHistoryPoMapper.selectList(queryWrapper);
        return PromiseHistoryConverter.convertor.po2PromiseHistoryList(promiseHistoryPos);
    }
}
