package com.jdh.o2oservice.infrastructure.repository.db.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhSettlementAreaFeeConfigPo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 *
 * @ClassName JdhSettlementAreaFeeConfigPoMapper
 * @Description
 * <AUTHOR>
 * @Date 2025/4/18 22:22
 **/
public interface JdhSettlementAreaFeeConfigPoMapper extends BaseMapper<JdhSettlementAreaFeeConfigPo> {

    /**
     * 批量插入
     *
     * @param jdhSettlementAreaFeeConfigPoList
     * @return {@link Integer}
     */
    Integer batchSaveJdhSettlementAreaFeeConfig(@Param("list") List<JdhSettlementAreaFeeConfigPo> jdhSettlementAreaFeeConfigPoList);
}