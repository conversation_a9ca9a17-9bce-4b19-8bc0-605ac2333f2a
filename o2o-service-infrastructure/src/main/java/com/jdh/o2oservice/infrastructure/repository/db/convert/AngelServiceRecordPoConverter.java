package com.jdh.o2oservice.infrastructure.repository.db.convert;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelServiceRecord;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhAngelServiceRecordPo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface AngelServiceRecordPoConverter {

    AngelServiceRecordPoConverter INS = Mappers.getMapper(AngelServiceRecordPoConverter.class);

    AngelServiceRecord convertToAngelServiceRecord(JdhAngelServiceRecordPo po);

    JdhAngelServiceRecordPo convertToJdhAngelServiceRecordPo(AngelServiceRecord entity);


    List<AngelServiceRecord> convertToAngelServiceRecordList(List<JdhAngelServiceRecordPo> poList);


}
