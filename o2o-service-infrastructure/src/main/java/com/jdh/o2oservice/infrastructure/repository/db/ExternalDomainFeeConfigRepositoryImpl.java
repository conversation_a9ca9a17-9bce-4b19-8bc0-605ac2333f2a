package com.jdh.o2oservice.infrastructure.repository.db;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jdh.o2oservice.base.constatnt.NumConstant;
import com.jdh.o2oservice.base.enums.YnStatusEnum;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.core.domain.dispatch.enums.DispatchErrorCode;
import com.jdh.o2oservice.core.domain.settlement.model.ExternalDomainFeeConfig;
import com.jdh.o2oservice.core.domain.settlement.model.ExternalDomainFeeConfigIdentifier;
import com.jdh.o2oservice.core.domain.settlement.model.JdhSettlementFeeDetailConfig;
import com.jdh.o2oservice.core.domain.settlement.repository.db.ExternalDomainFeeConfigRepository;
import com.jdh.o2oservice.core.domain.settlement.repository.query.ExternalDomainFeeConfigQuery;
import com.jdh.o2oservice.core.domain.settlement.repository.query.FeeDetailConfigQuery;
import com.jdh.o2oservice.infrastructure.repository.db.convert.JdhAngelSettlementPoConvert;
import com.jdh.o2oservice.infrastructure.repository.db.convert.JdhBasicPoConverter;
import com.jdh.o2oservice.infrastructure.repository.db.convert.JdhExternalDomainFeePoConverter;
import com.jdh.o2oservice.infrastructure.repository.db.dao.JdhExternalDomainFeePoMapper;
import com.jdh.o2oservice.infrastructure.repository.db.dao.JdhSettlementFeeDetailConfigPoMapper;
import com.jdh.o2oservice.infrastructure.repository.db.po.ExternalDomainFeeConfigPo;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhSettlementFeeDetailConfigPo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName ExternalDomainFeeConfigRepositoryImpl
 * @Description
 * <AUTHOR>
 * @Date 2025/4/25 10:33ExternalDomainFeeConfig
 **/
@Component
@Slf4j
public class ExternalDomainFeeConfigRepositoryImpl implements ExternalDomainFeeConfigRepository {

    /**
     * environment
     */
    @Value("${spring.profiles.active}")
    private String environment;

    /**
     *
     */
    @Resource
    private JdhExternalDomainFeePoMapper externalDomainFeePoMapper;

    /**
     * jdhSettlementFeeDetailConfigPoMapper
     */
    @Resource
    private JdhSettlementFeeDetailConfigPoMapper feeDetailConfigPoMapper;

    /**
     * 查询
     * @param externalDomainFeeConfigIdentifier
     * @return
     */
    @Override
    public ExternalDomainFeeConfig find(ExternalDomainFeeConfigIdentifier externalDomainFeeConfigIdentifier) {
        LambdaQueryWrapper<ExternalDomainFeeConfigPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(ExternalDomainFeeConfigPo::getFeeConfigId,externalDomainFeeConfigIdentifier.getFeeConfigId());
        queryWrapper.eq(ExternalDomainFeeConfigPo::getYn, YnStatusEnum.YES.getCode());
        ExternalDomainFeeConfigPo po = externalDomainFeePoMapper.selectOne(queryWrapper);
        return wrapDetail(po);
    }

    /**
     * 删除
     * @param entity
     * @return
     */
    @Override
    public int remove(ExternalDomainFeeConfig entity) {
        ExternalDomainFeeConfigPo po = JdhExternalDomainFeePoConverter.INSTANCE.entity2Po(entity);
        LambdaUpdateWrapper<ExternalDomainFeeConfigPo> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(ExternalDomainFeeConfigPo::getYn, YnStatusEnum.NO.getCode())
                .setSql("`update_time` = now()")
                .eq(ExternalDomainFeeConfigPo::getFeeConfigId, po.getFeeConfigId())
                .eq(ExternalDomainFeeConfigPo::getVersion, po.getVersion())
                .eq(ExternalDomainFeeConfigPo::getYn, po.getYn());

        return externalDomainFeePoMapper.update(null, updateWrapper);
    }

    /**
     * 保存
     * @param entity
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int save(ExternalDomainFeeConfig entity) {
        ExternalDomainFeeConfigPo feeConfigPo = JdhExternalDomainFeePoConverter.INSTANCE.entity2Po(entity);
        int count = 0;
        //新增
        if (Objects.isNull(feeConfigPo.getId())) {
            JdhBasicPoConverter.initInsertBasicPo(feeConfigPo);
            count = externalDomainFeePoMapper.insert(feeConfigPo);
        } else {
            //修改
            Integer oldVersion = entity.getVersion();
            entity.versionIncrease();
            feeConfigPo.setBranch(environment);
            feeConfigPo.setUpdateTime(new Date());
            feeConfigPo.setVersion(entity.getVersion());

            LambdaUpdateWrapper<ExternalDomainFeeConfigPo> updateWrapper = Wrappers.lambdaUpdate();
            updateWrapper.eq(ExternalDomainFeeConfigPo::getVersion,oldVersion)
                    .eq(ExternalDomainFeeConfigPo::getFeeConfigId,feeConfigPo.getFeeConfigId());

            count = externalDomainFeePoMapper.update(feeConfigPo, updateWrapper);
            if (count < 1) {
                throw new BusinessException(DispatchErrorCode.DISPATCH_CALLBACK_BUSY);
            }
        }
        List<JdhSettlementFeeDetailConfig> detailConfigList = entity.getDetailConfigList();
        if (CollectionUtils.isEmpty(detailConfigList)) {
            return count;
        }

        for (JdhSettlementFeeDetailConfig feeDetailConfig : detailConfigList) {
            JdhSettlementFeeDetailConfigPo feeDetailConfigPo = JdhAngelSettlementPoConvert.INSTANCE.convertToFeeDetailConfigPo(feeDetailConfig);
            feeDetailConfigPo.setFeeConfigId(Objects.nonNull(feeDetailConfigPo.getFeeConfigId()) ? feeDetailConfigPo.getFeeConfigId() : feeConfigPo.getFeeConfigId());
            //新增
            if (Objects.isNull(feeDetailConfigPo.getId())) {
                Date cur = new Date();
                feeDetailConfigPo.setCreateTime(cur);
                feeDetailConfigPo.setUpdateTime(cur);
                feeDetailConfigPo.setYn(YnStatusEnum.YES.getCode());
                feeDetailConfigPo.setBranch(environment);
                feeDetailConfigPo.setVersion(NumConstant.NUM_1);
                feeDetailConfigPoMapper.insert(feeDetailConfigPo);
            }
            //修改
            else {
                LambdaUpdateWrapper<JdhSettlementFeeDetailConfigPo> detailWrapper = Wrappers.lambdaUpdate();
                Integer serviceVersion = feeDetailConfig.getVersion();

                detailWrapper.set(JdhSettlementFeeDetailConfigPo::getVersion, serviceVersion + 1)
                        .set(Objects.isNull(feeDetailConfigPo.getFeeAmount()), JdhSettlementFeeDetailConfigPo::getFeeAmount, null)
                        .eq(JdhSettlementFeeDetailConfigPo::getVersion, serviceVersion)
                        .eq(JdhSettlementFeeDetailConfigPo::getFeeConfigId,feeDetailConfig.getFeeConfigId())
                        .eq(JdhSettlementFeeDetailConfigPo::getFeeConfigDetailId, feeDetailConfig.getFeeConfigDetailId());
                feeDetailConfigPo.setUpdateTime(new Date());
                int update = feeDetailConfigPoMapper.update(feeDetailConfigPo, detailWrapper);
                if (update < 1) {
                    throw new BusinessException(DispatchErrorCode.DISPATCH_CALLBACK_BUSY);
                }
            }
        }

        return count;
    }

    /**
     * 批量保存
     * @param externalDomainFeeConfigs
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchSave(List<ExternalDomainFeeConfig> externalDomainFeeConfigs) {
        if (CollectionUtils.isEmpty(externalDomainFeeConfigs)) {
            return 0;
        }
        return externalDomainFeeConfigs.stream().mapToInt(this::save).sum();
    }

    /**
     * 查询列表
     * @param query
     * @return
     */
    @Override
    public List<ExternalDomainFeeConfig> findList(ExternalDomainFeeConfigQuery query) {
        LambdaQueryWrapper<ExternalDomainFeeConfigPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(Objects.nonNull(query.getFeeConfigId()), ExternalDomainFeeConfigPo::getFeeConfigId, query.getFeeConfigId())
                .eq(StringUtils.isNotBlank(query.getDomainCode()), ExternalDomainFeeConfigPo::getDomainCode, query.getDomainCode())
                .eq(StringUtils.isNotBlank(query.getAggregateCode()), ExternalDomainFeeConfigPo::getAggregateCode, query.getAggregateCode())
                .eq(Objects.nonNull(query.getAggregateId()), ExternalDomainFeeConfigPo::getAggregateId, query.getAggregateId())
                .in(CollectionUtils.isNotEmpty(query.getAggregateIdList()), ExternalDomainFeeConfigPo::getAggregateId, query.getAggregateIdList())
                .eq(StringUtils.isNotBlank(query.getSettlementSubjectType()), ExternalDomainFeeConfigPo::getSettlementSubjectType, query.getSettlementSubjectType())
                .eq(StringUtils.isNotBlank(query.getSettlementSubjectSubType()), ExternalDomainFeeConfigPo::getSettlementSubjectSubType, query.getSettlementSubjectSubType())
                .eq(ExternalDomainFeeConfigPo::getYn, YnStatusEnum.YES.getCode());
        List<ExternalDomainFeeConfigPo> pos = externalDomainFeePoMapper.selectList(queryWrapper);
        return wrapDetail(pos);
    }

    /**
     * 查询费项明细列表
     * @param query
     * @return
     */
    public List<JdhSettlementFeeDetailConfig> findFeeDetailConfigList(FeeDetailConfigQuery query) {
        LambdaQueryWrapper<JdhSettlementFeeDetailConfigPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(Objects.nonNull(query.getFeeConfigId()),JdhSettlementFeeDetailConfigPo::getFeeConfigId, query.getFeeConfigId())
                .in(CollectionUtils.isNotEmpty(query.getFeeConfigIdList()),JdhSettlementFeeDetailConfigPo::getFeeConfigId, query.getFeeConfigIdList())
                .eq(Objects.nonNull(query.getFeeConfigDetailId()),JdhSettlementFeeDetailConfigPo::getFeeConfigDetailId,query.getFeeConfigDetailId())
                .eq(Objects.nonNull(query.getFeeType()),JdhSettlementFeeDetailConfigPo::getFeeType,query.getFeeType())
                .eq(JdhSettlementFeeDetailConfigPo::getYn, YnStatusEnum.YES.getCode());
        List<JdhSettlementFeeDetailConfigPo> list = feeDetailConfigPoMapper.selectList(queryWrapper);
        return wrapFeeDetailConfig(list);
    }

    /**
     *
     * @param list
     * @return
     */
    private List<JdhSettlementFeeDetailConfig> wrapFeeDetailConfig(List<JdhSettlementFeeDetailConfigPo> list) {
        return JdhExternalDomainFeePoConverter.INSTANCE.po2DetailEntity(list);
    }

    /**
     *
     * @param po
     * @return
     */
    private ExternalDomainFeeConfig wrapDetail(ExternalDomainFeeConfigPo po) {
        if (Objects.isNull(po)){
            return null;
        }
        ExternalDomainFeeConfig externalDomainFeeConfig = JdhExternalDomainFeePoConverter.INSTANCE.po2Entity(po);
        List<JdhSettlementFeeDetailConfig> feeDetailConfigList = findFeeDetailConfigList(FeeDetailConfigQuery.builder().feeConfigId(externalDomainFeeConfig.getFeeConfigId()).build());
        externalDomainFeeConfig.setDetailConfigList(feeDetailConfigList);
        return externalDomainFeeConfig;
    }

    /**
     *
     * @param poList
     * @return
     */
    private List<ExternalDomainFeeConfig> wrapDetail(List<ExternalDomainFeeConfigPo> poList) {
        if (CollectionUtils.isEmpty(poList)) {
            return new ArrayList<>();
        }
        List<Long> feeConfigIdList = poList.stream().map(ExternalDomainFeeConfigPo::getFeeConfigId).collect(Collectors.toList());
        List<JdhSettlementFeeDetailConfig> feeDetailConfigList = findFeeDetailConfigList(FeeDetailConfigQuery.builder().feeConfigIdList(feeConfigIdList).build());
        Map<Long, List<JdhSettlementFeeDetailConfig>> feeConfigId2Map = feeDetailConfigList.stream().collect(Collectors.groupingBy(JdhSettlementFeeDetailConfig::getFeeConfigId));

        return poList.stream().map(po -> {
            ExternalDomainFeeConfig externalDomainFeeConfig = JdhExternalDomainFeePoConverter.INSTANCE.po2Entity(po);
            externalDomainFeeConfig.setDetailConfigList(feeConfigId2Map.getOrDefault(po.getFeeConfigId(), new ArrayList<>()));
            return externalDomainFeeConfig;
        }).collect(Collectors.toList());
    }
}