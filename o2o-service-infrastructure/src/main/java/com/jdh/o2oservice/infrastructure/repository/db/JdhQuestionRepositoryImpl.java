package com.jdh.o2oservice.infrastructure.repository.db;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.base.Joiner;
import com.jd.common.util.StringUtils;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.enums.YnStatusEnum;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.errorcode.DynamicErrorCode;
import com.jdh.o2oservice.core.domain.product.model.JdhQuestion;
import com.jdh.o2oservice.core.domain.product.model.JdhQuestionIdentifier;
import com.jdh.o2oservice.core.domain.product.repository.db.JdhQuestionRepository;
import com.jdh.o2oservice.core.domain.product.repository.db.JdhServiceItemRepository;
import com.jdh.o2oservice.core.domain.product.repository.query.QuestionDbQuery;
import com.jdh.o2oservice.infrastructure.repository.db.convert.JdhBasicPoConverter;
import com.jdh.o2oservice.infrastructure.repository.db.convert.JdhQuestionPoConverter;
import com.jdh.o2oservice.infrastructure.repository.db.dao.JdhGroupQuesRelMapper;
import com.jdh.o2oservice.infrastructure.repository.db.dao.JdhQuestionMapper;
import com.jdh.o2oservice.infrastructure.repository.db.dao.JdhServiceItemPoMapper;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhGroupQuesRelPo;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhQuestionPo;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhServiceItemPo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/5/25
 * @description 题库
 */
@Repository
@Slf4j
public class JdhQuestionRepositoryImpl implements JdhQuestionRepository {

    @Autowired
    private JdhQuestionMapper jdhQuestionMapper;

    @Autowired
    private JdhGroupQuesRelMapper jdhGroupQuesRelMapper;

    @Autowired
    private JdhServiceItemPoMapper jdhServiceItemPoMapper;


    @Override
    public JdhQuestion find(JdhQuestionIdentifier jdhQuestionIdentifier) {
        LambdaQueryWrapper<JdhQuestionPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(JdhQuestionPo::getQuesId,jdhQuestionIdentifier.getQuesId());
        queryWrapper.eq(JdhQuestionPo::getYn,YnStatusEnum.YES.getCode());
        return JdhQuestionPoConverter.INSTANCE.toJdhQuestion(jdhQuestionMapper.selectOne(queryWrapper));
    }

    @Override
    public int remove(JdhQuestion entity) {

        LambdaQueryWrapper<JdhGroupQuesRelPo> jdhGroupQuesRelWrapper = Wrappers.lambdaQuery();
        jdhGroupQuesRelWrapper.like(JdhGroupQuesRelPo::getExtJson,entity.getQuesId());
        jdhGroupQuesRelWrapper.eq(JdhGroupQuesRelPo::getYn,YnStatusEnum.YES.getCode());
        List<JdhGroupQuesRelPo> jdhGroupQuesRelPos = jdhGroupQuesRelMapper.selectList(jdhGroupQuesRelWrapper);
        if(CollectionUtils.isNotEmpty(jdhGroupQuesRelPos)){
            throw new BusinessException(new DynamicErrorCode("-1","题目已被引用，不支持删除"));
        }

        //where条件
        LambdaUpdateWrapper<JdhQuestionPo> queryWrapper = new LambdaUpdateWrapper<>();
        queryWrapper.eq(JdhQuestionPo::getQuesId,entity.getQuesId());

        //修改数据
        JdhQuestionPo jdhQuestionPo = new JdhQuestionPo();
        jdhQuestionPo.setYn(YnStatusEnum.NO.getCode());
        jdhQuestionPo.setUpdateTime(new Date());
        jdhQuestionPo.setUpdateUser(entity.getUpdateUser());



        return jdhQuestionMapper.update(jdhQuestionPo,queryWrapper);
    }

    @Override
    public int save(JdhQuestion entity) {
        JdhQuestionPo jdhQuestionPo = JdhQuestionPoConverter.INSTANCE.toJdhQuestionPo(entity);
        return jdhQuestionMapper.insert(jdhQuestionPo);
    }

    @Override
    public int update(JdhQuestion entity) {
        //where条件
        LambdaUpdateWrapper<JdhQuestionPo> queryWrapper = new LambdaUpdateWrapper<>();
        queryWrapper.eq(JdhQuestionPo::getQuesId,entity.getQuesId());

        //修改数据
        JdhQuestionPo jdhQuestionPo = JdhQuestionPoConverter.INSTANCE.toJdhQuestionPo(entity);
        jdhQuestionPo.setUpdateTime(new Date());
        queryWrapper.setSql("`version` = `version` + 1");

        return jdhQuestionMapper.update(jdhQuestionPo,queryWrapper);
    }

    @Override
    public List<JdhQuestion> findList(QuestionDbQuery questionDbQuery) {
        LambdaQueryWrapper<JdhQuestionPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(questionDbQuery.getQuesId()!=null,JdhQuestionPo::getQuesId,questionDbQuery.getQuesId());
        queryWrapper.in(questionDbQuery.getQuesIds()!=null,JdhQuestionPo::getQuesId,questionDbQuery.getQuesIds());
        queryWrapper.like(StringUtils.isNotEmpty(questionDbQuery.getName()),JdhQuestionPo::getName,questionDbQuery.getName());
        queryWrapper.eq(questionDbQuery.getType()!=null,JdhQuestionPo::getType,questionDbQuery.getType());
        queryWrapper.eq(JdhQuestionPo::getYn,YnStatusEnum.YES.getCode());
        List<JdhQuestionPo> jdhQuestionPos = jdhQuestionMapper.selectList(queryWrapper);
        return JdhQuestionPoConverter.INSTANCE.toJdhQuestions(jdhQuestionPos);
    }

    @Override
    @LogAndAlarm
    public Page<JdhQuestion> findPageList(QuestionDbQuery questionDbQuery) {

        LambdaQueryWrapper<JdhQuestionPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(questionDbQuery.getQuesId()!=null,JdhQuestionPo::getQuesId,questionDbQuery.getQuesId());
        queryWrapper.like(StringUtils.isNotEmpty(questionDbQuery.getName()),JdhQuestionPo::getName,questionDbQuery.getName());
        queryWrapper.eq(questionDbQuery.getType()!=null,JdhQuestionPo::getType,questionDbQuery.getType());
        queryWrapper.eq(JdhQuestionPo::getYn,YnStatusEnum.YES.getCode());
        queryWrapper.eq(questionDbQuery.getSource()!=null,JdhQuestionPo::getSource,questionDbQuery.getSource());
        queryWrapper.orderByDesc(JdhQuestionPo::getCreateTime);

        if(questionDbQuery.getServiceItemId()!=null){
            LambdaQueryWrapper<JdhGroupQuesRelPo> jdhGroupQuesRelWrapper = Wrappers.lambdaQuery();
            jdhGroupQuesRelWrapper.eq(JdhGroupQuesRelPo::getServiceItemId,questionDbQuery.getServiceItemId());
            jdhGroupQuesRelWrapper.eq(JdhGroupQuesRelPo::getQuesCode,"customEvaluate");
            jdhGroupQuesRelWrapper.eq(JdhGroupQuesRelPo::getYn,YnStatusEnum.YES.getCode());
            List<JdhGroupQuesRelPo> jdhGroupQuesRelPos = jdhGroupQuesRelMapper.selectList(jdhGroupQuesRelWrapper);
            if(CollectionUtils.isNotEmpty(jdhGroupQuesRelPos)){
                queryWrapper.in(JdhQuestionPo::getQuesId,jdhGroupQuesRelPos.stream().map(t-> JSON.parseObject(t.getExtJson())).flatMap(t->t.getJSONArray("value").stream()).collect(Collectors.toList()));
            }else{
                queryWrapper.in(JdhQuestionPo::getQuesId, Collections.singletonList(-1L));
            }
        }
        Page<JdhQuestionPo> param = new Page<>(questionDbQuery.getPageNum(), questionDbQuery.getPageSize());
        Page<JdhQuestionPo> jdhQuestionPos = jdhQuestionMapper.selectPage(param,queryWrapper);

        List<JdhQuestion> jdhQuestions = JdhQuestionPoConverter.INSTANCE.toJdhQuestions(jdhQuestionPos.getRecords());
        if(CollectionUtils.isNotEmpty(jdhQuestions)){
            this.enhanceJdhQuestion(jdhQuestions);
        }
        return JdhBasicPoConverter.initPage(jdhQuestionPos, jdhQuestions);
    }

    public static void main(String[] args) {

        List<JdhGroupQuesRelPo> jdhGroupQuesRelPos = new ArrayList<>();

        JdhGroupQuesRelPo jdhGroupQuesRelPo = new JdhGroupQuesRelPo();
        jdhGroupQuesRelPo.setExtJson("{\"value\":[172481442218001]}");
        jdhGroupQuesRelPos.add(jdhGroupQuesRelPo);

        List<Object> dd = jdhGroupQuesRelPos.stream().map(t-> JSON.parseObject(t.getExtJson())).flatMap(t->t.getJSONArray("value").stream()).collect(Collectors.toList());
        System.out.println(JSON.toJSONString(dd));
    }

    @Override
    public JdhQuestion findByCode(String quesCode) {
        LambdaQueryWrapper<JdhQuestionPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(JdhQuestionPo::getQuesCode,quesCode);
        queryWrapper.eq(JdhQuestionPo::getYn,YnStatusEnum.YES.getCode());

        JdhQuestionPo jdhQuestionPo = jdhQuestionMapper.selectOne(queryWrapper);
        return JdhQuestionPoConverter.INSTANCE.toJdhQuestion(jdhQuestionPo);
    }

    /**
     * 维护服务项目名称
     * @param jdhQuestions
     */
    private void enhanceJdhQuestion(List<JdhQuestion> jdhQuestions){
        jdhQuestions.parallelStream().forEach(q->{
            LambdaQueryWrapper<JdhGroupQuesRelPo> jdhGroupQuesRelWrapper = Wrappers.lambdaQuery();
            jdhGroupQuesRelWrapper.like(JdhGroupQuesRelPo::getExtJson,q.getQuesId());
            jdhGroupQuesRelWrapper.eq(JdhGroupQuesRelPo::getYn,YnStatusEnum.YES.getCode());
            List<JdhGroupQuesRelPo> jdhGroupQuesRelPos = jdhGroupQuesRelMapper.selectList(jdhGroupQuesRelWrapper);
            if(CollectionUtils.isNotEmpty(jdhGroupQuesRelPos)){
                //查询标品库数据
                Set<Long> serviceItemIds = jdhGroupQuesRelPos.stream().map(JdhGroupQuesRelPo::getServiceItemId).collect(Collectors.toSet());
                LambdaQueryWrapper<JdhServiceItemPo> serviceItemWrapper = Wrappers.lambdaQuery();
                serviceItemWrapper.in(JdhServiceItemPo::getItemId,serviceItemIds);
                List<JdhServiceItemPo> jdhServiceItemPos = jdhServiceItemPoMapper.selectList(serviceItemWrapper);
                if(CollectionUtils.isNotEmpty(jdhServiceItemPos)){
                    List<String> itemNames = jdhServiceItemPos.stream().map(JdhServiceItemPo::getItemName).collect(Collectors.toList());
                    q.setServiceItemName(Joiner.on(",").join(itemNames));
                }
            }
        });
    }
}
