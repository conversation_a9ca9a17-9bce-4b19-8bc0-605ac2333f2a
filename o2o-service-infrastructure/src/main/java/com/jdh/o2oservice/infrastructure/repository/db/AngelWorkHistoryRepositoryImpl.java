package com.jdh.o2oservice.infrastructure.repository.db;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jdh.o2oservice.base.enums.YnStatusEnum;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelWorkHistory;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelWorkHistoryIdentifier;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelWorkHistoryRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.query.AngelWorkHistoryDbQuery;
import com.jdh.o2oservice.infrastructure.repository.db.convert.JdhAngelWorkPoConverter;
import com.jdh.o2oservice.infrastructure.repository.db.dao.JdhAngelWorkHistoryPoMapper;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhAngelWorkHistoryPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * @ClassName:AngelWorkHistoryRepositoryImpl
 * @Description:
 * @Author: yaoqinghai
 * @Date: 2024/4/24 01:22
 * @Vserion: 1.0
 **/
@Component
@Slf4j
public class AngelWorkHistoryRepositoryImpl implements AngelWorkHistoryRepository {

    @Resource
    private JdhAngelWorkHistoryPoMapper jdhAngelWorkHistoryPoMapper;

    /**
     * 通过ID寻找Aggregate。
     * 找到的Aggregate自动是可追踪的
     *
     * @param angelWorkHistoryIdentifier
     */
    @Override
    public AngelWorkHistory find(AngelWorkHistoryIdentifier angelWorkHistoryIdentifier) {
        return null;
    }

    /**
     * 将一个Aggregate从Repository移除
     * 操作后的aggregate对象自动取消追踪
     *
     * @param aggregate
     */
    @Override
    public int remove(AngelWorkHistory aggregate) {
        return 0;
    }

    /**
     * 保存一个Aggregate
     * 保存后自动重置追踪条件
     *
     * @param workHistory
     */
    @Override
    public int save(AngelWorkHistory workHistory) {
        JdhAngelWorkHistoryPo jdhAngelWorkHistoryPo = JdhAngelWorkPoConverter.INSTANCE.convertToJdhAngelWorkHistoryPo(workHistory);
        jdhAngelWorkHistoryPo.setYn(YnStatusEnum.YES.getCode());
        return jdhAngelWorkHistoryPoMapper.insert(jdhAngelWorkHistoryPo);
    }

    /**
     * 查询服务者工单历史列表
     *
     * @param workHistoryDbQuery
     * @return
     */
    @Override
    public List<AngelWorkHistory> findList(AngelWorkHistoryDbQuery workHistoryDbQuery) {
        LambdaQueryWrapper<JdhAngelWorkHistoryPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(Objects.nonNull(workHistoryDbQuery.getWorkId()),JdhAngelWorkHistoryPo::getWorkId, workHistoryDbQuery.getWorkId())
                .in(CollUtil.isNotEmpty(workHistoryDbQuery.getWorkIdList()),JdhAngelWorkHistoryPo::getWorkId, workHistoryDbQuery.getWorkIdList())
                .eq(Objects.nonNull(workHistoryDbQuery.getAfterStatus()),JdhAngelWorkHistoryPo::getAfterStatus, workHistoryDbQuery.getAfterStatus())
                .eq(JdhAngelWorkHistoryPo::getYn, YnStatusEnum.YES.getCode());
        queryWrapper.orderByAsc(JdhAngelWorkHistoryPo::getCreateTime);
        return JdhAngelWorkPoConverter.INSTANCE.convertToAngelWorkHistoryList(jdhAngelWorkHistoryPoMapper.selectList(queryWrapper));
    }
}
