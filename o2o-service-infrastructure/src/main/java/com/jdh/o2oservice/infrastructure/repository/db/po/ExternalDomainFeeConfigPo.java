package com.jdh.o2oservice.infrastructure.repository.db.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * @ClassName ExternalDomainFeeConfigPo
 * @Description
 * <AUTHOR>
 * @Date 2025/4/25 10:41
 **/
@Data
@TableName(value = "jdh_settlement_external_domain_fee_config",autoResultMap = true)
public class ExternalDomainFeeConfigPo extends JdhBasicPo{

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * '费项配置文件id'
     */
    private Long feeConfigId;

    /**
     * 领域编码
     */
    private String domainCode;

    /**
     * 聚合编码
     */
    private String aggregateCode;

    /**
     * 聚合ID
     */
    private String aggregateId;

    /**
     * 结算主体类型：护士、康复师等
     */
    private String settlementSubjectType;

    /**
     * 结算主体子类型：全职、兼职等
     */
    private String settlementSubjectSubType;
}