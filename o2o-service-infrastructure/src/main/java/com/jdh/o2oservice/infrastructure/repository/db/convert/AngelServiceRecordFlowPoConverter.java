package com.jdh.o2oservice.infrastructure.repository.db.convert;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelServiceRecordFlow;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhAngelServiceRecordFlowPo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import java.util.List;

@Mapper
public interface AngelServiceRecordFlowPoConverter {

    AngelServiceRecordFlowPoConverter INS = Mappers.getMapper(AngelServiceRecordFlowPoConverter.class);

    AngelServiceRecordFlow convertToAngelServiceRecordFlow(JdhAngelServiceRecordFlowPo po);

    JdhAngelServiceRecordFlowPo convertToAngelServiceRecordFlowPo(AngelServiceRecordFlow entity);


    List<AngelServiceRecordFlow> convertToAngelServiceRecordFlowList(List<JdhAngelServiceRecordFlowPo> poList);


}
