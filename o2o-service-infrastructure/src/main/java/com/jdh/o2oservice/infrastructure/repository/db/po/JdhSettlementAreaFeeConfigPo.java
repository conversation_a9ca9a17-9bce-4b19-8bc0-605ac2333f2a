package com.jdh.o2oservice.infrastructure.repository.db.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * @ClassName:JdhSettlementAreaFeeConfigPo
 * @Description:
 * @Author: lwm
 * @Date: 2025/4/18 11:14
 * @Vserion: 1.0
 **/
@Data
@TableName(value = "jdh_settlement_area_fee_config",autoResultMap = true)
public class JdhSettlementAreaFeeConfigPo {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * '费项配置文件id'
     */
    private Long feeConfigId;

    /**
     * <pre>
     * 结算主体类型：护士、康复师
     * </pre>
     */
    private String settlementSubjectType;

    /**
     * 结算主体子类型：全职、兼职
     */
    private String settlementSubjectSubType;

    /**
     * 省编码
     */
    private String provinceCode;
    /**
     * 市编码
     */
    private String cityCode;

    /**
     * 县地区code
     */
    private String countyCode;
    /**
     * 乡镇code
     */
    private String townCode;

    /**
     * 省名称
     */
    private String provinceName;

    /**
     * 市名称
     */
    private String cityName;
    /**
     * 区名称
     */
    private String countyName;
    /**
     * 乡镇
     */
    private String townName;
    /**
     * 目标地址code
     */
    private String destCode;

    /**
     * 数据来源分支
     */
    private String branch;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 是否有效 1-有效 0-无效
     */
    private Integer yn;
    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;
    /**
     * 创建人
     */
    private String createUser;
    /**
     * 修改人
     */
    private String updateUser;
}
