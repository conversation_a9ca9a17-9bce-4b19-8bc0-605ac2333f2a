package com.jdh.o2oservice.infrastructure.repository.db.convert;

import com.jdh.o2oservice.core.domain.angel.model.JdhAngel;
import com.jdh.o2oservice.core.domain.angel.model.JdhAngelExtend;
import com.jdh.o2oservice.core.domain.settlement.model.*;
import com.jdh.o2oservice.infrastructure.repository.db.po.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @ClassName:JdhAngelSettlementPoConvert
 * @Description:
 * @Author: lwm
 * @Date: 2025/4/18 11:14
 * @Vserion: 1.0
 **/
@Mapper
public interface JdhAngelSettlementPoConvert {


    JdhAngelSettlementPoConvert INSTANCE = Mappers.getMapper(JdhAngelSettlementPoConvert.class);


    JdhSettlementCityLevelConfig convertToJdhSettlementCityLevelConfig(JdhSettlementCityLevelConfigPo jdhAngelPo);

    /**
     * 对象转换
     *
     * @param jdhAngelPoList
     * @return model
     */
    List<JdhSettlementCityLevelConfig> convertToJdhSettlementCityLevelConfig(List<JdhSettlementCityLevelConfigPo> jdhAngelPoList);


    JdhAngelPo convertToJdhAngelPo(JdhAngel jdhAngel);


    /**
     * extendPo2Entity
     *
     * @param extendPo extendPo
     * @return {@link JdhAngelExtend}
     */
    JdhAngelExtend extendPo2Entity(JdhAngelExtendPo extendPo);

    /**
     *
     * @param jdhSettlementCityLevelConfig
     * @return
     */
    JdhSettlementCityLevelConfigPo convertToJdhSettlementCityLevelConfigPo(JdhSettlementCityLevelConfig jdhSettlementCityLevelConfig);

    /**
     *
     * @param jdhSettlementAreaFeeConfig
     * @return
     */
    JdhSettlementAreaFeeConfigPo convertToJdhSettlementAreaFeeConfigPo(JdhSettlementAreaFeeConfig jdhSettlementAreaFeeConfig);

    /**
     *
     * @param jdhSettlementCityLevelConfigList
     * @return
     */
    List<JdhSettlementCityLevelConfigPo> convertToJdhSettlementCityLevelConfigPoList(List<JdhSettlementCityLevelConfig> jdhSettlementCityLevelConfigList);

    /**
     *
     * @param request
     * @return
     */
    JdhSettlementCityLevelConfig convertToCityLevelConfig(ImportAngelSettleCityConfig request);

    /**
     *
     * @param request
     * @return
     */
    @Mapping(source = "angelType",target = "settlementSubjectSubType")
    JdhSettlementAreaFeeConfig convertToJdhAngelSettlementConfig(ImportAngelSettleConfig request);
    /**
     *
     * @param request
     * @return
     */
    JdhSettlementAreaFeeConfig convertToJdhAngelSettlementConfig(JdhSettlementAreaFeeConfigPo request);
    /**
     * 对象转换
     *
     * @param jdhAngelPoList
     * @return model
     */
    List<JdhSettlementAreaFeeConfig> convertToJdhSettlementAreaFeeConfigList(List<JdhSettlementAreaFeeConfigPo> jdhAngelPoList);
    /**
     * 对象转换
     *
     * @param jdhAngelPoList
     * @return model
     */
    List<JdhSettlementAreaFeeConfigPo> convertToJdhSettlementAreaFeeConfigPoList(List<JdhSettlementAreaFeeConfig> jdhAngelPoList);
    /**
     * 对象转换
     *
     * @param jdhAngelPoList
     * @return model
     */
    List<JdhSettlementFeeDetailConfig> convertToJdhSettlementFeeDetailConfigList(List<JdhSettlementFeeDetailConfigPo> jdhAngelPoList);

    JdhSettlementFeeDetailConfigPo convertToFeeDetailConfigPo(JdhSettlementFeeDetailConfig feeDetailConfig);


    List<JdhSettlementFeeDetailConfigPo> convertToFeeDetailConfigPos(List<JdhSettlementFeeDetailConfig> feeDetailConfigList);
}
