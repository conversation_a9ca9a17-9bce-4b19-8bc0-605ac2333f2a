package com.jdh.o2oservice.infrastructure.repository.db;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.jdh.o2oservice.base.enums.ThreadPoolConfigEnum;
import com.jdh.o2oservice.base.enums.YnStatusEnum;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.factory.ExecutorPoolFactory;
import com.jdh.o2oservice.core.domain.dispatch.enums.DispatchErrorCode;
import com.jdh.o2oservice.core.domain.dispatch.model.DispatchDetailGroupCount;
import com.jdh.o2oservice.core.domain.dispatch.model.JdhDispatch;
import com.jdh.o2oservice.core.domain.dispatch.model.JdhDispatchDetail;
import com.jdh.o2oservice.core.domain.dispatch.model.JdhDispatchIdentifier;
import com.jdh.o2oservice.core.domain.dispatch.repository.db.DispatchRepository;
import com.jdh.o2oservice.core.domain.dispatch.repository.query.*;
import com.jdh.o2oservice.core.domain.support.basic.enums.JdhDispatchDetailStatusEnum;
import com.jdh.o2oservice.core.domain.support.basic.enums.JdhDispatchStatusEnum;
import com.jdh.o2oservice.infrastructure.repository.db.convert.JdhBasicPoConverter;
import com.jdh.o2oservice.infrastructure.repository.db.convert.JdhDispatchInfrastructureConverter;
import com.jdh.o2oservice.infrastructure.repository.db.dao.JdhDispatchDetailPoMapper;
import com.jdh.o2oservice.infrastructure.repository.db.dao.JdhDispatchPoMapper;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhDispatchDetailPo;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhDispatchPo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

/**
 * @ClassName DispatchRepositoryImpl
 * @Description
 * <AUTHOR>
 * @Date 2024/4/21 11:51
 **/
@Component
@Slf4j
public class DispatchRepositoryImpl implements DispatchRepository {

    /**
     * environment
     */
    @Value("${spring.profiles.active}")
    private String environment;

    /**
     * jdhDispatchPoMapper
     */
    @Resource
    private JdhDispatchPoMapper jdhDispatchPoMapper;

    /**
     * jdhDispatchDetailPoMapper
     */
    @Resource
    private JdhDispatchDetailPoMapper jdhDispatchDetailPoMapper;
    @Resource
    private ExecutorPoolFactory executorPoolFactory;

    /**
     * findDispatch
     *
     * @param query 查询
     * @return {@link JdhDispatch}
     */
    @Override
    public JdhDispatch findDispatch(DispatchRepQuery query) {
        LambdaQueryWrapper<JdhDispatchPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(Objects.nonNull(query.getDispatchId()),JdhDispatchPo::getDispatchId, query.getDispatchId())
                .eq(Objects.nonNull(query.getPromiseId()),JdhDispatchPo::getPromiseId,query.getPromiseId())
                .eq(Objects.nonNull(query.getOutDispatchId()),JdhDispatchPo::getOutDispatchId,String.valueOf(query.getOutDispatchId()))
                .in(CollectionUtils.isNotEmpty(query.getDispatchStatusList()), JdhDispatchPo::getDispatchStatus, query.getDispatchStatusList())
                .notIn(CollectionUtils.isNotEmpty(query.getDispatchStatusNotInList()), JdhDispatchPo::getDispatchStatus, query.getDispatchStatusNotInList())
                .eq(JdhDispatchPo::getYn, YnStatusEnum.YES.getCode());

        JdhDispatchPo po = jdhDispatchPoMapper.selectOne(queryWrapper);
        return JdhDispatchInfrastructureConverter.INSTANCE.convert2JdhDispatch(po);
    }

    /**
     * findDispatch
     *
     * @param query 查询
     * @return {@link JdhDispatch}
     */
    @Override
    public JdhDispatch findValidDispatch(DispatchRepQuery query) {
        LambdaQueryWrapper<JdhDispatchPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(Objects.nonNull(query.getDispatchId()),JdhDispatchPo::getDispatchId, query.getDispatchId())
                .eq(Objects.nonNull(query.getPromiseId()),JdhDispatchPo::getPromiseId,query.getPromiseId())
                .eq(Objects.nonNull(query.getOutDispatchId()),JdhDispatchPo::getOutDispatchId,String.valueOf(query.getOutDispatchId()))
                .in(CollectionUtils.isNotEmpty(query.getDispatchStatusList()), JdhDispatchPo::getDispatchStatus, query.getDispatchStatusList())
                .notIn(CollectionUtils.isNotEmpty(query.getDispatchStatusNotInList()), JdhDispatchPo::getDispatchStatus, query.getDispatchStatusNotInList())
                .ne(JdhDispatchPo::getDispatchStatus, JdhDispatchStatusEnum.DISPATCH_INVALID.getStatus())
                .eq(JdhDispatchPo::getYn, YnStatusEnum.YES.getCode());

        JdhDispatchPo po = jdhDispatchPoMapper.selectOne(queryWrapper);
        return JdhDispatchInfrastructureConverter.INSTANCE.convert2JdhDispatch(po);
    }

    /**
     * 查询派单任务列表
     * @param query
     * @return
     */
    @Override
    public List<JdhDispatch> findDispatchList(DispatchRepQuery query) {
        LambdaQueryWrapper<JdhDispatchPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(Objects.nonNull(query.getDispatchId()),JdhDispatchPo::getDispatchId, query.getDispatchId())
                .eq(Objects.nonNull(query.getPromiseId()),JdhDispatchPo::getPromiseId,query.getPromiseId())
                .eq(Objects.nonNull(query.getOutDispatchId()),JdhDispatchPo::getOutDispatchId,String.valueOf(query.getOutDispatchId()))
                .in(CollectionUtils.isNotEmpty(query.getDispatchIdList()), JdhDispatchPo::getDispatchId, query.getDispatchIdList())
                .in(CollectionUtils.isNotEmpty(query.getPromiseIdList()), JdhDispatchPo::getPromiseId, query.getPromiseIdList())
                .eq(JdhDispatchPo::getYn, YnStatusEnum.YES.getCode());

        List<JdhDispatchPo> dispatchPoList = jdhDispatchPoMapper.selectList(queryWrapper);
        return wrap(dispatchPoList);
    }

    /**
     * findDispatchWithDetail
     * @param query 查询
     * @return
     */
    @Override
    public JdhDispatch findDispatchWithDetail(DispatchRepQuery query) {
        LambdaQueryWrapper<JdhDispatchPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(Objects.nonNull(query.getDispatchId()),JdhDispatchPo::getDispatchId, query.getDispatchId())
                .eq(Objects.nonNull(query.getPromiseId()),JdhDispatchPo::getPromiseId,query.getPromiseId())
                .eq(Objects.nonNull(query.getOutDispatchId()),JdhDispatchPo::getOutDispatchId,String.valueOf(query.getOutDispatchId()))
                .in(CollectionUtils.isNotEmpty(query.getDispatchStatusList()), JdhDispatchPo::getDispatchStatus, query.getDispatchStatusList())
                .notIn(CollectionUtils.isNotEmpty(query.getDispatchStatusNotInList()), JdhDispatchPo::getDispatchStatus, query.getDispatchStatusNotInList())
                .eq(JdhDispatchPo::getYn, YnStatusEnum.YES.getCode());

        JdhDispatchPo po = jdhDispatchPoMapper.selectOne(queryWrapper);
        return wrapDetail(po);
    }

    /**
     * findDispatch-有效的
     *
     * @param query 查询
     * @return {@link JdhDispatch}
     */
    @Override
    public JdhDispatch findValidDispatchWithDetail(DispatchRepQuery query) {
        LambdaQueryWrapper<JdhDispatchPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(Objects.nonNull(query.getDispatchId()),JdhDispatchPo::getDispatchId, query.getDispatchId())
                .eq(Objects.nonNull(query.getPromiseId()),JdhDispatchPo::getPromiseId,query.getPromiseId())
                .eq(Objects.nonNull(query.getOutDispatchId()),JdhDispatchPo::getOutDispatchId,String.valueOf(query.getOutDispatchId()))
                .in(CollectionUtils.isNotEmpty(query.getDispatchStatusList()), JdhDispatchPo::getDispatchStatus, query.getDispatchStatusList())
                .notIn(CollectionUtils.isNotEmpty(query.getDispatchStatusNotInList()), JdhDispatchPo::getDispatchStatus, query.getDispatchStatusNotInList())
                .ne(JdhDispatchPo::getDispatchStatus, JdhDispatchStatusEnum.DISPATCH_INVALID.getStatus())
                .eq(JdhDispatchPo::getYn, YnStatusEnum.YES.getCode());

        JdhDispatchPo po = jdhDispatchPoMapper.selectOne(queryWrapper);
        return wrapDetail(po);
    }

    /**
     * findDispatch
     *
     * @param query 查询
     * @return {@link JdhDispatch}
     */
    @Override
    public Page<JdhDispatch> findDispatchPage(DispatchRepQuery query) {
        Page<JdhDispatchPo> param = new Page<>(query.getPageNum(), query.getPageSize());
        LambdaQueryWrapper<JdhDispatchPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(Objects.nonNull(query.getDispatchId()),JdhDispatchPo::getDispatchId, query.getDispatchId())
                .eq(Objects.nonNull(query.getPromiseId()),JdhDispatchPo::getPromiseId,query.getPromiseId())
                .eq(Objects.nonNull(query.getOutDispatchId()),JdhDispatchPo::getOutDispatchId,String.valueOf(query.getOutDispatchId()))
                .eq(StringUtils.isNotBlank(query.getVerticalCode()),JdhDispatchPo::getVerticalCode,query.getVerticalCode())
                .eq(StringUtils.isNotBlank(query.getServiceType()),JdhDispatchPo::getServiceType,query.getServiceType())
                .in(CollectionUtils.isNotEmpty(query.getDispatchIdList()), JdhDispatchPo::getDispatchId, query.getDispatchIdList())
                .in(CollectionUtils.isNotEmpty(query.getDispatchStatusList()), JdhDispatchPo::getDispatchStatus, query.getDispatchStatusList())
                .le(Objects.nonNull(query.getEndTime()), JdhDispatchPo::getCreateTime, query.getEndTime())
                .ge(Objects.nonNull(query.getStartTime()), JdhDispatchPo::getCreateTime, query.getStartTime())
                .eq(JdhDispatchPo::getYn, YnStatusEnum.YES.getCode());
        queryWrapper.orderByAsc(JdhDispatchPo::getCreateTime, JdhDispatchPo::getUserPin);
        IPage<JdhDispatchPo> page = jdhDispatchPoMapper.selectPage(param, queryWrapper);
        if (page == null || CollectionUtil.isEmpty(page.getRecords())){
            return null;
        }
        List<JdhDispatch> list = JdhDispatchInfrastructureConverter.INSTANCE.convertToJdhDispatchList(page.getRecords());
        return JdhBasicPoConverter.initPage(page, list);
    }

    /**
     * findDispatchDetail
     * @param query
     * @return
     */
    @Override
    public JdhDispatchDetail findDispatchDetail(DispatchDetailRepQuery query) {
        LambdaQueryWrapper<JdhDispatchDetailPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(Objects.nonNull(query.getDispatchId()),JdhDispatchDetailPo::getDispatchId, query.getDispatchId())
                .eq(Objects.nonNull(query.getDispatchDetailId()),JdhDispatchDetailPo::getDispatchDetailId, query.getDispatchDetailId())
                .eq(Objects.nonNull(query.getAngelId()),JdhDispatchDetailPo::getAngelId, query.getAngelId())
                .eq(Objects.nonNull(query.getPromiseId()),JdhDispatchDetailPo::getPromiseId,query.getPromiseId())
                .eq(JdhDispatchDetailPo::getYn, YnStatusEnum.YES.getCode());

        JdhDispatchDetailPo po = jdhDispatchDetailPoMapper.selectOne(queryWrapper);
        return JdhDispatchInfrastructureConverter.INSTANCE.convert2JdhDispatchDetail(po);
    }

    /**
     * 查询派单任务明细列表
     * @param query
     * @return
     */
    @Override
    public List<JdhDispatchDetail> findDispatchDetailList(DispatchDetailListRepQuery query) {
        LambdaQueryWrapper<JdhDispatchDetailPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(Objects.nonNull(query.getDispatchId()),JdhDispatchDetailPo::getDispatchId, query.getDispatchId())
                .eq(Objects.nonNull(query.getPromiseId()),JdhDispatchDetailPo::getPromiseId,query.getPromiseId())
                .eq(Objects.nonNull(query.getDispatchDetailId()),JdhDispatchDetailPo::getDispatchDetailId,query.getDispatchDetailId())
                .eq(Objects.nonNull(query.getAngelId()),JdhDispatchDetailPo::getAngelId,query.getAngelId())
                .eq(Objects.nonNull(query.getDispatchDetailStatus()),JdhDispatchDetailPo::getDispatchDetailStatus,query.getDispatchDetailStatus())
                .eq(Objects.nonNull(query.getDispatchDetailType()), JdhDispatchDetailPo::getDispatchDetailType, query.getDispatchDetailType())
                .gt(Objects.nonNull(query.getExpireDate()), JdhDispatchDetailPo::getExpireDate, query.getExpireDate())
                .eq(Objects.nonNull(query.getDispatchRound()), JdhDispatchDetailPo::getDispatchRound, query.getDispatchRound())
                .eq(JdhDispatchDetailPo::getYn, YnStatusEnum.YES.getCode());
        List<JdhDispatchDetailPo> list = jdhDispatchDetailPoMapper.selectList(queryWrapper);
        return wrapDispatchDetail(list);
    }

    /**
     * 查询派单任务明细列表-分页
     * @param query
     * @return
     */
    @Override
    public Page<JdhDispatchDetail> findDispatchDetailPage(DispatchDetailPageRepQuery query) {
        log.info("DispatchRepositoryImpl -> findDispatchDetailPage query={}", JSON.toJSONString(query));
        Page<JdhDispatchDetailPo> param = new Page<>(query.getPageNum(), query.getPageSize());

        LambdaQueryWrapper<JdhDispatchDetailPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(Objects.nonNull(query.getDispatchId()),JdhDispatchDetailPo::getDispatchId, query.getDispatchId())
                .eq(Objects.nonNull(query.getPromiseId()),JdhDispatchDetailPo::getPromiseId,query.getPromiseId())
                .eq(Objects.nonNull(query.getDispatchDetailId()),JdhDispatchDetailPo::getDispatchDetailId,query.getDispatchDetailId())
                .eq(Objects.nonNull(query.getAngelId()),JdhDispatchDetailPo::getAngelId,query.getAngelId())
                .eq(Objects.nonNull(query.getDispatchDetailStatus()),JdhDispatchDetailPo::getDispatchDetailStatus,query.getDispatchDetailStatus())
                .eq(Objects.nonNull(query.getDispatchDetailType()), JdhDispatchDetailPo::getDispatchDetailType, query.getDispatchDetailType())
                .gt(Objects.nonNull(query.getExpireDate()), JdhDispatchDetailPo::getExpireDate, query.getExpireDate())
                .eq(Objects.nonNull(query.getDispatchRound()), JdhDispatchDetailPo::getDispatchRound, query.getDispatchRound())
                .eq(JdhDispatchDetailPo::getYn, YnStatusEnum.YES.getCode());
        IPage<JdhDispatchDetailPo> page = jdhDispatchDetailPoMapper.selectPage(param, queryWrapper);
        if (page == null || CollectionUtil.isEmpty(page.getRecords())){
            return null;
        }
        List<JdhDispatchDetail> list = wrapDispatchDetail(page.getRecords());
        return JdhBasicPoConverter.initPage(page, list);
    }

    /**
     * 分组查询派单任务明细数量
     * @param query
     * @return
     */
    @Override
    public List<DispatchDetailGroupCount> findDispatchDetailGroupCount(DispatchDetailGroupCountQuery query) {
        QueryWrapper<JdhDispatchDetailPo> wrapper = new QueryWrapper<>();
        wrapper.select(String.format("%s, count(*) as count", query.getColumn()))
                .groupBy(query.getColumn());

        LambdaQueryWrapper<JdhDispatchDetailPo> queryWrapper = wrapper.lambda();
        queryWrapper.eq(Objects.nonNull(query.getDispatchId()),JdhDispatchDetailPo::getDispatchId, query.getDispatchId())
                .eq(Objects.nonNull(query.getPromiseId()),JdhDispatchDetailPo::getPromiseId,query.getPromiseId())
                .eq(Objects.nonNull(query.getAngelId()),JdhDispatchDetailPo::getAngelId,query.getAngelId())
                .eq(Objects.nonNull(query.getDispatchDetailStatus()),JdhDispatchDetailPo::getDispatchDetailStatus,query.getDispatchDetailStatus())
                .eq(Objects.nonNull(query.getDispatchDetailType()), JdhDispatchDetailPo::getDispatchDetailType, query.getDispatchDetailType())
                .gt(Objects.nonNull(query.getExpireDate()), JdhDispatchDetailPo::getExpireDate, query.getExpireDate())
                .eq(Objects.nonNull(query.getDispatchRound()), JdhDispatchDetailPo::getDispatchRound, query.getDispatchRound())
                .eq(JdhDispatchDetailPo::getYn, YnStatusEnum.YES.getCode());

        List<Map<String, Object>> maps = jdhDispatchDetailPoMapper.selectMaps(queryWrapper);
        List<DispatchDetailGroupCount> result = new ArrayList<>();
        for (Map<String, Object> objectMap : maps) {
            Object o = objectMap.get(query.getColumn());
            if (Objects.nonNull(o)) {
                result.add(DispatchDetailGroupCount.builder().groupKeyValue(o).count((Long)objectMap.getOrDefault("count", 0L)).build());
            }
        }
        return result;
    }

    /**
     * 保存派单明细
     * @return
     */
    @Override
    public int saveDispatchDetail(JdhDispatchDetail dispatchDetail) {
        if (Objects.isNull(dispatchDetail)) {
            return 0;
        }
        JdhDispatchDetailPo dispatchDetailPo = JdhDispatchInfrastructureConverter.INSTANCE.convert2JdhDispatchDetailPo(dispatchDetail);
        //新增
        if (Objects.isNull(dispatchDetailPo.getId())) {
            JdhBasicPoConverter.initInsertBasicPo(dispatchDetailPo);
            jdhDispatchDetailPoMapper.insert(dispatchDetailPo);
        }
        //修改
        else {
            LambdaUpdateWrapper<JdhDispatchDetailPo> detailWrapper = Wrappers.lambdaUpdate();
            Integer serviceVersion = dispatchDetail.getVersion();

            detailWrapper.set(JdhDispatchDetailPo::getVersion, serviceVersion + 1)
                    .eq(JdhDispatchDetailPo::getVersion, serviceVersion)
                    .eq(JdhDispatchDetailPo::getDispatchId,dispatchDetail.getDispatchId())
                    .eq(JdhDispatchDetailPo::getDispatchDetailId, dispatchDetail.getDispatchDetailId());
            jdhDispatchDetailPoMapper.update(dispatchDetailPo, detailWrapper);
        }
        return 0;
    }

    /**
     * 通过ID寻找Aggregate。
     * 找到的Aggregate自动是可追踪的
     */
    @Override
    public JdhDispatch find(JdhDispatchIdentifier identifier) {
        LambdaQueryWrapper<JdhDispatchPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(JdhDispatchPo::getDispatchId,identifier.getDispatchId());
        queryWrapper.eq(JdhDispatchPo::getYn, YnStatusEnum.YES.getCode());
        JdhDispatchPo po = jdhDispatchPoMapper.selectOne(queryWrapper);
        return wrapDetail(po);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int remove(JdhDispatch aggregate) {
        log.info("DispatchRepositoryImpl -> remove aggregate:{}", JSON.toJSONString(aggregate));
        LambdaUpdateWrapper<JdhDispatchDetailPo> updateDetailWrapper = Wrappers.lambdaUpdate();
        updateDetailWrapper.setSql("`version` = `version` + 1").set(JdhDispatchDetailPo::getYn, YnStatusEnum.NO.getCode())
                .eq(JdhDispatchDetailPo::getDispatchId, aggregate.getDispatchId());
        jdhDispatchDetailPoMapper.update(null, updateDetailWrapper);

        LambdaUpdateWrapper<JdhDispatchPo> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.setSql("`version` = `version` + 1").set(JdhDispatchPo::getYn, YnStatusEnum.NO.getCode())
                .eq(JdhDispatchPo::getDispatchId, aggregate.getDispatchId());
        return jdhDispatchPoMapper.update(null, updateWrapper);
    }

    /**
     * 保存
     * @param jdhDispatch
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int save(JdhDispatch jdhDispatch) {
        JdhDispatchPo jdhDispatchPo = JdhDispatchInfrastructureConverter.INSTANCE.entity2Po(jdhDispatch);
        //新增
        if (Objects.isNull(jdhDispatchPo.getId())) {
            JdhBasicPoConverter.initInsertBasicPo(jdhDispatchPo);
            return jdhDispatchPoMapper.insert(jdhDispatchPo);
        }
        //修改
        Integer oldVersion = jdhDispatch.getVersion();
        jdhDispatch.versionIncrease();
        jdhDispatchPo.setBranch(environment);
        jdhDispatchPo.setUpdateTime(new Date());
        jdhDispatchPo.setVersion(jdhDispatch.getVersion());

        LambdaUpdateWrapper<JdhDispatchPo> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.eq(JdhDispatchPo::getVersion,oldVersion)
                .eq(JdhDispatchPo::getDispatchId,jdhDispatchPo.getDispatchId());

        int count = jdhDispatchPoMapper.update(jdhDispatchPo, updateWrapper);
        if (count < 1) {
            throw new BusinessException(DispatchErrorCode.DISPATCH_CALLBACK_BUSY);
        }

        List<JdhDispatchDetail> angelDetailList = jdhDispatch.getAngelDetailList();
        if (CollectionUtils.isEmpty(angelDetailList)) {
            return count;
        }
        List<JdhDispatchDetailPo> saveBatchList = new ArrayList<>();
        List<JdhDispatchDetail> updateBatchList = new ArrayList<>();
        for (JdhDispatchDetail dispatchDetail : angelDetailList) {
            //新增
            if (Objects.isNull(dispatchDetail.getId())) {
                JdhDispatchDetailPo dispatchDetailPo = JdhDispatchInfrastructureConverter.INSTANCE.convert2JdhDispatchDetailPo(dispatchDetail);
                JdhBasicPoConverter.initInsertBasicPo(dispatchDetailPo);
                //jdhDispatchDetailPoMapper.insert(dispatchDetailPo);
                saveBatchList.add(dispatchDetailPo);
            }
            //修改
            else {
                updateBatchList.add(dispatchDetail);
            }
        }
        if (CollectionUtils.isNotEmpty(saveBatchList)) {
            Lists.partition(saveBatchList, 500).forEach(list -> jdhDispatchDetailPoMapper.batchInsert(list));
        }

        // 更新明细时，当前单据状态为JdhDispatchDetailStatusEnum 已接单、已取消、已拒绝则和当前事务一起处理，其它明细开启线程额外处理
        if (CollectionUtils.isNotEmpty(updateBatchList)) {
            List<JdhDispatchDetail> consistencyList = Lists.newArrayList();
            List<JdhDispatchDetail> asyncList = Lists.newArrayList();
            for (JdhDispatchDetail detailPo : updateBatchList) {
                if ( JdhDispatchDetailStatusEnum.CONSISTENCY_STATUS.contains(detailPo.getDispatchDetailStatus())){
                    consistencyList.add(detailPo);
                }else {
                    asyncList.add(detailPo);
                }
            }

            for (JdhDispatchDetail detail : consistencyList) {
                LambdaUpdateWrapper<JdhDispatchDetailPo> detailWrapper = Wrappers.lambdaUpdate();
                detailWrapper.eq(JdhDispatchDetailPo::getVersion, detail.getVersion())
                        .eq(JdhDispatchDetailPo::getDispatchId,detail.getDispatchId())
                        .eq(JdhDispatchDetailPo::getDispatchDetailId, detail.getDispatchDetailId());

                detail.versionIncrease();
                JdhDispatchDetailPo dispatchDetailPo = JdhDispatchInfrastructureConverter.INSTANCE.convert2JdhDispatchDetailPo(detail);
                dispatchDetailPo.setUpdateTime(new Date());
                int update = jdhDispatchDetailPoMapper.update(dispatchDetailPo, detailWrapper);
                log.info("DispatchRepositoryImpl-> update dispatchDetailId={}", detail.getDispatchDetailId());
                if (update < 1) {
                    throw new BusinessException(DispatchErrorCode.DISPATCH_CALLBACK_BUSY);
                }
            }

            ExecutorService executorService = executorPoolFactory.get(ThreadPoolConfigEnum.DISPATCH_POOL);
            CompletableFuture.runAsync(()->{
                try {
                    for (JdhDispatchDetail detail : asyncList) {
                        LambdaUpdateWrapper<JdhDispatchDetailPo> detailWrapper = Wrappers.lambdaUpdate();
                        detailWrapper
                                .eq(JdhDispatchDetailPo::getDispatchId, detail.getDispatchId())
                                .eq(JdhDispatchDetailPo::getDispatchDetailId, detail.getDispatchDetailId());

                        detail.versionIncrease();
                        JdhDispatchDetailPo dispatchDetailPo = JdhDispatchInfrastructureConverter.INSTANCE.convert2JdhDispatchDetailPo(detail);
                        dispatchDetailPo.setUpdateTime(new Date());
                        int update = jdhDispatchDetailPoMapper.update(dispatchDetailPo, detailWrapper);
                        if (update < 1) {
                            throw new BusinessException(DispatchErrorCode.DISPATCH_CALLBACK_BUSY);
                        }
                    }
                }catch (Exception e){
                    log.error("DispatchRepositoryImpl-> update async list error", e);
                }
            },  executorService);
        }

        return count;
    }

    /**
     * 包装
     * @param poList
     * @return
     */
    private List<JdhDispatch> wrap(List<JdhDispatchPo> poList) {
        if (CollectionUtils.isEmpty(poList)){
            return new ArrayList<>();
        }
        return poList.stream().map(JdhDispatchInfrastructureConverter.INSTANCE::convert2JdhDispatch).collect(Collectors.toList());
    }

    /**
     * 包装（包含派单明细信息）
     * @param po
     * @return
     */
    private JdhDispatch wrapDetail(JdhDispatchPo po) {
        if (Objects.isNull(po)){
            return null;
        }
        JdhDispatch jdhDispatch = JdhDispatchInfrastructureConverter.INSTANCE.convert2JdhDispatch(po);
        List<JdhDispatchDetail> detailList = findDispatchDetailList(DispatchDetailListRepQuery.builder().dispatchId(po.getDispatchId()).dispatchRound(po.getDispatchRound()).build());
        jdhDispatch.setAngelDetailList(detailList);
        return jdhDispatch;
    }

    /**
     * wrapDispatchDetail
     * @return
     */
    private List<JdhDispatchDetail> wrapDispatchDetail(List<JdhDispatchDetailPo> list) {
        return JdhDispatchInfrastructureConverter.INSTANCE.convert2JdhDispatchDetail(list);
    }
}