package com.jdh.o2oservice.infrastructure.repository.db;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jd.medicine.base.common.util.StringUtil;
import com.jdh.o2oservice.base.enums.YnStatusEnum;
import com.jdh.o2oservice.core.domain.settlement.model.JdhSettlementCityLevelConfig;
import com.jdh.o2oservice.core.domain.settlement.model.JdhSettlementCityLevelConfigIdentifier;
import com.jdh.o2oservice.core.domain.settlement.repository.db.JdhSettlementCityLevelConfigRepository;
import com.jdh.o2oservice.core.domain.settlement.repository.query.CityAngelSettlementPageQuery;
import com.jdh.o2oservice.infrastructure.repository.db.convert.JdhAngelSettlementPoConvert;
import com.jdh.o2oservice.infrastructure.repository.db.convert.JdhBasicPoConverter;
import com.jdh.o2oservice.infrastructure.repository.db.dao.JdhSettlementCityLevelConfigPoMapper;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhAngelPo;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhSettlementCityLevelConfigPo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * JdhSettlementCityLevelConfigRepositoryImpl
 * @author:lwm
 * @createTime: 2025-04-18 16:06
 * @Description:
 */
@Component
@Slf4j
public class JdhSettlementCityLevelConfigRepositoryImpl implements JdhSettlementCityLevelConfigRepository {

    /**
     * jdhSettlementCityLevelConfigPoMapper
     */
    @Resource
    private JdhSettlementCityLevelConfigPoMapper jdhSettlementCityLevelConfigPoMapper;

    /**
     * 通过ID寻找Aggregate。
     * 找到的Aggregate自动是可追踪的
     *
     * @param jdhSettlementCityLevelConfigIdentifier
     */
    @Override
    public JdhSettlementCityLevelConfig find(JdhSettlementCityLevelConfigIdentifier jdhSettlementCityLevelConfigIdentifier) {
        return null;
    }

    /**
     * 将一个Aggregate从Repository移除
     * 操作后的aggregate对象自动取消追踪
     *
     * @param entity
     */
    @Override
    public int remove(JdhSettlementCityLevelConfig entity) {
        return 0;
    }

    /**
     * 保存地区费项配置
     *
     * @param jdhSettlementCityLevelConfig
     * @return count
     */
    @Override
    public int save(JdhSettlementCityLevelConfig jdhSettlementCityLevelConfig) {
        JdhSettlementCityLevelConfigPo settlementCityLevelConfigPo = JdhAngelSettlementPoConvert.INSTANCE.
                convertToJdhSettlementCityLevelConfigPo(jdhSettlementCityLevelConfig);
        return jdhSettlementCityLevelConfigPoMapper.insert(settlementCityLevelConfigPo);
    }

    /**
     * 批量保存地区费项配置
     *
     * @param jdhSettlementCityLevelConfigList
     */
    @Override
    public int batchSaveJdhCityLevelConfig(List<JdhSettlementCityLevelConfig> jdhSettlementCityLevelConfigList) {
        List<JdhSettlementCityLevelConfigPo> list = JdhAngelSettlementPoConvert.INSTANCE.
                convertToJdhSettlementCityLevelConfigPoList(jdhSettlementCityLevelConfigList);
        return jdhSettlementCityLevelConfigPoMapper.batchSaveJdhSettlementCityLevelConfig(list);
    }

    /**
     * 更新地区费项配置
     *
     * @param jdhSettlementCityLevelConfig
     * @return count
     */
    @Override
    public int update(JdhSettlementCityLevelConfig jdhSettlementCityLevelConfig) {
        LambdaUpdateWrapper<JdhSettlementCityLevelConfigPo> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(StringUtil.isNotBlank(jdhSettlementCityLevelConfig.getLevel()),JdhSettlementCityLevelConfigPo::getLevel, jdhSettlementCityLevelConfig.getLevel())
                .set(StringUtil.isNotBlank(jdhSettlementCityLevelConfig.getUpdateUser()),JdhSettlementCityLevelConfigPo::getUpdateUser, jdhSettlementCityLevelConfig.getUpdateUser())
                .eq(JdhSettlementCityLevelConfigPo::getCityConfigId,jdhSettlementCityLevelConfig.getCityConfigId())
                .eq(JdhSettlementCityLevelConfigPo::getYn,YnStatusEnum.YES.getCode());
        return jdhSettlementCityLevelConfigPoMapper.update(null, updateWrapper);
    }

    /**
     * 分页查询城市级别服务费配置
     *
     * @param cityAngelSettlementPageQuery
     * @return count
     */
    @Override
    public Page<JdhSettlementCityLevelConfig> queryPage(CityAngelSettlementPageQuery cityAngelSettlementPageQuery) {
        Page<JdhSettlementCityLevelConfigPo> param = new Page<>(cityAngelSettlementPageQuery.getPageNum(), cityAngelSettlementPageQuery.getPageSize());
        LambdaQueryWrapper<JdhSettlementCityLevelConfigPo> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper.eq(StringUtils.isNotBlank(cityAngelSettlementPageQuery.getProvinceCode()),JdhSettlementCityLevelConfigPo::getProvinceCode, cityAngelSettlementPageQuery.getProvinceCode())
                    .eq(StringUtils.isNotBlank(cityAngelSettlementPageQuery.getCityCode()), JdhSettlementCityLevelConfigPo::getCityCode, cityAngelSettlementPageQuery.getCityCode())
                    .eq(StringUtils.isNotBlank(cityAngelSettlementPageQuery.getDestCode()), JdhSettlementCityLevelConfigPo::getDestCode, cityAngelSettlementPageQuery.getDestCode())
                    .eq(StringUtils.isNotBlank(cityAngelSettlementPageQuery.getCityLevel()), JdhSettlementCityLevelConfigPo::getLevel, cityAngelSettlementPageQuery.getCityLevel())
                    .eq(StringUtils.isNotBlank(cityAngelSettlementPageQuery.getOperator()), JdhSettlementCityLevelConfigPo::getUpdateUser, cityAngelSettlementPageQuery.getOperator())
                    .eq(JdhSettlementCityLevelConfigPo::getYn, YnStatusEnum.YES.getCode()).orderByDesc(JdhSettlementCityLevelConfigPo::getCreateTime);
        IPage<JdhSettlementCityLevelConfigPo> page = jdhSettlementCityLevelConfigPoMapper.selectPage(param, queryWrapper);
        List<JdhSettlementCityLevelConfig> list= JdhAngelSettlementPoConvert.INSTANCE.convertToJdhSettlementCityLevelConfig(page.getRecords());
        return JdhBasicPoConverter.initPage(page, list);
    }

    /**
     * 查询地区费项配置列表
     *
     * @param cityAngelSettlementPageQuery
     */
    @Override
    public JdhSettlementCityLevelConfig queryJdhCityLevelConfig(CityAngelSettlementPageQuery cityAngelSettlementPageQuery) {
        LambdaQueryWrapper<JdhSettlementCityLevelConfigPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(StringUtils.isNotBlank(cityAngelSettlementPageQuery.getProvinceCode()),JdhSettlementCityLevelConfigPo::getProvinceCode, cityAngelSettlementPageQuery.getProvinceCode())
                .eq(StringUtils.isNotBlank(cityAngelSettlementPageQuery.getCityCode()), JdhSettlementCityLevelConfigPo::getCityCode, cityAngelSettlementPageQuery.getCityCode())
                .eq(StringUtils.isNotBlank(cityAngelSettlementPageQuery.getDestCode()), JdhSettlementCityLevelConfigPo::getDestCode, cityAngelSettlementPageQuery.getDestCode())
                .eq(StringUtils.isNotBlank(cityAngelSettlementPageQuery.getCityLevel()), JdhSettlementCityLevelConfigPo::getLevel, cityAngelSettlementPageQuery.getCityLevel())
                .eq(StringUtils.isNotBlank(cityAngelSettlementPageQuery.getOperator()), JdhSettlementCityLevelConfigPo::getUpdateUser, cityAngelSettlementPageQuery.getOperator())
                .eq(JdhSettlementCityLevelConfigPo::getYn, YnStatusEnum.YES.getCode()).orderByDesc(JdhSettlementCityLevelConfigPo::getCreateTime);
        List<JdhSettlementCityLevelConfigPo> list = jdhSettlementCityLevelConfigPoMapper.selectList(queryWrapper);
        if(CollectionUtils.isNotEmpty(list)){
            return JdhAngelSettlementPoConvert.INSTANCE.convertToJdhSettlementCityLevelConfig(list.get(0));
        }
        return null;
    }

    /**
     *
     * @param queryContext
     * @return
     */
    @Override
    public Integer queryCityConfigCount(CityAngelSettlementPageQuery queryContext) {
        LambdaQueryWrapper<JdhSettlementCityLevelConfigPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(StringUtils.isNotBlank(queryContext.getProvinceCode()),JdhSettlementCityLevelConfigPo::getProvinceCode, queryContext.getProvinceCode())
                .eq(StringUtils.isNotBlank(queryContext.getCityCode()), JdhSettlementCityLevelConfigPo::getCityCode, queryContext.getCityCode())
                .eq(StringUtils.isNotBlank(queryContext.getCityLevel()), JdhSettlementCityLevelConfigPo::getLevel, queryContext.getCityLevel())
                .eq(StringUtils.isNotBlank(queryContext.getOperator()), JdhSettlementCityLevelConfigPo::getUpdateUser, queryContext.getOperator())
                .eq(JdhSettlementCityLevelConfigPo::getYn, YnStatusEnum.YES.getCode());
        return jdhSettlementCityLevelConfigPoMapper.selectCount(queryWrapper);
    }
}
