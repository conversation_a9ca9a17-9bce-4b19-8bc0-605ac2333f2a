package com.jdh.o2oservice.infrastructure.repository.db.convert;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.core.domain.medpromise.model.MedicalPromise;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhMedicalPromisePo;
import org.apache.commons.collections4.CollectionUtils;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import java.util.Objects;

import java.util.List;

/**
 * @Description: 检测单转化类
 * @Interface: MedicalPromiseConvert
 * @Author: wangpengfei144
 * @Date: 2024/4/16
 */
@Mapper
public interface MedicalPromiseConvert {

    /**
     * 初始化
     */
    MedicalPromiseConvert INSTANCE  = Mappers.getMapper(MedicalPromiseConvert.class);

    /**
     * 转化
     * @param jdhMedicalPromisePo
     * @return
     */
//    MedicalPromise convert(JdhMedicalPromisePo jdhMedicalPromisePo);

    default     MedicalPromise convert(JdhMedicalPromisePo jdhMedicalPromisePo) {
        if ( jdhMedicalPromisePo == null ) {
            return null;
        }

        MedicalPromise.MedicalPromiseBuilder medicalPromise = MedicalPromise.builder();

        medicalPromise.id( jdhMedicalPromisePo.getId() );
        medicalPromise.medicalPromiseId( jdhMedicalPromisePo.getMedicalPromiseId() );
        medicalPromise.outerId( jdhMedicalPromisePo.getOuterId() );
        medicalPromise.verticalCode( jdhMedicalPromisePo.getVerticalCode() );
        medicalPromise.serviceType( jdhMedicalPromisePo.getServiceType() );
        if ( jdhMedicalPromisePo.getServiceId() != null ) {
            medicalPromise.serviceId( String.valueOf( jdhMedicalPromisePo.getServiceId() ) );
        }
        medicalPromise.userPin( jdhMedicalPromisePo.getUserPin() );
        medicalPromise.promiseId( jdhMedicalPromisePo.getPromiseId() );
        medicalPromise.voucherId( jdhMedicalPromisePo.getVoucherId() );
        medicalPromise.promisePatientId( jdhMedicalPromisePo.getPromisePatientId() );
        medicalPromise.status( jdhMedicalPromisePo.getStatus() );
        medicalPromise.specimenCode( jdhMedicalPromisePo.getSpecimenCode() );
        medicalPromise.serviceItemId( jdhMedicalPromisePo.getServiceItemId() );
        medicalPromise.serviceItemName( jdhMedicalPromisePo.getServiceItemName() );
        medicalPromise.providerId( jdhMedicalPromisePo.getProviderId() );
        medicalPromise.stationId( jdhMedicalPromisePo.getStationId() );
        medicalPromise.stationAddress( jdhMedicalPromisePo.getStationAddress() );
        medicalPromise.stationName( jdhMedicalPromisePo.getStationName() );
        medicalPromise.stationPhone( jdhMedicalPromisePo.getStationPhone() );
        medicalPromise.yn( jdhMedicalPromisePo.getYn() );
        medicalPromise.version( jdhMedicalPromisePo.getVersion() );
        medicalPromise.branch( jdhMedicalPromisePo.getBranch() );
        medicalPromise.createUser( jdhMedicalPromisePo.getCreateUser() );
        medicalPromise.createTime( jdhMedicalPromisePo.getCreateTime() );
        medicalPromise.updateUser( jdhMedicalPromisePo.getUpdateUser() );
        medicalPromise.updateTime( jdhMedicalPromisePo.getUpdateTime() );
        medicalPromise.freeze( jdhMedicalPromisePo.getFreeze() );
        medicalPromise.settleStatus( jdhMedicalPromisePo.getSettleStatus() );
        medicalPromise.checkTime( jdhMedicalPromisePo.getCheckTime() );
        medicalPromise.reportStatus( jdhMedicalPromisePo.getReportStatus() );
        medicalPromise.angelStationId( jdhMedicalPromisePo.getAngelStationId() );
        medicalPromise.angelStationName( jdhMedicalPromisePo.getAngelStationName() );
        medicalPromise.serialNum( jdhMedicalPromisePo.getSerialNum() );
        medicalPromise.reportTime( jdhMedicalPromisePo.getReportTime() );
        medicalPromise.checkStatus( jdhMedicalPromisePo.getCheckStatus() );
        medicalPromise.flag( jdhMedicalPromisePo.getFlag() );
        medicalPromise.mergeMedicalId( jdhMedicalPromisePo.getMergeMedicalId() );
        medicalPromise.waitingTestTimeOutDate( jdhMedicalPromisePo.getWaitingTestTimeOutDate() );
        medicalPromise.waitingTestTimeOutStatus( jdhMedicalPromisePo.getWaitingTestTimeOutStatus() );
        medicalPromise.testingTimeOutDate( jdhMedicalPromisePo.getTestingTimeOutDate() );
        medicalPromise.testingTimeOutStatus( jdhMedicalPromisePo.getTestingTimeOutStatus() );
//        medicalPromise.isTest( jdhMedicalPromisePo.getIsTest() );
        medicalPromise.reportShowType( jdhMedicalPromisePo.getReportShowType() );
        if (Objects.isNull(jdhMedicalPromisePo.getReportShowType())){
            medicalPromise.reportShowType(1);
        }
        medicalPromise.deliveryStepFlow(jdhMedicalPromisePo.getDeliveryStepFlow());

        return medicalPromise.build();
    }

    /**
     * 转化
     * @param jdhMedicalPromisePo
     * @return
     */
    List<MedicalPromise> convert(List<JdhMedicalPromisePo> jdhMedicalPromisePo);

    /**
     * 转化
     * @param jdhMedicalPromisePoPage
     * @return
     */
    default PageDto<MedicalPromise> convert(Page<JdhMedicalPromisePo> jdhMedicalPromisePoPage){

        if (jdhMedicalPromisePoPage == null){
            return null;
        }
        PageDto<MedicalPromise> pageDto = new PageDto<>();
        pageDto.setTotalPage(jdhMedicalPromisePoPage.getPages());
        pageDto.setTotalCount(jdhMedicalPromisePoPage.getTotal());
        pageDto.setPageNum(jdhMedicalPromisePoPage.getCurrent());
        pageDto.setPageSize(jdhMedicalPromisePoPage.getSize());

        if (CollectionUtils.isEmpty(jdhMedicalPromisePoPage.getRecords())){
            return pageDto;
        }

        List<MedicalPromise> convert = convert(jdhMedicalPromisePoPage.getRecords());
        pageDto.setList(convert);
        return pageDto;

    };

    /**
     * 转化
     * @param medicalPromise
     * @return
     */
    JdhMedicalPromisePo convert(MedicalPromise medicalPromise);

    /**
     * 转化
     * @param medicalPromise
     * @return
     */
    List<JdhMedicalPromisePo> convertList(List<MedicalPromise> medicalPromise);
}
