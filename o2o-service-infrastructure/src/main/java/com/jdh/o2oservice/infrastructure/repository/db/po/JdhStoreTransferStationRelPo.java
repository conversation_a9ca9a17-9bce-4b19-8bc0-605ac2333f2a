package com.jdh.o2oservice.infrastructure.repository.db.po;

import java.util.Date;
import lombok.*;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * 
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ToString
@TableName("jdh_store_transfer_station_rel")
public class JdhStoreTransferStationRelPo extends JdhBasicPo {

    /** 主键 */
    @TableId
    private Long id;

    /** 京东系统实验室id */
    @TableField("jd_store_id")
    private String jdStoreId;

    /** 京东接驳点id */
    @TableField("jd_transfer_station_id")
    private Long jdTransferStationId;

    /** 创建时间 */
    @TableField("create_time")
    private Date createTime;

    /** 创建人 */
    @TableField("create_user")
    private String createUser;

    /** 更新时间 */
    @TableField("update_time")
    private Date updateTime;

    /** 修改人 */
    @TableField("update_user")
    private String updateUser;

    /** 有效标志 0 无效 1有效 */
    @TableField("yn")
    private Integer yn;

    /** 数据来源分支 */
    @TableField("branch")
    private String branch;

    /** 版本号 */
    @TableField("version")
    private Integer version;
}