package com.jdh.o2oservice.infrastructure.repository.db.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * @ClassName:JdhSettlementCityLevelConfigPo
 * @Description:
 * @Author: lwm
 * @Date: 2025/4/18 11:14
 * @Vserion: 1.0
 **/
@Data
@TableName(value = "jdh_settlement_city_level_config",autoResultMap = true)
public class JdhSettlementCityLevelConfigPo {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * '地区配置id'
     */
    private Long cityConfigId;

    /**
     * 省编码
     */
    private String provinceCode;
    /**
     * 省名称
     */
    private String provinceName;
    /**
     * 市编码
     */
    private String cityCode;
    /**
     * 市名称
     */
    private String cityName;
    /**
     * 目标地址code
     */
    private String destCode;
    /**
     * 地区等级
     */
    private String level;
    /**
     * 数据来源分支
     */
    private String branch;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 是否有效 1-有效 0-无效
     */
    private Integer yn;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 创建人
     */
    private String createUser;
    /**
     * 修改时间
     */
    private Date updateTime;
    /**
     * 修改人
     */
    private String updateUser;
}
