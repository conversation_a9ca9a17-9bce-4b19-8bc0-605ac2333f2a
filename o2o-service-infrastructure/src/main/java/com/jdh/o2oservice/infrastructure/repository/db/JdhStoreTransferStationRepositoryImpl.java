package com.jdh.o2oservice.infrastructure.repository.db;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jdh.o2oservice.base.enums.YnStatusEnum;
import com.jdh.o2oservice.core.domain.provider.model.JdhStoreTransferStation;
import com.jdh.o2oservice.core.domain.provider.model.JdhStoreTransferStationIdentifier;
import com.jdh.o2oservice.core.domain.provider.repository.db.JdhStoreTransferStationRepository;
import com.jdh.o2oservice.infrastructure.repository.db.convert.JdhBasicPoConverter;
import com.jdh.o2oservice.infrastructure.repository.db.convert.JdhStoreTransferStationConvert;
import com.jdh.o2oservice.infrastructure.repository.db.dao.JdhStoreTransferStationMapper;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhStoreTransferStationPo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Component
@Slf4j
public class JdhStoreTransferStationRepositoryImpl implements JdhStoreTransferStationRepository {
    /**
     * 门店接驳点
     */
    @Resource
    JdhStoreTransferStationMapper jdhStoreTransferStationMapper;

    /**
     * 通过ID寻找Aggregate。
     * 找到的Aggregate自动是可追踪的
     *
     * @param jdhStoreTransferStationIdentifier
     */
    @Override
    public JdhStoreTransferStation find(JdhStoreTransferStationIdentifier jdhStoreTransferStationIdentifier) {
        return JdhStoreTransferStationConvert.INSTANCE.toJdhStoreTransferStation(jdhStoreTransferStationMapper.selectById(jdhStoreTransferStationIdentifier.getJdStationId()));
    }

    /**
     * 将一个Aggregate从Repository移除
     * 操作后的aggregate对象自动取消追踪
     *
     * @param entity
     */
    @Override
    public int remove(JdhStoreTransferStation entity) {
        LambdaUpdateWrapper<JdhStoreTransferStationPo> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(JdhStoreTransferStationPo::getYn, YnStatusEnum.NO.getCode())
                .setSql("`version` = version+1")
                .eq(JdhStoreTransferStationPo::getJdStationId, entity.getJdStationId())
                .eq(JdhStoreTransferStationPo::getYn, YnStatusEnum.YES.getCode());
        return jdhStoreTransferStationMapper.update(null, updateWrapper);
    }

    /**
     * 保存接驳点
     *
     * @param jdhStoreTransferStation jdhStoreTransferStation
     * @return count
     */
    @Override
    public int save(JdhStoreTransferStation jdhStoreTransferStation) {
        JdhStoreTransferStationPo po = JdhStoreTransferStationConvert.INSTANCE.toJdhStoreTransferStationPo(jdhStoreTransferStation);
        JdhBasicPoConverter.initInsertBasicPo(po);
        return jdhStoreTransferStationMapper.insert(po);
    }

    /**
     * 更新接驳点
     *
     * @param jdhStoreTransferStation jdhStoreTransferStation
     * @return count
     */
    @Override
    public int update(JdhStoreTransferStation jdhStoreTransferStation) {
        LambdaUpdateWrapper<JdhStoreTransferStationPo> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper
                .set(jdhStoreTransferStation.getStationType() != null, JdhStoreTransferStationPo::getStationType, jdhStoreTransferStation.getStationType())
                .set(StringUtils.isNotBlank(jdhStoreTransferStation.getJdStationName()), JdhStoreTransferStationPo::getJdStationName, jdhStoreTransferStation.getJdStationName())
                .set(StringUtils.isNotBlank(jdhStoreTransferStation.getJdStationAddress()), JdhStoreTransferStationPo::getJdStationAddress, jdhStoreTransferStation.getJdStationAddress())
                .set(jdhStoreTransferStation.getJdStationLatitude() != null, JdhStoreTransferStationPo::getJdStationLatitude, jdhStoreTransferStation.getJdStationLatitude())
                .set(jdhStoreTransferStation.getJdStationLongitude() != null, JdhStoreTransferStationPo::getJdStationLongitude, jdhStoreTransferStation.getJdStationLongitude())
                .set(jdhStoreTransferStation.getJdStationTargetId() != null, JdhStoreTransferStationPo::getJdStationTargetId, jdhStoreTransferStation.getJdStationTargetId())
                .set(StringUtils.isNotBlank(jdhStoreTransferStation.getThirdStationId()), JdhStoreTransferStationPo::getThirdStationId, jdhStoreTransferStation.getThirdStationId())
                .set(StringUtils.isNotBlank(jdhStoreTransferStation.getThirdStationTargetId()), JdhStoreTransferStationPo::getThirdStationTargetId, jdhStoreTransferStation.getThirdStationTargetId())
                .set(JdhStoreTransferStationPo::getUpdateUser, jdhStoreTransferStation.getUpdateUser())
                .setSql("`version` = version+1")
                .eq(JdhStoreTransferStationPo::getJdStationId, jdhStoreTransferStation.getJdStationId())
                .eq(JdhStoreTransferStationPo::getYn, YnStatusEnum.YES.getCode());
        return jdhStoreTransferStationMapper.update(null, updateWrapper);
    }

    /**
     * 删除接驳点
     *
     * @param jdhStoreTransferStation jdhStoreTransferStation
     * @return count
     */
    @Override
    public int delete(JdhStoreTransferStation jdhStoreTransferStation) {
        LambdaUpdateWrapper<JdhStoreTransferStationPo> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(JdhStoreTransferStationPo::getYn, YnStatusEnum.NO.getCode())
                .setSql("`version` = version+1")
                .eq(JdhStoreTransferStationPo::getJdStationId, jdhStoreTransferStation.getJdStationId())
                .eq(JdhStoreTransferStationPo::getYn, YnStatusEnum.YES.getCode());
        return jdhStoreTransferStationMapper.update(null, updateWrapper);
    }

    /**
     * 查询门店项目关系
     *
     * @param jdhStoreTransferStation jdhStoreTransferStation
     * @return list
     */
    @Override
    public List<JdhStoreTransferStation> queryList(JdhStoreTransferStation jdhStoreTransferStation) {
        if (jdhStoreTransferStation == null || (jdhStoreTransferStation.getJdStationId() == null && CollUtil.isEmpty(jdhStoreTransferStation.getJdStationIds()))) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<JdhStoreTransferStationPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(jdhStoreTransferStation.getJdStationId() != null, JdhStoreTransferStationPo::getJdStationId, jdhStoreTransferStation.getJdStationId())
                .in(CollUtil.isNotEmpty(jdhStoreTransferStation.getJdStationIds()), JdhStoreTransferStationPo::getJdStationId, jdhStoreTransferStation.getJdStationIds())
                .eq(JdhStoreTransferStationPo::getYn, YnStatusEnum.YES.getCode());
        return JdhStoreTransferStationConvert.INSTANCE.toJdhStoreTransferStation(jdhStoreTransferStationMapper.selectList(queryWrapper));
    }

    /**
     * 分页查询门店项目关系
     *
     * @param jdhStoreTransferStation
     */
    @Override
    public Page<JdhStoreTransferStation> queryPageList(JdhStoreTransferStation jdhStoreTransferStation) {
        IPage<JdhStoreTransferStationPo> iPage = new Page<>(jdhStoreTransferStation.getPageNum(), jdhStoreTransferStation.getPageSize());
        LambdaQueryWrapper<JdhStoreTransferStationPo> queryWrapper = Wrappers.lambdaQuery();
        IPage<JdhStoreTransferStationPo> storeTransferStationPoIPage = jdhStoreTransferStationMapper.selectPage(iPage, queryWrapper);
        if (Objects.isNull(storeTransferStationPoIPage) || CollectionUtils.isEmpty(storeTransferStationPoIPage.getRecords())) {
            return null;
        }
        List<JdhStoreTransferStation> storeTransferStation = JdhStoreTransferStationConvert.INSTANCE.toJdhStoreTransferStation(storeTransferStationPoIPage.getRecords());
        return JdhBasicPoConverter.initPage(storeTransferStationPoIPage, storeTransferStation);
    }

    /**
     * 查询门店项目关系
     *
     * @param jdhStoreTransferStation
     */
    @Override
    public JdhStoreTransferStation query(JdhStoreTransferStation jdhStoreTransferStation) {
        if (jdhStoreTransferStation == null || jdhStoreTransferStation.getJdStationId() == null) {
            return null;
        }
        LambdaQueryWrapper<JdhStoreTransferStationPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(JdhStoreTransferStationPo::getJdStationId, jdhStoreTransferStation.getJdStationId())
                .eq(JdhStoreTransferStationPo::getYn, YnStatusEnum.YES.getCode());
        return JdhStoreTransferStationConvert.INSTANCE.toJdhStoreTransferStation(jdhStoreTransferStationMapper.selectOne(queryWrapper));
    }
}
