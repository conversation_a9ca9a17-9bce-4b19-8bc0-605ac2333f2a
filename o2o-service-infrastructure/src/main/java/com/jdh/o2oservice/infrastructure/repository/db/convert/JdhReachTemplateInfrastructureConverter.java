package com.jdh.o2oservice.infrastructure.repository.db.convert;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.jdh.o2oservice.base.constatnt.NumConstant;
import com.jdh.o2oservice.core.domain.support.reach.model.JdhReachTemplate;
import com.jdh.o2oservice.core.domain.support.reach.model.ReachParamParse;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhReachTemplatePo;
import org.apache.commons.lang3.BooleanUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.Objects;


/**
 * JdhReachTemplateInfrastructureConverter
 *
 * <AUTHOR>
 * @date 2024/07/24
 */
@Mapper
public interface JdhReachTemplateInfrastructureConverter {

    /**
     * INSTANCE
     */
    JdhReachTemplateInfrastructureConverter INSTANCE = Mappers.getMapper(JdhReachTemplateInfrastructureConverter.class);


    /**
     * 实体2 po
     *
     * @param entity 实体
     * @return {@link JdhReachTemplatePo}
     */
    @Mappings({
            @Mapping(target = "paramParse",expression = "java(cn.hutool.core.collection.CollUtil.isEmpty(entity.getParamParse()) ? null : com.alibaba.fastjson.JSON.toJSONString(entity.getParamParse()))"),
            @Mapping(target = "urlParse",expression = "java(java.util.Objects.nonNull(entity.getUrlParse()) ? com.alibaba.fastjson.JSON.toJSONString(entity.getUrlParse()) : null)"),
            @Mapping(source = "bell", target = "bell", qualifiedByName = "convertInteger"),
            @Mapping(target = "extend",expression = "java(java.util.Objects.isNull(entity.getExtend()) ? null : com.alibaba.fastjson.JSON.toJSONString(entity.getExtend()))"),

    })
    JdhReachTemplatePo entity2Po(JdhReachTemplate entity);

    /**
     * 转换布尔值
     *
     * @param source 源
     * @return {@link Boolean}
     */
    @Named("convertBoolean")
    default Boolean convertBoolean(Integer source){
        if(Objects.isNull(source)){
            return false;
        }
        return BooleanUtils.toBoolean(source);
    }

    /**
     * 转换Integer
     *
     * @param source 源
     * @return {@link Boolean}
     */
    @Named("convertInteger")
    default Integer convertInteger(Boolean source){
        if(Objects.isNull(source)){
            return NumConstant.NUM_0;
        }
        return BooleanUtils.toInteger(source);
    }

}
