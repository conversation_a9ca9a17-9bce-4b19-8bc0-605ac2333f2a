package com.jdh.o2oservice.infrastructure.repository.db.po;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.util.Date;
@Data
@TableName(value = "jdh_angel_service_record",autoResultMap = true)
public class JdhAngelServiceRecordPo extends JdhBasicPo {

    /**
     * 自增主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 垂直业务身份编码
     */
    private String verticalCode;

    /**
     * 服务类型
     */
    private String serviceType;

    /**
     * 服务记录ID
     */
    private Long recordId;

    /**
     * 任务单ID
     */
    private Long taskId;

    /**
     * 服务工单Id
     */
    private Long workId;

    /**
     * 履约单Id
     */
    private Long promiseId;

    /**
     * 服务者Id
     */
    private String angelId;

    /**
     * 服务的项目ID明细
     */
    private String serviceItemIdList;

    /**
     * 初始状态：0、完成:1、 取消：2、评估结果高风险：-1
     */
    private Integer status;

    /**
     * 已完成的最后一个节点
     */
    private String lastFlowNode;

    /**
     * 是否有效 1有效 0无效
     */
    private Integer yn;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 数据来源分支
     */
    private String branch;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人
     */
    private String updateUser;

    /**
     * 修改时间
     */
    private Date updateTime;

}