package com.jdh.o2oservice.infrastructure.repository.db.convert;

import com.jdh.o2oservice.core.domain.product.model.JdhQuestion;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhQuestionPo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/25
 * @description 题库
 */
@Mapper
public interface JdhQuestionPoConverter {

    JdhQuestionPoConverter INSTANCE = Mappers.getMapper(JdhQuestionPoConverter.class);

    JdhQuestion toJdhQuestion(JdhQuestionPo jdhQuestionPo);

    JdhQuestionPo toJdhQuestionPo(JdhQuestion entity);

    List<JdhQuestion> toJdhQuestions(List<JdhQuestionPo> jdhQuestionPos);
}
