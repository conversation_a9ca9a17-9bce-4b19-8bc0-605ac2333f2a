package com.jdh.o2oservice.infrastructure.repository.db.convert;

import com.jdh.o2oservice.core.domain.settlement.model.ExternalDomainFeeConfig;
import com.jdh.o2oservice.core.domain.settlement.model.JdhSettlementFeeDetailConfig;
import com.jdh.o2oservice.infrastructure.repository.db.po.ExternalDomainFeeConfigPo;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhSettlementFeeDetailConfigPo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @ClassName JdhExternalDomainFeePoConverter
 * @Description
 * <AUTHOR>
 * @Date 2025/4/25 10:49
 **/
@Mapper
public interface JdhExternalDomainFeePoConverter {

    JdhExternalDomainFeePoConverter INSTANCE = Mappers.getMapper(JdhExternalDomainFeePoConverter.class);

    /**
     *
     * @param po
     * @return
     */
    @Mapping(target = "externalDomainCode", source = "domainCode")
    @Mapping(target = "externalAggregateCode", source = "aggregateCode")
    ExternalDomainFeeConfig po2Entity(ExternalDomainFeeConfigPo po);

    /**
     *
     * @param poList
     * @return
     */
    List<ExternalDomainFeeConfig> po2Entity(List<ExternalDomainFeeConfigPo> poList);

    /**
     *
     * @param feeConfig
     * @return
     */
    @Mapping(target = "domainCode", source = "externalDomainCode")
    @Mapping(target = "aggregateCode", source = "externalAggregateCode")
    ExternalDomainFeeConfigPo entity2Po(ExternalDomainFeeConfig feeConfig);

    /**
     * po2DetailEntity
     * @param poList
     * @return
     */
    List<JdhSettlementFeeDetailConfig> po2DetailEntity(List<JdhSettlementFeeDetailConfigPo> poList);
}