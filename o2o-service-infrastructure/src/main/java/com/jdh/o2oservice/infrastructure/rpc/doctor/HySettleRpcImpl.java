package com.jdh.o2oservice.infrastructure.rpc.doctor;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jd.medicine.b2c.base.export.domain.JsfResult;
import com.jd.medicine.settlement.center.export.dto.BankCardDetailDTO;
import com.jd.medicine.settlement.center.export.param.BankCardDetailParam;
import com.jd.medicine.settlement.center.export.param.withdraw.*;
import com.jd.medicine.settlement.center.export.service.app.BankCardEasyExportService;
import com.jd.medicine.settlement.center.export.service.app.DoctorWithdrawExportService;
import com.jd.ump.profiler.util.StringUtil;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.exception.ArgumentsException;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.SystemException;
import com.jdh.o2oservice.base.exception.errorcode.DynamicErrorCode;
import com.jdh.o2oservice.base.exception.errorcode.SystemErrorCode;
import com.jdh.o2oservice.core.domain.settlement.context.AngelSettlementQueryContext;
import com.jdh.o2oservice.core.domain.settlement.enums.SettleErrorCode;
import com.jdh.o2oservice.core.domain.settlement.rpc.HySettleRpc;
import com.jdh.o2oservice.core.domain.settlement.vo.*;
import com.jdh.o2oservice.infrastructure.rpc.convert.HySettleRpcConvert;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Objects;

/**
 * @Author: duanqiaona1
 * @Date: 2024/5/15 2:39 下午
 * @Description:
 */
@Service
@Slf4j
public class HySettleRpcImpl implements HySettleRpc {
    /**
     *
     */
    @Resource
    private BankCardEasyExportService bankCardEasyExportService;
    /**
     *
     */
    @Resource
    private DoctorWithdrawExportService doctorWithdrawExportService;

    /**
     * 费用承担部门ID
     */
    private String costDepartId = "********";
    /**
     * 费用承担部门名称
     */
    private String costDepartName = "京东健康-消费医疗部-到家快检组";


    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.doctor.HySettleRpcImpl.queryBankCardDetail")
    public BankCardDetailVo queryBankCardDetail(AngelSettlementQueryContext query) {
        try {
            BankCardDetailParam param = HySettleRpcConvert.ins.context2BankCardDetailParam(query);
            JsfResult<BankCardDetailDTO> result = bankCardEasyExportService.queryBankCardDetail(param);
            log.info("HySettleRpcImpl.queryBankCardDetail,param={},res={}", JSONObject.toJSONString(param), JSONObject.toJSONString(result));
            if (Objects.isNull(result) || !result.isSuccess()) {
                throw new BusinessException(SystemErrorCode.UNKNOWN_ERROR.formatDescription(result.getMsg()));
            }
            return HySettleRpcConvert.ins.dto2BankCardDetailVo(result.getData());
        } catch (Exception e) {
            log.error("HySettleRpcImpl.queryBankCardDetail,param={}", JSONObject.toJSONString(query));
            return null;
        }
    }


    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.doctor.HySettleRpcImpl.queryWithdrawAccount")
    public WithdrawAccountVo queryWithdrawAccount(AngelSettlementQueryContext query) {
        try {
            QueryWithdrawAccountParam param = HySettleRpcConvert.ins.context2QueryWithdrawAccountParam(query);
            JsfResult<WithdrawAccountDTO> result = doctorWithdrawExportService.queryWithdrawAccount(param);
            log.info("HySettleRpcImpl.withdraw,queryWithdrawAccount={},res={}", JSONObject.toJSONString(param), JSONObject.toJSONString(result));
            if (Objects.isNull(result) || !result.isSuccess()) {
                throw new ArgumentsException(new DynamicErrorCode(SettleErrorCode.QUERY_ANGEL_SETTLE_FAILED.getCode(), result.getMsg()));
            }
            return HySettleRpcConvert.ins.dto2WithdrawAccountVo(result.getData());
        } catch (Exception e) {
            log.error("HySettleRpcImpl.queryWithdrawAccount,param={}", JSONObject.toJSONString(query),e);
            throw new SystemException(SettleErrorCode.QUERY_ANGEL_SETTLE_FAILED,e.getMessage());
        }
    }


    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.doctor.HySettleRpcImpl.withdraw")
    public Long withdraw(AngelCashOutVo angelCashOutVo) {
        WithdrawParam param = HySettleRpcConvert.ins.context2WithdrawParam(angelCashOutVo);
        JsfResult<Long> result = doctorWithdrawExportService.withdraw(param);
        log.info("HySettleRpcImpl.withdraw,param={},res={}", JSONObject.toJSONString(param), JSONObject.toJSONString(result));
        if (Objects.isNull(result)) {
            return null;
        }
        if (!result.isSuccess()) {
            //FIXME 2025-07-01 由于税务改革，目前不能确定护士的纳税方案，临时提示文案
            if (StringUtils.equals(result.getCode(), "112009")) {
                throw new ArgumentsException(new DynamicErrorCode(SettleErrorCode.ANGEL_SUBMIT_CASH_FAILED.getCode(), "系统升级，暂不支持提现"));
            }
            throw new ArgumentsException(new DynamicErrorCode(SettleErrorCode.ANGEL_SUBMIT_CASH_FAILED.getCode(), result.getMsg()));
        }
        return result.getData();
    }


    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.doctor.HySettleRpcImpl.retryWithdraw")
    public Boolean retryWithdraw(Long settlementNo) {
        try {
            JsfResult<Boolean> result = doctorWithdrawExportService.retryWithdraw(settlementNo);
            log.info("HySettleRpcImpl.retryWithdraw,param={},res={}", JSONObject.toJSONString(settlementNo), JSONObject.toJSONString(result));
            if (Objects.isNull(result) || !result.isSuccess()) {
                throw new ArgumentsException(new DynamicErrorCode(SettleErrorCode.ANGEL_SUBMIT_CASH_FAILED.getCode(), result.getMsg()));
            }
            return result.getData();
        } catch (Exception e) {
            log.error("HySettleRpcImpl.retryWithdraw,param={}", JSONObject.toJSONString(settlementNo));
            return Boolean.FALSE;
        }
    }

    /**
     * 护士账户添加收入
     *
     * @param angelAddAccountAmountVo
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.doctor.HySettleRpcImpl.addWithdrawAccountAmount")
    public Boolean addWithdrawAccountAmount(AngelAddAccountAmountVo angelAddAccountAmountVo) {
        try {
            if(StringUtil.isEmpty(angelAddAccountAmountVo.getCostDepartId()) || StringUtil.isEmpty(angelAddAccountAmountVo.getCostDepartName())){
                angelAddAccountAmountVo.setCostDepartId(costDepartId);
                angelAddAccountAmountVo.setCostDepartName(costDepartName);
            }
            log.info("HySettleRpcImpl.addWithdrawAccountAmount.before.param={}", JSON.toJSONString(angelAddAccountAmountVo));
            AddWithdrawAccountParam param = HySettleRpcConvert.ins.angelAddAccountAmountVo2WithdrawParam(angelAddAccountAmountVo);
            if(BigDecimal.ZERO.compareTo(param.getAmount()) > 0 ){
                param.setDirection(-1);
            }
            log.info("HySettleRpcImpl.addWithdrawAccountAmount.after.param={}", JSON.toJSONString(param));
            JsfResult<Long> result = doctorWithdrawExportService.addWithdrawAccountAmount(param);
            log.info("HySettleRpcImpl.addWithdrawAccountAmount res param={},result={}", JSONObject.toJSONString(param), JSONObject.toJSONString(result));
            if (Objects.isNull(result) || !result.isSuccess()) {
                return Boolean.FALSE;
            }
            return Boolean.TRUE;
        } catch (Exception e) {
            log.error("HySettleRpcImpl.addWithdrawAccountAmount error param={},e", JSONObject.toJSONString(angelAddAccountAmountVo), e);
            return Boolean.FALSE;
        }
    }


    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.doctor.HySettleRpcImpl.queryWithDrawDetail")
    public WithdrawDetailVo queryWithDrawDetail(Long settlementNo) {
        try {
            QueryWithdrawDetailParam detailParam = HySettleRpcConvert.ins.getQueryWithdrawDetailParam(settlementNo);
            JsfResult<WithdrawDetailDTO> result = doctorWithdrawExportService.queryWithDrawDetail(detailParam);
            log.info("HySettleRpcImpl.queryWithDrawDetail,param={},res={}", JSONObject.toJSONString(settlementNo), JSONObject.toJSONString(result));
            if (Objects.isNull(result) || !result.isSuccess()) {
                throw new BusinessException(SystemErrorCode.UNKNOWN_ERROR.formatDescription(result.getMsg()));
            }
            return HySettleRpcConvert.ins.dto2WithdrawDetailVo(result.getData());
        } catch (Exception e) {
            log.error("HySettleRpcImpl.queryWithDrawDetail,param={}", JSONObject.toJSONString(settlementNo));
            return null;
        }
    }


    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.doctor.HySettleRpcImpl.queryWithdrawRecordDetail")
    public WithdrawDetailVo queryWithdrawRecordDetail(Long settlementNo,String userPin,Long accountId) {
        try {
            QueryWithdrawRecordDetailParam detailParam = HySettleRpcConvert.ins.getQueryWithdrawRecordDetailParam(settlementNo);
            detailParam.setPin(userPin);
            detailParam.setAccountId(accountId);
            JsfResult<WithdrawRecordDetailDTO> result = doctorWithdrawExportService.queryWithdrawRecordDetail(detailParam);
            log.info("HySettleRpcImpl.queryWithdrawRecordDetail,param={},res={}", JSONObject.toJSONString(settlementNo), JSONObject.toJSONString(result));
            if (Objects.isNull(result) || !result.isSuccess()) {
                throw new BusinessException(SystemErrorCode.UNKNOWN_ERROR.formatDescription(result.getMsg()));
            }
            return HySettleRpcConvert.ins.withdrawdDto2WithdrawDetailVo(result.getData());
        } catch (Exception e) {
            log.error("HySettleRpcImpl.queryWithdrawRecordDetail,param={}", JSONObject.toJSONString(settlementNo));
            return null;
        }
    }

}
