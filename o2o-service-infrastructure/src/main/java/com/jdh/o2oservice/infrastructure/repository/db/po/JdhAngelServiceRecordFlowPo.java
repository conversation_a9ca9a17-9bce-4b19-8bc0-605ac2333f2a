package com.jdh.o2oservice.infrastructure.repository.db.po;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.util.Date;
@Data
@TableName(value = "jdh_angel_service_record_flow",autoResultMap = true)
public class JdhAngelServiceRecordFlowPo extends JdhBasicPo {

    /**
     * 自增主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 服务记录ID
     */
    private Long recordId;

    /**
     * 服务记录节点ID
     */
    private Long recordFlowId;

    /**
     * 流程节点编码
     */
    private String flowCode;

    /**
     * 流程节点名称
     */
    private String flowName;

    /**
     * 顺序编码
     */
    private Integer sortId;

    /**
     * 初始状态：0，提交通过:1，提交未通过评估高风险状态：-1
     */
    private Integer status;

    /**
     * 节点内容明细
     */
    private String detail;

    /**
     * 是否有效 1有效 0无效
     */
    private Integer yn;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 数据来源分支
     */
    private String branch;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人
     */
    private String updateUser;

    /**
     * 修改时间
     */
    private Date updateTime;

}