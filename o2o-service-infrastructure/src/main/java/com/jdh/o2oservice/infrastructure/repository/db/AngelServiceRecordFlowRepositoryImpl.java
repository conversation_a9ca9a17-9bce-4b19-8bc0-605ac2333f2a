package com.jdh.o2oservice.infrastructure.repository.db;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jdh.o2oservice.base.enums.YnStatusEnum;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelServiceRecordFlow;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelServiceRecordFlowIdentifier;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelServiceRecordFlowRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.query.AngelServiceRecordFlowDBQuery;
import com.jdh.o2oservice.infrastructure.repository.db.convert.AngelServiceRecordFlowPoConverter;
import com.jdh.o2oservice.infrastructure.repository.db.convert.JdhBasicPoConverter;
import com.jdh.o2oservice.infrastructure.repository.db.dao.JdhAngelServiceRecordFlowPoMapper;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhAngelServiceRecordFlowPo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Component
@Slf4j
public class AngelServiceRecordFlowRepositoryImpl implements AngelServiceRecordFlowRepository {

    @Resource
    private JdhAngelServiceRecordFlowPoMapper jdhAngelServiceRecordFlowPoMapper;

    @Override
    public int remove(AngelServiceRecordFlow entity) {
        LambdaUpdateWrapper<JdhAngelServiceRecordFlowPo> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.setSql("`version` = `version` + 1").set(JdhAngelServiceRecordFlowPo::getYn, YnStatusEnum.NO.getCode())
                .set(JdhAngelServiceRecordFlowPo::getUpdateTime, new Date())
                .eq(JdhAngelServiceRecordFlowPo::getRecordFlowId, entity.getRecordFlowId());
        return jdhAngelServiceRecordFlowPoMapper.update(null, updateWrapper);
    }

    /**
     * 查询服务记录流程节点列表
     * @param query
     * @return
     */
    @Override
    public List<AngelServiceRecordFlow> findList(AngelServiceRecordFlowDBQuery query) {
        LambdaQueryWrapper<JdhAngelServiceRecordFlowPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(Objects.nonNull(query.getRecordId()), JdhAngelServiceRecordFlowPo::getRecordId, query.getRecordId())
                .eq(Objects.nonNull(query.getRecordFlowId()), JdhAngelServiceRecordFlowPo::getRecordFlowId, query.getRecordFlowId())
                .eq(StringUtils.isNotBlank(query.getFlowCode()), JdhAngelServiceRecordFlowPo::getFlowCode, query.getFlowCode())
                .eq(Objects.nonNull(query.getStatus()), JdhAngelServiceRecordFlowPo::getStatus, query.getStatus())
                .eq(JdhAngelServiceRecordFlowPo::getYn, YnStatusEnum.YES.getCode())
                .orderByAsc(JdhAngelServiceRecordFlowPo::getSortId);

        List<JdhAngelServiceRecordFlowPo> poList = jdhAngelServiceRecordFlowPoMapper.selectList(queryWrapper);
        return AngelServiceRecordFlowPoConverter.INS.convertToAngelServiceRecordFlowList(poList);
    }

    /**
     * 保存/更新
     * @param entity
     * @return
     */
    @Override
    public int save(AngelServiceRecordFlow entity) {
        if (Objects.isNull(entity)){
            return 0;
        }
        JdhAngelServiceRecordFlowPo angelServiceRecordFlowPo = AngelServiceRecordFlowPoConverter.INS.convertToAngelServiceRecordFlowPo(entity);
        log.info("AngelServiceRecordFlowRepositoryImpl -> save angelServiceRecordFlowPo={}", JSON.toJSONString(angelServiceRecordFlowPo));
        if (Objects.isNull(entity.getId())){
            JdhBasicPoConverter.initInsertBasicPo(angelServiceRecordFlowPo);
            return jdhAngelServiceRecordFlowPoMapper.insert(angelServiceRecordFlowPo);
        }else {
            LambdaUpdateWrapper<JdhAngelServiceRecordFlowPo> updateWrapper = Wrappers.lambdaUpdate();
            updateWrapper.eq(JdhAngelServiceRecordFlowPo::getRecordFlowId, entity.getRecordFlowId())
                    .set(Objects.nonNull(entity.getStatus()), JdhAngelServiceRecordFlowPo::getStatus, entity.getStatus())
                    .set(Objects.nonNull(entity.getSortId()), JdhAngelServiceRecordFlowPo::getSortId, entity.getSortId())
                    .set(StringUtils.isNotBlank(entity.getDetail()), JdhAngelServiceRecordFlowPo::getDetail, entity.getDetail())
                    .setSql("`update_time` = now()")
                    .setSql("`version` = version+1");
            return jdhAngelServiceRecordFlowPoMapper.update(null, updateWrapper);
        }
    }

    /**
     * 通过Identify 查询
     * @param identifier
     * @return
     */
    @Override
    public AngelServiceRecordFlow find(AngelServiceRecordFlowIdentifier identifier) {
        LambdaQueryWrapper<JdhAngelServiceRecordFlowPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(JdhAngelServiceRecordFlowPo::getRecordFlowId, identifier.getRecordFlowId())
                .eq(JdhAngelServiceRecordFlowPo::getYn, YnStatusEnum.YES.getCode());
        JdhAngelServiceRecordFlowPo po = jdhAngelServiceRecordFlowPoMapper.selectOne(queryWrapper);
        return AngelServiceRecordFlowPoConverter.INS.convertToAngelServiceRecordFlow(po);
    }
}
