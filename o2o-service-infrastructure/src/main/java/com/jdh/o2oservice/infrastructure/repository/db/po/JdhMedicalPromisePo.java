package com.jdh.o2oservice.infrastructure.repository.db.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * @Description: 检测单Po
 * @Author: wangpengfei144
 * @Date: 2024/4/16
 */
@Data
@TableName(value = "jdh_medical_promise",autoResultMap = true)
public class JdhMedicalPromisePo extends JdhBasicPo{

    /** 主键 */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /** 检测单ID */
    private Long medicalPromiseId;

    /** 垂直业务身份编码 */
    private String verticalCode;

    /** 服务类型 */
    private String serviceType;

    /** 服务ID */
    private Long serviceId;

    /** 用户pin */
    private String userPin;

    /** 履约单ID */
    private Long promiseId;

    /** 服务单ID */
    private Long voucherId;

    /** 用户唯一ID */
    private Long promisePatientId;

    /** 检测单状态：待检测/检测中/已出报告 */
    private Integer status;

    /** 样本条码 */
    private String specimenCode;

    /** 检测项目信息 */
    private String serviceItemId;

    /** 检测项目名称 */
    private String serviceItemName;

    /** 供应商渠道编码 */
    private Long providerId;

    /** 服务地点id */
    private String stationId;

    /** 服务地点详细地址 */
    private String stationAddress;

    /** 服务地点名称 */
    private String stationName;

    /** 服务地点联系方式 */
    private String stationPhone;

    /**
     * 是否冻结， 1冻结 0未冻结
     */
    private Integer freeze;
    /**
     * 结算状态 0-结算，1-已结算
     */
    private Integer settleStatus;
    /**
     * 检测时间
     */
    private Date checkTime;
    /**
     * 报告状态，1.已出，0.未出
     */
    private Integer reportStatus;
    /**
     * 供应商预约单no
     */
    private String outerId;

    /**
     * 服务站Id
     */
    private String angelStationId;

    /**
     * 服务站名称
     */
    private String angelStationName;
    /**
     * 序号
     */
    private String serialNum;
    /**
     * 报告时间
     */
    private Date reportTime;
    /**
     * 检测状态
     */
    private Integer checkStatus;

    /**
     * 检测单特殊标记（0.普通订单，1.加项订单）
     */
    private String flag;

    /** 合管检测单ID */
    private Long mergeMedicalId;

    /**
     * 待收样超时绝对时间
     */
    private Date waitingTestTimeOutDate;

    /**
     * 是否待收样超时
     * 0 - 否 1 - 是
     */
    private Integer waitingTestTimeOutStatus;

    /**
     * 检测超时绝对时间
     */
    private Date testingTimeOutDate;

    /**
     * 是否检测超时
     * 0 - 否 1 - 是
     */
    private Integer testingTimeOutStatus;

    /**
     * 是否测试单 1是 0 null 不是
     */
    private Integer isTest;

    /**
     * 报告展示类型，1.结构化，2.PDF
     */
    private Integer reportShowType;
    /**
     * 检测单配送步骤
     */
    private String deliveryStepFlow;


}
