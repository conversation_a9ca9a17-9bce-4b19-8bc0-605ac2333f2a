package com.jdh.o2oservice.infrastructure.repository.db.convert;

import com.jdh.o2oservice.core.domain.product.model.JdhGroupQuesRel;
import com.jdh.o2oservice.core.domain.product.model.JdhItemQuesGroupRel;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhGroupQuesRelPo;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhItemQuesGroupRelPo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/25
 * @description 题库
 */
@Mapper
public interface JdhGroupQuesRelConverter {

    JdhGroupQuesRelConverter INSTANCE = Mappers.getMapper(JdhGroupQuesRelConverter.class);


    JdhGroupQuesRel toJdhGroupQuesRel(JdhGroupQuesRelPo jdhGroupQuesRelPo);

    JdhGroupQuesRelPo toJdhGroupQuesRelPo(JdhGroupQuesRel entity);

    List<JdhGroupQuesRel> toJdhGroupQuesRels(List<JdhGroupQuesRelPo> jdhGroupQuesRelPos);
}
