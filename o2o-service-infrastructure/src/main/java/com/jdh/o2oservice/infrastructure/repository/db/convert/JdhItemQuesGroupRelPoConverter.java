package com.jdh.o2oservice.infrastructure.repository.db.convert;

import com.jdh.o2oservice.core.domain.product.model.JdhGroupQuesRel;
import com.jdh.o2oservice.core.domain.product.model.JdhItemQuesGroupRel;
import com.jdh.o2oservice.core.domain.product.model.JdhQuestion;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhGroupQuesRelPo;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhItemQuesGroupRelPo;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhQuestionPo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/25
 * @description 题库
 */
@Mapper
public interface JdhItemQuesGroupRelPoConverter {

    JdhItemQuesGroupRelPoConverter INSTANCE = Mappers.getMapper(JdhItemQuesGroupRelPoConverter.class);

    List<JdhItemQuesGroupRel> toJdhItemQuesGroupRels(List<JdhItemQuesGroupRelPo> jdhItemQuesGroupRelPos);

    JdhItemQuesGroupRel toJdhItemQuesGroupRel(JdhItemQuesGroupRelPo jdhItemQuesGroupRelPo);

    JdhItemQuesGroupRelPo toJdhItemQuesGroupRelPo(JdhItemQuesGroupRel entity);

}
