package com.jdh.o2oservice.infrastructure.rpc.reach;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jd.medicine.b2c.base.export.domain.Result;
import com.jd.medicine.b2c.base.export.domain.RxClientDTO;
import com.jd.newnethp.diag.message.center.export.MsgChannelExport;
import com.jd.newnethp.diag.message.center.export.MsgSendRuleExport;
import com.jd.newnethp.diag.message.center.export.dto.NethpMessageSendRuleDTO;
import com.jd.newnethp.diag.message.center.export.dto.dongdong.EventMessage;
import com.jd.newnethp.diag.message.center.export.dto.dongdong.base.ChatMessageUser;
import com.jd.newnethp.diag.message.center.export.dto.msgchannel.JdPushMsgChannelDTO;
import com.jd.newnethp.diag.message.center.export.dto.msgchannel.SmsMsgChannelDTO;
import com.jd.newnethp.diag.message.center.export.dto.queryparam.MessageSwitchParam;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.exception.SystemException;
import com.jdh.o2oservice.base.exception.errorcode.SystemErrorCode;
import com.jdh.o2oservice.core.domain.support.basic.rpc.ShortUrlRpc;
import com.jdh.o2oservice.core.domain.support.reach.context.ReachTemplateParamBO;
import com.jdh.o2oservice.core.domain.support.reach.model.JdhReachMessage;
import com.jdh.o2oservice.core.domain.support.reach.repository.rpc.bo.BatchSendSmsBO;
import com.jdh.o2oservice.core.domain.support.reach.rpc.NewnethpDiagMsgRpc;
import com.jdh.o2oservice.core.domain.support.reach.rpc.bo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.net.InetAddress;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.MessageFormat;
import java.util.*;

/**
 * 消息推送RPC
 * 互医消息推送配置白名单：http://nethp-ui-test.jd.com/nethp/trans/server
 * @author: yangxiyu
 * @date: 2024/4/22 2:01 下午
 * @version: 1.0
 */
@Component
@Slf4j
public class NewnethpDiagMsgRpcImpl implements NewnethpDiagMsgRpc {

    /** 业务成功状态码 */
    private static final String SUCCESS_CODE = "0000";
    /** 互医分配的渠道编号 */
    private static final String BUS_CHANNEL = "xiaoyi";
    /** 消息来源APP，互医分配 */
    private static final String FROM_APP = "jd.doctor";
    /** 消息触达APP，互医分配 */
    private static final String TO_APP = "jd.doctor";
    /** 履约中台应用标识 */
    private static final String JDOS_APPLICATION = "jdh-o2o-service";
    /** 通道扩展参数 */
    private static final String CLIENT_EXTRA_PARAMS;
    /** 互医分配的模版ID */
    private static final String NEWNETHP_DIAG_TEMPLATE_ID = "8888_1";
    /** 站内信需要唤起横幅的通知，也会有铃声震动 */
    private static final String NOTIFY_BANNER = "doctor_landNotify";
    /** 站内信仅通知（无横幅） */
    private static final String NOTIFY_SIMPLE = "notice_message_only";
    /** 唯一消息ID格式化，需要加上互医分配的渠道编号 */
    private static final String MSG_ID_FORMAT ="{0}_{1}";
    /** 医生APP转换h5链接的前缀 */
    private static final String SMS_OPEN_APP_LINK_PRE = "https://app.yiyaojd.com/doctor-message/home?category=jump&des=nurse_m&decoded=1&messageId={0}&url={1}";
    private static final String PUSH_OPEN_APP_FORMAT= "openApp.jdDoctor://virtual?params={\"des\":\"nurse_m\",\"category\":\"jump\",\"url\":\"${0}\"}";
    static {
        Map<String, String> extraParas = Maps.newHashMap();
        extraParas.put("busChannel", BUS_CHANNEL);
        CLIENT_EXTRA_PARAMS = JSON.toJSONString(extraParas);
    }


    /** 互医消息发送接口 */
    @Resource
    private MsgChannelExport msgChannelExport;

    /**
     * 互医医生端消息配置规则
     */
    @Resource
    private MsgSendRuleExport msgSendRuleExport;
    /**
     *
     */
    @Resource
    private ShortUrlRpc shortUrlRpc;


    public static void main(String[] args) {
        System.out.println(PUSH_OPEN_APP_FORMAT.replace("${0}", "asdasfa"));
    }

    @SuppressWarnings("JdJDSuspectedTestData")
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.reach.NewnethpDiagMsgRpcImpl.pushAppMsg")
    public ReachSendResult pushAppMsg(PushAppMsgBo param) {

        RxClientDTO clientDTO = initClient();

        JdPushMsgChannelDTO msgChannelDTO = new JdPushMsgChannelDTO();
        msgChannelDTO.setMsgId(MessageFormat.format(MSG_ID_FORMAT, clientDTO.getClient(), param.getMessageId().toString()));
        msgChannelDTO.setTitle(param.getTitle());
        // 超过100 需要截断
        msgChannelDTO.setContent(param.getContent());
        msgChannelDTO.setTemplateId(NEWNETHP_DIAG_TEMPLATE_ID);
        msgChannelDTO.setToUserPin(param.getToUserPin());

        msgChannelDTO.setExtras(param.getExtras());
        Map<String, String> extras = msgChannelDTO.getExtras();
        if (extras == null) {
            extras = new HashMap<>();
            msgChannelDTO.setExtras(extras);
        }
        attachVoiceParam(extras, param.getUseVoice(), param.getPushVoiceConfig(), param.getToUserPin());

        if (StringUtils.isNotBlank(param.getUri())){
            try {
                String url = URLEncoder.encode(param.getUri(), "UTF-8");
                msgChannelDTO.setUrl(PUSH_OPEN_APP_FORMAT.replace("${0}", url));
            }catch (UnsupportedEncodingException e){
                log.error("NewnethpDiagMsgRpcImpl->sendJdPushMsg url encode error url={}", param.getUri());
                throw new SystemException(SystemErrorCode.CONFIG_ERROR.formatDescription("url 配置错误"));
            }
        }

        ReachSendResult reachSendResult = new ReachSendResult();
        try {
            log.info("NewnethpDiagMsgRpcImpl->sendJdPushMsg param clientDTO={}, smsMsgChannelDTO={}", JSON.toJSONString(clientDTO), JSON.toJSONString(msgChannelDTO));
            Result<Boolean> result = msgChannelExport.sendJdPushMsg(clientDTO, msgChannelDTO);
            log.info("NewnethpDiagMsgRpcImpl->sendJdPushMsg result={}", JSON.toJSONString(result));
            if (!StringUtils.equals(result.getCode(), SUCCESS_CODE)) {
                log.warn("NewnethpDiagMsgRpcImpl->pushAppMsg error result={}", JSON.toJSONString(result));
                reachSendResult.setSuccess(Boolean.FALSE);
                reachSendResult.setErrorInfo(result.getMsg());
            }else {
                reachSendResult.setSuccess(Boolean.TRUE);
            }
        }catch (Throwable e){
            log.error("NewnethpDiagMsgRpcImpl->pushAppMsg error ", e);
            reachSendResult.setSuccess(Boolean.FALSE);
            reachSendResult.setErrorInfo(e.getMessage());
        }
        return reachSendResult;
    }

    /**
     * 组装语音播报参数
     * @param extras
     * @param useVoice
     * @param pushVoiceConfig
     * @param userPin
     */
    private void attachVoiceParam(Map<String, String> extras, Integer useVoice, PushVoiceConfigBO pushVoiceConfig, String userPin) {
        extras.put("voiceNoticeSwitch", "0");
        //如果配置需要语音播报，组装语音播报参数
        if (Objects.equals(CommonConstant.ONE, useVoice) && Objects.nonNull(pushVoiceConfig)) {
            //查询护士客户端语音播报开关是否开启
            //messageSwitchEnum == NEW_ORDER_VOICE_NOTICE，然后取status ==1，开启，其他或者为空，是未开启
            List<NethpMessageSendRuleBo> sendRuleBos = getMessageSwitch(MessageSwitchQuery.builder().userPin(userPin).userType("1").channelId("voice_notice").messageSwitchEnum("NEW_ORDER_VOICE_NOTICE").build());
            Optional<NethpMessageSendRuleBo> first = sendRuleBos.stream().filter(rule -> StringUtils.equals(rule.getMessageSwitchEnum(), "NEW_ORDER_VOICE_NOTICE") && Objects.equals(rule.getStatus(), 1)).findFirst();
            if (first.isPresent()) {
                extras.put("voiceNoticeSwitch", "1");
                extras.put("voiceNoticeFile", pushVoiceConfig.getVoiceNoticeFile());
            }
        }
    }

    /**
     * 推送站内信通知
     * 接口：https://joyspace.jd.com/pages/16OdToqj8Pcf0Eun0pdy
     * @param msgBo
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.reach.NewnethpDiagMsgRpcImpl.pushInAppMsg")
    public ReachSendResult pushInAppMsg(PushInAppMsgBo msgBo) {

        RxClientDTO clientDTO = initClient();
        JdhReachMessage message = msgBo.getMessage();

        String msgId = MessageFormat.format(MSG_ID_FORMAT, clientDTO.getClient(), message.getMessageId().toString());
        EventMessage eventMessage = new EventMessage();
        eventMessage.setMsgId(msgId);
        eventMessage.setFromApp(FROM_APP);
        eventMessage.setFromPin(JDOS_APPLICATION);
        eventMessage.setToApp(TO_APP);

        List<ChatMessageUser> messageUserList = Lists.newArrayList();
        ChatMessageUser user = new ChatMessageUser();
        user.setApp("jd.doctor");
        user.setPin(message.getUserPin());
        messageUserList.add(user);
        eventMessage.setTos(messageUserList);

        Map<String,String> msgload = Maps.newHashMap();
        msgload.put("messageText", message.getMessagePayload());
        if (Objects.nonNull(msgBo.getBell()) && msgBo.getBell()){
            msgload.put("sendType", NOTIFY_BANNER);
        }else{
            msgload.put("sendType", NOTIFY_SIMPLE);
        }
        msgload.put("msgId", msgId);
        msgload.put("busChannel", BUS_CHANNEL);
        //如果配置需要语音播报，组装语音播报参数
        attachVoiceParam(msgload, msgBo.getUseVoice(), msgBo.getPushVoiceConfig(), message.getUserPin());
        eventMessage.setMsgload(msgload);



        ReachSendResult reachSendResult = new ReachSendResult();
        try {
            log.info("NewnethpDiagMsgRpcImpl->sendEventMessage param clientDTO={}, smsMsgChannelDTO={}", JSON.toJSONString(clientDTO), JSON.toJSONString(eventMessage));
            Result<Boolean> result = msgChannelExport.sendEventMessage(clientDTO, eventMessage);
            log.info("NewnethpDiagMsgRpcImpl->sendEventMessage result={}", JSON.toJSONString(result));
            if (!StringUtils.equals(result.getCode(), SUCCESS_CODE)) {
                log.warn("NewnethpDiagMsgRpcImpl->pushInAppMsg error result={}", JSON.toJSONString(result));
                reachSendResult.setSuccess(Boolean.FALSE);
                reachSendResult.setErrorInfo(result.getMsg());
            } else {
                reachSendResult.setSuccess(Boolean.TRUE);
            }
        }catch (Throwable e){
            log.error("NewnethpDiagMsgRpcImpl->pushInAppMsg error", e);
            reachSendResult.setSuccess(Boolean.FALSE);
            reachSendResult.setErrorInfo(e.getMessage());
        }

        return reachSendResult;
    }

    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.reach.NewnethpDiagMsgRpcImpl.sendSms")
    public ReachSendResult sendSms(BatchSendSmsBO sendSmsBO) {
        RxClientDTO clientDTO = initClient();
        SmsMsgChannelDTO smsMsgChannelDTO = new SmsMsgChannelDTO();
        smsMsgChannelDTO.setMsgId(MessageFormat.format(MSG_ID_FORMAT, clientDTO.getClient(), sendSmsBO.getMessageId().toString()));
        smsMsgChannelDTO.setType("2");
        smsMsgChannelDTO.setTemplateId(String.valueOf(sendSmsBO.getTemplateId()));
        smsMsgChannelDTO.setPhoneNo(sendSmsBO.getPhone());

        smsMsgChannelDTO.setData(buildSmsParam(sendSmsBO, smsMsgChannelDTO));

        ReachSendResult reachSendResult = new ReachSendResult();
        try {
            log.info("NewnethpDiagMsgRpcImpl->sendSmsMsg param clientDTO={}, smsMsgChannelDTO={}", JSON.toJSONString(clientDTO), JSON.toJSONString(smsMsgChannelDTO));
            Result<Boolean> result = msgChannelExport.sendSmsMsg(clientDTO, smsMsgChannelDTO);
            log.info("NewnethpDiagMsgRpcImpl->sendSmsMsg result={}", JSON.toJSONString(result));

            if (!StringUtils.equals(result.getCode(), SUCCESS_CODE)) {
                log.warn("NewnethpDiagMsgRpcImpl->sendSms error result={}", JSON.toJSONString(result));
                reachSendResult.setSuccess(Boolean.FALSE);
                reachSendResult.setErrorInfo(result.getMsg());
            }else {
                reachSendResult.setSuccess(Boolean.TRUE);
            }
        }catch (Throwable e){
            log.error("NewnethpDiagMsgRpcImpl->sendSms error", e);
            reachSendResult.setSuccess(Boolean.FALSE);
            reachSendResult.setErrorInfo(e.getMessage());
        }
        return reachSendResult;
    }

    /**
     * 查询医生端消息配置规则
     * @param query
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.reach.NewnethpDiagMsgRpcImpl.getMessageSwitch")
    public List<NethpMessageSendRuleBo> getMessageSwitch(MessageSwitchQuery query){
        RxClientDTO clientDTO = initClient();
        MessageSwitchParam messageSwitchParam = new MessageSwitchParam();
        BeanUtils.copyProperties(query, messageSwitchParam);

        List<NethpMessageSendRuleBo> result = new ArrayList<>();
        try {
            log.info("NewnethpDiagMsgRpcImpl -> getMessageSwitch param clientDTO={}, messageSwitchParam={}", JSON.toJSONString(clientDTO), JSON.toJSONString(messageSwitchParam));
            Result<List<NethpMessageSendRuleDTO>> sendRuleResult = msgSendRuleExport.getMessageSwitch(clientDTO, messageSwitchParam);
            log.info("NewnethpDiagMsgRpcImpl -> getMessageSwitch result={}", JSON.toJSONString(sendRuleResult));
            if (StringUtils.equals(sendRuleResult.getCode(), SUCCESS_CODE) && CollectionUtils.isNotEmpty(sendRuleResult.getData())) {
                sendRuleResult.getData().forEach(dto -> {
                    NethpMessageSendRuleBo bo = new NethpMessageSendRuleBo();
                    BeanUtils.copyProperties(dto, bo);
                    result.add(bo);
                });
            }
        }catch (Throwable e){
            log.error("NewnethpDiagMsgRpcImpl -> getMessageSwitch error ", e);
        }
        return result;
    }

    /**
     * 1、因为医生APP的短信需要openApp协议打开，短链平台不支持openApp协议链接，所以需要配置一个固定的医生APP提供的H5链接前缀
     * 文档：https://cf.jd.com/pages/viewpage.action?pageId=629985011
     * @return
     */
    private List<String> buildSmsParam(BatchSendSmsBO sendSmsBO, SmsMsgChannelDTO smsMsgChannelDTO){
        if (ArrayUtils.isEmpty(sendSmsBO.getTemplateParam())){
            return Collections.emptyList();
        }
        List<String> params = Lists.newArrayList();
        for (ReachTemplateParamBO paramBO : sendSmsBO.getTemplateParam()) {
            if (paramBO.isOpenApp()){
                String urlBase64= Base64.getEncoder().encodeToString(paramBO.getValue().getBytes(StandardCharsets.UTF_8));
                String url = MessageFormat.format(SMS_OPEN_APP_LINK_PRE, smsMsgChannelDTO.getMsgId(), urlBase64);
                String value=  shortUrlRpc.generateShortUrl(url);
                params.add(value);
            }else {
                params.add(paramBO.getValue());
            }
        }
        return params;
    }

    /**
     * 构建站内消息推送参数
     * @param
     * @return
     */
    private static RxClientDTO initClient(){

        RxClientDTO clientDTO = new RxClientDTO();
        clientDTO.setClient(JDOS_APPLICATION);
        clientDTO.setExtraParas(CLIENT_EXTRA_PARAMS);
        try {
            InetAddress localHost = InetAddress.getLocalHost();
            String localIP = localHost.getHostAddress();
            clientDTO.setServerIp(localIP);
        }catch (Exception e){
            log.error("NewnethpDiagMsgRpcImpl->initClient getHostAddress error msg={}", e.getMessage());
        }
        return clientDTO;
    }
}
