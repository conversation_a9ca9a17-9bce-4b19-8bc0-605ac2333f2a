package com.jdh.o2oservice.infrastructure.repository.db.po;

import java.util.Date;
import lombok.*;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * 
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ToString
@TableName("jdh_store_transfer_station")
public class JdhStoreTransferStationPo extends JdhBasicPo {

    /** 主键 */
    @TableId
    private Long id;

    /** 接驳点类型 1-实验室自收样  2-无人机 */
    @TableField("station_type")
    private Integer stationType;

    /** 京东接驳点id */
    @TableField("jd_station_id")
    private Long jdStationId;

    /** 京东接驳点名称 */
    @TableField("jd_station_name")
    private String jdStationName;

    /** 京东接驳点地址 */
    @TableField("jd_station_address")
    private String jdStationAddress;

    /** 京东接驳点维度 */
    @TableField("jd_station_latitude")
    private Double jdStationLatitude;

    /** 京东接驳点经度 */
    @TableField("jd_station_longitude")
    private Double jdStationLongitude;

    /** 下一个京东站点id */
    @TableField("jd_station_target_id")
    private Long jdStationTargetId;

    /** 外部系统接驳点id */
    @TableField("third_station_id")
    private String thirdStationId;

    /** 下一个外部系统接驳点id */
    @TableField("third_station_target_id")
    private String thirdStationTargetId;

    /** 创建时间 */
    @TableField("create_time")
    private Date createTime;

    /** 创建人 */
    @TableField("create_user")
    private String createUser;

    /** 更新时间 */
    @TableField("update_time")
    private Date updateTime;

    /** 修改人 */
    @TableField("update_user")
    private String updateUser;

    /** 有效标志 0 无效 1有效 */
    @TableField("yn")
    private Integer yn;

    /** 数据来源分支 */
    @TableField("branch")
    private String branch;

    /** 版本号 */
    @TableField("version")
    private Integer version;
}