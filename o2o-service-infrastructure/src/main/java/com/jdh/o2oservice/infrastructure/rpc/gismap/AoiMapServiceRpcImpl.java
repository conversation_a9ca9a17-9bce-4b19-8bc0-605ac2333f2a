package com.jdh.o2oservice.infrastructure.rpc.gismap;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Maps;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.ducc.model.AoiMapTimeoutConfig;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode;
import com.jdh.o2oservice.base.util.O2oHttpClient;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelPromiseBizErrorCode;
import com.jdh.o2oservice.core.domain.support.gismap.AoiMapServiceRpc;
import com.jdh.o2oservice.core.domain.support.gismap.bo.*;
import com.jdh.o2oservice.core.domain.support.gismap.response.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.config.RequestConfig;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @ClassName:AoiMapServiceImpl
 * @Description:
 * @Author: yaoqinghai
 * @Date: 2024/4/26 11:02
 * @Vserion: 1.0
 **/
@Component
@Slf4j
public class AoiMapServiceRpcImpl implements AoiMapServiceRpc {

    @Value("${gis.map.appkey}")
    private String appKey;

    @Value("${gis.map.scene}")
    private String scene;

    @Value("${gis.map.baseurl}")
    private String baseUrl;

    @Value("${gis.map.tag}")
    private String tag;

    /**
     * 经纬度转换
     */
    private String baseUrl2 = "https://api.jdl.com";

    private static final Integer SUCCESS_CODE = 200;

    /**
     * 请求超时配置
     */
    private static RequestConfig requestConfig;

    @Resource
    private DuccConfig duccConfig;

    /**
     * 创建地图
     *
     * @param createMapBo
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "AoiMapServiceImpl.createMap")
    public CreateMapResponse createMap(CreateMapBo createMapBo) {
        String path = "/hope/addMap?LOP-DN=hope.map.jd.com";
        CreateMapResponse createMapResponse = doPost("addMap", path, createMapBo, CreateMapResponse.class);
        return createMapResponse;
    }

    /**
     * 创建图层
     *
     * @param createLayerBo
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "AoiMapServiceImpl.createLayer")
    public CreateLayerResponse createLayer(CreateLayerBo createLayerBo) {
        String path = "/hope/saveLayer?LOP-DN=hope.map.jd.com";
        String layerId = doPost("saveLayer", path, createLayerBo, String.class);
        return new CreateLayerResponse(layerId);
    }

    /**
     * 创建围栏
     *
     * @param createElementBo
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "AoiMapServiceImpl.createAngelStation")
    public CreateElementResponse createAngelStation(CreateElementBo createElementBo) {
        String path = "/hope/saveElement?LOP-DN=hope.map.jd.com";
        return doPost("saveElement", path, createElementBo, CreateElementResponse.class);
    }

    /**
     * 修改围栏基础信息
     *
     * @param modifyElementBaseBo
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "AoiMapServiceImpl.modifyAngelStationBase")
    public Boolean modifyAngelStationBase(ModifyElementBaseBo modifyElementBaseBo) {
        String path = "/hope/updateElement?LOP-DN=hope.map.jd.com";
        doPost("updateElement", path, modifyElementBaseBo, String.class);
        return true;
    }

    /**
     * 修改围栏地理数据
     *
     * @param modifyElementDataBo
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "AoiMapServiceRpcImpl.modifyAngelStation")
    public ModifyElementDataResponse modifyAngelStation(ModifyElementDataBo modifyElementDataBo) {
        String path = "/hope/updateElementByGeometry?LOP-DN=hope.map.jd.com";
        return doPost("updateElementByGeometry", path, modifyElementDataBo, ModifyElementDataResponse.class);
    }

    /**
     * 查询围栏数据
     *
     * @param queryElementBo
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "AoiMapServiceRpcImpl.queryElement")
    public QueryElementResponse queryElement(QueryElementBo queryElementBo) {
        String path = "/hope/getElementDetail?LOP-DN=hope.map.jd.com";
        return doPost("getElementDetail", path, queryElementBo, QueryElementResponse.class);
    }

    /**
     * 删除围栏
     *
     * @param removeElementBaseBo
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "AoiMapServiceRpcImpl.removeElement")
    public Boolean removeElement(RemoveElementBaseBo removeElementBaseBo) {
        String path = "/hope/deleteElement?LOP-DN=hope.map.jd.com";
        doPost("deleteElement", path, removeElementBaseBo, String.class);
        return true;
    }

    /**
     * 分单
     *
     * @param queryFenceBo
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "AoiMapServiceRpcImpl.queryElementList")
    public List<QueryFenceResponse> queryElementList(QueryFenceBo queryFenceBo) {
        String path = "/base/presortList?LOP-DN=meta.map.jd.com";
        List list = doPost("presortList", path, queryFenceBo, List.class);
        List<QueryFenceResponse> responseList = Lists.newArrayList();
        if(CollectionUtils.isEmpty(list)){
            return responseList;
        }
        list.stream().forEach(fence -> {
            QueryFenceResponse response = JSON.parseObject(JSON.toJSONString(fence), QueryFenceResponse.class);
            responseList.add(response);
        });
        return responseList;
    }

    /**
     * 与图:经纬度转换
     * https://joyspace.jd.com/h/personal/pages/D3ecYLP8cETHjsGZIwsI
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "AoiMapServiceRpcImpl.geoCoordinateConversion")
    public String geoCoordinateConversion(GeoCoordinateConversionBo geoCoordinateConversionBo){
        String path = "/base/geoCoordinateConversion?LOP-DN=meta.map.jd.com";
        String s = this.doPost(baseUrl2,"geoCoordinateConversion", path, geoCoordinateConversionBo, String.class);
        return s;
    }

    public <T> T doPost(String url,String method, String methodUrl, Object requestObj, Class<T> cls){
        String requestUrl = url.concat(methodUrl);
        log.info("[AoiMapServiceRpcImpl.doPost],开始执行请求, method:{}. url: {}, 参数: {}, tag={}", method, requestUrl, JSON.toJSONString(requestObj), tag);
        Map<String, String> headMap = Maps.newHashMap();
        headMap.put("content-type", "application/json");
        headMap.put("accept", "application/json");
        if(StringUtils.isNotBlank(tag)){
            headMap.put("tag", tag);
        }
        Map<String, Object> paramMap = generateParams2(requestObj);
        String httpResponse;
        if(CollectionUtils.isNotEmpty(duccConfig.getLimitSocketTimeoutPath()) && duccConfig.getLimitSocketTimeoutPath().contains(method)){
            log.info("[AoiMapServiceRpcImpl.doPost]开启超时限制!method={}", method);
            httpResponse = O2oHttpClient.postJson(requestUrl, headMap, JSON.toJSONString(paramMap), getRequestConfig());
        }else {
            log.info("[AoiMapServiceRpcImpl.doPost]不开启超时限制!method={}", method);
            httpResponse = O2oHttpClient.postJson(requestUrl, headMap, JSON.toJSONString(paramMap));
        }
        log.info("[AoiMapServiceRpcImpl.doPost],httpResponse={}", httpResponse);
        if (StringUtils.isBlank(httpResponse)) {
            log.info("[AoiMapServiceRpcImpl.doPost],返回结果为空!");
            throw new BusinessException(BusinessErrorCode.UNKNOWN_ERROR);
        }
        // 根据status判断是否执行成功
        GisBaseResponse gIsBaseResponse = JSON.parseObject(httpResponse, new TypeReference<GisBaseResponse>() {
        });
        if (Objects.nonNull(gIsBaseResponse) && Objects.equals(SUCCESS_CODE, gIsBaseResponse.getCode())) {
            String data = gIsBaseResponse.getData();
            return (T) data;
        } else {
            log.error("[AoiMapServiceImpl.doPost],地图交互异常!");
            throw new BusinessException(AngelPromiseBizErrorCode.GIS_ERR);
        }
    }

    /**
     * 执行post
     *
     * @return
     */
    public <T> T doPost(String method, String methodUrl, Object requestObj, Class<T> cls){
        String requestUrl = baseUrl.concat(methodUrl);
        log.info("[AoiMapServiceRpcImpl.doPost],开始执行请求, method:{}. url: {}, 参数: {}, tag={}", method, requestUrl, JSON.toJSONString(requestObj), tag);
        Map<String, String> headMap = Maps.newHashMap();
        headMap.put("content-type", "application/json");
        headMap.put("accept", "application/json");
        if(StringUtils.isNotBlank(tag)){
            headMap.put("tag", tag);
        }
        Map<String, Object> paramMap = generateParams(requestObj);
        String httpResponse;
        if(CollectionUtils.isNotEmpty(duccConfig.getLimitSocketTimeoutPath()) && duccConfig.getLimitSocketTimeoutPath().contains(method)){
            log.info("[AoiMapServiceRpcImpl.doPost]开启超时限制!method={}", method);
            httpResponse = O2oHttpClient.postJson(requestUrl, headMap, JSON.toJSONString(paramMap), getRequestConfig());
        }else {
            log.info("[AoiMapServiceRpcImpl.doPost]不开启超时限制!method={}", method);
            httpResponse = O2oHttpClient.postJson(requestUrl, headMap, JSON.toJSONString(paramMap));
        }
        log.info("[AoiMapServiceRpcImpl.doPost],httpResponse={}", httpResponse);
        if (StringUtils.isBlank(httpResponse)) {
            log.info("[AoiMapServiceRpcImpl.doPost],返回结果为空!");
            throw new BusinessException(BusinessErrorCode.UNKNOWN_ERROR);
        }
        // 根据status判断是否执行成功
        GisBaseResponse gIsBaseResponse = JSON.parseObject(httpResponse, new TypeReference<GisBaseResponse>() {
        });
        if (Objects.nonNull(gIsBaseResponse) && Objects.equals(SUCCESS_CODE, gIsBaseResponse.getCode())) {
            String data = gIsBaseResponse.getData();
            return JSON.parseObject(data, cls);
        } else {
            log.error("[AoiMapServiceImpl.doPost],地图交互异常!");
            throw new BusinessException(AngelPromiseBizErrorCode.GIS_ERR);
        }
    }

    /**
     * 组装请求参数
     *
     * @param body
     * @return
     */
    private Map<String, Object> generateParams(Object body) {
        JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(body));
        Map<String, Object> data = Maps.newHashMap();
        data.put("appKey", appKey);
        data.put("scene", scene);
        data.putAll(jsonObject);
        return data;
    }

    private Map<String, Object> generateParams2(Object body) {
        JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(body));
        Map<String, Object> data = Maps.newHashMap();
        data.put("appKey", "789F9F3418954AB39B5FB5274F88BD6F");
        data.putAll(jsonObject);
        return data;
    }

    /**
     * http超时配置
     *
     * @return RequestConfig
     */
    private RequestConfig getRequestConfig() {
        AoiMapTimeoutConfig requestDuccConfig = duccConfig.getRequestConfig();
        if(Objects.nonNull(requestConfig)
                && (Objects.isNull(requestDuccConfig.getSocketTimeout())
                        || requestDuccConfig.getSocketTimeout().equals(requestConfig.getSocketTimeout()))){
            return requestConfig;
        }
        synchronized (AoiMapServiceRpc.class){
            if(Objects.nonNull(requestConfig)
                    && (Objects.isNull(requestDuccConfig.getSocketTimeout())
                    || requestDuccConfig.getSocketTimeout().equals(requestConfig.getSocketTimeout()))){
                return requestConfig;
            }
            return RequestConfig.custom()
                    .setConnectTimeout(requestDuccConfig.getConnectTimeout())
                    .setConnectionRequestTimeout(requestDuccConfig.getConnectionRequestTimeout())
                    .setSocketTimeout(requestDuccConfig.getSocketTimeout())
                    .setRedirectsEnabled(true)
                    .build();
        }
    }

    public static void setRequestConfig(RequestConfig requestConfig) {
        AoiMapServiceRpcImpl.requestConfig = requestConfig;
    }
}
