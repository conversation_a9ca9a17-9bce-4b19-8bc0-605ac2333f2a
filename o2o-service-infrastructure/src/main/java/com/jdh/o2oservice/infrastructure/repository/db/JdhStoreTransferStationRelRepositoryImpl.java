package com.jdh.o2oservice.infrastructure.repository.db;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jdh.o2oservice.base.enums.YnStatusEnum;
import com.jdh.o2oservice.core.domain.provider.model.JdhStoreTransferStationRel;
import com.jdh.o2oservice.core.domain.provider.model.JdhStoreTransferStationRelIdentifier;
import com.jdh.o2oservice.core.domain.provider.repository.db.JdhStoreTransferStationRelRepository;
import com.jdh.o2oservice.infrastructure.repository.db.convert.JdhBasicPoConverter;
import com.jdh.o2oservice.infrastructure.repository.db.convert.JdhStoreTransferStationConvert;
import com.jdh.o2oservice.infrastructure.repository.db.dao.JdhStoreTransferStationRelMapper;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhStoreTransferStationRelPo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Component
@Slf4j
public class JdhStoreTransferStationRelRepositoryImpl implements JdhStoreTransferStationRelRepository {

    /**
     * 门店接驳点关联关系
     */
    @Resource
    JdhStoreTransferStationRelMapper jdhStoreTransferStationRelMapper;

    /**
     * 通过ID寻找Aggregate。
     * 找到的Aggregate自动是可追踪的
     *
     * @param jdhStoreTransferStationRelIdentifier
     */
    @Override
    public JdhStoreTransferStationRel find(JdhStoreTransferStationRelIdentifier jdhStoreTransferStationRelIdentifier) {
        return JdhStoreTransferStationConvert.INSTANCE.toJdhStoreTransferStationRel(jdhStoreTransferStationRelMapper.selectById(jdhStoreTransferStationRelIdentifier.getId()));
    }

    /**
     * 将一个Aggregate从Repository移除
     * 操作后的aggregate对象自动取消追踪
     *
     * @param entity
     */
    @Override
    public int remove(JdhStoreTransferStationRel entity) {
        LambdaUpdateWrapper<JdhStoreTransferStationRelPo> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(JdhStoreTransferStationRelPo::getYn, YnStatusEnum.NO.getCode())
                .setSql("`version` = version+1")
                .eq(JdhStoreTransferStationRelPo::getJdStoreId, entity.getJdStoreId())
                .eq(JdhStoreTransferStationRelPo::getJdTransferStationId, entity.getJdTransferStationId())
                .eq(JdhStoreTransferStationRelPo::getYn, YnStatusEnum.YES.getCode());
        return jdhStoreTransferStationRelMapper.update(null, updateWrapper);
    }

    /**
     * 保存接驳点
     *
     * @param jdhStoreTransferStationRel jdhStoreTransferStationRel
     * @return count
     */
    @Override
    public int save(JdhStoreTransferStationRel jdhStoreTransferStationRel) {
        JdhStoreTransferStationRelPo po = JdhStoreTransferStationConvert.INSTANCE.toJdhStoreTransferStationRelPo(jdhStoreTransferStationRel);
        JdhBasicPoConverter.initInsertBasicPo(po);
        return jdhStoreTransferStationRelMapper.insert(po);
    }

    /**
     * 更新接驳点
     *
     * @param jdhStoreTransferStationRel jdhStoreTransferStationRel
     * @return count
     */
    @Override
    public int update(JdhStoreTransferStationRel jdhStoreTransferStationRel) {
        LambdaUpdateWrapper<JdhStoreTransferStationRelPo> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(JdhStoreTransferStationRelPo::getUpdateUser, jdhStoreTransferStationRel.getUpdateUser())
                .setSql("`version` = version+1")
                .eq(JdhStoreTransferStationRelPo::getJdStoreId, jdhStoreTransferStationRel.getJdStoreId())
                .eq(JdhStoreTransferStationRelPo::getJdTransferStationId, jdhStoreTransferStationRel.getJdTransferStationId())
                .eq(JdhStoreTransferStationRelPo::getYn, YnStatusEnum.YES.getCode());
        return jdhStoreTransferStationRelMapper.update(null, updateWrapper);
    }

    /**
     * 删除接驳点
     *
     * @param jdhStoreTransferStationRel jdhStoreTransferStationRel
     * @return count
     */
    @Override
    public int delete(JdhStoreTransferStationRel jdhStoreTransferStationRel) {
        LambdaUpdateWrapper<JdhStoreTransferStationRelPo> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(JdhStoreTransferStationRelPo::getYn, YnStatusEnum.NO.getCode())
                .setSql("`version` = version+1")
                .eq(JdhStoreTransferStationRelPo::getJdStoreId, jdhStoreTransferStationRel.getJdStoreId())
                .eq(JdhStoreTransferStationRelPo::getJdTransferStationId, jdhStoreTransferStationRel.getJdTransferStationId())
                .eq(JdhStoreTransferStationRelPo::getYn, YnStatusEnum.YES.getCode());
        return jdhStoreTransferStationRelMapper.update(null, updateWrapper);
    }

    /**
     * 查询门店项目关系
     *
     * @param jdhStoreTransferStationRel jdhStoreTransferStationRel
     * @return list
     */
    @Override
    public List<JdhStoreTransferStationRel> queryList(JdhStoreTransferStationRel jdhStoreTransferStationRel) {
        if (jdhStoreTransferStationRel == null || (jdhStoreTransferStationRel.getJdTransferStationId() == null && StringUtils.isBlank(jdhStoreTransferStationRel.getJdStoreId()) && CollUtil.isEmpty(jdhStoreTransferStationRel.getJdStoreIds()))) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<JdhStoreTransferStationRelPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(jdhStoreTransferStationRel.getJdTransferStationId() != null, JdhStoreTransferStationRelPo::getJdTransferStationId, jdhStoreTransferStationRel.getJdTransferStationId())
                .eq(StringUtils.isNotBlank(jdhStoreTransferStationRel.getJdStoreId()), JdhStoreTransferStationRelPo::getJdStoreId, jdhStoreTransferStationRel.getJdStoreId())
                .in(CollUtil.isNotEmpty(jdhStoreTransferStationRel.getJdStoreIds()), JdhStoreTransferStationRelPo::getJdStoreId, jdhStoreTransferStationRel.getJdStoreIds())
                .eq(JdhStoreTransferStationRelPo::getYn, YnStatusEnum.YES.getCode());
        return JdhStoreTransferStationConvert.INSTANCE.toJdhStoreTransferStationRel(jdhStoreTransferStationRelMapper.selectList(queryWrapper));
    }

    /**
     * 分页查询门店项目关系
     *
     * @param jdhStoreTransferStationRel
     */
    @Override
    public Page<JdhStoreTransferStationRel> queryPageList(JdhStoreTransferStationRel jdhStoreTransferStationRel) {
        IPage<JdhStoreTransferStationRelPo> iPage = new Page<>(jdhStoreTransferStationRel.getPageNum(), jdhStoreTransferStationRel.getPageSize());
        LambdaQueryWrapper<JdhStoreTransferStationRelPo> queryWrapper = Wrappers.lambdaQuery();
        IPage<JdhStoreTransferStationRelPo> storeTransferStationRelPoIPage = jdhStoreTransferStationRelMapper.selectPage(iPage, queryWrapper);
        if (Objects.isNull(storeTransferStationRelPoIPage) || CollectionUtils.isEmpty(storeTransferStationRelPoIPage.getRecords())) {
            return null;
        }
        List<JdhStoreTransferStationRel> storeTransferStationRelPage = JdhStoreTransferStationConvert.INSTANCE.toJdhStoreTransferStationRel(storeTransferStationRelPoIPage.getRecords());
        return JdhBasicPoConverter.initPage(storeTransferStationRelPoIPage, storeTransferStationRelPage);
    }

    /**
     * 查询门店项目关系
     *
     * @param jdhStoreTransferStationRel
     */
    @Override
    public JdhStoreTransferStationRel query(JdhStoreTransferStationRel jdhStoreTransferStationRel) {
        if (jdhStoreTransferStationRel == null || (jdhStoreTransferStationRel.getJdTransferStationId() == null && StringUtils.isBlank(jdhStoreTransferStationRel.getJdStoreId()))) {
            return null;
        }
        LambdaQueryWrapper<JdhStoreTransferStationRelPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(JdhStoreTransferStationRelPo::getJdTransferStationId, jdhStoreTransferStationRel.getJdTransferStationId())
                .eq(JdhStoreTransferStationRelPo::getJdStoreId, jdhStoreTransferStationRel.getJdStoreId())
                .eq(JdhStoreTransferStationRelPo::getYn, YnStatusEnum.YES.getCode());
        return JdhStoreTransferStationConvert.INSTANCE.toJdhStoreTransferStationRel(jdhStoreTransferStationRelMapper.selectOne(queryWrapper));
    }
}
