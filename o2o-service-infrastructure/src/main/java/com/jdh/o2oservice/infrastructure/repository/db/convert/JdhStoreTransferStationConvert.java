package com.jdh.o2oservice.infrastructure.repository.db.convert;

import com.jdh.o2oservice.core.domain.provider.model.JdhStoreTransferStation;
import com.jdh.o2oservice.core.domain.provider.model.JdhStoreTransferStationRel;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhStoreTransferStationPo;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhStoreTransferStationRelPo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface JdhStoreTransferStationConvert {

    JdhStoreTransferStationConvert INSTANCE = Mappers.getMapper(JdhStoreTransferStationConvert.class);

    JdhStoreTransferStationRelPo toJdhStoreTransferStationRelPo(JdhStoreTransferStationRel jdhStoreTransferStationRel);

    JdhStoreTransferStationRel toJdhStoreTransferStationRel(JdhStoreTransferStationRelPo jdhStoreTransferStationRelPo);

    List<JdhStoreTransferStationRel> toJdhStoreTransferStationRel(List<JdhStoreTransferStationRelPo> jdhStoreTransferStationRelPo);

    JdhStoreTransferStationPo toJdhStoreTransferStationPo(JdhStoreTransferStation jdhStoreTransferStation);

    JdhStoreTransferStation toJdhStoreTransferStation(JdhStoreTransferStationPo jdhStoreTransferStationPo);

    List<JdhStoreTransferStation> toJdhStoreTransferStation(List<JdhStoreTransferStationPo> jdhStoreTransferStationPo);
}
