package com.jdh.o2oservice.infrastructure.repository.db.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jdh.o2oservice.infrastructure.repository.db.cache.AggregateId;
import lombok.Builder;
import lombok.Data;

import java.util.Date;

/**
 * @ClassName:JdhStation
 * @Description:
 * @Author: yaoqinghai
 * @Date: 2024/4/22 19:01
 * @Vserion: 1.0
 **/
@Data
@TableName(value = "jdh_angel_station",autoResultMap = true)

public class JdhAngelStationPo {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 服务站id
     */
    @AggregateId
    private Long angelStationId;

    /**
     * 围栏id
     */
    private String fenceId;

    /**
     * 服务站名称
     */
    private String angelStationName;

    /**
     * 地图id
     */
    private Long mapId;

    /**
     * 图层id
     */
    private Long layerId;

    /**
     * 业务模式 1护士-全职 2护士-兼职
     */
    private Integer stationModeType;

    /**
     * 范围的衡量类型（1：公里数，2：分钟）
     */
    private Integer fenceRangeType;

    /**
     * 范围的衡量值（如xx公里或xx分钟）
     */
    private Integer fenceRangeRadius;

    /**
     * 圆中心点纬度
     */
    private String fenceRangeCenterLat;

    /**
     * 圆中心点经度
     */
    private String fenceRangeCenterLng;

    /**
     * 围栏信息: -围栏地理数据:围栏边界点
     */
    private String fenceBoundaryList;

    /**
     * 站长erp
     */
    private String stationMaster;

    /**
     * 省编码
     */
    private String provinceCode;

    /**
     * 省名称
     */
    private String provinceName;

    /**
     * 市编码
     */
    private String cityCode;

    /**
     * 市名称
     */
    private String cityName;

    /**
     * 区编码
     */
    private String districtCode;

    /**
     * 区名称
     */
    private String districtName;

    /**
     * 县编码
     */
    private String countyCode;

    /**
     * 县名称
     */
    private String countyName;

    /**
     * 详细地址
     */
    private String addressDetail;

    /**
     * 全地址
     */
    private String fullAddress;

    /**
     * 站点状态：1启用 2停用
     */
    private Integer stationStatus;

    /**
     * 开始营业时间(24小时)
     */
    private String openHour;

    /**
     * 停止营业时间(24小时)
     */
    private String closeHour;

    /**
     * 是否有效 0-无效 1-有效
     */
    private Integer yn;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人
     */
    private String updateUser;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 围栏形状
     * @see com.jdh.o2oservice.core.domain.angel.enums.FenceShapeTypeEnum
     */
    private Integer fenceShapeType;

    /**
     * 服务资源类型 二进制编码 右向左 1位骑手 2位护士
     */
    private Long angelType;

    /**
     * 骑手供应商 服务资源类型 二进制编码 右向左 1位达达 2位顺丰
     */
    private Long deliverySupplier;

    /**
     * 实验室id
     */
    private String stationId;

    /**
     * 实验室名称
     */
    private String stationName;

    /**
     * 服务资源数量
     */
    private Integer angelNum;

    /**
     * 护士资源数量
     */
    private Integer nurseNum;

    /**
     * 三方店铺id
     */
    private String outShopId;

    /**
     * 三方店铺id
     */
    private Long jdTransferStationId;
}
