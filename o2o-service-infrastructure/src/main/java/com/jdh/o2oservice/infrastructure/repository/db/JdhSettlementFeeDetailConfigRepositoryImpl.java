package com.jdh.o2oservice.infrastructure.repository.db;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.enums.YnStatusEnum;
import com.jdh.o2oservice.core.domain.settlement.model.JdhSettlementAreaFeeConfig;
import com.jdh.o2oservice.core.domain.settlement.model.JdhSettlementFeeDetailConfig;
import com.jdh.o2oservice.core.domain.settlement.model.JdhSettlementFeeDetailConfigIdentifier;
import com.jdh.o2oservice.core.domain.settlement.repository.db.JdhSettlementFeeDetailConfigRepository;
import com.jdh.o2oservice.core.domain.product.repository.query.JdhAreaFeeConfigQuery;
import com.jdh.o2oservice.core.domain.settlement.repository.query.JdhAngelSettleAreaFeeQuery;
import com.jdh.o2oservice.infrastructure.repository.db.convert.JdhAngelSettlementPoConvert;
import com.jdh.o2oservice.infrastructure.repository.db.convert.JdhBasicPoConverter;
import com.jdh.o2oservice.infrastructure.repository.db.dao.JdhSettlementFeeDetailConfigPoMapper;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhProviderPo;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhSettlementAreaFeeConfigPo;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhSettlementFeeDetailConfigPo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * JdhSettlementFeeDetailConfigRepositoryImpl
 * @author:lwm
 * @createTime: 2025-04-18 16:06
 * @Description:
 */
@Component
@Slf4j
public class JdhSettlementFeeDetailConfigRepositoryImpl implements JdhSettlementFeeDetailConfigRepository {


    /**
     * jdhSettlementFeeDetailConfigPoMapper
     */
    @Resource
    private JdhSettlementFeeDetailConfigPoMapper jdhSettlementFeeDetailConfigPoMapper;


    /**
     * 通过ID寻找Aggregate。
     * 找到的Aggregate自动是可追踪的
     *
     * @param jdhSettlementFeeDetailConfigIdentifier
     */
    @Override
    public JdhSettlementFeeDetailConfig find(JdhSettlementFeeDetailConfigIdentifier jdhSettlementFeeDetailConfigIdentifier) {
        return null;
    }

    /**
     * 将一个Aggregate从Repository移除
     * 操作后的aggregate对象自动取消追踪
     *
     * @param entity
     */
    @Override
    public int remove(JdhSettlementFeeDetailConfig entity) {
        return 0;
    }

    /**
     * 保存地区费项配置
     *
     * @param jdhSettlementFeeDetailConfig
     * @return count
     */
    @Override
    public int save(JdhSettlementFeeDetailConfig jdhSettlementFeeDetailConfig) {
        JdhSettlementFeeDetailConfigPo jdhSettlementFeeDetail = JdhAngelSettlementPoConvert.INSTANCE.convertToFeeDetailConfigPo(jdhSettlementFeeDetailConfig);
        return jdhSettlementFeeDetailConfigPoMapper.insert(jdhSettlementFeeDetail);
    }

    /**
     * 批量保存地区费项配置
     *
     * @param feeDetailConfigList
     */
    @Override
    public int batchSaveJdhAreaFeeConfig(List<JdhSettlementFeeDetailConfig> feeDetailConfigList) {
        List<JdhSettlementFeeDetailConfigPo> jdhSettlementFeeDetailConfigPoList = JdhAngelSettlementPoConvert.INSTANCE.convertToFeeDetailConfigPos(feeDetailConfigList);
        return jdhSettlementFeeDetailConfigPoMapper.batchSaveJdhSettlementFeeDetailConfig(jdhSettlementFeeDetailConfigPoList);
    }

    /**
     * 更新地区费项配置
     *
     * @param jdhSettlementFeeDetailConfig
     * @return count
     */
    @Override
    public int update(JdhSettlementFeeDetailConfig jdhSettlementFeeDetailConfig) {
        LambdaUpdateWrapper<JdhSettlementFeeDetailConfigPo> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(JdhSettlementFeeDetailConfigPo::getFeeAmount, jdhSettlementFeeDetailConfig.getFeeAmount())
                .setSql("`version` = `version` + 1")
                .eq(JdhSettlementFeeDetailConfigPo::getFeeConfigDetailId, jdhSettlementFeeDetailConfig.getFeeConfigDetailId());
        return jdhSettlementFeeDetailConfigPoMapper.update(null, updateWrapper);
    }

    /**
     * 删除地区费项配置
     *
     * @param queryContext
     * @return count
     */
    @Override
    public int batchDelete(JdhAngelSettleAreaFeeQuery queryContext) {
        LambdaUpdateWrapper<JdhSettlementFeeDetailConfigPo> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(JdhSettlementFeeDetailConfigPo::getYn, CommonConstant.ZERO)
                .in(JdhSettlementFeeDetailConfigPo::getFeeConfigId, queryContext.getFeeConfigIdList());
        return jdhSettlementFeeDetailConfigPoMapper.update(null, updateWrapper);
    }

    /**
     * @param jdhSettlementFeeDetailConfig
     * @return
     */
    @Override
    public int updateByCode(JdhSettlementFeeDetailConfig jdhSettlementFeeDetailConfig) {
        return 0;
    }

    /**
     * 查询地区费项配置列表
     *
     * @param jdhAreaFeeConfigQuery
     */
    @Override
    public List<JdhSettlementFeeDetailConfig> queryJdhAreaFeeConfigList(JdhAngelSettleAreaFeeQuery jdhAreaFeeConfigQuery) {
        LambdaQueryWrapper<JdhSettlementFeeDetailConfigPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(JdhSettlementFeeDetailConfigPo::getFeeConfigId,jdhAreaFeeConfigQuery.getFeeConfigId())
                .eq(JdhSettlementFeeDetailConfigPo::getYn, YnStatusEnum.YES.getCode());
        List<JdhSettlementFeeDetailConfigPo> poList = jdhSettlementFeeDetailConfigPoMapper.selectList(queryWrapper);
        List<JdhSettlementFeeDetailConfig> list = JdhAngelSettlementPoConvert.INSTANCE.convertToJdhSettlementFeeDetailConfigList(poList);
        return list;
    }

    /**
     * 查询费项配置明细列表
     *
     * @param feeConfigIdList
     * @return
     */
    @Override
    public List<JdhSettlementFeeDetailConfig> queryJdhAreaFeeConfigListByFeeIdList(List<Long> feeConfigIdList) {
        LambdaQueryWrapper<JdhSettlementFeeDetailConfigPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(JdhSettlementFeeDetailConfigPo::getFeeConfigId, feeConfigIdList)
                .eq(JdhSettlementFeeDetailConfigPo::getYn, YnStatusEnum.YES.getCode());
        List<JdhSettlementFeeDetailConfigPo> poList = jdhSettlementFeeDetailConfigPoMapper.selectList(queryWrapper);
        List<JdhSettlementFeeDetailConfig> list = JdhAngelSettlementPoConvert.INSTANCE.convertToJdhSettlementFeeDetailConfigList(poList);
        return list;
    }
}
