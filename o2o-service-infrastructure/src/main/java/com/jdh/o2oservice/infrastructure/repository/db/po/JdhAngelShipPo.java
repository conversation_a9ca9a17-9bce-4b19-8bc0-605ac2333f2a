package com.jdh.o2oservice.infrastructure.repository.db.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jdh.o2oservice.base.mybatisplus.AcesCipherIndexHandler;
import com.jdh.o2oservice.base.mybatisplus.AcesCipherTextHandler;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelShipCancelCodeStatusEnum;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName:JdhAngelShipPo
 * @Description: 运单
 * @Author: yaoqinghai
 * @Date: 2024/4/21 14:23
 * @Vserion: 1.0
 **/
@Data
@TableName(value = "jdh_angel_ship",autoResultMap = true)
public class JdhAngelShipPo {
    /**
     * 自增主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 运单Id
     */
    private Long shipId;

    /**
     * 服务工单Id
     */
    private Long workId;

    /**
     * 外部运单Id
     */
    private String outShipId;

    /**
     * shopNo
     */
    private String shopNo;

    /**
     * 运单类型：1=自行寄送，2=达达
     */
    private Integer type;

    /**
     * 接收点Id
     */
    private String receiverId;

    /**
     * 接收点名称
     */
    private String receiverName;

    /**
     * 接收点全地址
     */
    private String receiverFullAddress;

    /**
     * 骑手Id
     */
    private String transferId;

    /**
     * 骑手姓名
     */
    @TableField(typeHandler = AcesCipherTextHandler.class)
    private String transferName;

    /**
     * 骑手姓名加密
     */
    @TableField(typeHandler = AcesCipherIndexHandler.class)
    private String transferNameIndex;

    /**
     * 骑手联系方式
     */
    @TableField(typeHandler = AcesCipherTextHandler.class)
    private String transferPhone;

    /**
     * 骑手联系方式加密
     */
    @TableField(typeHandler = AcesCipherIndexHandler.class)
    private String transferPhoneIndex;

    /**
     * 骑手头像
     */
    private String transferHeadImg;

    /**
     * 发件人姓名
     */
    @TableField(typeHandler = AcesCipherTextHandler.class)
    private String senderName;

    /**
     * 发件人姓名加密
     */
    @TableField(typeHandler = AcesCipherIndexHandler.class)
    private String senderNameIndex;

    /**
     * 发件人联系方式
     */
    @TableField(typeHandler = AcesCipherTextHandler.class)
    private String senderPhone;

    /**
     * 发件人联系方式加密
     */
    @TableField(typeHandler = AcesCipherIndexHandler.class)
    private String senderPhoneIndex;

    /**
     * 发件全地址
     */
    private String senderFullAddress;

    /**
     * 运单状态：
     */
    private Integer shipStatus;

    /**
     * 骑手接单纬度
     */
    private Double transferStartLat;

    /**
     * 骑手接单经度
     */
    private Double transferStartLng;

    /**
     * 配送距离，寄件地址到收件地址
     */
    private BigDecimal totalDistance;

    /**
     * 预计呼叫时间
     */
    private Date planCallTime;

    /**
     * 预计接单时间
     */
    private Date estimateGrabTime;

    /**
     * 预计完单时间
     */
    private Date estimateReceiveTime;

    /**
     * 配送距离，寄件地址到收件地址
     */
    private Date estimatePickUpTime;

    /**
     * 重发类型
     */
    private Integer repeatType;

    /**
     * 发件码加密
     */
    @TableField(typeHandler = AcesCipherTextHandler.class)
    private String sendCode;

    /**
     * 发件码索引
     */
    @TableField(typeHandler = AcesCipherIndexHandler.class)
    private String sendCodeIndex;

    /**
     * 收件码加密
     */
    @TableField(typeHandler = AcesCipherTextHandler.class)
    private String finishCode;

    /**
     * 收件码索引
     */
    @TableField(typeHandler = AcesCipherIndexHandler.class)
    private String finishCodeIndex;

    /**
     * 扩展信息：运单备注
     */
    private String extend;

    /**
     * 数据有效性 0：无效 1：有效
     */
    private Integer yn;

    /**
     * 版本号
     */
    private Integer	version;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人
     */
    private String updater;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 消医标准取消码
     * @see AngelShipCancelCodeStatusEnum
     */
    private Integer standCancelCode;

    /**
     * 物流动态信息
     */
    private String logisticsMessage;

    private Long parentShipId;//父运单id

}
