package com.jdh.o2oservice.infrastructure.repository.db.convert;

import com.jdh.o2oservice.core.domain.support.autobos.model.AutoBotsRecord;
import com.jdh.o2oservice.infrastructure.repository.db.po.AutoBotsRecordPo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025/5/15
 */
@Mapper
public interface AutoBotsRecordConvert {


    /**
     * 获取AutoBotsRecordConvert的单例实例。
     */
    AutoBotsRecordConvert INSTANCE = Mappers.getMapper(AutoBotsRecordConvert.class);

    /**
     * 将AutoBotsRecord对象转换为AutoBotsRecordPo对象。
     * @param autoBotsRecord 待转换的AutoBotsRecord对象。
     * @return 转换后的AutoBotsRecordPo对象。
     */
    AutoBotsRecordPo convert(AutoBotsRecord autoBotsRecord);


    /**
     * 将AutoBotsRecordPo对象转换为AutoBotsRecord对象。
     * @param autoBotsRecordPo 要转换的AutoBotsRecordPo对象。
     * @return 转换后的AutoBotsRecord对象。
     */
    AutoBotsRecord convert(AutoBotsRecordPo autoBotsRecordPo);

    /**
     * 将AutoBotsRecordPo列表转换为AutoBotsRecord列表
     * @param autoBotsRecordPo AutoBotsRecordPo对象列表
     * @return 转换后的AutoBotsRecord对象列表
     */
    List<AutoBotsRecord> convertList(List<AutoBotsRecordPo> autoBotsRecordPo);

}
