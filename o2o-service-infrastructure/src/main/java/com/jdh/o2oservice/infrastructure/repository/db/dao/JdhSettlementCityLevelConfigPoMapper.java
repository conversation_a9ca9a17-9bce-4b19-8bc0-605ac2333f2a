package com.jdh.o2oservice.infrastructure.repository.db.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhSettlementCityLevelConfigPo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 *
 * @ClassName JdhSettlementCityLevelConfigPoMapper
 * @Description
 * <AUTHOR>
 * @Date 2025/4/18 22:22
 **/
public interface JdhSettlementCityLevelConfigPoMapper extends BaseMapper<JdhSettlementCityLevelConfigPo> {

    /**
     * 批量插入
     *
     * @param jdhSettlementCityLevelConfigPoList
     * @return {@link Integer}
     */
    Integer batchSaveJdhSettlementCityLevelConfig(@Param("list") List<JdhSettlementCityLevelConfigPo> jdhSettlementCityLevelConfigPoList);
}