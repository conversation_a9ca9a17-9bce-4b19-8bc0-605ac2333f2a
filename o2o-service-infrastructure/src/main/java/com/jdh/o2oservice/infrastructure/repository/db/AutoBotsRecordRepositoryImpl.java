package com.jdh.o2oservice.infrastructure.repository.db;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.jd.medicine.base.common.util.StringUtil;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.enums.YnStatusEnum;
import com.jdh.o2oservice.base.factory.GenerateIdFactory;
import com.jdh.o2oservice.core.domain.support.autobos.bo.AutoBotsRequestQueryBO;
import com.jdh.o2oservice.core.domain.support.autobos.model.AutoBotsRecord;
import com.jdh.o2oservice.core.domain.support.autobos.repository.AutoBotsRecordRepository;
import com.jdh.o2oservice.infrastructure.repository.db.convert.AutoBotsRecordConvert;
import com.jdh.o2oservice.infrastructure.repository.db.dao.AutoBotsRecordMapper;
import com.jdh.o2oservice.infrastructure.repository.db.po.AutoBotsRecordPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description
 * @date 2025/5/15
 */
@Component
@Slf4j
public class AutoBotsRecordRepositoryImpl implements AutoBotsRecordRepository {

    /**
     * autobots记录mapper
     */
    @Resource
    private AutoBotsRecordMapper autobotsRecordMapper;

    /**
     * 用于生成唯一标识符的工厂。
     */
    @Autowired
    private GenerateIdFactory generateIdFactory;

    /**
     * 保存AutoBotsRecord对象到数据库中。
     *
     * @param autoBotsRecord 要保存的AutoBotsRecord对象。
     * @return 如果保存成功则返回true，否则返回false。
     */
    @Override
    public Long save(AutoBotsRecord autoBotsRecord) {
        AutoBotsRecordPo po = AutoBotsRecordConvert.INSTANCE.convert(autoBotsRecord);
        if (Objects.nonNull(po.getId())){

            LambdaUpdateWrapper<AutoBotsRecordPo> updateWrapper = new LambdaUpdateWrapper<>();

            updateWrapper
                    .set(StringUtil.isNotBlank(po.getResult()), AutoBotsRecordPo::getResult, po.getResult())
                    .setSql(CommonConstant.UPDATE_TIME_SQL_NOW)
                    .set(StringUtil.isNotBlank(po.getUpdateUser()), AutoBotsRecordPo::getUpdateUser, po.getUpdateUser())
                    .eq(AutoBotsRecordPo::getId,autoBotsRecord.getId())
            ;

            autobotsRecordMapper.update(null,updateWrapper);
            return po.getId();
        }
        Long businessId = generateIdFactory.getId();
        po.setBusinessId(businessId);
        po.setYn(YnStatusEnum.YES.getCode());
        autobotsRecordMapper.insert(po);
        return po.getId();
    }

    /**
     * 根据查询条件获取 AutoBots 记录
     *
     * @param queryBO 查询条件对象
     * @return AutoBots 记录实体
     */
    @Override
    public AutoBotsRecord queryAutoBotsRecord(AutoBotsRequestQueryBO queryBO) {
        LambdaQueryWrapper<AutoBotsRecordPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .eq(Objects.nonNull(queryBO.getId()), AutoBotsRecordPo::getId, queryBO.getId())
                .eq(Objects.nonNull(queryBO.getBusinessId()),AutoBotsRecordPo::getBusinessId, queryBO.getBusinessId())
                .eq(StringUtil.isNotBlank(queryBO.getTraceId()),AutoBotsRecordPo::getTraceId, queryBO.getTraceId())
                .eq(StringUtil.isNotBlank(queryBO.getReqId()),AutoBotsRecordPo::getReqId, queryBO.getReqId())
                .eq(StringUtil.isNotBlank(queryBO.getScene()),AutoBotsRecordPo::getScene, queryBO.getScene())
                .eq(StringUtil.isNotBlank(queryBO.getExtendId()),AutoBotsRecordPo::getExtendId, queryBO.getExtendId())
        ;

        AutoBotsRecordPo po = autobotsRecordMapper.selectOne(queryWrapper);
        return AutoBotsRecordConvert.INSTANCE.convert(po);
    }

    /**
     * 根据查询条件获取AutoBotsRecord列表
     *
     * @param queryBO 查询条件对象
     * @return AutoBotsRecord列表
     */
    @Override
    public List<AutoBotsRecord> queryAutoBotsRecordList(AutoBotsRequestQueryBO queryBO) {
        LambdaQueryWrapper<AutoBotsRecordPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .eq(Objects.nonNull(queryBO.getId()), AutoBotsRecordPo::getId, queryBO.getId())
                .eq(Objects.nonNull(queryBO.getBusinessId()),AutoBotsRecordPo::getBusinessId, queryBO.getBusinessId())
                .eq(StringUtil.isNotBlank(queryBO.getTraceId()),AutoBotsRecordPo::getTraceId, queryBO.getTraceId())
                .eq(StringUtil.isNotBlank(queryBO.getReqId()),AutoBotsRecordPo::getReqId, queryBO.getReqId())
                .eq(StringUtil.isNotBlank(queryBO.getScene()),AutoBotsRecordPo::getScene, queryBO.getScene())
                .eq(StringUtil.isNotBlank(queryBO.getExtendId()),AutoBotsRecordPo::getExtendId, queryBO.getExtendId())
        ;
        List<AutoBotsRecordPo> pos = autobotsRecordMapper.selectList(queryWrapper);
        return AutoBotsRecordConvert.INSTANCE.convertList(pos);
    }
}
