<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>jdh-o2o-service</artifactId>
        <groupId>com.jdh.o2oservice</groupId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>o2o-service-infrastructure</artifactId>

    <properties>
        <maven.deploy.skip>true</maven.deploy.skip>
    </properties>

    <dependencies>
        <!-- 数据库 start -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.sun</groupId>
                    <artifactId>jconsole</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.sun</groupId>
                    <artifactId>tools</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- 全局分布式唯一id生成 -->
        <dependency>
            <groupId>com.global</groupId>
            <artifactId>global-service-id-client</artifactId>
            <version>0.0.5-SNAPSHOT</version>
        </dependency>


        <dependency>
            <groupId>com.jdh.o2oservice</groupId>
            <artifactId>o2o-service-product-core</artifactId>
            <version>${revision}</version>
            <exclusions>
                <exclusion>
                    <artifactId>dbconfig-client</artifactId>
                    <groupId>com.jd.purchase.config</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.jdh.o2oservice</groupId>
            <artifactId>o2o-service-promise-core</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.jdh.o2oservice</groupId>
            <artifactId>o2o-service-application-ext</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>com.jdh.o2oservice</groupId>
            <artifactId>o2o-service-provider-core</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>com.jdh.o2oservice</groupId>
            <artifactId>o2o-service-report-core</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>com.jdh.o2oservice</groupId>
            <artifactId>o2o-service-settlement-core</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>com.jdh.o2oservice</groupId>
            <artifactId>o2o-service-trade-core</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>com.jdh.o2oservice</groupId>
            <artifactId>o2o-service-base</artifactId>
            <version>${revision}</version>
        </dependency>


        <dependency>
            <groupId>com.jdh.o2oservice</groupId>
            <artifactId>o2o-service-angelpromise-core</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.jdh.o2oservice</groupId>
            <artifactId>o2o-service-export</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>com.jdh.o2oservice</groupId>
            <artifactId>o2o-service-angel-core</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>com.jdh.o2oservice</groupId>
            <artifactId>o2o-service-dispatch-core</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>com.jdh.o2oservice</groupId>
            <artifactId>o2o-service-medpromise-core</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>com.jdh.o2oservice</groupId>
            <artifactId>o2o-service-support-core</artifactId>
            <version>${revision}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.hibernate.validator</groupId>
                    <artifactId>hibernate-validator</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.hibernate</groupId>
                    <artifactId>hibernate-validator</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.jdh.o2oservice</groupId>
            <artifactId>o2o-service-content-core</artifactId>
            <version>${revision}</version>
        </dependency>

        <!--Loc  -->
        <dependency>
            <groupId>com.jd.pop</groupId>
            <artifactId>pop-order-assembledflow-soa</artifactId>
            <version>2.3.11-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>org.projectlombok</groupId>
                    <artifactId>lombok</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.jd</groupId>
                    <artifactId>jsf</artifactId>
                </exclusion>
            </exclusions>
        </dependency>


        <!--Loc门店地址接口-->
        <dependency>
            <groupId>com.jd.pop.center</groupId>
            <artifactId>pop-o2o-center-spi</artifactId>
            <version>1.3.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-web</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>log4j</groupId>
                    <artifactId>log4j</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.hibernate.validator</groupId>
                    <artifactId>hibernate-validator</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.hibernate</groupId>
                    <artifactId>hibernate-validator</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--发送SMS短信-->
        <dependency>
            <groupId>com.jd.mobilePhoneMsg.sender</groupId>
            <artifactId>mobilePhoneMsg-sender-client-jsf</artifactId>
            <version>3.2-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.jd.fms.ivc</groupId>
            <artifactId>ivcsbd-service</artifactId>
            <version>2.3-SNAPSHOT</version>
        </dependency>

        <!-- 订单对象模型 -->
        <dependency>
            <groupId>com.jd.purchase.sdk.domain</groupId>
            <artifactId>purchase-sdk-domain</artifactId>
            <!--神坑,spring版本太低,加载xml报错-->
            <exclusions>
                <exclusion>
                    <artifactId>spring</artifactId>
                    <groupId>org.springframework</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>gson</artifactId>
                    <groupId>com.google.code.gson</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>xstream</artifactId>
                    <groupId>com.thoughtworks.xstream</groupId>
                </exclusion>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--拆单mq解析包-->
        <dependency>
            <groupId>com.jd.ofc</groupId>
            <artifactId>splitMessageSerialization-pb</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>xstream</artifactId>
                    <groupId>com.thoughtworks.xstream</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- 消息对象 -->
        <dependency>
            <groupId>com.jd.purchase.common</groupId>
            <artifactId>purchase-msg-domain</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jd.pop.seller</groupId>
            <artifactId>vender-center-open-api</artifactId>
        </dependency>

        <!--商品信息组件化-->
        <dependency>
            <groupId>gms.jd.component.crs.proxy</groupId>
            <artifactId>component-crs-proxy-rpc</artifactId>
            <version>1.4.41</version>
            <exclusions>
                <exclusion>
                    <groupId>com.google.guava</groupId>
                    <artifactId>guava</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.jd.pop.patient</groupId>
            <artifactId>pop-patient-client</artifactId>
            <version>4.1.56</version>
        </dependency>

        <dependency>
            <groupId>com.jd.health.xfyl</groupId>
            <artifactId>jdhealth-xfyl-merchant-export</artifactId>
        </dependency>
        <!--短链接-->
        <dependency>
            <groupId>com.jd.shorturl</groupId>
            <artifactId>shorturl-rpc-client</artifactId>
        </dependency>

        <!-- 全渠道门店列表平台化 -->
        <dependency>
            <artifactId>jw-store-openapi</artifactId>
            <groupId>com.jd.jw</groupId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <!--虚拟号-->
        <dependency>
            <groupId>com.jd.jdcc</groupId>
            <artifactId>privacy-number-gateway-facade</artifactId>
        </dependency>

        <!-- 消费医疗商家相关数据 -->
        <dependency>
            <groupId>com.jd.health.xfyl</groupId>
            <artifactId>jdhealth-xfyl-open-export</artifactId>
        </dependency>



        <!--订单金额计算服务（ocs）查询接口-->
        <dependency>
            <groupId>com.jd.ofc</groupId>
            <artifactId>jd-ofc-queryocs-jsf</artifactId>
        </dependency>

        <dependency>
            <groupId>com.thoughtworks.xstream</groupId>
            <artifactId>xstream</artifactId>
        </dependency>

        <!-- 通用下单 -->
        <dependency>
            <groupId>com.jd.trade2.vertical</groupId>
            <artifactId>vertical-export</artifactId>
            <version>2.0.3-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.jd.trade2.horizontal</groupId>
                    <artifactId>horizontal-commom</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-slf4j-impl</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j-core</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j-api</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j</artifactId>
                    <groupId>log4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-logging</artifactId>
                    <groupId>commons-logging</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>transmittable-thread-local</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.jd.trade2.core</groupId>
            <artifactId>trade-presale-core-export</artifactId>
            <version>2.0.1-jdh-presale-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.jd.trade2.core</groupId>
                    <artifactId>trade-description</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.jd.trade2.horizontal.instant.retail.base</groupId>
            <artifactId>horizontal-instant-retail-base-export</artifactId>
            <version>1.0.2-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.jd.trade2.core</groupId>
                    <artifactId>trade-description</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- 订单中台 -->
        <dependency>
            <groupId>com.jd.order</groupId>
            <artifactId>order-sdk-component-export</artifactId>
            <version>2.2.3</version>
        </dependency>
        <!-- 商品类型识别器 -->
        <dependency>
            <groupId>com.jd.gms</groupId>
            <artifactId>goods.discerner</artifactId>
            <version>1.0.2</version>
        </dependency>
        <!-- dbconfig -->
        <dependency>
            <groupId>com.jd.purchase.config</groupId>
            <artifactId>dbconfig-client</artifactId>
            <!-- dbconfig 说明： https://cf.jd.com/pages/viewpage.action?pageId=141961612 -->
            <version>1.0.7</version>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-reload4j</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- 京东自营套餐信息 -->
        <dependency>
            <groupId>com.jd.health.medical.examination</groupId>
            <artifactId>jdhealth-medical-examination-man-export</artifactId>
            <version>2.1.17-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>org.hibernate.validator</groupId>
                    <artifactId>hibernate-validator</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.hibernate</groupId>
                    <artifactId>hibernate-validator</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- 京图路线接口 -->
        <dependency>
            <groupId>com.jd.lbs.jdlbsapi</groupId>
            <artifactId>jdlbsapi-api</artifactId>
        </dependency>

        <!-- 获取用户收货地址接口 -->
        <dependency>
            <groupId>com.jd.cn.retail.jd</groupId>
            <artifactId>cn-retail-jd-address-export</artifactId>
            <version>1.1.2-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>transmittable-thread-local</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.jd.trade2.base</groupId>
                    <artifactId>trade-base-export</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- 地址编码服务 全地址转换经纬度 -->
        <dependency>
            <groupId>com.jd.lbs.geocode</groupId>
            <artifactId>geocode-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jd.cmp</groupId>
            <artifactId>nb-cmp-soa</artifactId>
            <version>2.4.97-health</version>
        </dependency>

        <!-- 获取移动端样式接口 -->
        <dependency>
            <groupId>com.jd</groupId>
            <artifactId>sdd_market-sdk</artifactId>
            <version>5.3-RELEASE</version>
        </dependency>

        <dependency>
            <groupId>com.jdh.b2c</groupId>
            <artifactId>jdh-b2c-export</artifactId>
            <version>1.0.0-trade-address-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.jd.medicine.b2c.app</groupId>
            <artifactId>medicine-b2c-app-export</artifactId>
            <version>1.1.18-SNAPSHOT</version>
        </dependency>

        <!--        GIS-->
        <dependency>
            <groupId>com.jd.lbs.geofencing</groupId>
            <artifactId>geofencing-api</artifactId>
            <version>2.6.6-SNAPSHOT</version>
        </dependency>
        <!--sku评价sdk-->
        <dependency>
            <groupId>com.jd.ugc</groupId>
            <artifactId>ugc-comment-api</artifactId>
            <version>1.2.14</version>
            <exclusions>
                <exclusion>
                    <groupId>com.jd</groupId>
                    <artifactId>jsf</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- 互联网医院派单相关 -->
        <dependency>
            <groupId>com.jd.newnethp.trade.center</groupId>
            <artifactId>newnethp-trade-center-export</artifactId>
            <version>1.1.25-nurseassign-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.jd.newnethp.diag.triage.center</groupId>
            <version>1.2.93</version>
            <artifactId>newnethp-diag-triage-center-export</artifactId>
        </dependency>
        <!-- uim2  -->
        <dependency>
            <groupId>com.jd.uim2</groupId>
            <artifactId>uim2-facade</artifactId>
        </dependency>

        <!--   人资     -->
        <dependency>
            <groupId>com.jd.enterprise</groupId>
            <artifactId>asia-hr-mdm-client</artifactId>
            <version>0.0.9-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.jd</groupId>
                    <artifactId>jsf</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- 互医消息发送 -->
        <dependency>
            <groupId>com.jd.newnethp.diag.message.center</groupId>
            <artifactId>newnethp-diag-message-center-export</artifactId>
            <!-- 正式版本待发布上线 需要联系 @刘德慧 @陈伟旭 -->
            <version>1.1.84</version>
        </dependency>
        <dependency>
            <groupId>com.jd.newnethp.cps.center</groupId>
            <artifactId>newnethp-cps-center-export</artifactId>
            <version>1.0.48</version>
        </dependency>

        <!--互医医生服务 -->
        <dependency>
            <groupId>com.jd.nethp.base.manage</groupId>
            <artifactId>nethp-base-manage-export</artifactId>
            <version>1.1.33-SNAPSHOT</version>
        </dependency>
        <!--互医医生服务 -->
        <dependency>
            <groupId>com.jd.medicine.doctor.entry.audit</groupId>
            <artifactId>jdh-nethp-doctor-entry-client</artifactId>
            <version>1.0.78-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.jdh.diag.export</groupId>
            <artifactId>jdh-nethp-doctor-diag-export</artifactId>
            <version>1.0.8</version>
            <exclusions>
                <exclusion>
                    <groupId>com.jd.medicine.b2c.base</groupId>
                    <artifactId>medicine-b2c-base-common</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.jdh.nethp.doctor.base</groupId>
            <artifactId>nethp-doctor-base-client</artifactId>
            <version>1.1.22</version>
        </dependency>
        <!--elasticsearch -->
        <dependency>
            <groupId>org.elasticsearch</groupId>
            <artifactId>elasticsearch</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>log4j-api</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.elasticsearch.client</groupId>
            <artifactId>transport</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>elasticsearch</artifactId>
                    <groupId>org.elasticsearch</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.elasticsearch.plugin</groupId>
            <artifactId>transport-netty4-client</artifactId>
        </dependency>

        <!--天算-->
        <dependency>
            <groupId>com.jd.jdh.net</groupId>
            <artifactId>net-domain-client</artifactId>
            <version>1.1.6</version>
        </dependency>

        <!--延时队列-->
        <dependency>
            <groupId>com.jd.health.delayqueue</groupId>
            <artifactId>jdhealth-delay-queue-export</artifactId>
            <version>1.0.1-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.google.maps</groupId>
            <artifactId>google-maps-services</artifactId>
        </dependency>

        <!--互医结算-->
        <dependency>
            <groupId>com.jd.medicine.settlement.center</groupId>
            <artifactId>medicine-settlement-center-export</artifactId>
            <version>1.9.9</version>
        </dependency>
        <!--视频中台-->
        <dependency>
            <groupId>vd-service-sdk</groupId>
            <artifactId>vd-service-sdk</artifactId>
            <version>1.4.5-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.jd.boundaryless</groupId>
            <artifactId>store-center-spi</artifactId>
            <version>1.6.3-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
                <exclusion>
                    <groupId>commons-collections</groupId>
                    <artifactId>commons-collections</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- 天网-敏感词接口 -->
        <dependency>
            <groupId>com.jd.risk.sensitive_word</groupId>
            <artifactId>sensitive_word_api</artifactId>
        </dependency>
        <!--投保接口-->
        <dependency>
            <groupId>com.jdd.baoxian.b.insurance.core.trade</groupId>
            <artifactId>insurance-core-trade-export</artifactId>
        </dependency>

        <!-- O2O门店服务 -->
        <dependency>
            <groupId>com.jd.o2o</groupId>
            <artifactId>o2o-arrive-store-api</artifactId>
        </dependency>

        <!--基础用户服务查询接口-->
        <dependency>
            <groupId>com.jd.user.sdk</groupId>
            <artifactId>user-sdk-export</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd.vtp</groupId>
            <artifactId>vtp-client</artifactId>
        </dependency>
             <!--上传文件-->
        <dependency>
            <groupId>com.jdh.laputa.base</groupId>
            <artifactId>laputa-base-client</artifactId>
        </dependency>


        <!--报告中心-->
        <dependency>
            <groupId>com.jd.hdp</groupId>
            <artifactId>jdh-health-record.jsf-service</artifactId>
            <version>1.0.6</version>
        </dependency>

        <!--可领券、可用券服务-->
        <dependency>
            <groupId>com.jd.coupon</groupId>
            <artifactId>coupon-soa-business-client</artifactId>
            <version>2.6.9</version>
        </dependency>

        <!--免费抢券服务-->
        <dependency>
            <groupId>com.jd.freecoupon</groupId>
            <artifactId>freecoupon-soa-client</artifactId>
            <version>1.0.26-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.jdh.o2o.promisego</groupId>
            <artifactId>jdh-o2o-promisego-export</artifactId>
            <version>1.0.8-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.jd.ugc.qa.soa</groupId>
            <artifactId>ugc-qa-client</artifactId>
            <version>0.0.8-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.jdh.o2oservice</groupId>
            <artifactId>o2o-service-user-core</artifactId>
            <version>${revision}</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>com.jd.union</groupId>
            <artifactId>union_search_api</artifactId>
            <version>1.0.26-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.jd.union</groupId>
            <artifactId>union-promote-link-api</artifactId>
            <version>3.3.7</version>
        </dependency>
        <dependency>
            <artifactId>newnethp-diag-review-center-export</artifactId>
            <groupId>com.jd.newnethp.diag.review.center</groupId>
            <version>1.1.74-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>mybatis</artifactId>
                    <groupId>org.mybatis</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.jd.medicine.b2c.base</groupId>
                    <artifactId>medicine-b2c-base-common</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>jsf</artifactId>
                    <groupId>com.jd</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>sso-uim-spring</artifactId>
                    <groupId>com.jd.common</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>mybatis-spring</artifactId>
                    <groupId>org.mybatis</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.jd.jmq</groupId>
                    <artifactId>jmq-client-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.jd.jmq</groupId>
                    <artifactId>jmq-model</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.jd.medicine.oplog.center</groupId>
            <artifactId>medicine-oplog-center-export</artifactId>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <artifactId>log4j-api</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-core</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j</artifactId>
                    <groupId>log4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j-1.2-api</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j-slf4j-impl</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-logging</artifactId>
                    <groupId>commons-logging</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j-over-slf4j</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-web</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>fastjson</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>jss-sdk-java</artifactId>
                    <groupId>com.jcloud</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jannotation</artifactId>
                    <groupId>com.jd.ump</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>profiler</artifactId>
                    <groupId>com.jd.ump</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>poi-ooxml</artifactId>
                    <groupId>org.apache.poi</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>poi</artifactId>
                    <groupId>org.apache.poi</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.jd.medicine.b2c.base</groupId>
                    <artifactId>medicine-b2c-base-export</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.jd.union</groupId>
            <artifactId>union_search_api</artifactId>
            <version>1.0.26-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.jd.union</groupId>
            <artifactId>union-promote-link-api</artifactId>
            <version>3.3.7</version>
        </dependency>
        <dependency>
            <groupId>com.jd.pop.order.sensitive</groupId>
            <artifactId>jos-order-sensitive-sdk</artifactId>
            <version>1.18-SNAPSHOT</version>
        </dependency>

        <!-- 长连接 -->
        <dependency>
            <groupId>com.jdh</groupId>
            <artifactId>jdh-message-center-delay-sdk</artifactId>
            <version>1.0.1-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.jd.medicine.b2c.trade.center</groupId>
            <artifactId>medicine-b2c-trade-center-export</artifactId>
            <version>3.1.4-SNAPSHOT</version>
        </dependency>

        <!--处方-->
        <dependency>
            <groupId>com.jd.pop.nethp.auditrx</groupId>
            <artifactId>nethp-rx-doctor-export</artifactId>
            <!--    SNAPSHOT -->
            <version>2.2.76</version>
        </dependency>

        <!--医患关系-->
        <dependency>
            <groupId>com.jd.newnethp.in.diag</groupId>
            <artifactId>newnethp-in-diag-export</artifactId>
            <version>1.7.37</version>
        </dependency>

        <!--物流中台-->
        <dependency>
            <groupId>cn.jdl.express</groupId>
            <artifactId>jdl-eca-api</artifactId>
            <version>2.0.1.6-SNAPSHOT</version>
        </dependency>

        <!-- 风险识别服务：https://joyspace.jd.com/pages/fy7ySa4MiKJWoJKIxKa9 -->
        <dependency>
            <groupId>com.jd.risk.riskservice</groupId>
            <artifactId>riskservice-client</artifactId>
            <version>2.3.2</version>
        </dependency>
        <!--查询病假条图片JSF接口-->
        <dependency>
            <groupId>com.jdh.afterdiag.medicalrecord</groupId>
            <artifactId>jdh-afterdiag-medicalrecord-export</artifactId>
            <version>1.1.59</version>
            <exclusions>
                <exclusion>
                    <groupId>org.mybatis</groupId>
                    <artifactId>mybatis</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-web</artifactId>
                </exclusion>

                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>easyexcel</artifactId>
                </exclusion>

                <exclusion>
                    <groupId>org.projectlombok</groupId>
                    <artifactId>lombok</artifactId>
                </exclusion>

                <exclusion>
                    <groupId>nethp-components-zootopia-joy</groupId>
                    <artifactId>nethp-components-zootopia-joy-export</artifactId>
                </exclusion>

                <exclusion>
                    <artifactId>mybatis-spring</artifactId>
                    <groupId>org.mybatis</groupId>
                </exclusion>

                <exclusion>
                    <groupId>com.jd.medicine.b2c.base</groupId>
                    <artifactId>medicine-b2c-base-common</artifactId>
                </exclusion>

            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.jdh.o2oservice.b2b</groupId>
            <artifactId>jdh-o2o-service-b2b-export</artifactId>
            <version>1.0.1-SNAPSHOT</version>
        </dependency>

        <!--智能体-->
        <dependency>
            <groupId>com.jd.autobots</groupId>
            <artifactId>autobots-client</artifactId>
            <version>1.2.1-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.jd.health.ares.open.platform</groupId>
            <artifactId>ares-open-platform-export</artifactId>
            <version>1.7.20-SNAPSHOT</version>
        </dependency>

    </dependencies>
</project>
